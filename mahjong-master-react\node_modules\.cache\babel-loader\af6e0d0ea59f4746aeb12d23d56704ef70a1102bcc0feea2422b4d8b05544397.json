{"ast": null, "code": "import { Buffer<PERSON>ttribute, BufferGeometry, Vector3, Vector4, Matrix4, Matrix3 } from 'three';\nconst _positionVector = /*@__PURE__*/new Vector3();\nconst _normalVector = /*@__PURE__*/new Vector3();\nconst _tangentVector = /*@__PURE__*/new Vector3();\nconst _tangentVector4 = /*@__PURE__*/new Vector4();\nconst _morphVector = /*@__PURE__*/new Vector3();\nconst _temp = /*@__PURE__*/new Vector3();\nconst _skinIndex = /*@__PURE__*/new Vector4();\nconst _skinWeight = /*@__PURE__*/new Vector4();\nconst _matrix = /*@__PURE__*/new Matrix4();\nconst _boneMatrix = /*@__PURE__*/new Matrix4();\n\n// Confirms that the two provided attributes are compatible\nfunction validateAttributes(attr1, attr2) {\n  if (!attr1 && !attr2) {\n    return;\n  }\n  const sameCount = attr1.count === attr2.count;\n  const sameNormalized = attr1.normalized === attr2.normalized;\n  const sameType = attr1.array.constructor === attr2.array.constructor;\n  const sameItemSize = attr1.itemSize === attr2.itemSize;\n  if (!sameCount || !sameNormalized || !sameType || !sameItemSize) {\n    throw new Error();\n  }\n}\n\n// Clones the given attribute with a new compatible buffer attribute but no data\nfunction createAttributeClone(attr, countOverride = null) {\n  const cons = attr.array.constructor;\n  const normalized = attr.normalized;\n  const itemSize = attr.itemSize;\n  const count = countOverride === null ? attr.count : countOverride;\n  return new BufferAttribute(new cons(itemSize * count), itemSize, normalized);\n}\n\n// target offset is the number of elements in the target buffer stride to skip before copying the\n// attributes contents in to.\nfunction copyAttributeContents(attr, target, targetOffset = 0) {\n  if (attr.isInterleavedBufferAttribute) {\n    const itemSize = attr.itemSize;\n    for (let i = 0, l = attr.count; i < l; i++) {\n      const io = i + targetOffset;\n      target.setX(io, attr.getX(i));\n      if (itemSize >= 2) target.setY(io, attr.getY(i));\n      if (itemSize >= 3) target.setZ(io, attr.getZ(i));\n      if (itemSize >= 4) target.setW(io, attr.getW(i));\n    }\n  } else {\n    const array = target.array;\n    const cons = array.constructor;\n    const byteOffset = array.BYTES_PER_ELEMENT * attr.itemSize * targetOffset;\n    const temp = new cons(array.buffer, byteOffset, attr.array.length);\n    temp.set(attr.array);\n  }\n}\n\n// Adds the \"matrix\" multiplied by \"scale\" to \"target\"\nfunction addScaledMatrix(target, matrix, scale) {\n  const targetArray = target.elements;\n  const matrixArray = matrix.elements;\n  for (let i = 0, l = matrixArray.length; i < l; i++) {\n    targetArray[i] += matrixArray[i] * scale;\n  }\n}\n\n// A version of \"SkinnedMesh.boneTransform\" for normals\nfunction boneNormalTransform(mesh, index, target) {\n  const skeleton = mesh.skeleton;\n  const geometry = mesh.geometry;\n  const bones = skeleton.bones;\n  const boneInverses = skeleton.boneInverses;\n  _skinIndex.fromBufferAttribute(geometry.attributes.skinIndex, index);\n  _skinWeight.fromBufferAttribute(geometry.attributes.skinWeight, index);\n  _matrix.elements.fill(0);\n  for (let i = 0; i < 4; i++) {\n    const weight = _skinWeight.getComponent(i);\n    if (weight !== 0) {\n      const boneIndex = _skinIndex.getComponent(i);\n      _boneMatrix.multiplyMatrices(bones[boneIndex].matrixWorld, boneInverses[boneIndex]);\n      addScaledMatrix(_matrix, _boneMatrix, weight);\n    }\n  }\n  _matrix.multiply(mesh.bindMatrix).premultiply(mesh.bindMatrixInverse);\n  target.transformDirection(_matrix);\n  return target;\n}\n\n// Applies the morph target data to the target vector\nfunction applyMorphTarget(morphData, morphInfluences, morphTargetsRelative, i, target) {\n  _morphVector.set(0, 0, 0);\n  for (let j = 0, jl = morphData.length; j < jl; j++) {\n    const influence = morphInfluences[j];\n    const morphAttribute = morphData[j];\n    if (influence === 0) continue;\n    _temp.fromBufferAttribute(morphAttribute, i);\n    if (morphTargetsRelative) {\n      _morphVector.addScaledVector(_temp, influence);\n    } else {\n      _morphVector.addScaledVector(_temp.sub(target), influence);\n    }\n  }\n  target.add(_morphVector);\n}\n\n// Modified version of BufferGeometryUtils.mergeBufferGeometries that ignores morph targets and updates a attributes in place\nfunction mergeBufferGeometries(geometries, options = {\n  useGroups: false,\n  updateIndex: false,\n  skipAttributes: []\n}, targetGeometry = new BufferGeometry()) {\n  const isIndexed = geometries[0].index !== null;\n  const {\n    useGroups = false,\n    updateIndex = false,\n    skipAttributes = []\n  } = options;\n  const attributesUsed = new Set(Object.keys(geometries[0].attributes));\n  const attributes = {};\n  let offset = 0;\n  targetGeometry.clearGroups();\n  for (let i = 0; i < geometries.length; ++i) {\n    const geometry = geometries[i];\n    let attributesCount = 0;\n\n    // ensure that all geometries are indexed, or none\n    if (isIndexed !== (geometry.index !== null)) {\n      throw new Error('StaticGeometryGenerator: All geometries must have compatible attributes; make sure index attribute exists among all geometries, or in none of them.');\n    }\n\n    // gather attributes, exit early if they're different\n    for (const name in geometry.attributes) {\n      if (!attributesUsed.has(name)) {\n        throw new Error('StaticGeometryGenerator: All geometries must have compatible attributes; make sure \"' + name + '\" attribute exists among all geometries, or in none of them.');\n      }\n      if (attributes[name] === undefined) {\n        attributes[name] = [];\n      }\n      attributes[name].push(geometry.attributes[name]);\n      attributesCount++;\n    }\n\n    // ensure geometries have the same number of attributes\n    if (attributesCount !== attributesUsed.size) {\n      throw new Error('StaticGeometryGenerator: Make sure all geometries have the same number of attributes.');\n    }\n    if (useGroups) {\n      let count;\n      if (isIndexed) {\n        count = geometry.index.count;\n      } else if (geometry.attributes.position !== undefined) {\n        count = geometry.attributes.position.count;\n      } else {\n        throw new Error('StaticGeometryGenerator: The geometry must have either an index or a position attribute');\n      }\n      targetGeometry.addGroup(offset, count, i);\n      offset += count;\n    }\n  }\n\n  // merge indices\n  if (isIndexed) {\n    let forceUpdateIndex = false;\n    if (!targetGeometry.index) {\n      let indexCount = 0;\n      for (let i = 0; i < geometries.length; ++i) {\n        indexCount += geometries[i].index.count;\n      }\n      targetGeometry.setIndex(new BufferAttribute(new Uint32Array(indexCount), 1, false));\n      forceUpdateIndex = true;\n    }\n    if (updateIndex || forceUpdateIndex) {\n      const targetIndex = targetGeometry.index;\n      let targetOffset = 0;\n      let indexOffset = 0;\n      for (let i = 0; i < geometries.length; ++i) {\n        const geometry = geometries[i];\n        const index = geometry.index;\n        if (skipAttributes[i] !== true) {\n          for (let j = 0; j < index.count; ++j) {\n            targetIndex.setX(targetOffset, index.getX(j) + indexOffset);\n            targetOffset++;\n          }\n        }\n        indexOffset += geometry.attributes.position.count;\n      }\n    }\n  }\n\n  // merge attributes\n  for (const name in attributes) {\n    const attrList = attributes[name];\n    if (!(name in targetGeometry.attributes)) {\n      let count = 0;\n      for (const key in attrList) {\n        count += attrList[key].count;\n      }\n      targetGeometry.setAttribute(name, createAttributeClone(attributes[name][0], count));\n    }\n    const targetAttribute = targetGeometry.attributes[name];\n    let offset = 0;\n    for (let i = 0, l = attrList.length; i < l; i++) {\n      const attr = attrList[i];\n      if (skipAttributes[i] !== true) {\n        copyAttributeContents(attr, targetAttribute, offset);\n      }\n      offset += attr.count;\n    }\n  }\n  return targetGeometry;\n}\nfunction checkTypedArrayEquality(a, b) {\n  if (a === null || b === null) {\n    return a === b;\n  }\n  if (a.length !== b.length) {\n    return false;\n  }\n  for (let i = 0, l = a.length; i < l; i++) {\n    if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction invertGeometry(geometry) {\n  const {\n    index,\n    attributes\n  } = geometry;\n  if (index) {\n    for (let i = 0, l = index.count; i < l; i += 3) {\n      const v0 = index.getX(i);\n      const v2 = index.getX(i + 2);\n      index.setX(i, v2);\n      index.setX(i + 2, v0);\n    }\n  } else {\n    for (const key in attributes) {\n      const attr = attributes[key];\n      const itemSize = attr.itemSize;\n      for (let i = 0, l = attr.count; i < l; i += 3) {\n        for (let j = 0; j < itemSize; j++) {\n          const v0 = attr.getComponent(i, j);\n          const v2 = attr.getComponent(i + 2, j);\n          attr.setComponent(i, j, v2);\n          attr.setComponent(i + 2, j, v0);\n        }\n      }\n    }\n  }\n  return geometry;\n}\n\n// Checks whether the geometry changed between this and last evaluation\nclass GeometryDiff {\n  constructor(mesh) {\n    this.matrixWorld = new Matrix4();\n    this.geometryHash = null;\n    this.boneMatrices = null;\n    this.primitiveCount = -1;\n    this.mesh = mesh;\n    this.update();\n  }\n  update() {\n    const mesh = this.mesh;\n    const geometry = mesh.geometry;\n    const skeleton = mesh.skeleton;\n    const primitiveCount = (geometry.index ? geometry.index.count : geometry.attributes.position.count) / 3;\n    this.matrixWorld.copy(mesh.matrixWorld);\n    this.geometryHash = geometry.attributes.position.version;\n    this.primitiveCount = primitiveCount;\n    if (skeleton) {\n      // ensure the bone matrix array is updated to the appropriate length\n      if (!skeleton.boneTexture) {\n        skeleton.computeBoneTexture();\n      }\n      skeleton.update();\n\n      // copy data if possible otherwise clone it\n      const boneMatrices = skeleton.boneMatrices;\n      if (!this.boneMatrices || this.boneMatrices.length !== boneMatrices.length) {\n        this.boneMatrices = boneMatrices.slice();\n      } else {\n        this.boneMatrices.set(boneMatrices);\n      }\n    } else {\n      this.boneMatrices = null;\n    }\n  }\n  didChange() {\n    const mesh = this.mesh;\n    const geometry = mesh.geometry;\n    const primitiveCount = (geometry.index ? geometry.index.count : geometry.attributes.position.count) / 3;\n    const identical = this.matrixWorld.equals(mesh.matrixWorld) && this.geometryHash === geometry.attributes.position.version && checkTypedArrayEquality(mesh.skeleton && mesh.skeleton.boneMatrices || null, this.boneMatrices) && this.primitiveCount === primitiveCount;\n    return !identical;\n  }\n}\nexport class StaticGeometryGenerator {\n  constructor(meshes) {\n    if (!Array.isArray(meshes)) {\n      meshes = [meshes];\n    }\n    const finalMeshes = [];\n    meshes.forEach(object => {\n      object.traverseVisible(c => {\n        if (c.isMesh) {\n          finalMeshes.push(c);\n        }\n      });\n    });\n    this.meshes = finalMeshes;\n    this.useGroups = true;\n    this.applyWorldTransforms = true;\n    this.attributes = ['position', 'normal', 'color', 'tangent', 'uv', 'uv2'];\n    this._intermediateGeometry = new Array(finalMeshes.length).fill().map(() => new BufferGeometry());\n    this._diffMap = new WeakMap();\n  }\n  getMaterials() {\n    const materials = [];\n    this.meshes.forEach(mesh => {\n      if (Array.isArray(mesh.material)) {\n        materials.push(...mesh.material);\n      } else {\n        materials.push(mesh.material);\n      }\n    });\n    return materials;\n  }\n  generate(targetGeometry = new BufferGeometry()) {\n    // track which attributes have been updated and which to skip to avoid unnecessary attribute copies\n    let skipAttributes = [];\n    const {\n      meshes,\n      useGroups,\n      _intermediateGeometry,\n      _diffMap\n    } = this;\n    for (let i = 0, l = meshes.length; i < l; i++) {\n      const mesh = meshes[i];\n      const geom = _intermediateGeometry[i];\n      const diff = _diffMap.get(mesh);\n      if (!diff || diff.didChange(mesh)) {\n        this._convertToStaticGeometry(mesh, geom);\n        skipAttributes.push(false);\n        if (!diff) {\n          _diffMap.set(mesh, new GeometryDiff(mesh));\n        } else {\n          diff.update();\n        }\n      } else {\n        skipAttributes.push(true);\n      }\n    }\n    if (_intermediateGeometry.length === 0) {\n      // if there are no geometries then just create a fake empty geometry to provide\n      targetGeometry.setIndex(null);\n\n      // remove all geometry\n      const attrs = targetGeometry.attributes;\n      for (const key in attrs) {\n        targetGeometry.deleteAttribute(key);\n      }\n\n      // create dummy attributes\n      for (const key in this.attributes) {\n        targetGeometry.setAttribute(this.attributes[key], new BufferAttribute(new Float32Array(0), 4, false));\n      }\n    } else {\n      mergeBufferGeometries(_intermediateGeometry, {\n        useGroups,\n        skipAttributes\n      }, targetGeometry);\n    }\n    for (const key in targetGeometry.attributes) {\n      targetGeometry.attributes[key].needsUpdate = true;\n    }\n    return targetGeometry;\n  }\n  _convertToStaticGeometry(mesh, targetGeometry = new BufferGeometry()) {\n    const geometry = mesh.geometry;\n    const applyWorldTransforms = this.applyWorldTransforms;\n    const includeNormal = this.attributes.includes('normal');\n    const includeTangent = this.attributes.includes('tangent');\n    const attributes = geometry.attributes;\n    const targetAttributes = targetGeometry.attributes;\n\n    // initialize the attributes if they don't exist\n    if (!targetGeometry.index && geometry.index) {\n      targetGeometry.index = geometry.index.clone();\n    }\n    if (!targetAttributes.position) {\n      targetGeometry.setAttribute('position', createAttributeClone(attributes.position));\n    }\n    if (includeNormal && !targetAttributes.normal && attributes.normal) {\n      targetGeometry.setAttribute('normal', createAttributeClone(attributes.normal));\n    }\n    if (includeTangent && !targetAttributes.tangent && attributes.tangent) {\n      targetGeometry.setAttribute('tangent', createAttributeClone(attributes.tangent));\n    }\n\n    // ensure the attributes are consistent\n    validateAttributes(geometry.index, targetGeometry.index);\n    validateAttributes(attributes.position, targetAttributes.position);\n    if (includeNormal) {\n      validateAttributes(attributes.normal, targetAttributes.normal);\n    }\n    if (includeTangent) {\n      validateAttributes(attributes.tangent, targetAttributes.tangent);\n    }\n\n    // generate transformed vertex attribute data\n    const position = attributes.position;\n    const normal = includeNormal ? attributes.normal : null;\n    const tangent = includeTangent ? attributes.tangent : null;\n    const morphPosition = geometry.morphAttributes.position;\n    const morphNormal = geometry.morphAttributes.normal;\n    const morphTangent = geometry.morphAttributes.tangent;\n    const morphTargetsRelative = geometry.morphTargetsRelative;\n    const morphInfluences = mesh.morphTargetInfluences;\n    const normalMatrix = new Matrix3();\n    normalMatrix.getNormalMatrix(mesh.matrixWorld);\n\n    // copy the index\n    if (geometry.index) {\n      targetGeometry.index.array.set(geometry.index.array);\n    }\n\n    // copy and apply other attributes\n    for (let i = 0, l = attributes.position.count; i < l; i++) {\n      _positionVector.fromBufferAttribute(position, i);\n      if (normal) {\n        _normalVector.fromBufferAttribute(normal, i);\n      }\n      if (tangent) {\n        _tangentVector4.fromBufferAttribute(tangent, i);\n        _tangentVector.fromBufferAttribute(tangent, i);\n      }\n\n      // apply morph target transform\n      if (morphInfluences) {\n        if (morphPosition) {\n          applyMorphTarget(morphPosition, morphInfluences, morphTargetsRelative, i, _positionVector);\n        }\n        if (morphNormal) {\n          applyMorphTarget(morphNormal, morphInfluences, morphTargetsRelative, i, _normalVector);\n        }\n        if (morphTangent) {\n          applyMorphTarget(morphTangent, morphInfluences, morphTargetsRelative, i, _tangentVector);\n        }\n      }\n\n      // apply bone transform\n      if (mesh.isSkinnedMesh) {\n        mesh.applyBoneTransform(i, _positionVector);\n        if (normal) {\n          boneNormalTransform(mesh, i, _normalVector);\n        }\n        if (tangent) {\n          boneNormalTransform(mesh, i, _tangentVector);\n        }\n      }\n\n      // update the vectors of the attributes\n      if (applyWorldTransforms) {\n        _positionVector.applyMatrix4(mesh.matrixWorld);\n      }\n      targetAttributes.position.setXYZ(i, _positionVector.x, _positionVector.y, _positionVector.z);\n      if (normal) {\n        if (applyWorldTransforms) {\n          _normalVector.applyNormalMatrix(normalMatrix);\n        }\n        targetAttributes.normal.setXYZ(i, _normalVector.x, _normalVector.y, _normalVector.z);\n      }\n      if (tangent) {\n        if (applyWorldTransforms) {\n          _tangentVector.transformDirection(mesh.matrixWorld);\n        }\n        targetAttributes.tangent.setXYZW(i, _tangentVector.x, _tangentVector.y, _tangentVector.z, _tangentVector4.w);\n      }\n    }\n\n    // copy other attributes over\n    for (const i in this.attributes) {\n      const key = this.attributes[i];\n      if (key === 'position' || key === 'tangent' || key === 'normal' || !(key in attributes)) {\n        continue;\n      }\n      if (!targetAttributes[key]) {\n        targetGeometry.setAttribute(key, createAttributeClone(attributes[key]));\n      }\n      validateAttributes(attributes[key], targetAttributes[key]);\n      copyAttributeContents(attributes[key], targetAttributes[key]);\n    }\n    if (mesh.matrixWorld.determinant() < 0) {\n      invertGeometry(targetGeometry);\n    }\n    return targetGeometry;\n  }\n}", "map": {"version": 3, "names": ["BufferAttribute", "BufferGeometry", "Vector3", "Vector4", "Matrix4", "Matrix3", "_positionVector", "_normalVector", "_tangentVector", "_tangentVector4", "_morphVector", "_temp", "_skinIndex", "_skinWeight", "_matrix", "_boneMatrix", "validateAttributes", "attr1", "attr2", "sameCount", "count", "sameNormalized", "normalized", "sameType", "array", "constructor", "sameItemSize", "itemSize", "Error", "createAttributeClone", "attr", "countOverride", "cons", "copyAttributeContents", "target", "targetOffset", "isInterleavedBufferAttribute", "i", "l", "io", "setX", "getX", "setY", "getY", "setZ", "getZ", "setW", "getW", "byteOffset", "BYTES_PER_ELEMENT", "temp", "buffer", "length", "set", "addScaledMatrix", "matrix", "scale", "targetArray", "elements", "matrixArray", "boneNormalTransform", "mesh", "index", "skeleton", "geometry", "bones", "boneInverses", "fromBufferAttribute", "attributes", "skinIndex", "skinWeight", "fill", "weight", "getComponent", "boneIndex", "multiplyMatrices", "matrixWorld", "multiply", "bindMatrix", "premultiply", "bindMatrixInverse", "transformDirection", "applyMorphTarget", "morphData", "morphInfluences", "morphTargetsRelative", "j", "jl", "influence", "morphAttribute", "addScaledVector", "sub", "add", "mergeBufferGeometries", "geometries", "options", "useGroups", "updateIndex", "skipAttributes", "targetGeometry", "isIndexed", "attributesUsed", "Set", "Object", "keys", "offset", "clearGroups", "attributesCount", "name", "has", "undefined", "push", "size", "position", "addGroup", "forceUpdateIndex", "indexCount", "setIndex", "Uint32Array", "targetIndex", "indexOffset", "attrList", "key", "setAttribute", "targetAttribute", "checkTypedArrayEquality", "a", "b", "invertGeometry", "v0", "v2", "setComponent", "GeometryDiff", "geometryHash", "boneMatrices", "primitiveCount", "update", "copy", "version", "boneTexture", "computeBoneTexture", "slice", "<PERSON><PERSON><PERSON><PERSON>", "identical", "equals", "StaticGeometryGenerator", "meshes", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "object", "traverseVisible", "c", "<PERSON><PERSON><PERSON>", "applyWorldTransforms", "_intermediateGeometry", "map", "_diffMap", "WeakMap", "getMaterials", "materials", "material", "generate", "geom", "diff", "get", "_convertToStaticGeometry", "attrs", "deleteAttribute", "Float32Array", "needsUpdate", "includeNormal", "includes", "includeTangent", "targetAttributes", "clone", "normal", "tangent", "morphPosition", "morphAttributes", "morphNormal", "morphTangent", "morphTargetInfluences", "normalMatrix", "getNormalMatrix", "isSkinnedMesh", "applyBoneTransform", "applyMatrix4", "setXYZ", "x", "y", "z", "applyNormalMatrix", "setXYZW", "w", "determinant"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/three-mesh-bvh/src/utils/StaticGeometryGenerator.js"], "sourcesContent": ["import { Buffer<PERSON>ttribute, BufferGeometry, Vector3, Vector4, Matrix4, Matrix3 } from 'three';\n\nconst _positionVector = /*@__PURE__*/ new Vector3();\nconst _normalVector = /*@__PURE__*/ new Vector3();\nconst _tangentVector = /*@__PURE__*/ new Vector3();\nconst _tangentVector4 = /*@__PURE__*/ new Vector4();\n\nconst _morphVector = /*@__PURE__*/ new Vector3();\nconst _temp = /*@__PURE__*/ new Vector3();\n\nconst _skinIndex = /*@__PURE__*/ new Vector4();\nconst _skinWeight = /*@__PURE__*/ new Vector4();\nconst _matrix = /*@__PURE__*/ new Matrix4();\nconst _boneMatrix = /*@__PURE__*/ new Matrix4();\n\n// Confirms that the two provided attributes are compatible\nfunction validateAttributes( attr1, attr2 ) {\n\n\tif ( ! attr1 && ! attr2 ) {\n\n\t\treturn;\n\n\t}\n\n\tconst sameCount = attr1.count === attr2.count;\n\tconst sameNormalized = attr1.normalized === attr2.normalized;\n\tconst sameType = attr1.array.constructor === attr2.array.constructor;\n\tconst sameItemSize = attr1.itemSize === attr2.itemSize;\n\n\tif ( ! sameCount || ! sameNormalized || ! sameType || ! sameItemSize ) {\n\n\t\tthrow new Error();\n\n\t}\n\n}\n\n// Clones the given attribute with a new compatible buffer attribute but no data\nfunction createAttributeClone( attr, countOverride = null ) {\n\n\tconst cons = attr.array.constructor;\n\tconst normalized = attr.normalized;\n\tconst itemSize = attr.itemSize;\n\tconst count = countOverride === null ? attr.count : countOverride;\n\n\treturn new BufferAttribute( new cons( itemSize * count ), itemSize, normalized );\n\n}\n\n// target offset is the number of elements in the target buffer stride to skip before copying the\n// attributes contents in to.\nfunction copyAttributeContents( attr, target, targetOffset = 0 ) {\n\n\tif ( attr.isInterleavedBufferAttribute ) {\n\n\t\tconst itemSize = attr.itemSize;\n\t\tfor ( let i = 0, l = attr.count; i < l; i ++ ) {\n\n\t\t\tconst io = i + targetOffset;\n\t\t\ttarget.setX( io, attr.getX( i ) );\n\t\t\tif ( itemSize >= 2 ) target.setY( io, attr.getY( i ) );\n\t\t\tif ( itemSize >= 3 ) target.setZ( io, attr.getZ( i ) );\n\t\t\tif ( itemSize >= 4 ) target.setW( io, attr.getW( i ) );\n\n\t\t}\n\n\t} else {\n\n\t\tconst array = target.array;\n\t\tconst cons = array.constructor;\n\t\tconst byteOffset = array.BYTES_PER_ELEMENT * attr.itemSize * targetOffset;\n\t\tconst temp = new cons( array.buffer, byteOffset, attr.array.length );\n\t\ttemp.set( attr.array );\n\n\t}\n\n}\n\n// Adds the \"matrix\" multiplied by \"scale\" to \"target\"\nfunction addScaledMatrix( target, matrix, scale ) {\n\n\tconst targetArray = target.elements;\n\tconst matrixArray = matrix.elements;\n\tfor ( let i = 0, l = matrixArray.length; i < l; i ++ ) {\n\n\t\ttargetArray[ i ] += matrixArray[ i ] * scale;\n\n\t}\n\n}\n\n// A version of \"SkinnedMesh.boneTransform\" for normals\nfunction boneNormalTransform( mesh, index, target ) {\n\n\tconst skeleton = mesh.skeleton;\n\tconst geometry = mesh.geometry;\n\tconst bones = skeleton.bones;\n\tconst boneInverses = skeleton.boneInverses;\n\n\t_skinIndex.fromBufferAttribute( geometry.attributes.skinIndex, index );\n\t_skinWeight.fromBufferAttribute( geometry.attributes.skinWeight, index );\n\n\t_matrix.elements.fill( 0 );\n\n\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\tconst weight = _skinWeight.getComponent( i );\n\n\t\tif ( weight !== 0 ) {\n\n\t\t\tconst boneIndex = _skinIndex.getComponent( i );\n\t\t\t_boneMatrix.multiplyMatrices( bones[ boneIndex ].matrixWorld, boneInverses[ boneIndex ] );\n\n\t\t\taddScaledMatrix( _matrix, _boneMatrix, weight );\n\n\t\t}\n\n\t}\n\n\t_matrix.multiply( mesh.bindMatrix ).premultiply( mesh.bindMatrixInverse );\n\ttarget.transformDirection( _matrix );\n\n\treturn target;\n\n}\n\n// Applies the morph target data to the target vector\nfunction applyMorphTarget( morphData, morphInfluences, morphTargetsRelative, i, target ) {\n\n\t_morphVector.set( 0, 0, 0 );\n\tfor ( let j = 0, jl = morphData.length; j < jl; j ++ ) {\n\n\t\tconst influence = morphInfluences[ j ];\n\t\tconst morphAttribute = morphData[ j ];\n\n\t\tif ( influence === 0 ) continue;\n\n\t\t_temp.fromBufferAttribute( morphAttribute, i );\n\n\t\tif ( morphTargetsRelative ) {\n\n\t\t\t_morphVector.addScaledVector( _temp, influence );\n\n\t\t} else {\n\n\t\t\t_morphVector.addScaledVector( _temp.sub( target ), influence );\n\n\t\t}\n\n\t}\n\n\ttarget.add( _morphVector );\n\n}\n\n// Modified version of BufferGeometryUtils.mergeBufferGeometries that ignores morph targets and updates a attributes in place\nfunction mergeBufferGeometries( geometries, options = { useGroups: false, updateIndex: false, skipAttributes: [] }, targetGeometry = new BufferGeometry() ) {\n\n\tconst isIndexed = geometries[ 0 ].index !== null;\n\tconst { useGroups = false, updateIndex = false, skipAttributes = [] } = options;\n\n\tconst attributesUsed = new Set( Object.keys( geometries[ 0 ].attributes ) );\n\tconst attributes = {};\n\n\tlet offset = 0;\n\n\ttargetGeometry.clearGroups();\n\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\tconst geometry = geometries[ i ];\n\t\tlet attributesCount = 0;\n\n\t\t// ensure that all geometries are indexed, or none\n\t\tif ( isIndexed !== ( geometry.index !== null ) ) {\n\n\t\t\tthrow new Error( 'StaticGeometryGenerator: All geometries must have compatible attributes; make sure index attribute exists among all geometries, or in none of them.' );\n\n\t\t}\n\n\t\t// gather attributes, exit early if they're different\n\t\tfor ( const name in geometry.attributes ) {\n\n\t\t\tif ( ! attributesUsed.has( name ) ) {\n\n\t\t\t\tthrow new Error( 'StaticGeometryGenerator: All geometries must have compatible attributes; make sure \"' + name + '\" attribute exists among all geometries, or in none of them.' );\n\n\t\t\t}\n\n\t\t\tif ( attributes[ name ] === undefined ) {\n\n\t\t\t\tattributes[ name ] = [];\n\n\t\t\t}\n\n\t\t\tattributes[ name ].push( geometry.attributes[ name ] );\n\t\t\tattributesCount ++;\n\n\t\t}\n\n\t\t// ensure geometries have the same number of attributes\n\t\tif ( attributesCount !== attributesUsed.size ) {\n\n\t\t\tthrow new Error( 'StaticGeometryGenerator: Make sure all geometries have the same number of attributes.' );\n\n\t\t}\n\n\t\tif ( useGroups ) {\n\n\t\t\tlet count;\n\t\t\tif ( isIndexed ) {\n\n\t\t\t\tcount = geometry.index.count;\n\n\t\t\t} else if ( geometry.attributes.position !== undefined ) {\n\n\t\t\t\tcount = geometry.attributes.position.count;\n\n\t\t\t} else {\n\n\t\t\t\tthrow new Error( 'StaticGeometryGenerator: The geometry must have either an index or a position attribute' );\n\n\t\t\t}\n\n\t\t\ttargetGeometry.addGroup( offset, count, i );\n\t\t\toffset += count;\n\n\t\t}\n\n\t}\n\n\t// merge indices\n\tif ( isIndexed ) {\n\n\t\tlet forceUpdateIndex = false;\n\t\tif ( ! targetGeometry.index ) {\n\n\t\t\tlet indexCount = 0;\n\t\t\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\t\t\tindexCount += geometries[ i ].index.count;\n\n\t\t\t}\n\n\t\t\ttargetGeometry.setIndex( new BufferAttribute( new Uint32Array( indexCount ), 1, false ) );\n\t\t\tforceUpdateIndex = true;\n\n\t\t}\n\n\t\tif ( updateIndex || forceUpdateIndex ) {\n\n\t\t\tconst targetIndex = targetGeometry.index;\n\t\t\tlet targetOffset = 0;\n\t\t\tlet indexOffset = 0;\n\t\t\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\t\t\tconst geometry = geometries[ i ];\n\t\t\t\tconst index = geometry.index;\n\t\t\t\tif ( skipAttributes[ i ] !== true ) {\n\n\t\t\t\t\tfor ( let j = 0; j < index.count; ++ j ) {\n\n\t\t\t\t\t\ttargetIndex.setX( targetOffset, index.getX( j ) + indexOffset );\n\t\t\t\t\t\ttargetOffset ++;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tindexOffset += geometry.attributes.position.count;\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t// merge attributes\n\tfor ( const name in attributes ) {\n\n\t\tconst attrList = attributes[ name ];\n\t\tif ( ! ( name in targetGeometry.attributes ) ) {\n\n\t\t\tlet count = 0;\n\t\t\tfor ( const key in attrList ) {\n\n\t\t\t\tcount += attrList[ key ].count;\n\n\t\t\t}\n\n\t\t\ttargetGeometry.setAttribute( name, createAttributeClone( attributes[ name ][ 0 ], count ) );\n\n\t\t}\n\n\t\tconst targetAttribute = targetGeometry.attributes[ name ];\n\t\tlet offset = 0;\n\t\tfor ( let i = 0, l = attrList.length; i < l; i ++ ) {\n\n\t\t\tconst attr = attrList[ i ];\n\t\t\tif ( skipAttributes[ i ] !== true ) {\n\n\t\t\t\tcopyAttributeContents( attr, targetAttribute, offset );\n\n\t\t\t}\n\n\t\t\toffset += attr.count;\n\n\t\t}\n\n\t}\n\n\treturn targetGeometry;\n\n}\n\nfunction checkTypedArrayEquality( a, b ) {\n\n\tif ( a === null || b === null ) {\n\n\t\treturn a === b;\n\n\t}\n\n\tif ( a.length !== b.length ) {\n\n\t\treturn false;\n\n\t}\n\n\tfor ( let i = 0, l = a.length; i < l; i ++ ) {\n\n\t\tif ( a[ i ] !== b[ i ] ) {\n\n\t\t\treturn false;\n\n\t\t}\n\n\t}\n\n\treturn true;\n\n}\n\nfunction invertGeometry( geometry ) {\n\n\tconst { index, attributes } = geometry;\n\tif ( index ) {\n\n\t\tfor ( let i = 0, l = index.count; i < l; i += 3 ) {\n\n\t\t\tconst v0 = index.getX( i );\n\t\t\tconst v2 = index.getX( i + 2 );\n\t\t\tindex.setX( i, v2 );\n\t\t\tindex.setX( i + 2, v0 );\n\n\t\t}\n\n\t} else {\n\n\t\tfor ( const key in attributes ) {\n\n\t\t\tconst attr = attributes[ key ];\n\t\t\tconst itemSize = attr.itemSize;\n\t\t\tfor ( let i = 0, l = attr.count; i < l; i += 3 ) {\n\n\t\t\t\tfor ( let j = 0; j < itemSize; j ++ ) {\n\n\t\t\t\t\tconst v0 = attr.getComponent( i, j );\n\t\t\t\t\tconst v2 = attr.getComponent( i + 2, j );\n\t\t\t\t\tattr.setComponent( i, j, v2 );\n\t\t\t\t\tattr.setComponent( i + 2, j, v0 );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\treturn geometry;\n\n\n}\n\n// Checks whether the geometry changed between this and last evaluation\nclass GeometryDiff {\n\n\tconstructor( mesh ) {\n\n\t\tthis.matrixWorld = new Matrix4();\n\t\tthis.geometryHash = null;\n\t\tthis.boneMatrices = null;\n\t\tthis.primitiveCount = - 1;\n\t\tthis.mesh = mesh;\n\n\t\tthis.update();\n\n\t}\n\n\tupdate() {\n\n\t\tconst mesh = this.mesh;\n\t\tconst geometry = mesh.geometry;\n\t\tconst skeleton = mesh.skeleton;\n\t\tconst primitiveCount = ( geometry.index ? geometry.index.count : geometry.attributes.position.count ) / 3;\n\t\tthis.matrixWorld.copy( mesh.matrixWorld );\n\t\tthis.geometryHash = geometry.attributes.position.version;\n\t\tthis.primitiveCount = primitiveCount;\n\n\t\tif ( skeleton ) {\n\n\t\t\t// ensure the bone matrix array is updated to the appropriate length\n\t\t\tif ( ! skeleton.boneTexture ) {\n\n\t\t\t\tskeleton.computeBoneTexture();\n\n\t\t\t}\n\n\t\t\tskeleton.update();\n\n\t\t\t// copy data if possible otherwise clone it\n\t\t\tconst boneMatrices = skeleton.boneMatrices;\n\t\t\tif ( ! this.boneMatrices || this.boneMatrices.length !== boneMatrices.length ) {\n\n\t\t\t\tthis.boneMatrices = boneMatrices.slice();\n\n\t\t\t} else {\n\n\t\t\t\tthis.boneMatrices.set( boneMatrices );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tthis.boneMatrices = null;\n\n\t\t}\n\n\t}\n\n\tdidChange() {\n\n\t\tconst mesh = this.mesh;\n\t\tconst geometry = mesh.geometry;\n\t\tconst primitiveCount = ( geometry.index ? geometry.index.count : geometry.attributes.position.count ) / 3;\n\t\tconst identical =\n\t\t\tthis.matrixWorld.equals( mesh.matrixWorld ) &&\n\t\t\tthis.geometryHash === geometry.attributes.position.version &&\n\t\t\tcheckTypedArrayEquality( mesh.skeleton && mesh.skeleton.boneMatrices || null, this.boneMatrices ) &&\n\t\t\tthis.primitiveCount === primitiveCount;\n\n\t\treturn ! identical;\n\n\t}\n\n}\n\nexport class StaticGeometryGenerator {\n\n\tconstructor( meshes ) {\n\n\t\tif ( ! Array.isArray( meshes ) ) {\n\n\t\t\tmeshes = [ meshes ];\n\n\t\t}\n\n\t\tconst finalMeshes = [];\n\t\tmeshes.forEach( object => {\n\n\t\t\tobject.traverseVisible( c => {\n\n\t\t\t\tif ( c.isMesh ) {\n\n\t\t\t\t\tfinalMeshes.push( c );\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t} );\n\n\t\tthis.meshes = finalMeshes;\n\t\tthis.useGroups = true;\n\t\tthis.applyWorldTransforms = true;\n\t\tthis.attributes = [ 'position', 'normal', 'color', 'tangent', 'uv', 'uv2' ];\n\t\tthis._intermediateGeometry = new Array( finalMeshes.length ).fill().map( () => new BufferGeometry() );\n\t\tthis._diffMap = new WeakMap();\n\n\t}\n\n\tgetMaterials() {\n\n\t\tconst materials = [];\n\t\tthis.meshes.forEach( mesh => {\n\n\t\t\tif ( Array.isArray( mesh.material ) ) {\n\n\t\t\t\tmaterials.push( ...mesh.material );\n\n\t\t\t} else {\n\n\t\t\t\tmaterials.push( mesh.material );\n\n\t\t\t}\n\n\t\t} );\n\t\treturn materials;\n\n\t}\n\n\tgenerate( targetGeometry = new BufferGeometry() ) {\n\n\t\t// track which attributes have been updated and which to skip to avoid unnecessary attribute copies\n\t\tlet skipAttributes = [];\n\t\tconst { meshes, useGroups, _intermediateGeometry, _diffMap } = this;\n\t\tfor ( let i = 0, l = meshes.length; i < l; i ++ ) {\n\n\t\t\tconst mesh = meshes[ i ];\n\t\t\tconst geom = _intermediateGeometry[ i ];\n\t\t\tconst diff = _diffMap.get( mesh );\n\t\t\tif ( ! diff || diff.didChange( mesh ) ) {\n\n\t\t\t\tthis._convertToStaticGeometry( mesh, geom );\n\t\t\t\tskipAttributes.push( false );\n\n\t\t\t\tif ( ! diff ) {\n\n\t\t\t\t\t_diffMap.set( mesh, new GeometryDiff( mesh ) );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tdiff.update();\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tskipAttributes.push( true );\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( _intermediateGeometry.length === 0 ) {\n\n\t\t\t// if there are no geometries then just create a fake empty geometry to provide\n\t\t\ttargetGeometry.setIndex( null );\n\n\t\t\t// remove all geometry\n\t\t\tconst attrs = targetGeometry.attributes;\n\t\t\tfor ( const key in attrs ) {\n\n\t\t\t\ttargetGeometry.deleteAttribute( key );\n\n\t\t\t}\n\n\t\t\t// create dummy attributes\n\t\t\tfor ( const key in this.attributes ) {\n\n\t\t\t\ttargetGeometry.setAttribute( this.attributes[ key ], new BufferAttribute( new Float32Array( 0 ), 4, false ) );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tmergeBufferGeometries( _intermediateGeometry, { useGroups, skipAttributes }, targetGeometry );\n\n\t\t}\n\n\t\tfor ( const key in targetGeometry.attributes ) {\n\n\t\t\ttargetGeometry.attributes[ key ].needsUpdate = true;\n\n\t\t}\n\n\t\treturn targetGeometry;\n\n\t}\n\n\t_convertToStaticGeometry( mesh, targetGeometry = new BufferGeometry() ) {\n\n\t\tconst geometry = mesh.geometry;\n\t\tconst applyWorldTransforms = this.applyWorldTransforms;\n\t\tconst includeNormal = this.attributes.includes( 'normal' );\n\t\tconst includeTangent = this.attributes.includes( 'tangent' );\n\t\tconst attributes = geometry.attributes;\n\t\tconst targetAttributes = targetGeometry.attributes;\n\n\t\t// initialize the attributes if they don't exist\n\t\tif ( ! targetGeometry.index && geometry.index ) {\n\n\t\t\ttargetGeometry.index = geometry.index.clone();\n\n\t\t}\n\n\t\tif ( ! targetAttributes.position ) {\n\n\t\t\ttargetGeometry.setAttribute( 'position', createAttributeClone( attributes.position ) );\n\n\t\t}\n\n\t\tif ( includeNormal && ! targetAttributes.normal && attributes.normal ) {\n\n\t\t\ttargetGeometry.setAttribute( 'normal', createAttributeClone( attributes.normal ) );\n\n\t\t}\n\n\t\tif ( includeTangent && ! targetAttributes.tangent && attributes.tangent ) {\n\n\t\t\ttargetGeometry.setAttribute( 'tangent', createAttributeClone( attributes.tangent ) );\n\n\t\t}\n\n\t\t// ensure the attributes are consistent\n\t\tvalidateAttributes( geometry.index, targetGeometry.index );\n\t\tvalidateAttributes( attributes.position, targetAttributes.position );\n\n\t\tif ( includeNormal ) {\n\n\t\t\tvalidateAttributes( attributes.normal, targetAttributes.normal );\n\n\t\t}\n\n\t\tif ( includeTangent ) {\n\n\t\t\tvalidateAttributes( attributes.tangent, targetAttributes.tangent );\n\n\t\t}\n\n\t\t// generate transformed vertex attribute data\n\t\tconst position = attributes.position;\n\t\tconst normal = includeNormal ? attributes.normal : null;\n\t\tconst tangent = includeTangent ? attributes.tangent : null;\n\t\tconst morphPosition = geometry.morphAttributes.position;\n\t\tconst morphNormal = geometry.morphAttributes.normal;\n\t\tconst morphTangent = geometry.morphAttributes.tangent;\n\t\tconst morphTargetsRelative = geometry.morphTargetsRelative;\n\t\tconst morphInfluences = mesh.morphTargetInfluences;\n\t\tconst normalMatrix = new Matrix3();\n\t\tnormalMatrix.getNormalMatrix( mesh.matrixWorld );\n\n\t\t// copy the index\n\t\tif ( geometry.index ) {\n\n\t\t\ttargetGeometry.index.array.set( geometry.index.array );\n\n\t\t}\n\n\t\t// copy and apply other attributes\n\t\tfor ( let i = 0, l = attributes.position.count; i < l; i ++ ) {\n\n\t\t\t_positionVector.fromBufferAttribute( position, i );\n\t\t\tif ( normal ) {\n\n\t\t\t\t_normalVector.fromBufferAttribute( normal, i );\n\n\t\t\t}\n\n\t\t\tif ( tangent ) {\n\n\t\t\t\t_tangentVector4.fromBufferAttribute( tangent, i );\n\t\t\t\t_tangentVector.fromBufferAttribute( tangent, i );\n\n\t\t\t}\n\n\t\t\t// apply morph target transform\n\t\t\tif ( morphInfluences ) {\n\n\t\t\t\tif ( morphPosition ) {\n\n\t\t\t\t\tapplyMorphTarget( morphPosition, morphInfluences, morphTargetsRelative, i, _positionVector );\n\n\t\t\t\t}\n\n\t\t\t\tif ( morphNormal ) {\n\n\t\t\t\t\tapplyMorphTarget( morphNormal, morphInfluences, morphTargetsRelative, i, _normalVector );\n\n\t\t\t\t}\n\n\t\t\t\tif ( morphTangent ) {\n\n\t\t\t\t\tapplyMorphTarget( morphTangent, morphInfluences, morphTargetsRelative, i, _tangentVector );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// apply bone transform\n\t\t\tif ( mesh.isSkinnedMesh ) {\n\n\t\t\t\tmesh.applyBoneTransform( i, _positionVector );\n\t\t\t\tif ( normal ) {\n\n\t\t\t\t\tboneNormalTransform( mesh, i, _normalVector );\n\n\t\t\t\t}\n\n\t\t\t\tif ( tangent ) {\n\n\t\t\t\t\tboneNormalTransform( mesh, i, _tangentVector );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// update the vectors of the attributes\n\t\t\tif ( applyWorldTransforms ) {\n\n\t\t\t\t_positionVector.applyMatrix4( mesh.matrixWorld );\n\n\t\t\t}\n\n\t\t\ttargetAttributes.position.setXYZ( i, _positionVector.x, _positionVector.y, _positionVector.z );\n\n\t\t\tif ( normal ) {\n\n\t\t\t\tif ( applyWorldTransforms ) {\n\n\t\t\t\t\t_normalVector.applyNormalMatrix( normalMatrix );\n\n\t\t\t\t}\n\n\t\t\t\ttargetAttributes.normal.setXYZ( i, _normalVector.x, _normalVector.y, _normalVector.z );\n\n\t\t\t}\n\n\t\t\tif ( tangent ) {\n\n\t\t\t\tif ( applyWorldTransforms ) {\n\n\t\t\t\t\t_tangentVector.transformDirection( mesh.matrixWorld );\n\n\t\t\t\t}\n\n\t\t\t\ttargetAttributes.tangent.setXYZW( i, _tangentVector.x, _tangentVector.y, _tangentVector.z, _tangentVector4.w );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// copy other attributes over\n\t\tfor ( const i in this.attributes ) {\n\n\t\t\tconst key = this.attributes[ i ];\n\t\t\tif ( key === 'position' || key === 'tangent' || key === 'normal' || ! ( key in attributes ) ) {\n\n\t\t\t\tcontinue;\n\n\t\t\t}\n\n\t\t\tif ( ! targetAttributes[ key ] ) {\n\n\t\t\t\ttargetGeometry.setAttribute( key, createAttributeClone( attributes[ key ] ) );\n\n\t\t\t}\n\n\t\t\tvalidateAttributes( attributes[ key ], targetAttributes[ key ] );\n\t\t\tcopyAttributeContents( attributes[ key ], targetAttributes[ key ] );\n\n\t\t}\n\n\t\tif ( mesh.matrixWorld.determinant() < 0 ) {\n\n\t\t\tinvertGeometry( targetGeometry );\n\n\t\t}\n\n\t\treturn targetGeometry;\n\n\t}\n\n}\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,cAAc,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,QAAQ,OAAO;AAE3F,MAAMC,eAAe,GAAG,aAAc,IAAIJ,OAAO,CAAC,CAAC;AACnD,MAAMK,aAAa,GAAG,aAAc,IAAIL,OAAO,CAAC,CAAC;AACjD,MAAMM,cAAc,GAAG,aAAc,IAAIN,OAAO,CAAC,CAAC;AAClD,MAAMO,eAAe,GAAG,aAAc,IAAIN,OAAO,CAAC,CAAC;AAEnD,MAAMO,YAAY,GAAG,aAAc,IAAIR,OAAO,CAAC,CAAC;AAChD,MAAMS,KAAK,GAAG,aAAc,IAAIT,OAAO,CAAC,CAAC;AAEzC,MAAMU,UAAU,GAAG,aAAc,IAAIT,OAAO,CAAC,CAAC;AAC9C,MAAMU,WAAW,GAAG,aAAc,IAAIV,OAAO,CAAC,CAAC;AAC/C,MAAMW,OAAO,GAAG,aAAc,IAAIV,OAAO,CAAC,CAAC;AAC3C,MAAMW,WAAW,GAAG,aAAc,IAAIX,OAAO,CAAC,CAAC;;AAE/C;AACA,SAASY,kBAAkBA,CAAEC,KAAK,EAAEC,KAAK,EAAG;EAE3C,IAAK,CAAED,KAAK,IAAI,CAAEC,KAAK,EAAG;IAEzB;EAED;EAEA,MAAMC,SAAS,GAAGF,KAAK,CAACG,KAAK,KAAKF,KAAK,CAACE,KAAK;EAC7C,MAAMC,cAAc,GAAGJ,KAAK,CAACK,UAAU,KAAKJ,KAAK,CAACI,UAAU;EAC5D,MAAMC,QAAQ,GAAGN,KAAK,CAACO,KAAK,CAACC,WAAW,KAAKP,KAAK,CAACM,KAAK,CAACC,WAAW;EACpE,MAAMC,YAAY,GAAGT,KAAK,CAACU,QAAQ,KAAKT,KAAK,CAACS,QAAQ;EAEtD,IAAK,CAAER,SAAS,IAAI,CAAEE,cAAc,IAAI,CAAEE,QAAQ,IAAI,CAAEG,YAAY,EAAG;IAEtE,MAAM,IAAIE,KAAK,CAAC,CAAC;EAElB;AAED;;AAEA;AACA,SAASC,oBAAoBA,CAAEC,IAAI,EAAEC,aAAa,GAAG,IAAI,EAAG;EAE3D,MAAMC,IAAI,GAAGF,IAAI,CAACN,KAAK,CAACC,WAAW;EACnC,MAAMH,UAAU,GAAGQ,IAAI,CAACR,UAAU;EAClC,MAAMK,QAAQ,GAAGG,IAAI,CAACH,QAAQ;EAC9B,MAAMP,KAAK,GAAGW,aAAa,KAAK,IAAI,GAAGD,IAAI,CAACV,KAAK,GAAGW,aAAa;EAEjE,OAAO,IAAI/B,eAAe,CAAE,IAAIgC,IAAI,CAAEL,QAAQ,GAAGP,KAAM,CAAC,EAAEO,QAAQ,EAAEL,UAAW,CAAC;AAEjF;;AAEA;AACA;AACA,SAASW,qBAAqBA,CAAEH,IAAI,EAAEI,MAAM,EAAEC,YAAY,GAAG,CAAC,EAAG;EAEhE,IAAKL,IAAI,CAACM,4BAA4B,EAAG;IAExC,MAAMT,QAAQ,GAAGG,IAAI,CAACH,QAAQ;IAC9B,KAAM,IAAIU,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGR,IAAI,CAACV,KAAK,EAAEiB,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;MAE9C,MAAME,EAAE,GAAGF,CAAC,GAAGF,YAAY;MAC3BD,MAAM,CAACM,IAAI,CAAED,EAAE,EAAET,IAAI,CAACW,IAAI,CAAEJ,CAAE,CAAE,CAAC;MACjC,IAAKV,QAAQ,IAAI,CAAC,EAAGO,MAAM,CAACQ,IAAI,CAAEH,EAAE,EAAET,IAAI,CAACa,IAAI,CAAEN,CAAE,CAAE,CAAC;MACtD,IAAKV,QAAQ,IAAI,CAAC,EAAGO,MAAM,CAACU,IAAI,CAAEL,EAAE,EAAET,IAAI,CAACe,IAAI,CAAER,CAAE,CAAE,CAAC;MACtD,IAAKV,QAAQ,IAAI,CAAC,EAAGO,MAAM,CAACY,IAAI,CAAEP,EAAE,EAAET,IAAI,CAACiB,IAAI,CAAEV,CAAE,CAAE,CAAC;IAEvD;EAED,CAAC,MAAM;IAEN,MAAMb,KAAK,GAAGU,MAAM,CAACV,KAAK;IAC1B,MAAMQ,IAAI,GAAGR,KAAK,CAACC,WAAW;IAC9B,MAAMuB,UAAU,GAAGxB,KAAK,CAACyB,iBAAiB,GAAGnB,IAAI,CAACH,QAAQ,GAAGQ,YAAY;IACzE,MAAMe,IAAI,GAAG,IAAIlB,IAAI,CAAER,KAAK,CAAC2B,MAAM,EAAEH,UAAU,EAAElB,IAAI,CAACN,KAAK,CAAC4B,MAAO,CAAC;IACpEF,IAAI,CAACG,GAAG,CAAEvB,IAAI,CAACN,KAAM,CAAC;EAEvB;AAED;;AAEA;AACA,SAAS8B,eAAeA,CAAEpB,MAAM,EAAEqB,MAAM,EAAEC,KAAK,EAAG;EAEjD,MAAMC,WAAW,GAAGvB,MAAM,CAACwB,QAAQ;EACnC,MAAMC,WAAW,GAAGJ,MAAM,CAACG,QAAQ;EACnC,KAAM,IAAIrB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGqB,WAAW,CAACP,MAAM,EAAEf,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;IAEtDoB,WAAW,CAAEpB,CAAC,CAAE,IAAIsB,WAAW,CAAEtB,CAAC,CAAE,GAAGmB,KAAK;EAE7C;AAED;;AAEA;AACA,SAASI,mBAAmBA,CAAEC,IAAI,EAAEC,KAAK,EAAE5B,MAAM,EAAG;EAEnD,MAAM6B,QAAQ,GAAGF,IAAI,CAACE,QAAQ;EAC9B,MAAMC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;EAC9B,MAAMC,KAAK,GAAGF,QAAQ,CAACE,KAAK;EAC5B,MAAMC,YAAY,GAAGH,QAAQ,CAACG,YAAY;EAE1CtD,UAAU,CAACuD,mBAAmB,CAAEH,QAAQ,CAACI,UAAU,CAACC,SAAS,EAAEP,KAAM,CAAC;EACtEjD,WAAW,CAACsD,mBAAmB,CAAEH,QAAQ,CAACI,UAAU,CAACE,UAAU,EAAER,KAAM,CAAC;EAExEhD,OAAO,CAAC4C,QAAQ,CAACa,IAAI,CAAE,CAAE,CAAC;EAE1B,KAAM,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;IAE9B,MAAMmC,MAAM,GAAG3D,WAAW,CAAC4D,YAAY,CAAEpC,CAAE,CAAC;IAE5C,IAAKmC,MAAM,KAAK,CAAC,EAAG;MAEnB,MAAME,SAAS,GAAG9D,UAAU,CAAC6D,YAAY,CAAEpC,CAAE,CAAC;MAC9CtB,WAAW,CAAC4D,gBAAgB,CAAEV,KAAK,CAAES,SAAS,CAAE,CAACE,WAAW,EAAEV,YAAY,CAAEQ,SAAS,CAAG,CAAC;MAEzFpB,eAAe,CAAExC,OAAO,EAAEC,WAAW,EAAEyD,MAAO,CAAC;IAEhD;EAED;EAEA1D,OAAO,CAAC+D,QAAQ,CAAEhB,IAAI,CAACiB,UAAW,CAAC,CAACC,WAAW,CAAElB,IAAI,CAACmB,iBAAkB,CAAC;EACzE9C,MAAM,CAAC+C,kBAAkB,CAAEnE,OAAQ,CAAC;EAEpC,OAAOoB,MAAM;AAEd;;AAEA;AACA,SAASgD,gBAAgBA,CAAEC,SAAS,EAAEC,eAAe,EAAEC,oBAAoB,EAAEhD,CAAC,EAAEH,MAAM,EAAG;EAExFxB,YAAY,CAAC2C,GAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;EAC3B,KAAM,IAAIiC,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGJ,SAAS,CAAC/B,MAAM,EAAEkC,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAG,EAAG;IAEtD,MAAME,SAAS,GAAGJ,eAAe,CAAEE,CAAC,CAAE;IACtC,MAAMG,cAAc,GAAGN,SAAS,CAAEG,CAAC,CAAE;IAErC,IAAKE,SAAS,KAAK,CAAC,EAAG;IAEvB7E,KAAK,CAACwD,mBAAmB,CAAEsB,cAAc,EAAEpD,CAAE,CAAC;IAE9C,IAAKgD,oBAAoB,EAAG;MAE3B3E,YAAY,CAACgF,eAAe,CAAE/E,KAAK,EAAE6E,SAAU,CAAC;IAEjD,CAAC,MAAM;MAEN9E,YAAY,CAACgF,eAAe,CAAE/E,KAAK,CAACgF,GAAG,CAAEzD,MAAO,CAAC,EAAEsD,SAAU,CAAC;IAE/D;EAED;EAEAtD,MAAM,CAAC0D,GAAG,CAAElF,YAAa,CAAC;AAE3B;;AAEA;AACA,SAASmF,qBAAqBA,CAAEC,UAAU,EAAEC,OAAO,GAAG;EAAEC,SAAS,EAAE,KAAK;EAAEC,WAAW,EAAE,KAAK;EAAEC,cAAc,EAAE;AAAG,CAAC,EAAEC,cAAc,GAAG,IAAIlG,cAAc,CAAC,CAAC,EAAG;EAE3J,MAAMmG,SAAS,GAAGN,UAAU,CAAE,CAAC,CAAE,CAAChC,KAAK,KAAK,IAAI;EAChD,MAAM;IAAEkC,SAAS,GAAG,KAAK;IAAEC,WAAW,GAAG,KAAK;IAAEC,cAAc,GAAG;EAAG,CAAC,GAAGH,OAAO;EAE/E,MAAMM,cAAc,GAAG,IAAIC,GAAG,CAAEC,MAAM,CAACC,IAAI,CAAEV,UAAU,CAAE,CAAC,CAAE,CAAC1B,UAAW,CAAE,CAAC;EAC3E,MAAMA,UAAU,GAAG,CAAC,CAAC;EAErB,IAAIqC,MAAM,GAAG,CAAC;EAEdN,cAAc,CAACO,WAAW,CAAC,CAAC;EAC5B,KAAM,IAAIrE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyD,UAAU,CAAC1C,MAAM,EAAE,EAAGf,CAAC,EAAG;IAE9C,MAAM2B,QAAQ,GAAG8B,UAAU,CAAEzD,CAAC,CAAE;IAChC,IAAIsE,eAAe,GAAG,CAAC;;IAEvB;IACA,IAAKP,SAAS,MAAOpC,QAAQ,CAACF,KAAK,KAAK,IAAI,CAAE,EAAG;MAEhD,MAAM,IAAIlC,KAAK,CAAE,qJAAsJ,CAAC;IAEzK;;IAEA;IACA,KAAM,MAAMgF,IAAI,IAAI5C,QAAQ,CAACI,UAAU,EAAG;MAEzC,IAAK,CAAEiC,cAAc,CAACQ,GAAG,CAAED,IAAK,CAAC,EAAG;QAEnC,MAAM,IAAIhF,KAAK,CAAE,sFAAsF,GAAGgF,IAAI,GAAG,8DAA+D,CAAC;MAElL;MAEA,IAAKxC,UAAU,CAAEwC,IAAI,CAAE,KAAKE,SAAS,EAAG;QAEvC1C,UAAU,CAAEwC,IAAI,CAAE,GAAG,EAAE;MAExB;MAEAxC,UAAU,CAAEwC,IAAI,CAAE,CAACG,IAAI,CAAE/C,QAAQ,CAACI,UAAU,CAAEwC,IAAI,CAAG,CAAC;MACtDD,eAAe,EAAG;IAEnB;;IAEA;IACA,IAAKA,eAAe,KAAKN,cAAc,CAACW,IAAI,EAAG;MAE9C,MAAM,IAAIpF,KAAK,CAAE,uFAAwF,CAAC;IAE3G;IAEA,IAAKoE,SAAS,EAAG;MAEhB,IAAI5E,KAAK;MACT,IAAKgF,SAAS,EAAG;QAEhBhF,KAAK,GAAG4C,QAAQ,CAACF,KAAK,CAAC1C,KAAK;MAE7B,CAAC,MAAM,IAAK4C,QAAQ,CAACI,UAAU,CAAC6C,QAAQ,KAAKH,SAAS,EAAG;QAExD1F,KAAK,GAAG4C,QAAQ,CAACI,UAAU,CAAC6C,QAAQ,CAAC7F,KAAK;MAE3C,CAAC,MAAM;QAEN,MAAM,IAAIQ,KAAK,CAAE,yFAA0F,CAAC;MAE7G;MAEAuE,cAAc,CAACe,QAAQ,CAAET,MAAM,EAAErF,KAAK,EAAEiB,CAAE,CAAC;MAC3CoE,MAAM,IAAIrF,KAAK;IAEhB;EAED;;EAEA;EACA,IAAKgF,SAAS,EAAG;IAEhB,IAAIe,gBAAgB,GAAG,KAAK;IAC5B,IAAK,CAAEhB,cAAc,CAACrC,KAAK,EAAG;MAE7B,IAAIsD,UAAU,GAAG,CAAC;MAClB,KAAM,IAAI/E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyD,UAAU,CAAC1C,MAAM,EAAE,EAAGf,CAAC,EAAG;QAE9C+E,UAAU,IAAItB,UAAU,CAAEzD,CAAC,CAAE,CAACyB,KAAK,CAAC1C,KAAK;MAE1C;MAEA+E,cAAc,CAACkB,QAAQ,CAAE,IAAIrH,eAAe,CAAE,IAAIsH,WAAW,CAAEF,UAAW,CAAC,EAAE,CAAC,EAAE,KAAM,CAAE,CAAC;MACzFD,gBAAgB,GAAG,IAAI;IAExB;IAEA,IAAKlB,WAAW,IAAIkB,gBAAgB,EAAG;MAEtC,MAAMI,WAAW,GAAGpB,cAAc,CAACrC,KAAK;MACxC,IAAI3B,YAAY,GAAG,CAAC;MACpB,IAAIqF,WAAW,GAAG,CAAC;MACnB,KAAM,IAAInF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyD,UAAU,CAAC1C,MAAM,EAAE,EAAGf,CAAC,EAAG;QAE9C,MAAM2B,QAAQ,GAAG8B,UAAU,CAAEzD,CAAC,CAAE;QAChC,MAAMyB,KAAK,GAAGE,QAAQ,CAACF,KAAK;QAC5B,IAAKoC,cAAc,CAAE7D,CAAC,CAAE,KAAK,IAAI,EAAG;UAEnC,KAAM,IAAIiD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,KAAK,CAAC1C,KAAK,EAAE,EAAGkE,CAAC,EAAG;YAExCiC,WAAW,CAAC/E,IAAI,CAAEL,YAAY,EAAE2B,KAAK,CAACrB,IAAI,CAAE6C,CAAE,CAAC,GAAGkC,WAAY,CAAC;YAC/DrF,YAAY,EAAG;UAEhB;QAED;QAEAqF,WAAW,IAAIxD,QAAQ,CAACI,UAAU,CAAC6C,QAAQ,CAAC7F,KAAK;MAElD;IAED;EAED;;EAEA;EACA,KAAM,MAAMwF,IAAI,IAAIxC,UAAU,EAAG;IAEhC,MAAMqD,QAAQ,GAAGrD,UAAU,CAAEwC,IAAI,CAAE;IACnC,IAAK,EAAIA,IAAI,IAAIT,cAAc,CAAC/B,UAAU,CAAE,EAAG;MAE9C,IAAIhD,KAAK,GAAG,CAAC;MACb,KAAM,MAAMsG,GAAG,IAAID,QAAQ,EAAG;QAE7BrG,KAAK,IAAIqG,QAAQ,CAAEC,GAAG,CAAE,CAACtG,KAAK;MAE/B;MAEA+E,cAAc,CAACwB,YAAY,CAAEf,IAAI,EAAE/E,oBAAoB,CAAEuC,UAAU,CAAEwC,IAAI,CAAE,CAAE,CAAC,CAAE,EAAExF,KAAM,CAAE,CAAC;IAE5F;IAEA,MAAMwG,eAAe,GAAGzB,cAAc,CAAC/B,UAAU,CAAEwC,IAAI,CAAE;IACzD,IAAIH,MAAM,GAAG,CAAC;IACd,KAAM,IAAIpE,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGmF,QAAQ,CAACrE,MAAM,EAAEf,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;MAEnD,MAAMP,IAAI,GAAG2F,QAAQ,CAAEpF,CAAC,CAAE;MAC1B,IAAK6D,cAAc,CAAE7D,CAAC,CAAE,KAAK,IAAI,EAAG;QAEnCJ,qBAAqB,CAAEH,IAAI,EAAE8F,eAAe,EAAEnB,MAAO,CAAC;MAEvD;MAEAA,MAAM,IAAI3E,IAAI,CAACV,KAAK;IAErB;EAED;EAEA,OAAO+E,cAAc;AAEtB;AAEA,SAAS0B,uBAAuBA,CAAEC,CAAC,EAAEC,CAAC,EAAG;EAExC,IAAKD,CAAC,KAAK,IAAI,IAAIC,CAAC,KAAK,IAAI,EAAG;IAE/B,OAAOD,CAAC,KAAKC,CAAC;EAEf;EAEA,IAAKD,CAAC,CAAC1E,MAAM,KAAK2E,CAAC,CAAC3E,MAAM,EAAG;IAE5B,OAAO,KAAK;EAEb;EAEA,KAAM,IAAIf,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGwF,CAAC,CAAC1E,MAAM,EAAEf,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;IAE5C,IAAKyF,CAAC,CAAEzF,CAAC,CAAE,KAAK0F,CAAC,CAAE1F,CAAC,CAAE,EAAG;MAExB,OAAO,KAAK;IAEb;EAED;EAEA,OAAO,IAAI;AAEZ;AAEA,SAAS2F,cAAcA,CAAEhE,QAAQ,EAAG;EAEnC,MAAM;IAAEF,KAAK;IAAEM;EAAW,CAAC,GAAGJ,QAAQ;EACtC,IAAKF,KAAK,EAAG;IAEZ,KAAM,IAAIzB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGwB,KAAK,CAAC1C,KAAK,EAAEiB,CAAC,GAAGC,CAAC,EAAED,CAAC,IAAI,CAAC,EAAG;MAEjD,MAAM4F,EAAE,GAAGnE,KAAK,CAACrB,IAAI,CAAEJ,CAAE,CAAC;MAC1B,MAAM6F,EAAE,GAAGpE,KAAK,CAACrB,IAAI,CAAEJ,CAAC,GAAG,CAAE,CAAC;MAC9ByB,KAAK,CAACtB,IAAI,CAAEH,CAAC,EAAE6F,EAAG,CAAC;MACnBpE,KAAK,CAACtB,IAAI,CAAEH,CAAC,GAAG,CAAC,EAAE4F,EAAG,CAAC;IAExB;EAED,CAAC,MAAM;IAEN,KAAM,MAAMP,GAAG,IAAItD,UAAU,EAAG;MAE/B,MAAMtC,IAAI,GAAGsC,UAAU,CAAEsD,GAAG,CAAE;MAC9B,MAAM/F,QAAQ,GAAGG,IAAI,CAACH,QAAQ;MAC9B,KAAM,IAAIU,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGR,IAAI,CAACV,KAAK,EAAEiB,CAAC,GAAGC,CAAC,EAAED,CAAC,IAAI,CAAC,EAAG;QAEhD,KAAM,IAAIiD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3D,QAAQ,EAAE2D,CAAC,EAAG,EAAG;UAErC,MAAM2C,EAAE,GAAGnG,IAAI,CAAC2C,YAAY,CAAEpC,CAAC,EAAEiD,CAAE,CAAC;UACpC,MAAM4C,EAAE,GAAGpG,IAAI,CAAC2C,YAAY,CAAEpC,CAAC,GAAG,CAAC,EAAEiD,CAAE,CAAC;UACxCxD,IAAI,CAACqG,YAAY,CAAE9F,CAAC,EAAEiD,CAAC,EAAE4C,EAAG,CAAC;UAC7BpG,IAAI,CAACqG,YAAY,CAAE9F,CAAC,GAAG,CAAC,EAAEiD,CAAC,EAAE2C,EAAG,CAAC;QAElC;MAED;IAED;EAED;EAEA,OAAOjE,QAAQ;AAGhB;;AAEA;AACA,MAAMoE,YAAY,CAAC;EAElB3G,WAAWA,CAAEoC,IAAI,EAAG;IAEnB,IAAI,CAACe,WAAW,GAAG,IAAIxE,OAAO,CAAC,CAAC;IAChC,IAAI,CAACiI,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,cAAc,GAAG,CAAE,CAAC;IACzB,IAAI,CAAC1E,IAAI,GAAGA,IAAI;IAEhB,IAAI,CAAC2E,MAAM,CAAC,CAAC;EAEd;EAEAA,MAAMA,CAAA,EAAG;IAER,MAAM3E,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMG,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IAC9B,MAAMD,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IAC9B,MAAMwE,cAAc,GAAG,CAAEvE,QAAQ,CAACF,KAAK,GAAGE,QAAQ,CAACF,KAAK,CAAC1C,KAAK,GAAG4C,QAAQ,CAACI,UAAU,CAAC6C,QAAQ,CAAC7F,KAAK,IAAK,CAAC;IACzG,IAAI,CAACwD,WAAW,CAAC6D,IAAI,CAAE5E,IAAI,CAACe,WAAY,CAAC;IACzC,IAAI,CAACyD,YAAY,GAAGrE,QAAQ,CAACI,UAAU,CAAC6C,QAAQ,CAACyB,OAAO;IACxD,IAAI,CAACH,cAAc,GAAGA,cAAc;IAEpC,IAAKxE,QAAQ,EAAG;MAEf;MACA,IAAK,CAAEA,QAAQ,CAAC4E,WAAW,EAAG;QAE7B5E,QAAQ,CAAC6E,kBAAkB,CAAC,CAAC;MAE9B;MAEA7E,QAAQ,CAACyE,MAAM,CAAC,CAAC;;MAEjB;MACA,MAAMF,YAAY,GAAGvE,QAAQ,CAACuE,YAAY;MAC1C,IAAK,CAAE,IAAI,CAACA,YAAY,IAAI,IAAI,CAACA,YAAY,CAAClF,MAAM,KAAKkF,YAAY,CAAClF,MAAM,EAAG;QAE9E,IAAI,CAACkF,YAAY,GAAGA,YAAY,CAACO,KAAK,CAAC,CAAC;MAEzC,CAAC,MAAM;QAEN,IAAI,CAACP,YAAY,CAACjF,GAAG,CAAEiF,YAAa,CAAC;MAEtC;IAED,CAAC,MAAM;MAEN,IAAI,CAACA,YAAY,GAAG,IAAI;IAEzB;EAED;EAEAQ,SAASA,CAAA,EAAG;IAEX,MAAMjF,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMG,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IAC9B,MAAMuE,cAAc,GAAG,CAAEvE,QAAQ,CAACF,KAAK,GAAGE,QAAQ,CAACF,KAAK,CAAC1C,KAAK,GAAG4C,QAAQ,CAACI,UAAU,CAAC6C,QAAQ,CAAC7F,KAAK,IAAK,CAAC;IACzG,MAAM2H,SAAS,GACd,IAAI,CAACnE,WAAW,CAACoE,MAAM,CAAEnF,IAAI,CAACe,WAAY,CAAC,IAC3C,IAAI,CAACyD,YAAY,KAAKrE,QAAQ,CAACI,UAAU,CAAC6C,QAAQ,CAACyB,OAAO,IAC1Db,uBAAuB,CAAEhE,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACE,QAAQ,CAACuE,YAAY,IAAI,IAAI,EAAE,IAAI,CAACA,YAAa,CAAC,IACjG,IAAI,CAACC,cAAc,KAAKA,cAAc;IAEvC,OAAO,CAAEQ,SAAS;EAEnB;AAED;AAEA,OAAO,MAAME,uBAAuB,CAAC;EAEpCxH,WAAWA,CAAEyH,MAAM,EAAG;IAErB,IAAK,CAAEC,KAAK,CAACC,OAAO,CAAEF,MAAO,CAAC,EAAG;MAEhCA,MAAM,GAAG,CAAEA,MAAM,CAAE;IAEpB;IAEA,MAAMG,WAAW,GAAG,EAAE;IACtBH,MAAM,CAACI,OAAO,CAAEC,MAAM,IAAI;MAEzBA,MAAM,CAACC,eAAe,CAAEC,CAAC,IAAI;QAE5B,IAAKA,CAAC,CAACC,MAAM,EAAG;UAEfL,WAAW,CAACtC,IAAI,CAAE0C,CAAE,CAAC;QAEtB;MAED,CAAE,CAAC;IAEJ,CAAE,CAAC;IAEH,IAAI,CAACP,MAAM,GAAGG,WAAW;IACzB,IAAI,CAACrD,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC2D,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACvF,UAAU,GAAG,CAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAE;IAC3E,IAAI,CAACwF,qBAAqB,GAAG,IAAIT,KAAK,CAAEE,WAAW,CAACjG,MAAO,CAAC,CAACmB,IAAI,CAAC,CAAC,CAACsF,GAAG,CAAE,MAAM,IAAI5J,cAAc,CAAC,CAAE,CAAC;IACrG,IAAI,CAAC6J,QAAQ,GAAG,IAAIC,OAAO,CAAC,CAAC;EAE9B;EAEAC,YAAYA,CAAA,EAAG;IAEd,MAAMC,SAAS,GAAG,EAAE;IACpB,IAAI,CAACf,MAAM,CAACI,OAAO,CAAEzF,IAAI,IAAI;MAE5B,IAAKsF,KAAK,CAACC,OAAO,CAAEvF,IAAI,CAACqG,QAAS,CAAC,EAAG;QAErCD,SAAS,CAAClD,IAAI,CAAE,GAAGlD,IAAI,CAACqG,QAAS,CAAC;MAEnC,CAAC,MAAM;QAEND,SAAS,CAAClD,IAAI,CAAElD,IAAI,CAACqG,QAAS,CAAC;MAEhC;IAED,CAAE,CAAC;IACH,OAAOD,SAAS;EAEjB;EAEAE,QAAQA,CAAEhE,cAAc,GAAG,IAAIlG,cAAc,CAAC,CAAC,EAAG;IAEjD;IACA,IAAIiG,cAAc,GAAG,EAAE;IACvB,MAAM;MAAEgD,MAAM;MAAElD,SAAS;MAAE4D,qBAAqB;MAAEE;IAAS,CAAC,GAAG,IAAI;IACnE,KAAM,IAAIzH,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG4G,MAAM,CAAC9F,MAAM,EAAEf,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;MAEjD,MAAMwB,IAAI,GAAGqF,MAAM,CAAE7G,CAAC,CAAE;MACxB,MAAM+H,IAAI,GAAGR,qBAAqB,CAAEvH,CAAC,CAAE;MACvC,MAAMgI,IAAI,GAAGP,QAAQ,CAACQ,GAAG,CAAEzG,IAAK,CAAC;MACjC,IAAK,CAAEwG,IAAI,IAAIA,IAAI,CAACvB,SAAS,CAAEjF,IAAK,CAAC,EAAG;QAEvC,IAAI,CAAC0G,wBAAwB,CAAE1G,IAAI,EAAEuG,IAAK,CAAC;QAC3ClE,cAAc,CAACa,IAAI,CAAE,KAAM,CAAC;QAE5B,IAAK,CAAEsD,IAAI,EAAG;UAEbP,QAAQ,CAACzG,GAAG,CAAEQ,IAAI,EAAE,IAAIuE,YAAY,CAAEvE,IAAK,CAAE,CAAC;QAE/C,CAAC,MAAM;UAENwG,IAAI,CAAC7B,MAAM,CAAC,CAAC;QAEd;MAED,CAAC,MAAM;QAENtC,cAAc,CAACa,IAAI,CAAE,IAAK,CAAC;MAE5B;IAED;IAEA,IAAK6C,qBAAqB,CAACxG,MAAM,KAAK,CAAC,EAAG;MAEzC;MACA+C,cAAc,CAACkB,QAAQ,CAAE,IAAK,CAAC;;MAE/B;MACA,MAAMmD,KAAK,GAAGrE,cAAc,CAAC/B,UAAU;MACvC,KAAM,MAAMsD,GAAG,IAAI8C,KAAK,EAAG;QAE1BrE,cAAc,CAACsE,eAAe,CAAE/C,GAAI,CAAC;MAEtC;;MAEA;MACA,KAAM,MAAMA,GAAG,IAAI,IAAI,CAACtD,UAAU,EAAG;QAEpC+B,cAAc,CAACwB,YAAY,CAAE,IAAI,CAACvD,UAAU,CAAEsD,GAAG,CAAE,EAAE,IAAI1H,eAAe,CAAE,IAAI0K,YAAY,CAAE,CAAE,CAAC,EAAE,CAAC,EAAE,KAAM,CAAE,CAAC;MAE9G;IAED,CAAC,MAAM;MAEN7E,qBAAqB,CAAE+D,qBAAqB,EAAE;QAAE5D,SAAS;QAAEE;MAAe,CAAC,EAAEC,cAAe,CAAC;IAE9F;IAEA,KAAM,MAAMuB,GAAG,IAAIvB,cAAc,CAAC/B,UAAU,EAAG;MAE9C+B,cAAc,CAAC/B,UAAU,CAAEsD,GAAG,CAAE,CAACiD,WAAW,GAAG,IAAI;IAEpD;IAEA,OAAOxE,cAAc;EAEtB;EAEAoE,wBAAwBA,CAAE1G,IAAI,EAAEsC,cAAc,GAAG,IAAIlG,cAAc,CAAC,CAAC,EAAG;IAEvE,MAAM+D,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IAC9B,MAAM2F,oBAAoB,GAAG,IAAI,CAACA,oBAAoB;IACtD,MAAMiB,aAAa,GAAG,IAAI,CAACxG,UAAU,CAACyG,QAAQ,CAAE,QAAS,CAAC;IAC1D,MAAMC,cAAc,GAAG,IAAI,CAAC1G,UAAU,CAACyG,QAAQ,CAAE,SAAU,CAAC;IAC5D,MAAMzG,UAAU,GAAGJ,QAAQ,CAACI,UAAU;IACtC,MAAM2G,gBAAgB,GAAG5E,cAAc,CAAC/B,UAAU;;IAElD;IACA,IAAK,CAAE+B,cAAc,CAACrC,KAAK,IAAIE,QAAQ,CAACF,KAAK,EAAG;MAE/CqC,cAAc,CAACrC,KAAK,GAAGE,QAAQ,CAACF,KAAK,CAACkH,KAAK,CAAC,CAAC;IAE9C;IAEA,IAAK,CAAED,gBAAgB,CAAC9D,QAAQ,EAAG;MAElCd,cAAc,CAACwB,YAAY,CAAE,UAAU,EAAE9F,oBAAoB,CAAEuC,UAAU,CAAC6C,QAAS,CAAE,CAAC;IAEvF;IAEA,IAAK2D,aAAa,IAAI,CAAEG,gBAAgB,CAACE,MAAM,IAAI7G,UAAU,CAAC6G,MAAM,EAAG;MAEtE9E,cAAc,CAACwB,YAAY,CAAE,QAAQ,EAAE9F,oBAAoB,CAAEuC,UAAU,CAAC6G,MAAO,CAAE,CAAC;IAEnF;IAEA,IAAKH,cAAc,IAAI,CAAEC,gBAAgB,CAACG,OAAO,IAAI9G,UAAU,CAAC8G,OAAO,EAAG;MAEzE/E,cAAc,CAACwB,YAAY,CAAE,SAAS,EAAE9F,oBAAoB,CAAEuC,UAAU,CAAC8G,OAAQ,CAAE,CAAC;IAErF;;IAEA;IACAlK,kBAAkB,CAAEgD,QAAQ,CAACF,KAAK,EAAEqC,cAAc,CAACrC,KAAM,CAAC;IAC1D9C,kBAAkB,CAAEoD,UAAU,CAAC6C,QAAQ,EAAE8D,gBAAgB,CAAC9D,QAAS,CAAC;IAEpE,IAAK2D,aAAa,EAAG;MAEpB5J,kBAAkB,CAAEoD,UAAU,CAAC6G,MAAM,EAAEF,gBAAgB,CAACE,MAAO,CAAC;IAEjE;IAEA,IAAKH,cAAc,EAAG;MAErB9J,kBAAkB,CAAEoD,UAAU,CAAC8G,OAAO,EAAEH,gBAAgB,CAACG,OAAQ,CAAC;IAEnE;;IAEA;IACA,MAAMjE,QAAQ,GAAG7C,UAAU,CAAC6C,QAAQ;IACpC,MAAMgE,MAAM,GAAGL,aAAa,GAAGxG,UAAU,CAAC6G,MAAM,GAAG,IAAI;IACvD,MAAMC,OAAO,GAAGJ,cAAc,GAAG1G,UAAU,CAAC8G,OAAO,GAAG,IAAI;IAC1D,MAAMC,aAAa,GAAGnH,QAAQ,CAACoH,eAAe,CAACnE,QAAQ;IACvD,MAAMoE,WAAW,GAAGrH,QAAQ,CAACoH,eAAe,CAACH,MAAM;IACnD,MAAMK,YAAY,GAAGtH,QAAQ,CAACoH,eAAe,CAACF,OAAO;IACrD,MAAM7F,oBAAoB,GAAGrB,QAAQ,CAACqB,oBAAoB;IAC1D,MAAMD,eAAe,GAAGvB,IAAI,CAAC0H,qBAAqB;IAClD,MAAMC,YAAY,GAAG,IAAInL,OAAO,CAAC,CAAC;IAClCmL,YAAY,CAACC,eAAe,CAAE5H,IAAI,CAACe,WAAY,CAAC;;IAEhD;IACA,IAAKZ,QAAQ,CAACF,KAAK,EAAG;MAErBqC,cAAc,CAACrC,KAAK,CAACtC,KAAK,CAAC6B,GAAG,CAAEW,QAAQ,CAACF,KAAK,CAACtC,KAAM,CAAC;IAEvD;;IAEA;IACA,KAAM,IAAIa,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG8B,UAAU,CAAC6C,QAAQ,CAAC7F,KAAK,EAAEiB,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;MAE7D/B,eAAe,CAAC6D,mBAAmB,CAAE8C,QAAQ,EAAE5E,CAAE,CAAC;MAClD,IAAK4I,MAAM,EAAG;QAEb1K,aAAa,CAAC4D,mBAAmB,CAAE8G,MAAM,EAAE5I,CAAE,CAAC;MAE/C;MAEA,IAAK6I,OAAO,EAAG;QAEdzK,eAAe,CAAC0D,mBAAmB,CAAE+G,OAAO,EAAE7I,CAAE,CAAC;QACjD7B,cAAc,CAAC2D,mBAAmB,CAAE+G,OAAO,EAAE7I,CAAE,CAAC;MAEjD;;MAEA;MACA,IAAK+C,eAAe,EAAG;QAEtB,IAAK+F,aAAa,EAAG;UAEpBjG,gBAAgB,CAAEiG,aAAa,EAAE/F,eAAe,EAAEC,oBAAoB,EAAEhD,CAAC,EAAE/B,eAAgB,CAAC;QAE7F;QAEA,IAAK+K,WAAW,EAAG;UAElBnG,gBAAgB,CAAEmG,WAAW,EAAEjG,eAAe,EAAEC,oBAAoB,EAAEhD,CAAC,EAAE9B,aAAc,CAAC;QAEzF;QAEA,IAAK+K,YAAY,EAAG;UAEnBpG,gBAAgB,CAAEoG,YAAY,EAAElG,eAAe,EAAEC,oBAAoB,EAAEhD,CAAC,EAAE7B,cAAe,CAAC;QAE3F;MAED;;MAEA;MACA,IAAKqD,IAAI,CAAC6H,aAAa,EAAG;QAEzB7H,IAAI,CAAC8H,kBAAkB,CAAEtJ,CAAC,EAAE/B,eAAgB,CAAC;QAC7C,IAAK2K,MAAM,EAAG;UAEbrH,mBAAmB,CAAEC,IAAI,EAAExB,CAAC,EAAE9B,aAAc,CAAC;QAE9C;QAEA,IAAK2K,OAAO,EAAG;UAEdtH,mBAAmB,CAAEC,IAAI,EAAExB,CAAC,EAAE7B,cAAe,CAAC;QAE/C;MAED;;MAEA;MACA,IAAKmJ,oBAAoB,EAAG;QAE3BrJ,eAAe,CAACsL,YAAY,CAAE/H,IAAI,CAACe,WAAY,CAAC;MAEjD;MAEAmG,gBAAgB,CAAC9D,QAAQ,CAAC4E,MAAM,CAAExJ,CAAC,EAAE/B,eAAe,CAACwL,CAAC,EAAExL,eAAe,CAACyL,CAAC,EAAEzL,eAAe,CAAC0L,CAAE,CAAC;MAE9F,IAAKf,MAAM,EAAG;QAEb,IAAKtB,oBAAoB,EAAG;UAE3BpJ,aAAa,CAAC0L,iBAAiB,CAAET,YAAa,CAAC;QAEhD;QAEAT,gBAAgB,CAACE,MAAM,CAACY,MAAM,CAAExJ,CAAC,EAAE9B,aAAa,CAACuL,CAAC,EAAEvL,aAAa,CAACwL,CAAC,EAAExL,aAAa,CAACyL,CAAE,CAAC;MAEvF;MAEA,IAAKd,OAAO,EAAG;QAEd,IAAKvB,oBAAoB,EAAG;UAE3BnJ,cAAc,CAACyE,kBAAkB,CAAEpB,IAAI,CAACe,WAAY,CAAC;QAEtD;QAEAmG,gBAAgB,CAACG,OAAO,CAACgB,OAAO,CAAE7J,CAAC,EAAE7B,cAAc,CAACsL,CAAC,EAAEtL,cAAc,CAACuL,CAAC,EAAEvL,cAAc,CAACwL,CAAC,EAAEvL,eAAe,CAAC0L,CAAE,CAAC;MAE/G;IAED;;IAEA;IACA,KAAM,MAAM9J,CAAC,IAAI,IAAI,CAAC+B,UAAU,EAAG;MAElC,MAAMsD,GAAG,GAAG,IAAI,CAACtD,UAAU,CAAE/B,CAAC,CAAE;MAChC,IAAKqF,GAAG,KAAK,UAAU,IAAIA,GAAG,KAAK,SAAS,IAAIA,GAAG,KAAK,QAAQ,IAAI,EAAIA,GAAG,IAAItD,UAAU,CAAE,EAAG;QAE7F;MAED;MAEA,IAAK,CAAE2G,gBAAgB,CAAErD,GAAG,CAAE,EAAG;QAEhCvB,cAAc,CAACwB,YAAY,CAAED,GAAG,EAAE7F,oBAAoB,CAAEuC,UAAU,CAAEsD,GAAG,CAAG,CAAE,CAAC;MAE9E;MAEA1G,kBAAkB,CAAEoD,UAAU,CAAEsD,GAAG,CAAE,EAAEqD,gBAAgB,CAAErD,GAAG,CAAG,CAAC;MAChEzF,qBAAqB,CAAEmC,UAAU,CAAEsD,GAAG,CAAE,EAAEqD,gBAAgB,CAAErD,GAAG,CAAG,CAAC;IAEpE;IAEA,IAAK7D,IAAI,CAACe,WAAW,CAACwH,WAAW,CAAC,CAAC,GAAG,CAAC,EAAG;MAEzCpE,cAAc,CAAE7B,cAAe,CAAC;IAEjC;IAEA,OAAOA,cAAc;EAEtB;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}