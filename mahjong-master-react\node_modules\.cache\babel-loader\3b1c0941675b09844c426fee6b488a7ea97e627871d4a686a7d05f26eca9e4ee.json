{"ast": null, "code": "import { frame, cancelFrame } from '../frameloop/frame.mjs';\nimport { numberValueTypes } from '../value/types/maps/number.mjs';\nimport { getValueAsType } from '../value/types/utils/get-as-type.mjs';\nclass MotionValueState {\n  constructor() {\n    this.latest = {};\n    this.values = new Map();\n  }\n  set(name, value, render, computed) {\n    let useDefaultValueType = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;\n    const existingValue = this.values.get(name);\n    if (existingValue) {\n      existingValue.onRemove();\n    }\n    const onChange = () => {\n      const v = value.get();\n      if (useDefaultValueType) {\n        this.latest[name] = getValueAsType(v, numberValueTypes[name]);\n      } else {\n        this.latest[name] = v;\n      }\n      render && frame.render(render);\n    };\n    onChange();\n    const cancelOnChange = value.on(\"change\", onChange);\n    computed && value.addDependent(computed);\n    const remove = () => {\n      cancelOnChange();\n      render && cancelFrame(render);\n      this.values.delete(name);\n      computed && value.removeDependent(computed);\n    };\n    this.values.set(name, {\n      value,\n      onRemove: remove\n    });\n    return remove;\n  }\n  get(name) {\n    var _this$values$get;\n    return (_this$values$get = this.values.get(name)) === null || _this$values$get === void 0 ? void 0 : _this$values$get.value;\n  }\n  destroy() {\n    for (const value of this.values.values()) {\n      value.onRemove();\n    }\n  }\n}\nexport { MotionValueState };", "map": {"version": 3, "names": ["frame", "cancelFrame", "numberValueTypes", "getValueAsType", "MotionValueState", "constructor", "latest", "values", "Map", "set", "name", "value", "render", "computed", "useDefaultValueType", "arguments", "length", "undefined", "existingValue", "get", "onRemove", "onChange", "v", "cancelOnChange", "on", "addDependent", "remove", "delete", "removeDependent", "_this$values$get", "destroy"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/motion-dom/dist/es/effects/MotionValueState.mjs"], "sourcesContent": ["import { frame, cancelFrame } from '../frameloop/frame.mjs';\nimport { numberValueTypes } from '../value/types/maps/number.mjs';\nimport { getValueAsType } from '../value/types/utils/get-as-type.mjs';\n\nclass MotionValueState {\n    constructor() {\n        this.latest = {};\n        this.values = new Map();\n    }\n    set(name, value, render, computed, useDefaultValueType = true) {\n        const existingValue = this.values.get(name);\n        if (existingValue) {\n            existingValue.onRemove();\n        }\n        const onChange = () => {\n            const v = value.get();\n            if (useDefaultValueType) {\n                this.latest[name] = getValueAsType(v, numberValueTypes[name]);\n            }\n            else {\n                this.latest[name] = v;\n            }\n            render && frame.render(render);\n        };\n        onChange();\n        const cancelOnChange = value.on(\"change\", onChange);\n        computed && value.addDependent(computed);\n        const remove = () => {\n            cancelOnChange();\n            render && cancelFrame(render);\n            this.values.delete(name);\n            computed && value.removeDependent(computed);\n        };\n        this.values.set(name, { value, onRemove: remove });\n        return remove;\n    }\n    get(name) {\n        return this.values.get(name)?.value;\n    }\n    destroy() {\n        for (const value of this.values.values()) {\n            value.onRemove();\n        }\n    }\n}\n\nexport { MotionValueState };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,WAAW,QAAQ,wBAAwB;AAC3D,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,cAAc,QAAQ,sCAAsC;AAErE,MAAMC,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC3B;EACAC,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAA8B;IAAA,IAA5BC,mBAAmB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACzD,MAAMG,aAAa,GAAG,IAAI,CAACX,MAAM,CAACY,GAAG,CAACT,IAAI,CAAC;IAC3C,IAAIQ,aAAa,EAAE;MACfA,aAAa,CAACE,QAAQ,CAAC,CAAC;IAC5B;IACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;MACnB,MAAMC,CAAC,GAAGX,KAAK,CAACQ,GAAG,CAAC,CAAC;MACrB,IAAIL,mBAAmB,EAAE;QACrB,IAAI,CAACR,MAAM,CAACI,IAAI,CAAC,GAAGP,cAAc,CAACmB,CAAC,EAAEpB,gBAAgB,CAACQ,IAAI,CAAC,CAAC;MACjE,CAAC,MACI;QACD,IAAI,CAACJ,MAAM,CAACI,IAAI,CAAC,GAAGY,CAAC;MACzB;MACAV,MAAM,IAAIZ,KAAK,CAACY,MAAM,CAACA,MAAM,CAAC;IAClC,CAAC;IACDS,QAAQ,CAAC,CAAC;IACV,MAAME,cAAc,GAAGZ,KAAK,CAACa,EAAE,CAAC,QAAQ,EAAEH,QAAQ,CAAC;IACnDR,QAAQ,IAAIF,KAAK,CAACc,YAAY,CAACZ,QAAQ,CAAC;IACxC,MAAMa,MAAM,GAAGA,CAAA,KAAM;MACjBH,cAAc,CAAC,CAAC;MAChBX,MAAM,IAAIX,WAAW,CAACW,MAAM,CAAC;MAC7B,IAAI,CAACL,MAAM,CAACoB,MAAM,CAACjB,IAAI,CAAC;MACxBG,QAAQ,IAAIF,KAAK,CAACiB,eAAe,CAACf,QAAQ,CAAC;IAC/C,CAAC;IACD,IAAI,CAACN,MAAM,CAACE,GAAG,CAACC,IAAI,EAAE;MAAEC,KAAK;MAAES,QAAQ,EAAEM;IAAO,CAAC,CAAC;IAClD,OAAOA,MAAM;EACjB;EACAP,GAAGA,CAACT,IAAI,EAAE;IAAA,IAAAmB,gBAAA;IACN,QAAAA,gBAAA,GAAO,IAAI,CAACtB,MAAM,CAACY,GAAG,CAACT,IAAI,CAAC,cAAAmB,gBAAA,uBAArBA,gBAAA,CAAuBlB,KAAK;EACvC;EACAmB,OAAOA,CAAA,EAAG;IACN,KAAK,MAAMnB,KAAK,IAAI,IAAI,CAACJ,MAAM,CAACA,MAAM,CAAC,CAAC,EAAE;MACtCI,KAAK,CAACS,QAAQ,CAAC,CAAC;IACpB;EACJ;AACJ;AAEA,SAAShB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}