{"ast": null, "code": "import React,{useState}from'react';// 简化的麻将牌组件\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";function MahjongTile(_ref){let{id,type,value,isSelected,onClick}=_ref;const getTileColor=()=>{switch(type){case'character':return'bg-red-100 border-red-300 text-red-800';case'bamboo':return'bg-green-100 border-green-300 text-green-800';case'dot':return'bg-blue-100 border-blue-300 text-blue-800';case'wind':return'bg-yellow-100 border-yellow-300 text-yellow-800';case'dragon':return'bg-purple-100 border-purple-300 text-purple-800';default:return'bg-gray-100 border-gray-300 text-gray-800';}};const handleClick=()=>{console.log('MahjongTile clicked:',id);alert('点击了牌: '+id+' - 值: '+value);onClick();};return/*#__PURE__*/_jsx(\"div\",{className:'mahjong-tile '+getTileColor()+(isSelected?' selected':''),onClick:handleClick,style:{width:'48px',height:'64px',borderRadius:'8px',border:'2px solid #ccc',cursor:'pointer',display:'flex',alignItems:'center',justifyContent:'center',fontWeight:'bold',fontSize:'14px',userSelect:'none',WebkitUserSelect:'none',MozUserSelect:'none',msUserSelect:'none'},children:value});}function App(){const[showWelcome,setShowWelcome]=useState(false);// 直接进入游戏界面进行测试\nconst[selectedTiles,setSelectedTiles]=useState([]);// 生成示例麻将牌\nconst[tiles]=useState(()=>{const tileTypes=['character','bamboo','dot','wind','dragon'];return Array.from({length:13},(_,i)=>({id:\"tile-\".concat(i),type:tileTypes[Math.floor(Math.random()*tileTypes.length)],value:Math.floor(Math.random()*9)+1,isSelected:false}));});const handleTileClick=tileId=>{console.log('点击了麻将牌:',tileId);// 添加调试信息\nsetSelectedTiles(prev=>{const newSelection=prev.includes(tileId)?prev.filter(id=>id!==tileId):[...prev,tileId];console.log('选中的牌:',newSelection);// 添加调试信息\nreturn newSelection;});};const handleAction=action=>{console.log(\"\\u6267\\u884C\\u52A8\\u4F5C: \".concat(action,\"\\uFF0C\\u9009\\u4E2D\\u7684\\u724C: \").concat(selectedTiles));setSelectedTiles([]);};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 opacity-20\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full h-full bg-gradient-to-br from-yellow-500/10 to-green-500/10\"})}),showWelcome&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center text-white\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-6xl font-bold mb-4 text-yellow-400\",children:\"\\u795E\\u706F\\u9EBB\\u5C06\\u5927\\u5E08\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl mb-8 text-gray-300\",children:\"\\u8D85\\u8D8A\\u4F20\\u7EDF\\uFF0C\\u91CD\\u65B0\\u5B9A\\u4E49\\u9EBB\\u5C06\\u4F53\\u9A8C\"}),/*#__PURE__*/_jsx(\"button\",{className:\"bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-3 px-8 rounded-xl text-lg\",onClick:()=>setShowWelcome(false),children:\"\\u5F00\\u59CB\\u6E38\\u620F\"})]})}),!showWelcome&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-0 left-0 right-0 z-40 bg-black bg-opacity-50 p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center max-w-7xl mx-auto\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-yellow-400\",children:\"\\u795E\\u706F\\u9EBB\\u5C06\\u5927\\u5E08\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 text-white\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u5728\\u7EBF\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-white\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-400\",children:\"\\u5F53\\u524D\\u5C40\\u6570:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 text-yellow-400 font-bold\",children:\"\\u7B2C1\\u5C40\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-white\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-400\",children:\"\\u98CE\\u5708:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 text-green-400 font-bold\",children:\"\\u4E1C\\u98CE\"})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col items-center justify-center h-screen pt-20 pb-20\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-green-800 rounded-3xl p-8 shadow-2xl\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-white text-xl font-bold mb-6 text-center\",children:\"\\u60A8\\u7684\\u624B\\u724C\"}),/*#__PURE__*/_jsx(\"div\",{className:\"mb-4 text-center\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>alert('测试按钮工作正常！'),className:\"bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600\",children:\"\\u6D4B\\u8BD5\\u70B9\\u51FB\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex gap-2 flex-wrap justify-center\",children:tiles.map(tile=>/*#__PURE__*/_jsx(MahjongTile,{id:tile.id,type:tile.type,value:tile.value,isSelected:selectedTiles.includes(tile.id),onClick:()=>handleTileClick(tile.id)},tile.id))})]})}),selectedTiles.length>0&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute bottom-4 left-1/2 transform -translate-x-1/2 z-40\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-black bg-opacity-70 rounded-2xl p-4 flex space-x-3\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg\",onClick:()=>handleAction('出牌'),children:\"\\u51FA\\u724C\"}),/*#__PURE__*/_jsx(\"button\",{className:\"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg\",onClick:()=>handleAction('吃'),children:\"\\u5403\"}),/*#__PURE__*/_jsx(\"button\",{className:\"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg\",onClick:()=>handleAction('碰'),children:\"\\u78B0\"}),/*#__PURE__*/_jsx(\"button\",{className:\"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg\",onClick:()=>handleAction('杠'),children:\"\\u6760\"}),/*#__PURE__*/_jsx(\"button\",{className:\"bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-6 rounded-lg\",onClick:()=>handleAction('胡牌'),children:\"\\u80E1\\u724C\"})]})})]})]});}export default App;", "map": {"version": 3, "names": ["React", "useState", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "MahjongTile", "_ref", "id", "type", "value", "isSelected", "onClick", "getTileColor", "handleClick", "console", "log", "alert", "className", "style", "width", "height", "borderRadius", "border", "cursor", "display", "alignItems", "justifyContent", "fontWeight", "fontSize", "userSelect", "WebkitUserSelect", "MozUserSelect", "msUserSelect", "children", "App", "showWelcome", "setShowWelcome", "selectedTiles", "setSelectedTiles", "tiles", "tileTypes", "Array", "from", "length", "_", "i", "concat", "Math", "floor", "random", "handleTileClick", "tileId", "prev", "newSelection", "includes", "filter", "handleAction", "action", "map", "tile"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\n// 简化的麻将牌组件\ninterface MahjongTileProps {\n  id: string;\n  type: string;\n  value: string | number;\n  isSelected: boolean;\n  onClick: () => void;\n}\n\nfunction MahjongTile({ id, type, value, isSelected, onClick }: MahjongTileProps) {\n  const getTileColor = () => {\n    switch (type) {\n      case 'character': return 'bg-red-100 border-red-300 text-red-800';\n      case 'bamboo': return 'bg-green-100 border-green-300 text-green-800';\n      case 'dot': return 'bg-blue-100 border-blue-300 text-blue-800';\n      case 'wind': return 'bg-yellow-100 border-yellow-300 text-yellow-800';\n      case 'dragon': return 'bg-purple-100 border-purple-300 text-purple-800';\n      default: return 'bg-gray-100 border-gray-300 text-gray-800';\n    }\n  };\n\n  const handleClick = () => {\n    console.log('MahjongTile clicked:', id);\n    alert('点击了牌: ' + id + ' - 值: ' + value);\n    onClick();\n  };\n\n  return (\n    <div\n      className={'mahjong-tile ' + getTileColor() + (isSelected ? ' selected' : '')}\n      onClick={handleClick}\n      style={{\n        width: '48px',\n        height: '64px',\n        borderRadius: '8px',\n        border: '2px solid #ccc',\n        cursor: 'pointer',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        fontWeight: 'bold',\n        fontSize: '14px',\n        userSelect: 'none',\n        WebkitUserSelect: 'none',\n        MozUserSelect: 'none',\n        msUserSelect: 'none'\n      }}\n    >\n      {value}\n    </div>\n  );\n}\n\nfunction App() {\n  const [showWelcome, setShowWelcome] = useState(false); // 直接进入游戏界面进行测试\n  const [selectedTiles, setSelectedTiles] = useState<string[]>([]);\n\n  // 生成示例麻将牌\n  const [tiles] = useState(() => {\n    const tileTypes = ['character', 'bamboo', 'dot', 'wind', 'dragon'];\n    return Array.from({ length: 13 }, (_, i) => ({\n      id: `tile-${i}`,\n      type: tileTypes[Math.floor(Math.random() * tileTypes.length)],\n      value: Math.floor(Math.random() * 9) + 1,\n      isSelected: false\n    }));\n  });\n\n  const handleTileClick = (tileId: string) => {\n    console.log('点击了麻将牌:', tileId); // 添加调试信息\n    setSelectedTiles(prev => {\n      const newSelection = prev.includes(tileId)\n        ? prev.filter(id => id !== tileId)\n        : [...prev, tileId];\n      console.log('选中的牌:', newSelection); // 添加调试信息\n      return newSelection;\n    });\n  };\n\n  const handleAction = (action: string) => {\n    console.log(`执行动作: ${action}，选中的牌: ${selectedTiles}`);\n    setSelectedTiles([]);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden\">\n      {/* 背景装饰 */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"w-full h-full bg-gradient-to-br from-yellow-500/10 to-green-500/10\"></div>\n      </div>\n\n      {/* 欢迎界面 */}\n      {showWelcome && (\n        <div className=\"absolute inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80\">\n          <div className=\"text-center text-white\">\n            <h1 className=\"text-6xl font-bold mb-4 text-yellow-400\">\n              神灯麻将大师\n            </h1>\n            <p className=\"text-xl mb-8 text-gray-300\">\n              超越传统，重新定义麻将体验\n            </p>\n            <button\n              className=\"bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-3 px-8 rounded-xl text-lg\"\n              onClick={() => setShowWelcome(false)}\n            >\n              开始游戏\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* 主游戏界面 */}\n      {!showWelcome && (\n        <>\n          {/* 顶部状态栏 */}\n          <div className=\"absolute top-0 left-0 right-0 z-40 bg-black bg-opacity-50 p-4\">\n            <div className=\"flex justify-between items-center max-w-7xl mx-auto\">\n              <div className=\"flex items-center space-x-4\">\n                <h2 className=\"text-2xl font-bold text-yellow-400\">神灯麻将大师</h2>\n                <div className=\"flex items-center space-x-2 text-white\">\n                  <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\n                  <span>在线</span>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"text-white\">\n                  <span className=\"text-gray-400\">当前局数:</span>\n                  <span className=\"ml-2 text-yellow-400 font-bold\">第1局</span>\n                </div>\n                <div className=\"text-white\">\n                  <span className=\"text-gray-400\">风圈:</span>\n                  <span className=\"ml-2 text-green-400 font-bold\">东风</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* 游戏区域 */}\n          <div className=\"flex flex-col items-center justify-center h-screen pt-20 pb-20\">\n            <div className=\"bg-green-800 rounded-3xl p-8 shadow-2xl\">\n              <h3 className=\"text-white text-xl font-bold mb-6 text-center\">您的手牌</h3>\n\n              {/* 测试按钮 */}\n              <div className=\"mb-4 text-center\">\n                <button\n                  onClick={() => alert('测试按钮工作正常！')}\n                  className=\"bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600\"\n                >\n                  测试点击\n                </button>\n              </div>\n\n              <div className=\"flex gap-2 flex-wrap justify-center\">\n                {tiles.map((tile) => (\n                  <MahjongTile\n                    key={tile.id}\n                    id={tile.id}\n                    type={tile.type}\n                    value={tile.value}\n                    isSelected={selectedTiles.includes(tile.id)}\n                    onClick={() => handleTileClick(tile.id)}\n                  />\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* 底部操作栏 */}\n          {selectedTiles.length > 0 && (\n            <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 z-40\">\n              <div className=\"bg-black bg-opacity-70 rounded-2xl p-4 flex space-x-3\">\n                <button\n                  className=\"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg\"\n                  onClick={() => handleAction('出牌')}\n                >\n                  出牌\n                </button>\n                <button\n                  className=\"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg\"\n                  onClick={() => handleAction('吃')}\n                >\n                  吃\n                </button>\n                <button\n                  className=\"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg\"\n                  onClick={() => handleAction('碰')}\n                >\n                  碰\n                </button>\n                <button\n                  className=\"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg\"\n                  onClick={() => handleAction('杠')}\n                >\n                  杠\n                </button>\n                <button\n                  className=\"bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-6 rounded-lg\"\n                  onClick={() => handleAction('胡牌')}\n                >\n                  胡牌\n                </button>\n              </div>\n            </div>\n          )}\n        </>\n      )}\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CAEvC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBASA,QAAS,CAAAC,WAAWA,CAAAC,IAAA,CAA6D,IAA5D,CAAEC,EAAE,CAAEC,IAAI,CAAEC,KAAK,CAAEC,UAAU,CAAEC,OAA0B,CAAC,CAAAL,IAAA,CAC7E,KAAM,CAAAM,YAAY,CAAGA,CAAA,GAAM,CACzB,OAAQJ,IAAI,EACV,IAAK,WAAW,CAAE,MAAO,wCAAwC,CACjE,IAAK,QAAQ,CAAE,MAAO,8CAA8C,CACpE,IAAK,KAAK,CAAE,MAAO,2CAA2C,CAC9D,IAAK,MAAM,CAAE,MAAO,iDAAiD,CACrE,IAAK,QAAQ,CAAE,MAAO,iDAAiD,CACvE,QAAS,MAAO,2CAA2C,CAC7D,CACF,CAAC,CAED,KAAM,CAAAK,WAAW,CAAGA,CAAA,GAAM,CACxBC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAER,EAAE,CAAC,CACvCS,KAAK,CAAC,QAAQ,CAAGT,EAAE,CAAG,QAAQ,CAAGE,KAAK,CAAC,CACvCE,OAAO,CAAC,CAAC,CACX,CAAC,CAED,mBACEX,IAAA,QACEiB,SAAS,CAAE,eAAe,CAAGL,YAAY,CAAC,CAAC,EAAIF,UAAU,CAAG,WAAW,CAAG,EAAE,CAAE,CAC9EC,OAAO,CAAEE,WAAY,CACrBK,KAAK,CAAE,CACLC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,gBAAgB,CACxBC,MAAM,CAAE,SAAS,CACjBC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBC,UAAU,CAAE,MAAM,CAClBC,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,MAAM,CAClBC,gBAAgB,CAAE,MAAM,CACxBC,aAAa,CAAE,MAAM,CACrBC,YAAY,CAAE,MAChB,CAAE,CAAAC,QAAA,CAEDxB,KAAK,CACH,CAAC,CAEV,CAEA,QAAS,CAAAyB,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGtC,QAAQ,CAAC,KAAK,CAAC,CAAE;AACvD,KAAM,CAACuC,aAAa,CAAEC,gBAAgB,CAAC,CAAGxC,QAAQ,CAAW,EAAE,CAAC,CAEhE;AACA,KAAM,CAACyC,KAAK,CAAC,CAAGzC,QAAQ,CAAC,IAAM,CAC7B,KAAM,CAAA0C,SAAS,CAAG,CAAC,WAAW,CAAE,QAAQ,CAAE,KAAK,CAAE,MAAM,CAAE,QAAQ,CAAC,CAClE,MAAO,CAAAC,KAAK,CAACC,IAAI,CAAC,CAAEC,MAAM,CAAE,EAAG,CAAC,CAAE,CAACC,CAAC,CAAEC,CAAC,IAAM,CAC3CtC,EAAE,SAAAuC,MAAA,CAAUD,CAAC,CAAE,CACfrC,IAAI,CAAEgC,SAAS,CAACO,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAGT,SAAS,CAACG,MAAM,CAAC,CAAC,CAC7DlC,KAAK,CAAEsC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAG,CAAC,CAAC,CAAG,CAAC,CACxCvC,UAAU,CAAE,KACd,CAAC,CAAC,CAAC,CACL,CAAC,CAAC,CAEF,KAAM,CAAAwC,eAAe,CAAIC,MAAc,EAAK,CAC1CrC,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEoC,MAAM,CAAC,CAAE;AAChCb,gBAAgB,CAACc,IAAI,EAAI,CACvB,KAAM,CAAAC,YAAY,CAAGD,IAAI,CAACE,QAAQ,CAACH,MAAM,CAAC,CACtCC,IAAI,CAACG,MAAM,CAAChD,EAAE,EAAIA,EAAE,GAAK4C,MAAM,CAAC,CAChC,CAAC,GAAGC,IAAI,CAAED,MAAM,CAAC,CACrBrC,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEsC,YAAY,CAAC,CAAE;AACpC,MAAO,CAAAA,YAAY,CACrB,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAG,YAAY,CAAIC,MAAc,EAAK,CACvC3C,OAAO,CAACC,GAAG,8BAAA+B,MAAA,CAAUW,MAAM,qCAAAX,MAAA,CAAUT,aAAa,CAAE,CAAC,CACrDC,gBAAgB,CAAC,EAAE,CAAC,CACtB,CAAC,CAED,mBACEpC,KAAA,QAAKe,SAAS,CAAC,mGAAmG,CAAAgB,QAAA,eAEhHjC,IAAA,QAAKiB,SAAS,CAAC,6BAA6B,CAAAgB,QAAA,cAC1CjC,IAAA,QAAKiB,SAAS,CAAC,oEAAoE,CAAM,CAAC,CACvF,CAAC,CAGLkB,WAAW,eACVnC,IAAA,QAAKiB,SAAS,CAAC,+EAA+E,CAAAgB,QAAA,cAC5F/B,KAAA,QAAKe,SAAS,CAAC,wBAAwB,CAAAgB,QAAA,eACrCjC,IAAA,OAAIiB,SAAS,CAAC,yCAAyC,CAAAgB,QAAA,CAAC,sCAExD,CAAI,CAAC,cACLjC,IAAA,MAAGiB,SAAS,CAAC,4BAA4B,CAAAgB,QAAA,CAAC,gFAE1C,CAAG,CAAC,cACJjC,IAAA,WACEiB,SAAS,CAAC,qFAAqF,CAC/FN,OAAO,CAAEA,CAAA,GAAMyB,cAAc,CAAC,KAAK,CAAE,CAAAH,QAAA,CACtC,0BAED,CAAQ,CAAC,EACN,CAAC,CACH,CACN,CAGA,CAACE,WAAW,eACXjC,KAAA,CAAAE,SAAA,EAAA6B,QAAA,eAEEjC,IAAA,QAAKiB,SAAS,CAAC,+DAA+D,CAAAgB,QAAA,cAC5E/B,KAAA,QAAKe,SAAS,CAAC,qDAAqD,CAAAgB,QAAA,eAClE/B,KAAA,QAAKe,SAAS,CAAC,6BAA6B,CAAAgB,QAAA,eAC1CjC,IAAA,OAAIiB,SAAS,CAAC,oCAAoC,CAAAgB,QAAA,CAAC,sCAAM,CAAI,CAAC,cAC9D/B,KAAA,QAAKe,SAAS,CAAC,wCAAwC,CAAAgB,QAAA,eACrDjC,IAAA,QAAKiB,SAAS,CAAC,iDAAiD,CAAM,CAAC,cACvEjB,IAAA,SAAAiC,QAAA,CAAM,cAAE,CAAM,CAAC,EACZ,CAAC,EACH,CAAC,cAEN/B,KAAA,QAAKe,SAAS,CAAC,6BAA6B,CAAAgB,QAAA,eAC1C/B,KAAA,QAAKe,SAAS,CAAC,YAAY,CAAAgB,QAAA,eACzBjC,IAAA,SAAMiB,SAAS,CAAC,eAAe,CAAAgB,QAAA,CAAC,2BAAK,CAAM,CAAC,cAC5CjC,IAAA,SAAMiB,SAAS,CAAC,gCAAgC,CAAAgB,QAAA,CAAC,eAAG,CAAM,CAAC,EACxD,CAAC,cACN/B,KAAA,QAAKe,SAAS,CAAC,YAAY,CAAAgB,QAAA,eACzBjC,IAAA,SAAMiB,SAAS,CAAC,eAAe,CAAAgB,QAAA,CAAC,eAAG,CAAM,CAAC,cAC1CjC,IAAA,SAAMiB,SAAS,CAAC,+BAA+B,CAAAgB,QAAA,CAAC,cAAE,CAAM,CAAC,EACtD,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNjC,IAAA,QAAKiB,SAAS,CAAC,gEAAgE,CAAAgB,QAAA,cAC7E/B,KAAA,QAAKe,SAAS,CAAC,yCAAyC,CAAAgB,QAAA,eACtDjC,IAAA,OAAIiB,SAAS,CAAC,+CAA+C,CAAAgB,QAAA,CAAC,0BAAI,CAAI,CAAC,cAGvEjC,IAAA,QAAKiB,SAAS,CAAC,kBAAkB,CAAAgB,QAAA,cAC/BjC,IAAA,WACEW,OAAO,CAAEA,CAAA,GAAMK,KAAK,CAAC,WAAW,CAAE,CAClCC,SAAS,CAAC,0DAA0D,CAAAgB,QAAA,CACrE,0BAED,CAAQ,CAAC,CACN,CAAC,cAENjC,IAAA,QAAKiB,SAAS,CAAC,qCAAqC,CAAAgB,QAAA,CACjDM,KAAK,CAACmB,GAAG,CAAEC,IAAI,eACd3D,IAAA,CAACK,WAAW,EAEVE,EAAE,CAAEoD,IAAI,CAACpD,EAAG,CACZC,IAAI,CAAEmD,IAAI,CAACnD,IAAK,CAChBC,KAAK,CAAEkD,IAAI,CAAClD,KAAM,CAClBC,UAAU,CAAE2B,aAAa,CAACiB,QAAQ,CAACK,IAAI,CAACpD,EAAE,CAAE,CAC5CI,OAAO,CAAEA,CAAA,GAAMuC,eAAe,CAACS,IAAI,CAACpD,EAAE,CAAE,EALnCoD,IAAI,CAACpD,EAMX,CACF,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,CAGL8B,aAAa,CAACM,MAAM,CAAG,CAAC,eACvB3C,IAAA,QAAKiB,SAAS,CAAC,4DAA4D,CAAAgB,QAAA,cACzE/B,KAAA,QAAKe,SAAS,CAAC,uDAAuD,CAAAgB,QAAA,eACpEjC,IAAA,WACEiB,SAAS,CAAC,+EAA+E,CACzFN,OAAO,CAAEA,CAAA,GAAM6C,YAAY,CAAC,IAAI,CAAE,CAAAvB,QAAA,CACnC,cAED,CAAQ,CAAC,cACTjC,IAAA,WACEiB,SAAS,CAAC,+EAA+E,CACzFN,OAAO,CAAEA,CAAA,GAAM6C,YAAY,CAAC,GAAG,CAAE,CAAAvB,QAAA,CAClC,QAED,CAAQ,CAAC,cACTjC,IAAA,WACEiB,SAAS,CAAC,+EAA+E,CACzFN,OAAO,CAAEA,CAAA,GAAM6C,YAAY,CAAC,GAAG,CAAE,CAAAvB,QAAA,CAClC,QAED,CAAQ,CAAC,cACTjC,IAAA,WACEiB,SAAS,CAAC,+EAA+E,CACzFN,OAAO,CAAEA,CAAA,GAAM6C,YAAY,CAAC,GAAG,CAAE,CAAAvB,QAAA,CAClC,QAED,CAAQ,CAAC,cACTjC,IAAA,WACEiB,SAAS,CAAC,uEAAuE,CACjFN,OAAO,CAAEA,CAAA,GAAM6C,YAAY,CAAC,IAAI,CAAE,CAAAvB,QAAA,CACnC,cAED,CAAQ,CAAC,EACN,CAAC,CACH,CACN,EACD,CACH,EACE,CAAC,CAEV,CAEA,cAAe,CAAAC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}