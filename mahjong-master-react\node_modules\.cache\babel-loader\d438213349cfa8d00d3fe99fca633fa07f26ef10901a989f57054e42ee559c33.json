{"ast": null, "code": "import React,{useState}from'react';import{motion,AnimatePresence}from'framer-motion';// 简化的麻将牌组件\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";function MahjongTile(_ref){let{id,type,value,isSelected,onClick}=_ref;const getTileColor=()=>{switch(type){case'character':return'bg-red-100 border-red-300 text-red-800';case'bamboo':return'bg-green-100 border-green-300 text-green-800';case'dot':return'bg-blue-100 border-blue-300 text-blue-800';case'wind':return'bg-yellow-100 border-yellow-300 text-yellow-800';case'dragon':return'bg-purple-100 border-purple-300 text-purple-800';default:return'bg-gray-100 border-gray-300 text-gray-800';}};const handleClick=e=>{e.preventDefault();e.stopPropagation();console.log('MahjongTile clicked:',id);alert(\"\\u70B9\\u51FB\\u4E86\\u724C: \".concat(id,\" - \\u503C: \").concat(value));onClick();};return/*#__PURE__*/_jsx(\"div\",{className:\"\\n        w-12 h-16 rounded-lg border-2 cursor-pointer flex items-center justify-center font-bold text-sm\\n        transition-all duration-200 hover:scale-105 hover:shadow-lg select-none\\n        \".concat(getTileColor(),\"\\n        \").concat(isSelected?'ring-4 ring-yellow-400 transform -translate-y-2':'',\"\\n      \"),onClick:handleClick,onMouseDown:handleClick,onTouchStart:handleClick,style:{userSelect:'none',WebkitUserSelect:'none',MozUserSelect:'none',msUserSelect:'none',pointerEvents:'auto'},children:value});}function App(){const[showWelcome,setShowWelcome]=useState(false);// 直接进入游戏界面进行测试\nconst[selectedTiles,setSelectedTiles]=useState([]);// 生成示例麻将牌\nconst[tiles]=useState(()=>{const tileTypes=['character','bamboo','dot','wind','dragon'];return Array.from({length:13},(_,i)=>({id:\"tile-\".concat(i),type:tileTypes[Math.floor(Math.random()*tileTypes.length)],value:Math.floor(Math.random()*9)+1,isSelected:false}));});const handleTileClick=tileId=>{console.log('点击了麻将牌:',tileId);// 添加调试信息\nsetSelectedTiles(prev=>{const newSelection=prev.includes(tileId)?prev.filter(id=>id!==tileId):[...prev,tileId];console.log('选中的牌:',newSelection);// 添加调试信息\nreturn newSelection;});};const handleAction=action=>{console.log(\"\\u6267\\u884C\\u52A8\\u4F5C: \".concat(action,\"\\uFF0C\\u9009\\u4E2D\\u7684\\u724C: \").concat(selectedTiles));setSelectedTiles([]);};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 opacity-20\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full h-full bg-gradient-to-br from-yellow-500/10 to-green-500/10\"})}),/*#__PURE__*/_jsx(AnimatePresence,{children:showWelcome&&/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:\"absolute inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80\",children:/*#__PURE__*/_jsxs(motion.div,{initial:{scale:0.8,y:50},animate:{scale:1,y:0},exit:{scale:0.8,y:-50},className:\"text-center text-white\",children:[/*#__PURE__*/_jsx(motion.h1,{className:\"text-6xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent\",animate:{filter:[\"drop-shadow(0 0 20px rgba(245, 158, 11, 0.5))\",\"drop-shadow(0 0 40px rgba(245, 158, 11, 0.8))\",\"drop-shadow(0 0 20px rgba(245, 158, 11, 0.5))\"]},transition:{duration:2,repeat:Infinity},children:\"\\u795E\\u706F\\u9EBB\\u5C06\\u5927\\u5E08\"}),/*#__PURE__*/_jsx(motion.p,{className:\"text-xl mb-8 text-gray-300\",initial:{opacity:0},animate:{opacity:1},transition:{delay:0.5},children:\"\\u8D85\\u8D8A\\u4F20\\u7EDF\\uFF0C\\u91CD\\u65B0\\u5B9A\\u4E49\\u9EBB\\u5C06\\u4F53\\u9A8C\"}),/*#__PURE__*/_jsx(motion.button,{className:\"bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white font-bold py-3 px-8 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 text-lg\",whileHover:{scale:1.05},whileTap:{scale:0.95},onClick:()=>setShowWelcome(false),children:\"\\u5F00\\u59CB\\u6E38\\u620F\"})]})})}),!showWelcome&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(motion.div,{initial:{y:-100},animate:{y:0},className:\"absolute top-0 left-0 right-0 z-40 bg-black bg-opacity-50 backdrop-blur-sm p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center max-w-7xl mx-auto\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-yellow-400\",children:\"\\u795E\\u706F\\u9EBB\\u5C06\\u5927\\u5E08\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 text-white\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u5728\\u7EBF\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-white\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-400\",children:\"\\u5F53\\u524D\\u5C40\\u6570:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 text-yellow-400 font-bold\",children:\"\\u7B2C1\\u5C40\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-white\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-400\",children:\"\\u98CE\\u5708:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 text-green-400 font-bold\",children:\"\\u4E1C\\u98CE\"})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col items-center justify-center h-screen pt-20 pb-20\",children:/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},className:\"bg-green-800 rounded-3xl p-8 shadow-2xl\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-white text-xl font-bold mb-6 text-center\",children:\"\\u60A8\\u7684\\u624B\\u724C\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex gap-2 flex-wrap justify-center\",children:tiles.map(tile=>/*#__PURE__*/_jsx(MahjongTile,{id:tile.id,type:tile.type,value:tile.value,isSelected:selectedTiles.includes(tile.id),onClick:()=>handleTileClick(tile.id)},tile.id))})]})}),/*#__PURE__*/_jsx(AnimatePresence,{children:selectedTiles.length>0&&/*#__PURE__*/_jsx(motion.div,{initial:{y:100,opacity:0},animate:{y:0,opacity:1},exit:{y:100,opacity:0},className:\"absolute bottom-4 left-1/2 transform -translate-x-1/2 z-40\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-black bg-opacity-70 backdrop-blur-sm rounded-2xl p-4 flex space-x-3\",children:[/*#__PURE__*/_jsx(motion.button,{className:\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\",whileHover:{scale:1.05},whileTap:{scale:0.95},onClick:()=>handleAction('出牌'),children:\"\\u51FA\\u724C\"}),/*#__PURE__*/_jsx(motion.button,{className:\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\",whileHover:{scale:1.05},whileTap:{scale:0.95},onClick:()=>handleAction('吃'),children:\"\\u5403\"}),/*#__PURE__*/_jsx(motion.button,{className:\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\",whileHover:{scale:1.05},whileTap:{scale:0.95},onClick:()=>handleAction('碰'),children:\"\\u78B0\"}),/*#__PURE__*/_jsx(motion.button,{className:\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\",whileHover:{scale:1.05},whileTap:{scale:0.95},onClick:()=>handleAction('杠'),children:\"\\u6760\"}),/*#__PURE__*/_jsx(motion.button,{className:\"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-bold py-2 px-6 rounded-lg shadow-lg transform hover:scale-105 transition-all duration-150\",whileHover:{scale:1.05},whileTap:{scale:0.95},onClick:()=>handleAction('胡牌'),children:\"\\u80E1\\u724C\"})]})})})]})]});}export default App;", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "MahjongTile", "_ref", "id", "type", "value", "isSelected", "onClick", "getTileColor", "handleClick", "e", "preventDefault", "stopPropagation", "console", "log", "alert", "concat", "className", "onMouseDown", "onTouchStart", "style", "userSelect", "WebkitUserSelect", "MozUserSelect", "msUserSelect", "pointerEvents", "children", "App", "showWelcome", "setShowWelcome", "selectedTiles", "setSelectedTiles", "tiles", "tileTypes", "Array", "from", "length", "_", "i", "Math", "floor", "random", "handleTileClick", "tileId", "prev", "newSelection", "includes", "filter", "handleAction", "action", "div", "initial", "opacity", "animate", "exit", "scale", "y", "h1", "transition", "duration", "repeat", "Infinity", "p", "delay", "button", "whileHover", "whileTap", "map", "tile"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\n// 简化的麻将牌组件\ninterface MahjongTileProps {\n  id: string;\n  type: string;\n  value: string | number;\n  isSelected: boolean;\n  onClick: () => void;\n}\n\nfunction MahjongTile({ id, type, value, isSelected, onClick }: MahjongTileProps) {\n  const getTileColor = () => {\n    switch (type) {\n      case 'character': return 'bg-red-100 border-red-300 text-red-800';\n      case 'bamboo': return 'bg-green-100 border-green-300 text-green-800';\n      case 'dot': return 'bg-blue-100 border-blue-300 text-blue-800';\n      case 'wind': return 'bg-yellow-100 border-yellow-300 text-yellow-800';\n      case 'dragon': return 'bg-purple-100 border-purple-300 text-purple-800';\n      default: return 'bg-gray-100 border-gray-300 text-gray-800';\n    }\n  };\n\n  const handleClick = (e: any) => {\n    e.preventDefault();\n    e.stopPropagation();\n    console.log('MahjongTile clicked:', id);\n    alert(`点击了牌: ${id} - 值: ${value}`);\n    onClick();\n  };\n\n  return (\n    <div\n      className={`\n        w-12 h-16 rounded-lg border-2 cursor-pointer flex items-center justify-center font-bold text-sm\n        transition-all duration-200 hover:scale-105 hover:shadow-lg select-none\n        ${getTileColor()}\n        ${isSelected ? 'ring-4 ring-yellow-400 transform -translate-y-2' : ''}\n      `}\n      onClick={handleClick}\n      onMouseDown={handleClick}\n      onTouchStart={handleClick}\n      style={{\n        userSelect: 'none',\n        WebkitUserSelect: 'none',\n        MozUserSelect: 'none',\n        msUserSelect: 'none',\n        pointerEvents: 'auto'\n      }}\n    >\n      {value}\n    </div>\n  );\n}\n\nfunction App() {\n  const [showWelcome, setShowWelcome] = useState(false); // 直接进入游戏界面进行测试\n  const [selectedTiles, setSelectedTiles] = useState<string[]>([]);\n\n  // 生成示例麻将牌\n  const [tiles] = useState(() => {\n    const tileTypes = ['character', 'bamboo', 'dot', 'wind', 'dragon'];\n    return Array.from({ length: 13 }, (_, i) => ({\n      id: `tile-${i}`,\n      type: tileTypes[Math.floor(Math.random() * tileTypes.length)],\n      value: Math.floor(Math.random() * 9) + 1,\n      isSelected: false\n    }));\n  });\n\n  const handleTileClick = (tileId: string) => {\n    console.log('点击了麻将牌:', tileId); // 添加调试信息\n    setSelectedTiles(prev => {\n      const newSelection = prev.includes(tileId)\n        ? prev.filter(id => id !== tileId)\n        : [...prev, tileId];\n      console.log('选中的牌:', newSelection); // 添加调试信息\n      return newSelection;\n    });\n  };\n\n  const handleAction = (action: string) => {\n    console.log(`执行动作: ${action}，选中的牌: ${selectedTiles}`);\n    setSelectedTiles([]);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden\">\n      {/* 背景装饰 */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"w-full h-full bg-gradient-to-br from-yellow-500/10 to-green-500/10\"></div>\n      </div>\n\n      {/* 欢迎界面 */}\n      <AnimatePresence>\n        {showWelcome && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"absolute inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80\"\n          >\n            <motion.div\n              initial={{ scale: 0.8, y: 50 }}\n              animate={{ scale: 1, y: 0 }}\n              exit={{ scale: 0.8, y: -50 }}\n              className=\"text-center text-white\"\n            >\n              <motion.h1\n                className=\"text-6xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent\"\n                animate={{\n                  filter: [\n                    \"drop-shadow(0 0 20px rgba(245, 158, 11, 0.5))\",\n                    \"drop-shadow(0 0 40px rgba(245, 158, 11, 0.8))\",\n                    \"drop-shadow(0 0 20px rgba(245, 158, 11, 0.5))\"\n                  ]\n                }}\n                transition={{ duration: 2, repeat: Infinity }}\n              >\n                神灯麻将大师\n              </motion.h1>\n              <motion.p\n                className=\"text-xl mb-8 text-gray-300\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.5 }}\n              >\n                超越传统，重新定义麻将体验\n              </motion.p>\n              <motion.button\n                className=\"bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white font-bold py-3 px-8 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 text-lg\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => setShowWelcome(false)}\n              >\n                开始游戏\n              </motion.button>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* 主游戏界面 */}\n      {!showWelcome && (\n        <>\n          {/* 顶部状态栏 */}\n          <motion.div\n            initial={{ y: -100 }}\n            animate={{ y: 0 }}\n            className=\"absolute top-0 left-0 right-0 z-40 bg-black bg-opacity-50 backdrop-blur-sm p-4\"\n          >\n            <div className=\"flex justify-between items-center max-w-7xl mx-auto\">\n              <div className=\"flex items-center space-x-4\">\n                <h2 className=\"text-2xl font-bold text-yellow-400\">神灯麻将大师</h2>\n                <div className=\"flex items-center space-x-2 text-white\">\n                  <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\n                  <span>在线</span>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"text-white\">\n                  <span className=\"text-gray-400\">当前局数:</span>\n                  <span className=\"ml-2 text-yellow-400 font-bold\">第1局</span>\n                </div>\n                <div className=\"text-white\">\n                  <span className=\"text-gray-400\">风圈:</span>\n                  <span className=\"ml-2 text-green-400 font-bold\">东风</span>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* 游戏区域 */}\n          <div className=\"flex flex-col items-center justify-center h-screen pt-20 pb-20\">\n            <motion.div\n              initial={{ opacity: 0, y: 50 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"bg-green-800 rounded-3xl p-8 shadow-2xl\"\n            >\n              <h3 className=\"text-white text-xl font-bold mb-6 text-center\">您的手牌</h3>\n              <div className=\"flex gap-2 flex-wrap justify-center\">\n                {tiles.map((tile) => (\n                  <MahjongTile\n                    key={tile.id}\n                    id={tile.id}\n                    type={tile.type}\n                    value={tile.value}\n                    isSelected={selectedTiles.includes(tile.id)}\n                    onClick={() => handleTileClick(tile.id)}\n                  />\n                ))}\n              </div>\n            </motion.div>\n          </div>\n\n          {/* 底部操作栏 */}\n          <AnimatePresence>\n            {selectedTiles.length > 0 && (\n              <motion.div\n                initial={{ y: 100, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                exit={{ y: 100, opacity: 0 }}\n                className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 z-40\"\n              >\n                <div className=\"bg-black bg-opacity-70 backdrop-blur-sm rounded-2xl p-4 flex space-x-3\">\n                  <motion.button\n                    className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => handleAction('出牌')}\n                  >\n                    出牌\n                  </motion.button>\n                  <motion.button\n                    className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => handleAction('吃')}\n                  >\n                    吃\n                  </motion.button>\n                  <motion.button\n                    className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => handleAction('碰')}\n                  >\n                    碰\n                  </motion.button>\n                  <motion.button\n                    className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => handleAction('杠')}\n                  >\n                    杠\n                  </motion.button>\n                  <motion.button\n                    className=\"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-bold py-2 px-6 rounded-lg shadow-lg transform hover:scale-105 transition-all duration-150\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => handleAction('胡牌')}\n                  >\n                    胡牌\n                  </motion.button>\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </>\n      )}\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CAEvD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBASA,QAAS,CAAAC,WAAWA,CAAAC,IAAA,CAA6D,IAA5D,CAAEC,EAAE,CAAEC,IAAI,CAAEC,KAAK,CAAEC,UAAU,CAAEC,OAA0B,CAAC,CAAAL,IAAA,CAC7E,KAAM,CAAAM,YAAY,CAAGA,CAAA,GAAM,CACzB,OAAQJ,IAAI,EACV,IAAK,WAAW,CAAE,MAAO,wCAAwC,CACjE,IAAK,QAAQ,CAAE,MAAO,8CAA8C,CACpE,IAAK,KAAK,CAAE,MAAO,2CAA2C,CAC9D,IAAK,MAAM,CAAE,MAAO,iDAAiD,CACrE,IAAK,QAAQ,CAAE,MAAO,iDAAiD,CACvE,QAAS,MAAO,2CAA2C,CAC7D,CACF,CAAC,CAED,KAAM,CAAAK,WAAW,CAAIC,CAAM,EAAK,CAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBD,CAAC,CAACE,eAAe,CAAC,CAAC,CACnBC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAEX,EAAE,CAAC,CACvCY,KAAK,8BAAAC,MAAA,CAAUb,EAAE,gBAAAa,MAAA,CAASX,KAAK,CAAE,CAAC,CAClCE,OAAO,CAAC,CAAC,CACX,CAAC,CAED,mBACEX,IAAA,QACEqB,SAAS,wMAAAD,MAAA,CAGLR,YAAY,CAAC,CAAC,eAAAQ,MAAA,CACdV,UAAU,CAAG,iDAAiD,CAAG,EAAE,YACrE,CACFC,OAAO,CAAEE,WAAY,CACrBS,WAAW,CAAET,WAAY,CACzBU,YAAY,CAAEV,WAAY,CAC1BW,KAAK,CAAE,CACLC,UAAU,CAAE,MAAM,CAClBC,gBAAgB,CAAE,MAAM,CACxBC,aAAa,CAAE,MAAM,CACrBC,YAAY,CAAE,MAAM,CACpBC,aAAa,CAAE,MACjB,CAAE,CAAAC,QAAA,CAEDrB,KAAK,CACH,CAAC,CAEV,CAEA,QAAS,CAAAsB,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CAAE;AACvD,KAAM,CAACsC,aAAa,CAAEC,gBAAgB,CAAC,CAAGvC,QAAQ,CAAW,EAAE,CAAC,CAEhE;AACA,KAAM,CAACwC,KAAK,CAAC,CAAGxC,QAAQ,CAAC,IAAM,CAC7B,KAAM,CAAAyC,SAAS,CAAG,CAAC,WAAW,CAAE,QAAQ,CAAE,KAAK,CAAE,MAAM,CAAE,QAAQ,CAAC,CAClE,MAAO,CAAAC,KAAK,CAACC,IAAI,CAAC,CAAEC,MAAM,CAAE,EAAG,CAAC,CAAE,CAACC,CAAC,CAAEC,CAAC,IAAM,CAC3CnC,EAAE,SAAAa,MAAA,CAAUsB,CAAC,CAAE,CACflC,IAAI,CAAE6B,SAAS,CAACM,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAGR,SAAS,CAACG,MAAM,CAAC,CAAC,CAC7D/B,KAAK,CAAEkC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAG,CAAC,CAAC,CAAG,CAAC,CACxCnC,UAAU,CAAE,KACd,CAAC,CAAC,CAAC,CACL,CAAC,CAAC,CAEF,KAAM,CAAAoC,eAAe,CAAIC,MAAc,EAAK,CAC1C9B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAE6B,MAAM,CAAC,CAAE;AAChCZ,gBAAgB,CAACa,IAAI,EAAI,CACvB,KAAM,CAAAC,YAAY,CAAGD,IAAI,CAACE,QAAQ,CAACH,MAAM,CAAC,CACtCC,IAAI,CAACG,MAAM,CAAC5C,EAAE,EAAIA,EAAE,GAAKwC,MAAM,CAAC,CAChC,CAAC,GAAGC,IAAI,CAAED,MAAM,CAAC,CACrB9B,OAAO,CAACC,GAAG,CAAC,OAAO,CAAE+B,YAAY,CAAC,CAAE;AACpC,MAAO,CAAAA,YAAY,CACrB,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAG,YAAY,CAAIC,MAAc,EAAK,CACvCpC,OAAO,CAACC,GAAG,8BAAAE,MAAA,CAAUiC,MAAM,qCAAAjC,MAAA,CAAUc,aAAa,CAAE,CAAC,CACrDC,gBAAgB,CAAC,EAAE,CAAC,CACtB,CAAC,CAED,mBACEjC,KAAA,QAAKmB,SAAS,CAAC,mGAAmG,CAAAS,QAAA,eAEhH9B,IAAA,QAAKqB,SAAS,CAAC,6BAA6B,CAAAS,QAAA,cAC1C9B,IAAA,QAAKqB,SAAS,CAAC,oEAAoE,CAAM,CAAC,CACvF,CAAC,cAGNrB,IAAA,CAACF,eAAe,EAAAgC,QAAA,CACbE,WAAW,eACVhC,IAAA,CAACH,MAAM,CAACyD,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBC,OAAO,CAAE,CAAED,OAAO,CAAE,CAAE,CAAE,CACxBE,IAAI,CAAE,CAAEF,OAAO,CAAE,CAAE,CAAE,CACrBnC,SAAS,CAAC,+EAA+E,CAAAS,QAAA,cAEzF5B,KAAA,CAACL,MAAM,CAACyD,GAAG,EACTC,OAAO,CAAE,CAAEI,KAAK,CAAE,GAAG,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BH,OAAO,CAAE,CAAEE,KAAK,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC5BF,IAAI,CAAE,CAAEC,KAAK,CAAE,GAAG,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BvC,SAAS,CAAC,wBAAwB,CAAAS,QAAA,eAElC9B,IAAA,CAACH,MAAM,CAACgE,EAAE,EACRxC,SAAS,CAAC,sGAAsG,CAChHoC,OAAO,CAAE,CACPN,MAAM,CAAE,CACN,+CAA+C,CAC/C,+CAA+C,CAC/C,+CAA+C,CAEnD,CAAE,CACFW,UAAU,CAAE,CAAEC,QAAQ,CAAE,CAAC,CAAEC,MAAM,CAAEC,QAAS,CAAE,CAAAnC,QAAA,CAC/C,sCAED,CAAW,CAAC,cACZ9B,IAAA,CAACH,MAAM,CAACqE,CAAC,EACP7C,SAAS,CAAC,4BAA4B,CACtCkC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBC,OAAO,CAAE,CAAED,OAAO,CAAE,CAAE,CAAE,CACxBM,UAAU,CAAE,CAAEK,KAAK,CAAE,GAAI,CAAE,CAAArC,QAAA,CAC5B,gFAED,CAAU,CAAC,cACX9B,IAAA,CAACH,MAAM,CAACuE,MAAM,EACZ/C,SAAS,CAAC,4NAA4N,CACtOgD,UAAU,CAAE,CAAEV,KAAK,CAAE,IAAK,CAAE,CAC5BW,QAAQ,CAAE,CAAEX,KAAK,CAAE,IAAK,CAAE,CAC1BhD,OAAO,CAAEA,CAAA,GAAMsB,cAAc,CAAC,KAAK,CAAE,CAAAH,QAAA,CACtC,0BAED,CAAe,CAAC,EACN,CAAC,CACH,CACb,CACc,CAAC,CAGjB,CAACE,WAAW,eACX9B,KAAA,CAAAE,SAAA,EAAA0B,QAAA,eAEE9B,IAAA,CAACH,MAAM,CAACyD,GAAG,EACTC,OAAO,CAAE,CAAEK,CAAC,CAAE,CAAC,GAAI,CAAE,CACrBH,OAAO,CAAE,CAAEG,CAAC,CAAE,CAAE,CAAE,CAClBvC,SAAS,CAAC,gFAAgF,CAAAS,QAAA,cAE1F5B,KAAA,QAAKmB,SAAS,CAAC,qDAAqD,CAAAS,QAAA,eAClE5B,KAAA,QAAKmB,SAAS,CAAC,6BAA6B,CAAAS,QAAA,eAC1C9B,IAAA,OAAIqB,SAAS,CAAC,oCAAoC,CAAAS,QAAA,CAAC,sCAAM,CAAI,CAAC,cAC9D5B,KAAA,QAAKmB,SAAS,CAAC,wCAAwC,CAAAS,QAAA,eACrD9B,IAAA,QAAKqB,SAAS,CAAC,iDAAiD,CAAM,CAAC,cACvErB,IAAA,SAAA8B,QAAA,CAAM,cAAE,CAAM,CAAC,EACZ,CAAC,EACH,CAAC,cAEN5B,KAAA,QAAKmB,SAAS,CAAC,6BAA6B,CAAAS,QAAA,eAC1C5B,KAAA,QAAKmB,SAAS,CAAC,YAAY,CAAAS,QAAA,eACzB9B,IAAA,SAAMqB,SAAS,CAAC,eAAe,CAAAS,QAAA,CAAC,2BAAK,CAAM,CAAC,cAC5C9B,IAAA,SAAMqB,SAAS,CAAC,gCAAgC,CAAAS,QAAA,CAAC,eAAG,CAAM,CAAC,EACxD,CAAC,cACN5B,KAAA,QAAKmB,SAAS,CAAC,YAAY,CAAAS,QAAA,eACzB9B,IAAA,SAAMqB,SAAS,CAAC,eAAe,CAAAS,QAAA,CAAC,eAAG,CAAM,CAAC,cAC1C9B,IAAA,SAAMqB,SAAS,CAAC,+BAA+B,CAAAS,QAAA,CAAC,cAAE,CAAM,CAAC,EACtD,CAAC,EACH,CAAC,EACH,CAAC,CACI,CAAC,cAGb9B,IAAA,QAAKqB,SAAS,CAAC,gEAAgE,CAAAS,QAAA,cAC7E5B,KAAA,CAACL,MAAM,CAACyD,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEI,CAAC,CAAE,EAAG,CAAE,CAC/BH,OAAO,CAAE,CAAED,OAAO,CAAE,CAAC,CAAEI,CAAC,CAAE,CAAE,CAAE,CAC9BvC,SAAS,CAAC,yCAAyC,CAAAS,QAAA,eAEnD9B,IAAA,OAAIqB,SAAS,CAAC,+CAA+C,CAAAS,QAAA,CAAC,0BAAI,CAAI,CAAC,cACvE9B,IAAA,QAAKqB,SAAS,CAAC,qCAAqC,CAAAS,QAAA,CACjDM,KAAK,CAACmC,GAAG,CAAEC,IAAI,eACdxE,IAAA,CAACK,WAAW,EAEVE,EAAE,CAAEiE,IAAI,CAACjE,EAAG,CACZC,IAAI,CAAEgE,IAAI,CAAChE,IAAK,CAChBC,KAAK,CAAE+D,IAAI,CAAC/D,KAAM,CAClBC,UAAU,CAAEwB,aAAa,CAACgB,QAAQ,CAACsB,IAAI,CAACjE,EAAE,CAAE,CAC5CI,OAAO,CAAEA,CAAA,GAAMmC,eAAe,CAAC0B,IAAI,CAACjE,EAAE,CAAE,EALnCiE,IAAI,CAACjE,EAMX,CACF,CAAC,CACC,CAAC,EACI,CAAC,CACV,CAAC,cAGNP,IAAA,CAACF,eAAe,EAAAgC,QAAA,CACbI,aAAa,CAACM,MAAM,CAAG,CAAC,eACvBxC,IAAA,CAACH,MAAM,CAACyD,GAAG,EACTC,OAAO,CAAE,CAAEK,CAAC,CAAE,GAAG,CAAEJ,OAAO,CAAE,CAAE,CAAE,CAChCC,OAAO,CAAE,CAAEG,CAAC,CAAE,CAAC,CAAEJ,OAAO,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEE,CAAC,CAAE,GAAG,CAAEJ,OAAO,CAAE,CAAE,CAAE,CAC7BnC,SAAS,CAAC,4DAA4D,CAAAS,QAAA,cAEtE5B,KAAA,QAAKmB,SAAS,CAAC,wEAAwE,CAAAS,QAAA,eACrF9B,IAAA,CAACH,MAAM,CAACuE,MAAM,EACZ/C,SAAS,CAAC,oNAAoN,CAC9NgD,UAAU,CAAE,CAAEV,KAAK,CAAE,IAAK,CAAE,CAC5BW,QAAQ,CAAE,CAAEX,KAAK,CAAE,IAAK,CAAE,CAC1BhD,OAAO,CAAEA,CAAA,GAAMyC,YAAY,CAAC,IAAI,CAAE,CAAAtB,QAAA,CACnC,cAED,CAAe,CAAC,cAChB9B,IAAA,CAACH,MAAM,CAACuE,MAAM,EACZ/C,SAAS,CAAC,oNAAoN,CAC9NgD,UAAU,CAAE,CAAEV,KAAK,CAAE,IAAK,CAAE,CAC5BW,QAAQ,CAAE,CAAEX,KAAK,CAAE,IAAK,CAAE,CAC1BhD,OAAO,CAAEA,CAAA,GAAMyC,YAAY,CAAC,GAAG,CAAE,CAAAtB,QAAA,CAClC,QAED,CAAe,CAAC,cAChB9B,IAAA,CAACH,MAAM,CAACuE,MAAM,EACZ/C,SAAS,CAAC,oNAAoN,CAC9NgD,UAAU,CAAE,CAAEV,KAAK,CAAE,IAAK,CAAE,CAC5BW,QAAQ,CAAE,CAAEX,KAAK,CAAE,IAAK,CAAE,CAC1BhD,OAAO,CAAEA,CAAA,GAAMyC,YAAY,CAAC,GAAG,CAAE,CAAAtB,QAAA,CAClC,QAED,CAAe,CAAC,cAChB9B,IAAA,CAACH,MAAM,CAACuE,MAAM,EACZ/C,SAAS,CAAC,oNAAoN,CAC9NgD,UAAU,CAAE,CAAEV,KAAK,CAAE,IAAK,CAAE,CAC5BW,QAAQ,CAAE,CAAEX,KAAK,CAAE,IAAK,CAAE,CAC1BhD,OAAO,CAAEA,CAAA,GAAMyC,YAAY,CAAC,GAAG,CAAE,CAAAtB,QAAA,CAClC,QAED,CAAe,CAAC,cAChB9B,IAAA,CAACH,MAAM,CAACuE,MAAM,EACZ/C,SAAS,CAAC,wLAAwL,CAClMgD,UAAU,CAAE,CAAEV,KAAK,CAAE,IAAK,CAAE,CAC5BW,QAAQ,CAAE,CAAEX,KAAK,CAAE,IAAK,CAAE,CAC1BhD,OAAO,CAAEA,CAAA,GAAMyC,YAAY,CAAC,IAAI,CAAE,CAAAtB,QAAA,CACnC,cAED,CAAe,CAAC,EACb,CAAC,CACI,CACb,CACc,CAAC,EAClB,CACH,EACE,CAAC,CAEV,CAEA,cAAe,CAAAC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}