{"ast": null, "code": "var _jsxFileName = \"F:\\\\= \\u795E\\u706F\\u667A\\u5E93\\\\- AI \\u521B\\u4F5C\\\\AI APP\\\\\\u795E\\u706F\\u9EBB\\u5C06\\u5927\\u5E08\\\\mahjong-master-react\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\n\n// 简化的麻将牌组件\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction MahjongTile({\n  id,\n  type,\n  value,\n  isSelected,\n  onClick\n}) {\n  const getTileColor = () => {\n    switch (type) {\n      case 'character':\n        return 'bg-red-100 border-red-300 text-red-800';\n      case 'bamboo':\n        return 'bg-green-100 border-green-300 text-green-800';\n      case 'dot':\n        return 'bg-blue-100 border-blue-300 text-blue-800';\n      case 'wind':\n        return 'bg-yellow-100 border-yellow-300 text-yellow-800';\n      case 'dragon':\n        return 'bg-purple-100 border-purple-300 text-purple-800';\n      default:\n        return 'bg-gray-100 border-gray-300 text-gray-800';\n    }\n  };\n  const handleClick = () => {\n    console.log('MahjongTile clicked:', id);\n    alert('点击了牌: ' + id + ' - 值: ' + value);\n    onClick();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: 'mahjong-tile ' + getTileColor() + (isSelected ? ' selected' : ''),\n    onClick: handleClick,\n    style: {\n      width: '48px',\n      height: '64px',\n      borderRadius: '8px',\n      border: '2px solid #ccc',\n      cursor: 'pointer',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      fontWeight: 'bold',\n      fontSize: '14px',\n      userSelect: 'none',\n      WebkitUserSelect: 'none',\n      MozUserSelect: 'none',\n      msUserSelect: 'none'\n    },\n    children: value\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n}\n_c = MahjongTile;\nfunction App() {\n  _s();\n  const [showWelcome, setShowWelcome] = useState(false); // 直接进入游戏界面进行测试\n  const [selectedTiles, setSelectedTiles] = useState([]);\n\n  // 生成示例麻将牌\n  const [tiles] = useState(() => {\n    const tileTypes = ['character', 'bamboo', 'dot', 'wind', 'dragon'];\n    return Array.from({\n      length: 13\n    }, (_, i) => ({\n      id: `tile-${i}`,\n      type: tileTypes[Math.floor(Math.random() * tileTypes.length)],\n      value: Math.floor(Math.random() * 9) + 1,\n      isSelected: false\n    }));\n  });\n  const handleTileClick = tileId => {\n    console.log('点击了麻将牌:', tileId); // 添加调试信息\n    setSelectedTiles(prev => {\n      const newSelection = prev.includes(tileId) ? prev.filter(id => id !== tileId) : [...prev, tileId];\n      console.log('选中的牌:', newSelection); // 添加调试信息\n      return newSelection;\n    });\n  };\n  const handleAction = action => {\n    console.log(`执行动作: ${action}，选中的牌: ${selectedTiles}`);\n    setSelectedTiles([]);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 opacity-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full h-full bg-gradient-to-br from-yellow-500/10 to-green-500/10\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), showWelcome && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-white\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-6xl font-bold mb-4 text-yellow-400\",\n          children: \"\\u795E\\u706F\\u9EBB\\u5C06\\u5927\\u5E08\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl mb-8 text-gray-300\",\n          children: \"\\u8D85\\u8D8A\\u4F20\\u7EDF\\uFF0C\\u91CD\\u65B0\\u5B9A\\u4E49\\u9EBB\\u5C06\\u4F53\\u9A8C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-3 px-8 rounded-xl text-lg\",\n          onClick: () => setShowWelcome(false),\n          children: \"\\u5F00\\u59CB\\u6E38\\u620F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 9\n    }, this), !showWelcome && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 left-0 right-0 z-40 bg-black bg-opacity-50 p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center max-w-7xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-yellow-400\",\n              children: \"\\u795E\\u706F\\u9EBB\\u5C06\\u5927\\u5E08\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-3 h-3 bg-green-500 rounded-full animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u5728\\u7EBF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-400\",\n                children: \"\\u5F53\\u524D\\u5C40\\u6570:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-yellow-400 font-bold\",\n                children: \"\\u7B2C1\\u5C40\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-400\",\n                children: \"\\u98CE\\u5708:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-green-400 font-bold\",\n                children: \"\\u4E1C\\u98CE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center justify-center h-screen pt-20 pb-20\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-green-800 rounded-3xl p-8 shadow-2xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-white text-xl font-bold mb-6 text-center\",\n            children: \"\\u60A8\\u7684\\u624B\\u724C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => alert('测试按钮工作正常！'),\n              className: \"bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600\",\n              children: \"\\u6D4B\\u8BD5\\u70B9\\u51FB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2 flex-wrap justify-center\",\n            children: tiles.map(tile => /*#__PURE__*/_jsxDEV(MahjongTile, {\n              id: tile.id,\n              type: tile.type,\n              value: tile.value,\n              isSelected: selectedTiles.includes(tile.id),\n              onClick: () => handleTileClick(tile.id)\n            }, tile.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this), selectedTiles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-4 left-1/2 transform -translate-x-1/2 z-40\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-black bg-opacity-70 rounded-2xl p-4 flex space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg\",\n            onClick: () => handleAction('出牌'),\n            children: \"\\u51FA\\u724C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg\",\n            onClick: () => handleAction('吃'),\n            children: \"\\u5403\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg\",\n            onClick: () => handleAction('碰'),\n            children: \"\\u78B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg\",\n            onClick: () => handleAction('杠'),\n            children: \"\\u6760\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-6 rounded-lg\",\n            onClick: () => handleAction('胡牌'),\n            children: \"\\u80E1\\u724C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"r8uTuIMEPIPsU7gw/B1n7DzHFVc=\");\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"MahjongTile\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MahjongTile", "id", "type", "value", "isSelected", "onClick", "getTileColor", "handleClick", "console", "log", "alert", "className", "style", "width", "height", "borderRadius", "border", "cursor", "display", "alignItems", "justifyContent", "fontWeight", "fontSize", "userSelect", "WebkitUserSelect", "MozUserSelect", "msUserSelect", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "App", "_s", "showWelcome", "setShowWelcome", "selectedTiles", "setSelectedTiles", "tiles", "tileTypes", "Array", "from", "length", "_", "i", "Math", "floor", "random", "handleTileClick", "tileId", "prev", "newSelection", "includes", "filter", "handleAction", "action", "map", "tile", "_c2", "$RefreshReg$"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\n// 简化的麻将牌组件\ninterface MahjongTileProps {\n  id: string;\n  type: string;\n  value: string | number;\n  isSelected: boolean;\n  onClick: () => void;\n}\n\nfunction MahjongTile({ id, type, value, isSelected, onClick }: MahjongTileProps) {\n  const getTileColor = () => {\n    switch (type) {\n      case 'character': return 'bg-red-100 border-red-300 text-red-800';\n      case 'bamboo': return 'bg-green-100 border-green-300 text-green-800';\n      case 'dot': return 'bg-blue-100 border-blue-300 text-blue-800';\n      case 'wind': return 'bg-yellow-100 border-yellow-300 text-yellow-800';\n      case 'dragon': return 'bg-purple-100 border-purple-300 text-purple-800';\n      default: return 'bg-gray-100 border-gray-300 text-gray-800';\n    }\n  };\n\n  const handleClick = () => {\n    console.log('MahjongTile clicked:', id);\n    alert('点击了牌: ' + id + ' - 值: ' + value);\n    onClick();\n  };\n\n  return (\n    <div\n      className={'mahjong-tile ' + getTileColor() + (isSelected ? ' selected' : '')}\n      onClick={handleClick}\n      style={{\n        width: '48px',\n        height: '64px',\n        borderRadius: '8px',\n        border: '2px solid #ccc',\n        cursor: 'pointer',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        fontWeight: 'bold',\n        fontSize: '14px',\n        userSelect: 'none',\n        WebkitUserSelect: 'none',\n        MozUserSelect: 'none',\n        msUserSelect: 'none'\n      }}\n    >\n      {value}\n    </div>\n  );\n}\n\nfunction App() {\n  const [showWelcome, setShowWelcome] = useState(false); // 直接进入游戏界面进行测试\n  const [selectedTiles, setSelectedTiles] = useState<string[]>([]);\n\n  // 生成示例麻将牌\n  const [tiles] = useState(() => {\n    const tileTypes = ['character', 'bamboo', 'dot', 'wind', 'dragon'];\n    return Array.from({ length: 13 }, (_, i) => ({\n      id: `tile-${i}`,\n      type: tileTypes[Math.floor(Math.random() * tileTypes.length)],\n      value: Math.floor(Math.random() * 9) + 1,\n      isSelected: false\n    }));\n  });\n\n  const handleTileClick = (tileId: string) => {\n    console.log('点击了麻将牌:', tileId); // 添加调试信息\n    setSelectedTiles(prev => {\n      const newSelection = prev.includes(tileId)\n        ? prev.filter(id => id !== tileId)\n        : [...prev, tileId];\n      console.log('选中的牌:', newSelection); // 添加调试信息\n      return newSelection;\n    });\n  };\n\n  const handleAction = (action: string) => {\n    console.log(`执行动作: ${action}，选中的牌: ${selectedTiles}`);\n    setSelectedTiles([]);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden\">\n      {/* 背景装饰 */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"w-full h-full bg-gradient-to-br from-yellow-500/10 to-green-500/10\"></div>\n      </div>\n\n      {/* 欢迎界面 */}\n      {showWelcome && (\n        <div className=\"absolute inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80\">\n          <div className=\"text-center text-white\">\n            <h1 className=\"text-6xl font-bold mb-4 text-yellow-400\">\n              神灯麻将大师\n            </h1>\n            <p className=\"text-xl mb-8 text-gray-300\">\n              超越传统，重新定义麻将体验\n            </p>\n            <button\n              className=\"bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-3 px-8 rounded-xl text-lg\"\n              onClick={() => setShowWelcome(false)}\n            >\n              开始游戏\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* 主游戏界面 */}\n      {!showWelcome && (\n        <>\n          {/* 顶部状态栏 */}\n          <div className=\"absolute top-0 left-0 right-0 z-40 bg-black bg-opacity-50 p-4\">\n            <div className=\"flex justify-between items-center max-w-7xl mx-auto\">\n              <div className=\"flex items-center space-x-4\">\n                <h2 className=\"text-2xl font-bold text-yellow-400\">神灯麻将大师</h2>\n                <div className=\"flex items-center space-x-2 text-white\">\n                  <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\n                  <span>在线</span>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"text-white\">\n                  <span className=\"text-gray-400\">当前局数:</span>\n                  <span className=\"ml-2 text-yellow-400 font-bold\">第1局</span>\n                </div>\n                <div className=\"text-white\">\n                  <span className=\"text-gray-400\">风圈:</span>\n                  <span className=\"ml-2 text-green-400 font-bold\">东风</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* 游戏区域 */}\n          <div className=\"flex flex-col items-center justify-center h-screen pt-20 pb-20\">\n            <div className=\"bg-green-800 rounded-3xl p-8 shadow-2xl\">\n              <h3 className=\"text-white text-xl font-bold mb-6 text-center\">您的手牌</h3>\n\n              {/* 测试按钮 */}\n              <div className=\"mb-4 text-center\">\n                <button\n                  onClick={() => alert('测试按钮工作正常！')}\n                  className=\"bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600\"\n                >\n                  测试点击\n                </button>\n              </div>\n\n              <div className=\"flex gap-2 flex-wrap justify-center\">\n                {tiles.map((tile) => (\n                  <MahjongTile\n                    key={tile.id}\n                    id={tile.id}\n                    type={tile.type}\n                    value={tile.value}\n                    isSelected={selectedTiles.includes(tile.id)}\n                    onClick={() => handleTileClick(tile.id)}\n                  />\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* 底部操作栏 */}\n          {selectedTiles.length > 0 && (\n            <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 z-40\">\n              <div className=\"bg-black bg-opacity-70 rounded-2xl p-4 flex space-x-3\">\n                <button\n                  className=\"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg\"\n                  onClick={() => handleAction('出牌')}\n                >\n                  出牌\n                </button>\n                <button\n                  className=\"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg\"\n                  onClick={() => handleAction('吃')}\n                >\n                  吃\n                </button>\n                <button\n                  className=\"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg\"\n                  onClick={() => handleAction('碰')}\n                >\n                  碰\n                </button>\n                <button\n                  className=\"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg\"\n                  onClick={() => handleAction('杠')}\n                >\n                  杠\n                </button>\n                <button\n                  className=\"bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-6 rounded-lg\"\n                  onClick={() => handleAction('胡牌')}\n                >\n                  胡牌\n                </button>\n              </div>\n            </div>\n          )}\n        </>\n      )}\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,SAASC,WAAWA,CAAC;EAAEC,EAAE;EAAEC,IAAI;EAAEC,KAAK;EAAEC,UAAU;EAAEC;AAA0B,CAAC,EAAE;EAC/E,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,QAAQJ,IAAI;MACV,KAAK,WAAW;QAAE,OAAO,wCAAwC;MACjE,KAAK,QAAQ;QAAE,OAAO,8CAA8C;MACpE,KAAK,KAAK;QAAE,OAAO,2CAA2C;MAC9D,KAAK,MAAM;QAAE,OAAO,iDAAiD;MACrE,KAAK,QAAQ;QAAE,OAAO,iDAAiD;MACvE;QAAS,OAAO,2CAA2C;IAC7D;EACF,CAAC;EAED,MAAMK,WAAW,GAAGA,CAAA,KAAM;IACxBC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAER,EAAE,CAAC;IACvCS,KAAK,CAAC,QAAQ,GAAGT,EAAE,GAAG,QAAQ,GAAGE,KAAK,CAAC;IACvCE,OAAO,CAAC,CAAC;EACX,CAAC;EAED,oBACER,OAAA;IACEc,SAAS,EAAE,eAAe,GAAGL,YAAY,CAAC,CAAC,IAAIF,UAAU,GAAG,WAAW,GAAG,EAAE,CAAE;IAC9EC,OAAO,EAAEE,WAAY;IACrBK,KAAK,EAAE;MACLC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,YAAY,EAAE,KAAK;MACnBC,MAAM,EAAE,gBAAgB;MACxBC,MAAM,EAAE,SAAS;MACjBC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,MAAM;MAClBC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClBC,gBAAgB,EAAE,MAAM;MACxBC,aAAa,EAAE,MAAM;MACrBC,YAAY,EAAE;IAChB,CAAE;IAAAC,QAAA,EAEDxB;EAAK;IAAAyB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACC,EAAA,GA1CQhC,WAAW;AA4CpB,SAASiC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAW,EAAE,CAAC;;EAEhE;EACA,MAAM,CAAC4C,KAAK,CAAC,GAAG5C,QAAQ,CAAC,MAAM;IAC7B,MAAM6C,SAAS,GAAG,CAAC,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC;IAClE,OAAOC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAG,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,MAAM;MAC3C5C,EAAE,EAAE,QAAQ4C,CAAC,EAAE;MACf3C,IAAI,EAAEsC,SAAS,CAACM,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGR,SAAS,CAACG,MAAM,CAAC,CAAC;MAC7DxC,KAAK,EAAE2C,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACxC5C,UAAU,EAAE;IACd,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EAEF,MAAM6C,eAAe,GAAIC,MAAc,IAAK;IAC1C1C,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEyC,MAAM,CAAC,CAAC,CAAC;IAChCZ,gBAAgB,CAACa,IAAI,IAAI;MACvB,MAAMC,YAAY,GAAGD,IAAI,CAACE,QAAQ,CAACH,MAAM,CAAC,GACtCC,IAAI,CAACG,MAAM,CAACrD,EAAE,IAAIA,EAAE,KAAKiD,MAAM,CAAC,GAChC,CAAC,GAAGC,IAAI,EAAED,MAAM,CAAC;MACrB1C,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE2C,YAAY,CAAC,CAAC,CAAC;MACpC,OAAOA,YAAY;IACrB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,YAAY,GAAIC,MAAc,IAAK;IACvChD,OAAO,CAACC,GAAG,CAAC,SAAS+C,MAAM,UAAUnB,aAAa,EAAE,CAAC;IACrDC,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,oBACEzC,OAAA;IAAKc,SAAS,EAAC,mGAAmG;IAAAgB,QAAA,gBAEhH9B,OAAA;MAAKc,SAAS,EAAC,6BAA6B;MAAAgB,QAAA,eAC1C9B,OAAA;QAAKc,SAAS,EAAC;MAAoE;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvF,CAAC,EAGLI,WAAW,iBACVtC,OAAA;MAAKc,SAAS,EAAC,+EAA+E;MAAAgB,QAAA,eAC5F9B,OAAA;QAAKc,SAAS,EAAC,wBAAwB;QAAAgB,QAAA,gBACrC9B,OAAA;UAAIc,SAAS,EAAC,yCAAyC;UAAAgB,QAAA,EAAC;QAExD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlC,OAAA;UAAGc,SAAS,EAAC,4BAA4B;UAAAgB,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJlC,OAAA;UACEc,SAAS,EAAC,qFAAqF;UAC/FN,OAAO,EAAEA,CAAA,KAAM+B,cAAc,CAAC,KAAK,CAAE;UAAAT,QAAA,EACtC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA,CAACI,WAAW,iBACXtC,OAAA,CAAAE,SAAA;MAAA4B,QAAA,gBAEE9B,OAAA;QAAKc,SAAS,EAAC,+DAA+D;QAAAgB,QAAA,eAC5E9B,OAAA;UAAKc,SAAS,EAAC,qDAAqD;UAAAgB,QAAA,gBAClE9B,OAAA;YAAKc,SAAS,EAAC,6BAA6B;YAAAgB,QAAA,gBAC1C9B,OAAA;cAAIc,SAAS,EAAC,oCAAoC;cAAAgB,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DlC,OAAA;cAAKc,SAAS,EAAC,wCAAwC;cAAAgB,QAAA,gBACrD9B,OAAA;gBAAKc,SAAS,EAAC;cAAiD;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvElC,OAAA;gBAAA8B,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlC,OAAA;YAAKc,SAAS,EAAC,6BAA6B;YAAAgB,QAAA,gBAC1C9B,OAAA;cAAKc,SAAS,EAAC,YAAY;cAAAgB,QAAA,gBACzB9B,OAAA;gBAAMc,SAAS,EAAC,eAAe;gBAAAgB,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5ClC,OAAA;gBAAMc,SAAS,EAAC,gCAAgC;gBAAAgB,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNlC,OAAA;cAAKc,SAAS,EAAC,YAAY;cAAAgB,QAAA,gBACzB9B,OAAA;gBAAMc,SAAS,EAAC,eAAe;gBAAAgB,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1ClC,OAAA;gBAAMc,SAAS,EAAC,+BAA+B;gBAAAgB,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlC,OAAA;QAAKc,SAAS,EAAC,gEAAgE;QAAAgB,QAAA,eAC7E9B,OAAA;UAAKc,SAAS,EAAC,yCAAyC;UAAAgB,QAAA,gBACtD9B,OAAA;YAAIc,SAAS,EAAC,+CAA+C;YAAAgB,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGvElC,OAAA;YAAKc,SAAS,EAAC,kBAAkB;YAAAgB,QAAA,eAC/B9B,OAAA;cACEQ,OAAO,EAAEA,CAAA,KAAMK,KAAK,CAAC,WAAW,CAAE;cAClCC,SAAS,EAAC,0DAA0D;cAAAgB,QAAA,EACrE;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENlC,OAAA;YAAKc,SAAS,EAAC,qCAAqC;YAAAgB,QAAA,EACjDY,KAAK,CAACkB,GAAG,CAAEC,IAAI,iBACd7D,OAAA,CAACG,WAAW;cAEVC,EAAE,EAAEyD,IAAI,CAACzD,EAAG;cACZC,IAAI,EAAEwD,IAAI,CAACxD,IAAK;cAChBC,KAAK,EAAEuD,IAAI,CAACvD,KAAM;cAClBC,UAAU,EAAEiC,aAAa,CAACgB,QAAQ,CAACK,IAAI,CAACzD,EAAE,CAAE;cAC5CI,OAAO,EAAEA,CAAA,KAAM4C,eAAe,CAACS,IAAI,CAACzD,EAAE;YAAE,GALnCyD,IAAI,CAACzD,EAAE;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMb,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLM,aAAa,CAACM,MAAM,GAAG,CAAC,iBACvB9C,OAAA;QAAKc,SAAS,EAAC,4DAA4D;QAAAgB,QAAA,eACzE9B,OAAA;UAAKc,SAAS,EAAC,uDAAuD;UAAAgB,QAAA,gBACpE9B,OAAA;YACEc,SAAS,EAAC,+EAA+E;YACzFN,OAAO,EAAEA,CAAA,KAAMkD,YAAY,CAAC,IAAI,CAAE;YAAA5B,QAAA,EACnC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlC,OAAA;YACEc,SAAS,EAAC,+EAA+E;YACzFN,OAAO,EAAEA,CAAA,KAAMkD,YAAY,CAAC,GAAG,CAAE;YAAA5B,QAAA,EAClC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlC,OAAA;YACEc,SAAS,EAAC,+EAA+E;YACzFN,OAAO,EAAEA,CAAA,KAAMkD,YAAY,CAAC,GAAG,CAAE;YAAA5B,QAAA,EAClC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlC,OAAA;YACEc,SAAS,EAAC,+EAA+E;YACzFN,OAAO,EAAEA,CAAA,KAAMkD,YAAY,CAAC,GAAG,CAAE;YAAA5B,QAAA,EAClC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlC,OAAA;YACEc,SAAS,EAAC,uEAAuE;YACjFN,OAAO,EAAEA,CAAA,KAAMkD,YAAY,CAAC,IAAI,CAAE;YAAA5B,QAAA,EACnC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA,eACD,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACG,EAAA,CA5JQD,GAAG;AAAA0B,GAAA,GAAH1B,GAAG;AA8JZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAA2B,GAAA;AAAAC,YAAA,CAAA5B,EAAA;AAAA4B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}