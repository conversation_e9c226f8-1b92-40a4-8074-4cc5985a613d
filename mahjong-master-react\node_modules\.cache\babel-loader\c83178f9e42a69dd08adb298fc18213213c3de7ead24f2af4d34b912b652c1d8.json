{"ast": null, "code": "/**\n * Main content for the worker that handles the loading and execution of\n * modules within it.\n */\nfunction workerBootstrap() {\n  var modules = Object.create(null);\n\n  // Handle messages for registering a module\n  function registerModule(ref, callback) {\n    var id = ref.id;\n    var name = ref.name;\n    var dependencies = ref.dependencies;\n    if (dependencies === void 0) dependencies = [];\n    var init = ref.init;\n    if (init === void 0) init = function () {};\n    var getTransferables = ref.getTransferables;\n    if (getTransferables === void 0) getTransferables = null;\n\n    // Only register once\n    if (modules[id]) {\n      return;\n    }\n    try {\n      // If any dependencies are modules, ensure they're registered and grab their value\n      dependencies = dependencies.map(function (dep) {\n        if (dep && dep.isWorkerModule) {\n          registerModule(dep, function (depResult) {\n            if (depResult instanceof Error) {\n              throw depResult;\n            }\n          });\n          dep = modules[dep.id].value;\n        }\n        return dep;\n      });\n\n      // Rehydrate functions\n      init = rehydrate(\"<\" + name + \">.init\", init);\n      if (getTransferables) {\n        getTransferables = rehydrate(\"<\" + name + \">.getTransferables\", getTransferables);\n      }\n\n      // Initialize the module and store its value\n      var value = null;\n      if (typeof init === 'function') {\n        value = init.apply(void 0, dependencies);\n      } else {\n        console.error('worker module init function failed to rehydrate');\n      }\n      modules[id] = {\n        id: id,\n        value: value,\n        getTransferables: getTransferables\n      };\n      callback(value);\n    } catch (err) {\n      if (!(err && err.noLog)) {\n        console.error(err);\n      }\n      callback(err);\n    }\n  }\n\n  // Handle messages for calling a registered module's result function\n  function callModule(ref, callback) {\n    var ref$1;\n    var id = ref.id;\n    var args = ref.args;\n    if (!modules[id] || typeof modules[id].value !== 'function') {\n      callback(new Error(\"Worker module \" + id + \": not found or its 'init' did not return a function\"));\n    }\n    try {\n      var result = (ref$1 = modules[id]).value.apply(ref$1, args);\n      if (result && typeof result.then === 'function') {\n        result.then(handleResult, function (rej) {\n          return callback(rej instanceof Error ? rej : new Error('' + rej));\n        });\n      } else {\n        handleResult(result);\n      }\n    } catch (err) {\n      callback(err);\n    }\n    function handleResult(result) {\n      try {\n        var tx = modules[id].getTransferables && modules[id].getTransferables(result);\n        if (!tx || !Array.isArray(tx) || !tx.length) {\n          tx = undefined; //postMessage is very picky about not passing null or empty transferables\n        }\n        callback(result, tx);\n      } catch (err) {\n        console.error(err);\n        callback(err);\n      }\n    }\n  }\n  function rehydrate(name, str) {\n    var result = void 0;\n    self.troikaDefine = function (r) {\n      return result = r;\n    };\n    var url = URL.createObjectURL(new Blob([\"/** \" + name.replace(/\\*/g, '') + \" **/\\n\\ntroikaDefine(\\n\" + str + \"\\n)\"], {\n      type: 'application/javascript'\n    }));\n    try {\n      importScripts(url);\n    } catch (err) {\n      console.error(err);\n    }\n    URL.revokeObjectURL(url);\n    delete self.troikaDefine;\n    return result;\n  }\n\n  // Handler for all messages within the worker\n  self.addEventListener('message', function (e) {\n    var ref = e.data;\n    var messageId = ref.messageId;\n    var action = ref.action;\n    var data = ref.data;\n    try {\n      // Module registration\n      if (action === 'registerModule') {\n        registerModule(data, function (result) {\n          if (result instanceof Error) {\n            postMessage({\n              messageId: messageId,\n              success: false,\n              error: result.message\n            });\n          } else {\n            postMessage({\n              messageId: messageId,\n              success: true,\n              result: {\n                isCallable: typeof result === 'function'\n              }\n            });\n          }\n        });\n      }\n      // Invocation\n      if (action === 'callModule') {\n        callModule(data, function (result, transferables) {\n          if (result instanceof Error) {\n            postMessage({\n              messageId: messageId,\n              success: false,\n              error: result.message\n            });\n          } else {\n            postMessage({\n              messageId: messageId,\n              success: true,\n              result: result\n            }, transferables || undefined);\n          }\n        });\n      }\n    } catch (err) {\n      postMessage({\n        messageId: messageId,\n        success: false,\n        error: err.stack\n      });\n    }\n  });\n}\n\n/**\n * Fallback for `defineWorkerModule` that behaves identically but runs in the main\n * thread, for when the execution environment doesn't support web workers or they\n * are disallowed due to e.g. CSP security restrictions.\n */\nfunction defineMainThreadModule(options) {\n  var moduleFunc = function () {\n    var args = [],\n      len = arguments.length;\n    while (len--) args[len] = arguments[len];\n    return moduleFunc._getInitResult().then(function (initResult) {\n      if (typeof initResult === 'function') {\n        return initResult.apply(void 0, args);\n      } else {\n        throw new Error('Worker module function was called but `init` did not return a callable function');\n      }\n    });\n  };\n  moduleFunc._getInitResult = function () {\n    // We can ignore getTransferables in main thread. TODO workerId?\n    var dependencies = options.dependencies;\n    var init = options.init;\n\n    // Resolve dependencies\n    dependencies = Array.isArray(dependencies) ? dependencies.map(function (dep) {\n      if (dep) {\n        // If it's a worker module, use its main thread impl\n        dep = dep.onMainThread || dep;\n        // If it's a main thread worker module, use its init return value\n        if (dep._getInitResult) {\n          dep = dep._getInitResult();\n        }\n      }\n      return dep;\n    }) : [];\n\n    // Invoke init with the resolved dependencies\n    var initPromise = Promise.all(dependencies).then(function (deps) {\n      return init.apply(null, deps);\n    });\n\n    // Cache the resolved promise for subsequent calls\n    moduleFunc._getInitResult = function () {\n      return initPromise;\n    };\n    return initPromise;\n  };\n  return moduleFunc;\n}\nvar supportsWorkers = function () {\n  var supported = false;\n\n  // Only attempt worker initialization in browsers; elsewhere it would just be\n  // noise e.g. loading into a Node environment for SSR.\n  if (typeof window !== 'undefined' && typeof window.document !== 'undefined') {\n    try {\n      // TODO additional checks for things like importScripts within the worker?\n      //  Would need to be an async check.\n      var worker = new Worker(URL.createObjectURL(new Blob([''], {\n        type: 'application/javascript'\n      })));\n      worker.terminate();\n      supported = true;\n    } catch (err) {\n      if (typeof process !== 'undefined' && process.env.NODE_ENV === 'test') ;else {\n        console.log(\"Troika createWorkerModule: web workers not allowed; falling back to main thread execution. Cause: [\" + err.message + \"]\");\n      }\n    }\n  }\n\n  // Cached result\n  supportsWorkers = function () {\n    return supported;\n  };\n  return supported;\n};\nvar _workerModuleId = 0;\nvar _messageId = 0;\nvar _allowInitAsString = false;\nvar workers = Object.create(null);\nvar registeredModules = Object.create(null); //workerId -> Set<unregisterFn>\nvar openRequests = Object.create(null);\n\n/**\n * Define a module of code that will be executed with a web worker. This provides a simple\n * interface for moving chunks of logic off the main thread, and managing their dependencies\n * among one another.\n *\n * @param {object} options\n * @param {function} options.init\n * @param {array} [options.dependencies]\n * @param {function} [options.getTransferables]\n * @param {string} [options.name]\n * @param {string} [options.workerId]\n * @return {function(...[*]): {then}}\n */\nfunction defineWorkerModule(options) {\n  if ((!options || typeof options.init !== 'function') && !_allowInitAsString) {\n    throw new Error('requires `options.init` function');\n  }\n  var dependencies = options.dependencies;\n  var init = options.init;\n  var getTransferables = options.getTransferables;\n  var workerId = options.workerId;\n  var onMainThread = defineMainThreadModule(options);\n  if (workerId == null) {\n    workerId = '#default';\n  }\n  var id = \"workerModule\" + ++_workerModuleId;\n  var name = options.name || id;\n  var registrationPromise = null;\n  dependencies = dependencies && dependencies.map(function (dep) {\n    // Wrap raw functions as worker modules with no dependencies\n    if (typeof dep === 'function' && !dep.workerModuleData) {\n      _allowInitAsString = true;\n      dep = defineWorkerModule({\n        workerId: workerId,\n        name: \"<\" + name + \"> function dependency: \" + dep.name,\n        init: \"function(){return (\\n\" + stringifyFunction(dep) + \"\\n)}\"\n      });\n      _allowInitAsString = false;\n    }\n    // Grab postable data for worker modules\n    if (dep && dep.workerModuleData) {\n      dep = dep.workerModuleData;\n    }\n    return dep;\n  });\n  function moduleFunc() {\n    var args = [],\n      len = arguments.length;\n    while (len--) args[len] = arguments[len];\n    if (!supportsWorkers()) {\n      return onMainThread.apply(void 0, args);\n    }\n\n    // Register this module if needed\n    if (!registrationPromise) {\n      registrationPromise = callWorker(workerId, 'registerModule', moduleFunc.workerModuleData);\n      var unregister = function () {\n        registrationPromise = null;\n        registeredModules[workerId].delete(unregister);\n      };\n      (registeredModules[workerId] || (registeredModules[workerId] = new Set())).add(unregister);\n    }\n\n    // Invoke the module, returning a promise\n    return registrationPromise.then(function (ref) {\n      var isCallable = ref.isCallable;\n      if (isCallable) {\n        return callWorker(workerId, 'callModule', {\n          id: id,\n          args: args\n        });\n      } else {\n        throw new Error('Worker module function was called but `init` did not return a callable function');\n      }\n    });\n  }\n  moduleFunc.workerModuleData = {\n    isWorkerModule: true,\n    id: id,\n    name: name,\n    dependencies: dependencies,\n    init: stringifyFunction(init),\n    getTransferables: getTransferables && stringifyFunction(getTransferables)\n  };\n  moduleFunc.onMainThread = onMainThread;\n  return moduleFunc;\n}\n\n/**\n * Terminate an active Worker by a workerId that was passed to defineWorkerModule.\n * This only terminates the Worker itself; the worker module will remain available\n * and if you call it again its Worker will be respawned.\n * @param {string} workerId\n */\nfunction terminateWorker(workerId) {\n  // Unregister all modules that were registered in that worker\n  if (registeredModules[workerId]) {\n    registeredModules[workerId].forEach(function (unregister) {\n      unregister();\n    });\n  }\n  // Terminate the Worker object\n  if (workers[workerId]) {\n    workers[workerId].terminate();\n    delete workers[workerId];\n  }\n}\n\n/**\n * Stringifies a function into a form that can be deserialized in the worker\n * @param fn\n */\nfunction stringifyFunction(fn) {\n  var str = fn.toString();\n  // If it was defined in object method/property format, it needs to be modified\n  if (!/^function/.test(str) && /^\\w+\\s*\\(/.test(str)) {\n    str = 'function ' + str;\n  }\n  return str;\n}\nfunction getWorker(workerId) {\n  var worker = workers[workerId];\n  if (!worker) {\n    // Bootstrap the worker's content\n    var bootstrap = stringifyFunction(workerBootstrap);\n\n    // Create the worker from the bootstrap function content\n    worker = workers[workerId] = new Worker(URL.createObjectURL(new Blob([\"/** Worker Module Bootstrap: \" + workerId.replace(/\\*/g, '') + \" **/\\n\\n;(\" + bootstrap + \")()\"], {\n      type: 'application/javascript'\n    })));\n\n    // Single handler for response messages from the worker\n    worker.onmessage = function (e) {\n      var response = e.data;\n      var msgId = response.messageId;\n      var callback = openRequests[msgId];\n      if (!callback) {\n        throw new Error('WorkerModule response with empty or unknown messageId');\n      }\n      delete openRequests[msgId];\n      callback(response);\n    };\n  }\n  return worker;\n}\n\n// Issue a call to the worker with a callback to handle the response\nfunction callWorker(workerId, action, data) {\n  return new Promise(function (resolve, reject) {\n    var messageId = ++_messageId;\n    openRequests[messageId] = function (response) {\n      if (response.success) {\n        resolve(response.result);\n      } else {\n        reject(new Error(\"Error in worker \" + action + \" call: \" + response.error));\n      }\n    };\n    getWorker(workerId).postMessage({\n      messageId: messageId,\n      action: action,\n      data: data\n    });\n  });\n}\nexport { defineWorkerModule, stringifyFunction, terminateWorker };", "map": {"version": 3, "names": ["workerBootstrap", "modules", "Object", "create", "registerModule", "ref", "callback", "id", "name", "dependencies", "init", "getTransferables", "map", "dep", "isWorkerModule", "depResult", "Error", "value", "rehydrate", "apply", "console", "error", "err", "noLog", "callModule", "ref$1", "args", "result", "then", "handleResult", "rej", "tx", "Array", "isArray", "length", "undefined", "str", "self", "troikaDefine", "r", "url", "URL", "createObjectURL", "Blob", "replace", "type", "importScripts", "revokeObjectURL", "addEventListener", "e", "data", "messageId", "action", "postMessage", "success", "message", "isCallable", "transferables", "stack", "defineMainThreadModule", "options", "moduleFunc", "len", "arguments", "_getInitResult", "initResult", "onMainThread", "initPromise", "Promise", "all", "deps", "supportsWorkers", "supported", "window", "document", "worker", "Worker", "terminate", "process", "env", "NODE_ENV", "log", "_workerModuleId", "_messageId", "_allowInitAsString", "workers", "registeredModules", "openRequests", "defineWorkerModule", "workerId", "registrationPromise", "workerModuleData", "stringifyFunction", "callWorker", "unregister", "delete", "Set", "add", "terminateWorker", "for<PERSON>ach", "fn", "toString", "test", "getWorker", "bootstrap", "onmessage", "response", "msgId", "resolve", "reject"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/troika-worker-utils/dist/troika-worker-utils.esm.js"], "sourcesContent": ["/**\n * Main content for the worker that handles the loading and execution of\n * modules within it.\n */\nfunction workerBootstrap() {\n  var modules = Object.create(null);\n\n  // Handle messages for registering a module\n  function registerModule(ref, callback) {\n    var id = ref.id;\n    var name = ref.name;\n    var dependencies = ref.dependencies; if ( dependencies === void 0 ) dependencies = [];\n    var init = ref.init; if ( init === void 0 ) init = function(){};\n    var getTransferables = ref.getTransferables; if ( getTransferables === void 0 ) getTransferables = null;\n\n    // Only register once\n    if (modules[id]) { return }\n\n    try {\n      // If any dependencies are modules, ensure they're registered and grab their value\n      dependencies = dependencies.map(function (dep) {\n        if (dep && dep.isWorkerModule) {\n          registerModule(dep, function (depResult) {\n            if (depResult instanceof Error) { throw depResult }\n          });\n          dep = modules[dep.id].value;\n        }\n        return dep\n      });\n\n      // Rehydrate functions\n      init = rehydrate((\"<\" + name + \">.init\"), init);\n      if (getTransferables) {\n        getTransferables = rehydrate((\"<\" + name + \">.getTransferables\"), getTransferables);\n      }\n\n      // Initialize the module and store its value\n      var value = null;\n      if (typeof init === 'function') {\n        value = init.apply(void 0, dependencies);\n      } else {\n        console.error('worker module init function failed to rehydrate');\n      }\n      modules[id] = {\n        id: id,\n        value: value,\n        getTransferables: getTransferables\n      };\n      callback(value);\n    } catch(err) {\n      if (!(err && err.noLog)) {\n        console.error(err);\n      }\n      callback(err);\n    }\n  }\n\n  // Handle messages for calling a registered module's result function\n  function callModule(ref, callback) {\n    var ref$1;\n\n    var id = ref.id;\n    var args = ref.args;\n    if (!modules[id] || typeof modules[id].value !== 'function') {\n      callback(new Error((\"Worker module \" + id + \": not found or its 'init' did not return a function\")));\n    }\n    try {\n      var result = (ref$1 = modules[id]).value.apply(ref$1, args);\n      if (result && typeof result.then === 'function') {\n        result.then(handleResult, function (rej) { return callback(rej instanceof Error ? rej : new Error('' + rej)); });\n      } else {\n        handleResult(result);\n      }\n    } catch(err) {\n      callback(err);\n    }\n    function handleResult(result) {\n      try {\n        var tx = modules[id].getTransferables && modules[id].getTransferables(result);\n        if (!tx || !Array.isArray(tx) || !tx.length) {\n          tx = undefined; //postMessage is very picky about not passing null or empty transferables\n        }\n        callback(result, tx);\n      } catch(err) {\n        console.error(err);\n        callback(err);\n      }\n    }\n  }\n\n  function rehydrate(name, str) {\n    var result = void 0;\n    self.troikaDefine = function (r) { return result = r; };\n    var url = URL.createObjectURL(\n      new Blob(\n        [(\"/** \" + (name.replace(/\\*/g, '')) + \" **/\\n\\ntroikaDefine(\\n\" + str + \"\\n)\")],\n        {type: 'application/javascript'}\n      )\n    );\n    try {\n      importScripts(url);\n    } catch(err) {\n      console.error(err);\n    }\n    URL.revokeObjectURL(url);\n    delete self.troikaDefine;\n    return result\n  }\n\n  // Handler for all messages within the worker\n  self.addEventListener('message', function (e) {\n    var ref = e.data;\n    var messageId = ref.messageId;\n    var action = ref.action;\n    var data = ref.data;\n    try {\n      // Module registration\n      if (action === 'registerModule') {\n        registerModule(data, function (result) {\n          if (result instanceof Error) {\n            postMessage({\n              messageId: messageId,\n              success: false,\n              error: result.message\n            });\n          } else {\n            postMessage({\n              messageId: messageId,\n              success: true,\n              result: {isCallable: typeof result === 'function'}\n            });\n          }\n        });\n      }\n      // Invocation\n      if (action === 'callModule') {\n        callModule(data, function (result, transferables) {\n          if (result instanceof Error) {\n            postMessage({\n              messageId: messageId,\n              success: false,\n              error: result.message\n            });\n          } else {\n            postMessage({\n              messageId: messageId,\n              success: true,\n              result: result\n            }, transferables || undefined);\n          }\n        });\n      }\n    } catch(err) {\n      postMessage({\n        messageId: messageId,\n        success: false,\n        error: err.stack\n      });\n    }\n  });\n}\n\n/**\n * Fallback for `defineWorkerModule` that behaves identically but runs in the main\n * thread, for when the execution environment doesn't support web workers or they\n * are disallowed due to e.g. CSP security restrictions.\n */\nfunction defineMainThreadModule(options) {\n  var moduleFunc = function() {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    return moduleFunc._getInitResult().then(function (initResult) {\n      if (typeof initResult === 'function') {\n        return initResult.apply(void 0, args)\n      } else {\n        throw new Error('Worker module function was called but `init` did not return a callable function')\n      }\n    })\n  };\n  moduleFunc._getInitResult = function() {\n    // We can ignore getTransferables in main thread. TODO workerId?\n    var dependencies = options.dependencies;\n    var init = options.init;\n\n    // Resolve dependencies\n    dependencies = Array.isArray(dependencies) ? dependencies.map(function (dep) {\n      if (dep) {\n        // If it's a worker module, use its main thread impl\n        dep = dep.onMainThread || dep;\n        // If it's a main thread worker module, use its init return value\n        if (dep._getInitResult) {\n          dep = dep._getInitResult();\n        }\n      }\n      return dep\n    }) : [];\n\n    // Invoke init with the resolved dependencies\n    var initPromise = Promise.all(dependencies).then(function (deps) {\n      return init.apply(null, deps)\n    });\n\n    // Cache the resolved promise for subsequent calls\n    moduleFunc._getInitResult = function () { return initPromise; };\n\n    return initPromise\n  };\n  return moduleFunc\n}\n\nvar supportsWorkers = function () {\n  var supported = false;\n\n  // Only attempt worker initialization in browsers; elsewhere it would just be\n  // noise e.g. loading into a Node environment for SSR.\n  if (typeof window !== 'undefined' && typeof window.document !== 'undefined') {\n    try {\n      // TODO additional checks for things like importScripts within the worker?\n      //  Would need to be an async check.\n      var worker = new Worker(\n        URL.createObjectURL(new Blob([''], { type: 'application/javascript' }))\n      );\n      worker.terminate();\n      supported = true;\n    } catch (err) {\n      if (typeof process !== 'undefined' && process.env.NODE_ENV === 'test') ; else {\n        console.log(\n          (\"Troika createWorkerModule: web workers not allowed; falling back to main thread execution. Cause: [\" + (err.message) + \"]\")\n        );\n      }\n    }\n  }\n\n  // Cached result\n  supportsWorkers = function () { return supported; };\n  return supported\n};\n\nvar _workerModuleId = 0;\nvar _messageId = 0;\nvar _allowInitAsString = false;\nvar workers = Object.create(null);\nvar registeredModules = Object.create(null); //workerId -> Set<unregisterFn>\nvar openRequests = Object.create(null);\n\n\n/**\n * Define a module of code that will be executed with a web worker. This provides a simple\n * interface for moving chunks of logic off the main thread, and managing their dependencies\n * among one another.\n *\n * @param {object} options\n * @param {function} options.init\n * @param {array} [options.dependencies]\n * @param {function} [options.getTransferables]\n * @param {string} [options.name]\n * @param {string} [options.workerId]\n * @return {function(...[*]): {then}}\n */\nfunction defineWorkerModule(options) {\n  if ((!options || typeof options.init !== 'function') && !_allowInitAsString) {\n    throw new Error('requires `options.init` function')\n  }\n  var dependencies = options.dependencies;\n  var init = options.init;\n  var getTransferables = options.getTransferables;\n  var workerId = options.workerId;\n\n  var onMainThread = defineMainThreadModule(options);\n\n  if (workerId == null) {\n    workerId = '#default';\n  }\n  var id = \"workerModule\" + (++_workerModuleId);\n  var name = options.name || id;\n  var registrationPromise = null;\n\n  dependencies = dependencies && dependencies.map(function (dep) {\n    // Wrap raw functions as worker modules with no dependencies\n    if (typeof dep === 'function' && !dep.workerModuleData) {\n      _allowInitAsString = true;\n      dep = defineWorkerModule({\n        workerId: workerId,\n        name: (\"<\" + name + \"> function dependency: \" + (dep.name)),\n        init: (\"function(){return (\\n\" + (stringifyFunction(dep)) + \"\\n)}\")\n      });\n      _allowInitAsString = false;\n    }\n    // Grab postable data for worker modules\n    if (dep && dep.workerModuleData) {\n      dep = dep.workerModuleData;\n    }\n    return dep\n  });\n\n  function moduleFunc() {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    if (!supportsWorkers()) {\n      return onMainThread.apply(void 0, args)\n    }\n\n    // Register this module if needed\n    if (!registrationPromise) {\n      registrationPromise = callWorker(workerId,'registerModule', moduleFunc.workerModuleData);\n      var unregister = function () {\n        registrationPromise = null;\n        registeredModules[workerId].delete(unregister);\n      }\n      ;(registeredModules[workerId] || (registeredModules[workerId] = new Set())).add(unregister);\n    }\n\n    // Invoke the module, returning a promise\n    return registrationPromise.then(function (ref) {\n      var isCallable = ref.isCallable;\n\n      if (isCallable) {\n        return callWorker(workerId,'callModule', {id: id, args: args})\n      } else {\n        throw new Error('Worker module function was called but `init` did not return a callable function')\n      }\n    })\n  }\n  moduleFunc.workerModuleData = {\n    isWorkerModule: true,\n    id: id,\n    name: name,\n    dependencies: dependencies,\n    init: stringifyFunction(init),\n    getTransferables: getTransferables && stringifyFunction(getTransferables)\n  };\n\n  moduleFunc.onMainThread = onMainThread;\n\n  return moduleFunc\n}\n\n/**\n * Terminate an active Worker by a workerId that was passed to defineWorkerModule.\n * This only terminates the Worker itself; the worker module will remain available\n * and if you call it again its Worker will be respawned.\n * @param {string} workerId\n */\nfunction terminateWorker(workerId) {\n  // Unregister all modules that were registered in that worker\n  if (registeredModules[workerId]) {\n    registeredModules[workerId].forEach(function (unregister) {\n      unregister();\n    });\n  }\n  // Terminate the Worker object\n  if (workers[workerId]) {\n    workers[workerId].terminate();\n    delete workers[workerId];\n  }\n}\n\n/**\n * Stringifies a function into a form that can be deserialized in the worker\n * @param fn\n */\nfunction stringifyFunction(fn) {\n  var str = fn.toString();\n  // If it was defined in object method/property format, it needs to be modified\n  if (!/^function/.test(str) && /^\\w+\\s*\\(/.test(str)) {\n    str = 'function ' + str;\n  }\n  return str\n}\n\n\nfunction getWorker(workerId) {\n  var worker = workers[workerId];\n  if (!worker) {\n    // Bootstrap the worker's content\n    var bootstrap = stringifyFunction(workerBootstrap);\n\n    // Create the worker from the bootstrap function content\n    worker = workers[workerId] = new Worker(\n      URL.createObjectURL(\n        new Blob(\n          [(\"/** Worker Module Bootstrap: \" + (workerId.replace(/\\*/g, '')) + \" **/\\n\\n;(\" + bootstrap + \")()\")],\n          {type: 'application/javascript'}\n        )\n      )\n    );\n\n    // Single handler for response messages from the worker\n    worker.onmessage = function (e) {\n      var response = e.data;\n      var msgId = response.messageId;\n      var callback = openRequests[msgId];\n      if (!callback) {\n        throw new Error('WorkerModule response with empty or unknown messageId')\n      }\n      delete openRequests[msgId];\n      callback(response);\n    };\n  }\n  return worker\n}\n\n// Issue a call to the worker with a callback to handle the response\nfunction callWorker(workerId, action, data) {\n  return new Promise(function (resolve, reject) {\n    var messageId = ++_messageId;\n    openRequests[messageId] = function (response) {\n      if (response.success) {\n        resolve(response.result);\n      } else {\n        reject(new Error((\"Error in worker \" + action + \" call: \" + (response.error))));\n      }\n    };\n    getWorker(workerId).postMessage({\n      messageId: messageId,\n      action: action,\n      data: data\n    });\n  })\n}\n\nexport { defineWorkerModule, stringifyFunction, terminateWorker };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAeA,CAAA,EAAG;EACzB,IAAIC,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;;EAEjC;EACA,SAASC,cAAcA,CAACC,GAAG,EAAEC,QAAQ,EAAE;IACrC,IAAIC,EAAE,GAAGF,GAAG,CAACE,EAAE;IACf,IAAIC,IAAI,GAAGH,GAAG,CAACG,IAAI;IACnB,IAAIC,YAAY,GAAGJ,GAAG,CAACI,YAAY;IAAE,IAAKA,YAAY,KAAK,KAAK,CAAC,EAAGA,YAAY,GAAG,EAAE;IACrF,IAAIC,IAAI,GAAGL,GAAG,CAACK,IAAI;IAAE,IAAKA,IAAI,KAAK,KAAK,CAAC,EAAGA,IAAI,GAAG,SAAAA,CAAA,EAAU,CAAC,CAAC;IAC/D,IAAIC,gBAAgB,GAAGN,GAAG,CAACM,gBAAgB;IAAE,IAAKA,gBAAgB,KAAK,KAAK,CAAC,EAAGA,gBAAgB,GAAG,IAAI;;IAEvG;IACA,IAAIV,OAAO,CAACM,EAAE,CAAC,EAAE;MAAE;IAAO;IAE1B,IAAI;MACF;MACAE,YAAY,GAAGA,YAAY,CAACG,GAAG,CAAC,UAAUC,GAAG,EAAE;QAC7C,IAAIA,GAAG,IAAIA,GAAG,CAACC,cAAc,EAAE;UAC7BV,cAAc,CAACS,GAAG,EAAE,UAAUE,SAAS,EAAE;YACvC,IAAIA,SAAS,YAAYC,KAAK,EAAE;cAAE,MAAMD,SAAS;YAAC;UACpD,CAAC,CAAC;UACFF,GAAG,GAAGZ,OAAO,CAACY,GAAG,CAACN,EAAE,CAAC,CAACU,KAAK;QAC7B;QACA,OAAOJ,GAAG;MACZ,CAAC,CAAC;;MAEF;MACAH,IAAI,GAAGQ,SAAS,CAAE,GAAG,GAAGV,IAAI,GAAG,QAAQ,EAAGE,IAAI,CAAC;MAC/C,IAAIC,gBAAgB,EAAE;QACpBA,gBAAgB,GAAGO,SAAS,CAAE,GAAG,GAAGV,IAAI,GAAG,oBAAoB,EAAGG,gBAAgB,CAAC;MACrF;;MAEA;MACA,IAAIM,KAAK,GAAG,IAAI;MAChB,IAAI,OAAOP,IAAI,KAAK,UAAU,EAAE;QAC9BO,KAAK,GAAGP,IAAI,CAACS,KAAK,CAAC,KAAK,CAAC,EAAEV,YAAY,CAAC;MAC1C,CAAC,MAAM;QACLW,OAAO,CAACC,KAAK,CAAC,iDAAiD,CAAC;MAClE;MACApB,OAAO,CAACM,EAAE,CAAC,GAAG;QACZA,EAAE,EAAEA,EAAE;QACNU,KAAK,EAAEA,KAAK;QACZN,gBAAgB,EAAEA;MACpB,CAAC;MACDL,QAAQ,CAACW,KAAK,CAAC;IACjB,CAAC,CAAC,OAAMK,GAAG,EAAE;MACX,IAAI,EAAEA,GAAG,IAAIA,GAAG,CAACC,KAAK,CAAC,EAAE;QACvBH,OAAO,CAACC,KAAK,CAACC,GAAG,CAAC;MACpB;MACAhB,QAAQ,CAACgB,GAAG,CAAC;IACf;EACF;;EAEA;EACA,SAASE,UAAUA,CAACnB,GAAG,EAAEC,QAAQ,EAAE;IACjC,IAAImB,KAAK;IAET,IAAIlB,EAAE,GAAGF,GAAG,CAACE,EAAE;IACf,IAAImB,IAAI,GAAGrB,GAAG,CAACqB,IAAI;IACnB,IAAI,CAACzB,OAAO,CAACM,EAAE,CAAC,IAAI,OAAON,OAAO,CAACM,EAAE,CAAC,CAACU,KAAK,KAAK,UAAU,EAAE;MAC3DX,QAAQ,CAAC,IAAIU,KAAK,CAAE,gBAAgB,GAAGT,EAAE,GAAG,qDAAsD,CAAC,CAAC;IACtG;IACA,IAAI;MACF,IAAIoB,MAAM,GAAG,CAACF,KAAK,GAAGxB,OAAO,CAACM,EAAE,CAAC,EAAEU,KAAK,CAACE,KAAK,CAACM,KAAK,EAAEC,IAAI,CAAC;MAC3D,IAAIC,MAAM,IAAI,OAAOA,MAAM,CAACC,IAAI,KAAK,UAAU,EAAE;QAC/CD,MAAM,CAACC,IAAI,CAACC,YAAY,EAAE,UAAUC,GAAG,EAAE;UAAE,OAAOxB,QAAQ,CAACwB,GAAG,YAAYd,KAAK,GAAGc,GAAG,GAAG,IAAId,KAAK,CAAC,EAAE,GAAGc,GAAG,CAAC,CAAC;QAAE,CAAC,CAAC;MAClH,CAAC,MAAM;QACLD,YAAY,CAACF,MAAM,CAAC;MACtB;IACF,CAAC,CAAC,OAAML,GAAG,EAAE;MACXhB,QAAQ,CAACgB,GAAG,CAAC;IACf;IACA,SAASO,YAAYA,CAACF,MAAM,EAAE;MAC5B,IAAI;QACF,IAAII,EAAE,GAAG9B,OAAO,CAACM,EAAE,CAAC,CAACI,gBAAgB,IAAIV,OAAO,CAACM,EAAE,CAAC,CAACI,gBAAgB,CAACgB,MAAM,CAAC;QAC7E,IAAI,CAACI,EAAE,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,EAAE,CAAC,IAAI,CAACA,EAAE,CAACG,MAAM,EAAE;UAC3CH,EAAE,GAAGI,SAAS,CAAC,CAAC;QAClB;QACA7B,QAAQ,CAACqB,MAAM,EAAEI,EAAE,CAAC;MACtB,CAAC,CAAC,OAAMT,GAAG,EAAE;QACXF,OAAO,CAACC,KAAK,CAACC,GAAG,CAAC;QAClBhB,QAAQ,CAACgB,GAAG,CAAC;MACf;IACF;EACF;EAEA,SAASJ,SAASA,CAACV,IAAI,EAAE4B,GAAG,EAAE;IAC5B,IAAIT,MAAM,GAAG,KAAK,CAAC;IACnBU,IAAI,CAACC,YAAY,GAAG,UAAUC,CAAC,EAAE;MAAE,OAAOZ,MAAM,GAAGY,CAAC;IAAE,CAAC;IACvD,IAAIC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAC3B,IAAIC,IAAI,CACN,CAAE,MAAM,GAAInC,IAAI,CAACoC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAE,GAAG,yBAAyB,GAAGR,GAAG,GAAG,KAAK,CAAE,EAChF;MAACS,IAAI,EAAE;IAAwB,CACjC,CACF,CAAC;IACD,IAAI;MACFC,aAAa,CAACN,GAAG,CAAC;IACpB,CAAC,CAAC,OAAMlB,GAAG,EAAE;MACXF,OAAO,CAACC,KAAK,CAACC,GAAG,CAAC;IACpB;IACAmB,GAAG,CAACM,eAAe,CAACP,GAAG,CAAC;IACxB,OAAOH,IAAI,CAACC,YAAY;IACxB,OAAOX,MAAM;EACf;;EAEA;EACAU,IAAI,CAACW,gBAAgB,CAAC,SAAS,EAAE,UAAUC,CAAC,EAAE;IAC5C,IAAI5C,GAAG,GAAG4C,CAAC,CAACC,IAAI;IAChB,IAAIC,SAAS,GAAG9C,GAAG,CAAC8C,SAAS;IAC7B,IAAIC,MAAM,GAAG/C,GAAG,CAAC+C,MAAM;IACvB,IAAIF,IAAI,GAAG7C,GAAG,CAAC6C,IAAI;IACnB,IAAI;MACF;MACA,IAAIE,MAAM,KAAK,gBAAgB,EAAE;QAC/BhD,cAAc,CAAC8C,IAAI,EAAE,UAAUvB,MAAM,EAAE;UACrC,IAAIA,MAAM,YAAYX,KAAK,EAAE;YAC3BqC,WAAW,CAAC;cACVF,SAAS,EAAEA,SAAS;cACpBG,OAAO,EAAE,KAAK;cACdjC,KAAK,EAAEM,MAAM,CAAC4B;YAChB,CAAC,CAAC;UACJ,CAAC,MAAM;YACLF,WAAW,CAAC;cACVF,SAAS,EAAEA,SAAS;cACpBG,OAAO,EAAE,IAAI;cACb3B,MAAM,EAAE;gBAAC6B,UAAU,EAAE,OAAO7B,MAAM,KAAK;cAAU;YACnD,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;MACA;MACA,IAAIyB,MAAM,KAAK,YAAY,EAAE;QAC3B5B,UAAU,CAAC0B,IAAI,EAAE,UAAUvB,MAAM,EAAE8B,aAAa,EAAE;UAChD,IAAI9B,MAAM,YAAYX,KAAK,EAAE;YAC3BqC,WAAW,CAAC;cACVF,SAAS,EAAEA,SAAS;cACpBG,OAAO,EAAE,KAAK;cACdjC,KAAK,EAAEM,MAAM,CAAC4B;YAChB,CAAC,CAAC;UACJ,CAAC,MAAM;YACLF,WAAW,CAAC;cACVF,SAAS,EAAEA,SAAS;cACpBG,OAAO,EAAE,IAAI;cACb3B,MAAM,EAAEA;YACV,CAAC,EAAE8B,aAAa,IAAItB,SAAS,CAAC;UAChC;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAMb,GAAG,EAAE;MACX+B,WAAW,CAAC;QACVF,SAAS,EAAEA,SAAS;QACpBG,OAAO,EAAE,KAAK;QACdjC,KAAK,EAAEC,GAAG,CAACoC;MACb,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,sBAAsBA,CAACC,OAAO,EAAE;EACvC,IAAIC,UAAU,GAAG,SAAAA,CAAA,EAAW;IAC1B,IAAInC,IAAI,GAAG,EAAE;MAAEoC,GAAG,GAAGC,SAAS,CAAC7B,MAAM;IACrC,OAAQ4B,GAAG,EAAE,EAAGpC,IAAI,CAAEoC,GAAG,CAAE,GAAGC,SAAS,CAAED,GAAG,CAAE;IAE9C,OAAOD,UAAU,CAACG,cAAc,CAAC,CAAC,CAACpC,IAAI,CAAC,UAAUqC,UAAU,EAAE;MAC5D,IAAI,OAAOA,UAAU,KAAK,UAAU,EAAE;QACpC,OAAOA,UAAU,CAAC9C,KAAK,CAAC,KAAK,CAAC,EAAEO,IAAI,CAAC;MACvC,CAAC,MAAM;QACL,MAAM,IAAIV,KAAK,CAAC,iFAAiF,CAAC;MACpG;IACF,CAAC,CAAC;EACJ,CAAC;EACD6C,UAAU,CAACG,cAAc,GAAG,YAAW;IACrC;IACA,IAAIvD,YAAY,GAAGmD,OAAO,CAACnD,YAAY;IACvC,IAAIC,IAAI,GAAGkD,OAAO,CAAClD,IAAI;;IAEvB;IACAD,YAAY,GAAGuB,KAAK,CAACC,OAAO,CAACxB,YAAY,CAAC,GAAGA,YAAY,CAACG,GAAG,CAAC,UAAUC,GAAG,EAAE;MAC3E,IAAIA,GAAG,EAAE;QACP;QACAA,GAAG,GAAGA,GAAG,CAACqD,YAAY,IAAIrD,GAAG;QAC7B;QACA,IAAIA,GAAG,CAACmD,cAAc,EAAE;UACtBnD,GAAG,GAAGA,GAAG,CAACmD,cAAc,CAAC,CAAC;QAC5B;MACF;MACA,OAAOnD,GAAG;IACZ,CAAC,CAAC,GAAG,EAAE;;IAEP;IACA,IAAIsD,WAAW,GAAGC,OAAO,CAACC,GAAG,CAAC5D,YAAY,CAAC,CAACmB,IAAI,CAAC,UAAU0C,IAAI,EAAE;MAC/D,OAAO5D,IAAI,CAACS,KAAK,CAAC,IAAI,EAAEmD,IAAI,CAAC;IAC/B,CAAC,CAAC;;IAEF;IACAT,UAAU,CAACG,cAAc,GAAG,YAAY;MAAE,OAAOG,WAAW;IAAE,CAAC;IAE/D,OAAOA,WAAW;EACpB,CAAC;EACD,OAAON,UAAU;AACnB;AAEA,IAAIU,eAAe,GAAG,SAAAA,CAAA,EAAY;EAChC,IAAIC,SAAS,GAAG,KAAK;;EAErB;EACA;EACA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,WAAW,EAAE;IAC3E,IAAI;MACF;MACA;MACA,IAAIC,MAAM,GAAG,IAAIC,MAAM,CACrBnC,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;QAAEE,IAAI,EAAE;MAAyB,CAAC,CAAC,CACxE,CAAC;MACD8B,MAAM,CAACE,SAAS,CAAC,CAAC;MAClBL,SAAS,GAAG,IAAI;IAClB,CAAC,CAAC,OAAOlD,GAAG,EAAE;MACZ,IAAI,OAAOwD,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE,CAAC,KAAM;QAC5E5D,OAAO,CAAC6D,GAAG,CACR,qGAAqG,GAAI3D,GAAG,CAACiC,OAAQ,GAAG,GAC3H,CAAC;MACH;IACF;EACF;;EAEA;EACAgB,eAAe,GAAG,SAAAA,CAAA,EAAY;IAAE,OAAOC,SAAS;EAAE,CAAC;EACnD,OAAOA,SAAS;AAClB,CAAC;AAED,IAAIU,eAAe,GAAG,CAAC;AACvB,IAAIC,UAAU,GAAG,CAAC;AAClB,IAAIC,kBAAkB,GAAG,KAAK;AAC9B,IAAIC,OAAO,GAAGnF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AACjC,IAAImF,iBAAiB,GAAGpF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7C,IAAIoF,YAAY,GAAGrF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;;AAGtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqF,kBAAkBA,CAAC5B,OAAO,EAAE;EACnC,IAAI,CAAC,CAACA,OAAO,IAAI,OAAOA,OAAO,CAAClD,IAAI,KAAK,UAAU,KAAK,CAAC0E,kBAAkB,EAAE;IAC3E,MAAM,IAAIpE,KAAK,CAAC,kCAAkC,CAAC;EACrD;EACA,IAAIP,YAAY,GAAGmD,OAAO,CAACnD,YAAY;EACvC,IAAIC,IAAI,GAAGkD,OAAO,CAAClD,IAAI;EACvB,IAAIC,gBAAgB,GAAGiD,OAAO,CAACjD,gBAAgB;EAC/C,IAAI8E,QAAQ,GAAG7B,OAAO,CAAC6B,QAAQ;EAE/B,IAAIvB,YAAY,GAAGP,sBAAsB,CAACC,OAAO,CAAC;EAElD,IAAI6B,QAAQ,IAAI,IAAI,EAAE;IACpBA,QAAQ,GAAG,UAAU;EACvB;EACA,IAAIlF,EAAE,GAAG,cAAc,GAAI,EAAE2E,eAAgB;EAC7C,IAAI1E,IAAI,GAAGoD,OAAO,CAACpD,IAAI,IAAID,EAAE;EAC7B,IAAImF,mBAAmB,GAAG,IAAI;EAE9BjF,YAAY,GAAGA,YAAY,IAAIA,YAAY,CAACG,GAAG,CAAC,UAAUC,GAAG,EAAE;IAC7D;IACA,IAAI,OAAOA,GAAG,KAAK,UAAU,IAAI,CAACA,GAAG,CAAC8E,gBAAgB,EAAE;MACtDP,kBAAkB,GAAG,IAAI;MACzBvE,GAAG,GAAG2E,kBAAkB,CAAC;QACvBC,QAAQ,EAAEA,QAAQ;QAClBjF,IAAI,EAAG,GAAG,GAAGA,IAAI,GAAG,yBAAyB,GAAIK,GAAG,CAACL,IAAM;QAC3DE,IAAI,EAAG,uBAAuB,GAAIkF,iBAAiB,CAAC/E,GAAG,CAAE,GAAG;MAC9D,CAAC,CAAC;MACFuE,kBAAkB,GAAG,KAAK;IAC5B;IACA;IACA,IAAIvE,GAAG,IAAIA,GAAG,CAAC8E,gBAAgB,EAAE;MAC/B9E,GAAG,GAAGA,GAAG,CAAC8E,gBAAgB;IAC5B;IACA,OAAO9E,GAAG;EACZ,CAAC,CAAC;EAEF,SAASgD,UAAUA,CAAA,EAAG;IACpB,IAAInC,IAAI,GAAG,EAAE;MAAEoC,GAAG,GAAGC,SAAS,CAAC7B,MAAM;IACrC,OAAQ4B,GAAG,EAAE,EAAGpC,IAAI,CAAEoC,GAAG,CAAE,GAAGC,SAAS,CAAED,GAAG,CAAE;IAE9C,IAAI,CAACS,eAAe,CAAC,CAAC,EAAE;MACtB,OAAOL,YAAY,CAAC/C,KAAK,CAAC,KAAK,CAAC,EAAEO,IAAI,CAAC;IACzC;;IAEA;IACA,IAAI,CAACgE,mBAAmB,EAAE;MACxBA,mBAAmB,GAAGG,UAAU,CAACJ,QAAQ,EAAC,gBAAgB,EAAE5B,UAAU,CAAC8B,gBAAgB,CAAC;MACxF,IAAIG,UAAU,GAAG,SAAAA,CAAA,EAAY;QAC3BJ,mBAAmB,GAAG,IAAI;QAC1BJ,iBAAiB,CAACG,QAAQ,CAAC,CAACM,MAAM,CAACD,UAAU,CAAC;MAChD,CAAC;MACA,CAACR,iBAAiB,CAACG,QAAQ,CAAC,KAAKH,iBAAiB,CAACG,QAAQ,CAAC,GAAG,IAAIO,GAAG,CAAC,CAAC,CAAC,EAAEC,GAAG,CAACH,UAAU,CAAC;IAC7F;;IAEA;IACA,OAAOJ,mBAAmB,CAAC9D,IAAI,CAAC,UAAUvB,GAAG,EAAE;MAC7C,IAAImD,UAAU,GAAGnD,GAAG,CAACmD,UAAU;MAE/B,IAAIA,UAAU,EAAE;QACd,OAAOqC,UAAU,CAACJ,QAAQ,EAAC,YAAY,EAAE;UAAClF,EAAE,EAAEA,EAAE;UAAEmB,IAAI,EAAEA;QAAI,CAAC,CAAC;MAChE,CAAC,MAAM;QACL,MAAM,IAAIV,KAAK,CAAC,iFAAiF,CAAC;MACpG;IACF,CAAC,CAAC;EACJ;EACA6C,UAAU,CAAC8B,gBAAgB,GAAG;IAC5B7E,cAAc,EAAE,IAAI;IACpBP,EAAE,EAAEA,EAAE;IACNC,IAAI,EAAEA,IAAI;IACVC,YAAY,EAAEA,YAAY;IAC1BC,IAAI,EAAEkF,iBAAiB,CAAClF,IAAI,CAAC;IAC7BC,gBAAgB,EAAEA,gBAAgB,IAAIiF,iBAAiB,CAACjF,gBAAgB;EAC1E,CAAC;EAEDkD,UAAU,CAACK,YAAY,GAAGA,YAAY;EAEtC,OAAOL,UAAU;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqC,eAAeA,CAACT,QAAQ,EAAE;EACjC;EACA,IAAIH,iBAAiB,CAACG,QAAQ,CAAC,EAAE;IAC/BH,iBAAiB,CAACG,QAAQ,CAAC,CAACU,OAAO,CAAC,UAAUL,UAAU,EAAE;MACxDA,UAAU,CAAC,CAAC;IACd,CAAC,CAAC;EACJ;EACA;EACA,IAAIT,OAAO,CAACI,QAAQ,CAAC,EAAE;IACrBJ,OAAO,CAACI,QAAQ,CAAC,CAACZ,SAAS,CAAC,CAAC;IAC7B,OAAOQ,OAAO,CAACI,QAAQ,CAAC;EAC1B;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASG,iBAAiBA,CAACQ,EAAE,EAAE;EAC7B,IAAIhE,GAAG,GAAGgE,EAAE,CAACC,QAAQ,CAAC,CAAC;EACvB;EACA,IAAI,CAAC,WAAW,CAACC,IAAI,CAAClE,GAAG,CAAC,IAAI,WAAW,CAACkE,IAAI,CAAClE,GAAG,CAAC,EAAE;IACnDA,GAAG,GAAG,WAAW,GAAGA,GAAG;EACzB;EACA,OAAOA,GAAG;AACZ;AAGA,SAASmE,SAASA,CAACd,QAAQ,EAAE;EAC3B,IAAId,MAAM,GAAGU,OAAO,CAACI,QAAQ,CAAC;EAC9B,IAAI,CAACd,MAAM,EAAE;IACX;IACA,IAAI6B,SAAS,GAAGZ,iBAAiB,CAAC5F,eAAe,CAAC;;IAElD;IACA2E,MAAM,GAAGU,OAAO,CAACI,QAAQ,CAAC,GAAG,IAAIb,MAAM,CACrCnC,GAAG,CAACC,eAAe,CACjB,IAAIC,IAAI,CACN,CAAE,+BAA+B,GAAI8C,QAAQ,CAAC7C,OAAO,CAAC,KAAK,EAAE,EAAE,CAAE,GAAG,YAAY,GAAG4D,SAAS,GAAG,KAAK,CAAE,EACtG;MAAC3D,IAAI,EAAE;IAAwB,CACjC,CACF,CACF,CAAC;;IAED;IACA8B,MAAM,CAAC8B,SAAS,GAAG,UAAUxD,CAAC,EAAE;MAC9B,IAAIyD,QAAQ,GAAGzD,CAAC,CAACC,IAAI;MACrB,IAAIyD,KAAK,GAAGD,QAAQ,CAACvD,SAAS;MAC9B,IAAI7C,QAAQ,GAAGiF,YAAY,CAACoB,KAAK,CAAC;MAClC,IAAI,CAACrG,QAAQ,EAAE;QACb,MAAM,IAAIU,KAAK,CAAC,uDAAuD,CAAC;MAC1E;MACA,OAAOuE,YAAY,CAACoB,KAAK,CAAC;MAC1BrG,QAAQ,CAACoG,QAAQ,CAAC;IACpB,CAAC;EACH;EACA,OAAO/B,MAAM;AACf;;AAEA;AACA,SAASkB,UAAUA,CAACJ,QAAQ,EAAErC,MAAM,EAAEF,IAAI,EAAE;EAC1C,OAAO,IAAIkB,OAAO,CAAC,UAAUwC,OAAO,EAAEC,MAAM,EAAE;IAC5C,IAAI1D,SAAS,GAAG,EAAEgC,UAAU;IAC5BI,YAAY,CAACpC,SAAS,CAAC,GAAG,UAAUuD,QAAQ,EAAE;MAC5C,IAAIA,QAAQ,CAACpD,OAAO,EAAE;QACpBsD,OAAO,CAACF,QAAQ,CAAC/E,MAAM,CAAC;MAC1B,CAAC,MAAM;QACLkF,MAAM,CAAC,IAAI7F,KAAK,CAAE,kBAAkB,GAAGoC,MAAM,GAAG,SAAS,GAAIsD,QAAQ,CAACrF,KAAO,CAAC,CAAC;MACjF;IACF,CAAC;IACDkF,SAAS,CAACd,QAAQ,CAAC,CAACpC,WAAW,CAAC;MAC9BF,SAAS,EAAEA,SAAS;MACpBC,MAAM,EAAEA,MAAM;MACdF,IAAI,EAAEA;IACR,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,SAASsC,kBAAkB,EAAEI,iBAAiB,EAAEM,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}