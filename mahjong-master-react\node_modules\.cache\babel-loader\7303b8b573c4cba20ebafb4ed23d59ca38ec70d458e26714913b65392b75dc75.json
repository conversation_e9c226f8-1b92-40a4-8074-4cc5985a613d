{"ast": null, "code": "var _jsxFileName = \"F:\\\\= \\u795E\\u706F\\u667A\\u5E93\\\\- AI \\u521B\\u4F5C\\\\AI APP\\\\\\u795E\\u706F\\u9EBB\\u5C06\\u5927\\u5E08\\\\mahjong-master-react\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\n\n// 简化的麻将牌组件\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction MahjongTile({\n  id,\n  type,\n  value,\n  isSelected,\n  onClick\n}) {\n  const getTileColor = () => {\n    switch (type) {\n      case 'character':\n        return 'bg-red-100 border-red-300 text-red-800';\n      case 'bamboo':\n        return 'bg-green-100 border-green-300 text-green-800';\n      case 'dot':\n        return 'bg-blue-100 border-blue-300 text-blue-800';\n      case 'wind':\n        return 'bg-yellow-100 border-yellow-300 text-yellow-800';\n      case 'dragon':\n        return 'bg-purple-100 border-purple-300 text-purple-800';\n      default:\n        return 'bg-gray-100 border-gray-300 text-gray-800';\n    }\n  };\n  const handleClick = () => {\n    console.log('MahjongTile clicked:', id);\n    alert('点击了牌: ' + id + ' - 值: ' + value);\n    onClick();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n        w-12 h-16 rounded-lg border-2 cursor-pointer flex items-center justify-center font-bold text-sm\n        transition-all duration-200 hover:scale-105 hover:shadow-lg select-none\n        ${getTileColor()}\n        ${isSelected ? 'ring-4 ring-yellow-400 transform -translate-y-2' : ''}\n      `,\n    onClick: handleClick,\n    onMouseDown: handleClick,\n    onTouchStart: handleClick,\n    style: {\n      userSelect: 'none',\n      WebkitUserSelect: 'none',\n      MozUserSelect: 'none',\n      msUserSelect: 'none',\n      pointerEvents: 'auto'\n    },\n    children: value\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n}\n_c = MahjongTile;\nfunction App() {\n  _s();\n  const [showWelcome, setShowWelcome] = useState(false); // 直接进入游戏界面进行测试\n  const [selectedTiles, setSelectedTiles] = useState([]);\n\n  // 生成示例麻将牌\n  const [tiles] = useState(() => {\n    const tileTypes = ['character', 'bamboo', 'dot', 'wind', 'dragon'];\n    return Array.from({\n      length: 13\n    }, (_, i) => ({\n      id: `tile-${i}`,\n      type: tileTypes[Math.floor(Math.random() * tileTypes.length)],\n      value: Math.floor(Math.random() * 9) + 1,\n      isSelected: false\n    }));\n  });\n  const handleTileClick = tileId => {\n    console.log('点击了麻将牌:', tileId); // 添加调试信息\n    setSelectedTiles(prev => {\n      const newSelection = prev.includes(tileId) ? prev.filter(id => id !== tileId) : [...prev, tileId];\n      console.log('选中的牌:', newSelection); // 添加调试信息\n      return newSelection;\n    });\n  };\n  const handleAction = action => {\n    console.log(`执行动作: ${action}，选中的牌: ${selectedTiles}`);\n    setSelectedTiles([]);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 opacity-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full h-full bg-gradient-to-br from-yellow-500/10 to-green-500/10\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: showWelcome && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        exit: {\n          opacity: 0\n        },\n        className: \"absolute inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            scale: 0.8,\n            y: 50\n          },\n          animate: {\n            scale: 1,\n            y: 0\n          },\n          exit: {\n            scale: 0.8,\n            y: -50\n          },\n          className: \"text-center text-white\",\n          children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n            className: \"text-6xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent\",\n            animate: {\n              filter: [\"drop-shadow(0 0 20px rgba(245, 158, 11, 0.5))\", \"drop-shadow(0 0 40px rgba(245, 158, 11, 0.8))\", \"drop-shadow(0 0 20px rgba(245, 158, 11, 0.5))\"]\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity\n            },\n            children: \"\\u795E\\u706F\\u9EBB\\u5C06\\u5927\\u5E08\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            className: \"text-xl mb-8 text-gray-300\",\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              delay: 0.5\n            },\n            children: \"\\u8D85\\u8D8A\\u4F20\\u7EDF\\uFF0C\\u91CD\\u65B0\\u5B9A\\u4E49\\u9EBB\\u5C06\\u4F53\\u9A8C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            className: \"bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white font-bold py-3 px-8 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 text-lg\",\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            onClick: () => setShowWelcome(false),\n            children: \"\\u5F00\\u59CB\\u6E38\\u620F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), !showWelcome && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          y: -100\n        },\n        animate: {\n          y: 0\n        },\n        className: \"absolute top-0 left-0 right-0 z-40 bg-black bg-opacity-50 backdrop-blur-sm p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center max-w-7xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-yellow-400\",\n              children: \"\\u795E\\u706F\\u9EBB\\u5C06\\u5927\\u5E08\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-3 h-3 bg-green-500 rounded-full animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u5728\\u7EBF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-400\",\n                children: \"\\u5F53\\u524D\\u5C40\\u6570:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-yellow-400 font-bold\",\n                children: \"\\u7B2C1\\u5C40\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-400\",\n                children: \"\\u98CE\\u5708:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-green-400 font-bold\",\n                children: \"\\u4E1C\\u98CE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center justify-center h-screen pt-20 pb-20\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          className: \"bg-green-800 rounded-3xl p-8 shadow-2xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-white text-xl font-bold mb-6 text-center\",\n            children: \"\\u60A8\\u7684\\u624B\\u724C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => alert('测试按钮工作正常！'),\n              className: \"bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600\",\n              children: \"\\u6D4B\\u8BD5\\u70B9\\u51FB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2 flex-wrap justify-center\",\n            children: tiles.map(tile => /*#__PURE__*/_jsxDEV(MahjongTile, {\n              id: tile.id,\n              type: tile.type,\n              value: tile.value,\n              isSelected: selectedTiles.includes(tile.id),\n              onClick: () => handleTileClick(tile.id)\n            }, tile.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: selectedTiles.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            y: 100,\n            opacity: 0\n          },\n          animate: {\n            y: 0,\n            opacity: 1\n          },\n          exit: {\n            y: 100,\n            opacity: 0\n          },\n          className: \"absolute bottom-4 left-1/2 transform -translate-x-1/2 z-40\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-black bg-opacity-70 backdrop-blur-sm rounded-2xl p-4 flex space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              className: \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => handleAction('出牌'),\n              children: \"\\u51FA\\u724C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              className: \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => handleAction('吃'),\n              children: \"\\u5403\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              className: \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => handleAction('碰'),\n              children: \"\\u78B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              className: \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => handleAction('杠'),\n              children: \"\\u6760\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              className: \"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-bold py-2 px-6 rounded-lg shadow-lg transform hover:scale-105 transition-all duration-150\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => handleAction('胡牌'),\n              children: \"\\u80E1\\u724C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"r8uTuIMEPIPsU7gw/B1n7DzHFVc=\");\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"MahjongTile\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MahjongTile", "id", "type", "value", "isSelected", "onClick", "getTileColor", "handleClick", "console", "log", "alert", "className", "onMouseDown", "onTouchStart", "style", "userSelect", "WebkitUserSelect", "MozUserSelect", "msUserSelect", "pointerEvents", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "App", "_s", "showWelcome", "setShowWelcome", "selectedTiles", "setSelectedTiles", "tiles", "tileTypes", "Array", "from", "length", "_", "i", "Math", "floor", "random", "handleTileClick", "tileId", "prev", "newSelection", "includes", "filter", "handleAction", "action", "AnimatePresence", "motion", "div", "initial", "opacity", "animate", "exit", "scale", "y", "h1", "transition", "duration", "repeat", "Infinity", "p", "delay", "button", "whileHover", "whileTap", "map", "tile", "_c2", "$RefreshReg$"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\n// 简化的麻将牌组件\ninterface MahjongTileProps {\n  id: string;\n  type: string;\n  value: string | number;\n  isSelected: boolean;\n  onClick: () => void;\n}\n\nfunction MahjongTile({ id, type, value, isSelected, onClick }: MahjongTileProps) {\n  const getTileColor = () => {\n    switch (type) {\n      case 'character': return 'bg-red-100 border-red-300 text-red-800';\n      case 'bamboo': return 'bg-green-100 border-green-300 text-green-800';\n      case 'dot': return 'bg-blue-100 border-blue-300 text-blue-800';\n      case 'wind': return 'bg-yellow-100 border-yellow-300 text-yellow-800';\n      case 'dragon': return 'bg-purple-100 border-purple-300 text-purple-800';\n      default: return 'bg-gray-100 border-gray-300 text-gray-800';\n    }\n  };\n\n  const handleClick = () => {\n    console.log('MahjongTile clicked:', id);\n    alert('点击了牌: ' + id + ' - 值: ' + value);\n    onClick();\n  };\n\n  return (\n    <div\n      className={`\n        w-12 h-16 rounded-lg border-2 cursor-pointer flex items-center justify-center font-bold text-sm\n        transition-all duration-200 hover:scale-105 hover:shadow-lg select-none\n        ${getTileColor()}\n        ${isSelected ? 'ring-4 ring-yellow-400 transform -translate-y-2' : ''}\n      `}\n      onClick={handleClick}\n      onMouseDown={handleClick}\n      onTouchStart={handleClick}\n      style={{\n        userSelect: 'none',\n        WebkitUserSelect: 'none',\n        MozUserSelect: 'none',\n        msUserSelect: 'none',\n        pointerEvents: 'auto'\n      }}\n    >\n      {value}\n    </div>\n  );\n}\n\nfunction App() {\n  const [showWelcome, setShowWelcome] = useState(false); // 直接进入游戏界面进行测试\n  const [selectedTiles, setSelectedTiles] = useState<string[]>([]);\n\n  // 生成示例麻将牌\n  const [tiles] = useState(() => {\n    const tileTypes = ['character', 'bamboo', 'dot', 'wind', 'dragon'];\n    return Array.from({ length: 13 }, (_, i) => ({\n      id: `tile-${i}`,\n      type: tileTypes[Math.floor(Math.random() * tileTypes.length)],\n      value: Math.floor(Math.random() * 9) + 1,\n      isSelected: false\n    }));\n  });\n\n  const handleTileClick = (tileId: string) => {\n    console.log('点击了麻将牌:', tileId); // 添加调试信息\n    setSelectedTiles(prev => {\n      const newSelection = prev.includes(tileId)\n        ? prev.filter(id => id !== tileId)\n        : [...prev, tileId];\n      console.log('选中的牌:', newSelection); // 添加调试信息\n      return newSelection;\n    });\n  };\n\n  const handleAction = (action: string) => {\n    console.log(`执行动作: ${action}，选中的牌: ${selectedTiles}`);\n    setSelectedTiles([]);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden\">\n      {/* 背景装饰 */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"w-full h-full bg-gradient-to-br from-yellow-500/10 to-green-500/10\"></div>\n      </div>\n\n      {/* 欢迎界面 */}\n      <AnimatePresence>\n        {showWelcome && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"absolute inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80\"\n          >\n            <motion.div\n              initial={{ scale: 0.8, y: 50 }}\n              animate={{ scale: 1, y: 0 }}\n              exit={{ scale: 0.8, y: -50 }}\n              className=\"text-center text-white\"\n            >\n              <motion.h1\n                className=\"text-6xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent\"\n                animate={{\n                  filter: [\n                    \"drop-shadow(0 0 20px rgba(245, 158, 11, 0.5))\",\n                    \"drop-shadow(0 0 40px rgba(245, 158, 11, 0.8))\",\n                    \"drop-shadow(0 0 20px rgba(245, 158, 11, 0.5))\"\n                  ]\n                }}\n                transition={{ duration: 2, repeat: Infinity }}\n              >\n                神灯麻将大师\n              </motion.h1>\n              <motion.p\n                className=\"text-xl mb-8 text-gray-300\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.5 }}\n              >\n                超越传统，重新定义麻将体验\n              </motion.p>\n              <motion.button\n                className=\"bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white font-bold py-3 px-8 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 text-lg\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => setShowWelcome(false)}\n              >\n                开始游戏\n              </motion.button>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* 主游戏界面 */}\n      {!showWelcome && (\n        <>\n          {/* 顶部状态栏 */}\n          <motion.div\n            initial={{ y: -100 }}\n            animate={{ y: 0 }}\n            className=\"absolute top-0 left-0 right-0 z-40 bg-black bg-opacity-50 backdrop-blur-sm p-4\"\n          >\n            <div className=\"flex justify-between items-center max-w-7xl mx-auto\">\n              <div className=\"flex items-center space-x-4\">\n                <h2 className=\"text-2xl font-bold text-yellow-400\">神灯麻将大师</h2>\n                <div className=\"flex items-center space-x-2 text-white\">\n                  <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\n                  <span>在线</span>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"text-white\">\n                  <span className=\"text-gray-400\">当前局数:</span>\n                  <span className=\"ml-2 text-yellow-400 font-bold\">第1局</span>\n                </div>\n                <div className=\"text-white\">\n                  <span className=\"text-gray-400\">风圈:</span>\n                  <span className=\"ml-2 text-green-400 font-bold\">东风</span>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* 游戏区域 */}\n          <div className=\"flex flex-col items-center justify-center h-screen pt-20 pb-20\">\n            <motion.div\n              initial={{ opacity: 0, y: 50 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"bg-green-800 rounded-3xl p-8 shadow-2xl\"\n            >\n              <h3 className=\"text-white text-xl font-bold mb-6 text-center\">您的手牌</h3>\n\n              {/* 测试按钮 */}\n              <div className=\"mb-4 text-center\">\n                <button\n                  onClick={() => alert('测试按钮工作正常！')}\n                  className=\"bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600\"\n                >\n                  测试点击\n                </button>\n              </div>\n\n              <div className=\"flex gap-2 flex-wrap justify-center\">\n                {tiles.map((tile) => (\n                  <MahjongTile\n                    key={tile.id}\n                    id={tile.id}\n                    type={tile.type}\n                    value={tile.value}\n                    isSelected={selectedTiles.includes(tile.id)}\n                    onClick={() => handleTileClick(tile.id)}\n                  />\n                ))}\n              </div>\n            </motion.div>\n          </div>\n\n          {/* 底部操作栏 */}\n          <AnimatePresence>\n            {selectedTiles.length > 0 && (\n              <motion.div\n                initial={{ y: 100, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                exit={{ y: 100, opacity: 0 }}\n                className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 z-40\"\n              >\n                <div className=\"bg-black bg-opacity-70 backdrop-blur-sm rounded-2xl p-4 flex space-x-3\">\n                  <motion.button\n                    className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => handleAction('出牌')}\n                  >\n                    出牌\n                  </motion.button>\n                  <motion.button\n                    className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => handleAction('吃')}\n                  >\n                    吃\n                  </motion.button>\n                  <motion.button\n                    className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => handleAction('碰')}\n                  >\n                    碰\n                  </motion.button>\n                  <motion.button\n                    className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => handleAction('杠')}\n                  >\n                    杠\n                  </motion.button>\n                  <motion.button\n                    className=\"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-bold py-2 px-6 rounded-lg shadow-lg transform hover:scale-105 transition-all duration-150\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => handleAction('胡牌')}\n                  >\n                    胡牌\n                  </motion.button>\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </>\n      )}\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,SAASC,WAAWA,CAAC;EAAEC,EAAE;EAAEC,IAAI;EAAEC,KAAK;EAAEC,UAAU;EAAEC;AAA0B,CAAC,EAAE;EAC/E,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,QAAQJ,IAAI;MACV,KAAK,WAAW;QAAE,OAAO,wCAAwC;MACjE,KAAK,QAAQ;QAAE,OAAO,8CAA8C;MACpE,KAAK,KAAK;QAAE,OAAO,2CAA2C;MAC9D,KAAK,MAAM;QAAE,OAAO,iDAAiD;MACrE,KAAK,QAAQ;QAAE,OAAO,iDAAiD;MACvE;QAAS,OAAO,2CAA2C;IAC7D;EACF,CAAC;EAED,MAAMK,WAAW,GAAGA,CAAA,KAAM;IACxBC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAER,EAAE,CAAC;IACvCS,KAAK,CAAC,QAAQ,GAAGT,EAAE,GAAG,QAAQ,GAAGE,KAAK,CAAC;IACvCE,OAAO,CAAC,CAAC;EACX,CAAC;EAED,oBACER,OAAA;IACEc,SAAS,EAAE;AACjB;AACA;AACA,UAAUL,YAAY,CAAC,CAAC;AACxB,UAAUF,UAAU,GAAG,iDAAiD,GAAG,EAAE;AAC7E,OAAQ;IACFC,OAAO,EAAEE,WAAY;IACrBK,WAAW,EAAEL,WAAY;IACzBM,YAAY,EAAEN,WAAY;IAC1BO,KAAK,EAAE;MACLC,UAAU,EAAE,MAAM;MAClBC,gBAAgB,EAAE,MAAM;MACxBC,aAAa,EAAE,MAAM;MACrBC,YAAY,EAAE,MAAM;MACpBC,aAAa,EAAE;IACjB,CAAE;IAAAC,QAAA,EAEDjB;EAAK;IAAAkB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACC,EAAA,GAxCQzB,WAAW;AA0CpB,SAAS0B,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACvD,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAW,EAAE,CAAC;;EAEhE;EACA,MAAM,CAACqC,KAAK,CAAC,GAAGrC,QAAQ,CAAC,MAAM;IAC7B,MAAMsC,SAAS,GAAG,CAAC,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC;IAClE,OAAOC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAG,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,MAAM;MAC3CrC,EAAE,EAAE,QAAQqC,CAAC,EAAE;MACfpC,IAAI,EAAE+B,SAAS,CAACM,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGR,SAAS,CAACG,MAAM,CAAC,CAAC;MAC7DjC,KAAK,EAAEoC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACxCrC,UAAU,EAAE;IACd,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EAEF,MAAMsC,eAAe,GAAIC,MAAc,IAAK;IAC1CnC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEkC,MAAM,CAAC,CAAC,CAAC;IAChCZ,gBAAgB,CAACa,IAAI,IAAI;MACvB,MAAMC,YAAY,GAAGD,IAAI,CAACE,QAAQ,CAACH,MAAM,CAAC,GACtCC,IAAI,CAACG,MAAM,CAAC9C,EAAE,IAAIA,EAAE,KAAK0C,MAAM,CAAC,GAChC,CAAC,GAAGC,IAAI,EAAED,MAAM,CAAC;MACrBnC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEoC,YAAY,CAAC,CAAC,CAAC;MACpC,OAAOA,YAAY;IACrB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,YAAY,GAAIC,MAAc,IAAK;IACvCzC,OAAO,CAACC,GAAG,CAAC,SAASwC,MAAM,UAAUnB,aAAa,EAAE,CAAC;IACrDC,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,oBACElC,OAAA;IAAKc,SAAS,EAAC,mGAAmG;IAAAS,QAAA,gBAEhHvB,OAAA;MAAKc,SAAS,EAAC,6BAA6B;MAAAS,QAAA,eAC1CvB,OAAA;QAAKc,SAAS,EAAC;MAAoE;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvF,CAAC,eAGN3B,OAAA,CAACqD,eAAe;MAAA9B,QAAA,EACbQ,WAAW,iBACV/B,OAAA,CAACsD,MAAM,CAACC,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBE,IAAI,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACrB3C,SAAS,EAAC,+EAA+E;QAAAS,QAAA,eAEzFvB,OAAA,CAACsD,MAAM,CAACC,GAAG;UACTC,OAAO,EAAE;YAAEI,KAAK,EAAE,GAAG;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BH,OAAO,EAAE;YAAEE,KAAK,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC5BF,IAAI,EAAE;YAAEC,KAAK,EAAE,GAAG;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7B/C,SAAS,EAAC,wBAAwB;UAAAS,QAAA,gBAElCvB,OAAA,CAACsD,MAAM,CAACQ,EAAE;YACRhD,SAAS,EAAC,sGAAsG;YAChH4C,OAAO,EAAE;cACPR,MAAM,EAAE,CACN,+CAA+C,EAC/C,+CAA+C,EAC/C,+CAA+C;YAEnD,CAAE;YACFa,UAAU,EAAE;cAAEC,QAAQ,EAAE,CAAC;cAAEC,MAAM,EAAEC;YAAS,CAAE;YAAA3C,QAAA,EAC/C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACZ3B,OAAA,CAACsD,MAAM,CAACa,CAAC;YACPrD,SAAS,EAAC,4BAA4B;YACtC0C,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBC,OAAO,EAAE;cAAED,OAAO,EAAE;YAAE,CAAE;YACxBM,UAAU,EAAE;cAAEK,KAAK,EAAE;YAAI,CAAE;YAAA7C,QAAA,EAC5B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACX3B,OAAA,CAACsD,MAAM,CAACe,MAAM;YACZvD,SAAS,EAAC,4NAA4N;YACtOwD,UAAU,EAAE;cAAEV,KAAK,EAAE;YAAK,CAAE;YAC5BW,QAAQ,EAAE;cAAEX,KAAK,EAAE;YAAK,CAAE;YAC1BpD,OAAO,EAAEA,CAAA,KAAMwB,cAAc,CAAC,KAAK,CAAE;YAAAT,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,EAGjB,CAACI,WAAW,iBACX/B,OAAA,CAAAE,SAAA;MAAAqB,QAAA,gBAEEvB,OAAA,CAACsD,MAAM,CAACC,GAAG;QACTC,OAAO,EAAE;UAAEK,CAAC,EAAE,CAAC;QAAI,CAAE;QACrBH,OAAO,EAAE;UAAEG,CAAC,EAAE;QAAE,CAAE;QAClB/C,SAAS,EAAC,gFAAgF;QAAAS,QAAA,eAE1FvB,OAAA;UAAKc,SAAS,EAAC,qDAAqD;UAAAS,QAAA,gBAClEvB,OAAA;YAAKc,SAAS,EAAC,6BAA6B;YAAAS,QAAA,gBAC1CvB,OAAA;cAAIc,SAAS,EAAC,oCAAoC;cAAAS,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9D3B,OAAA;cAAKc,SAAS,EAAC,wCAAwC;cAAAS,QAAA,gBACrDvB,OAAA;gBAAKc,SAAS,EAAC;cAAiD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvE3B,OAAA;gBAAAuB,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3B,OAAA;YAAKc,SAAS,EAAC,6BAA6B;YAAAS,QAAA,gBAC1CvB,OAAA;cAAKc,SAAS,EAAC,YAAY;cAAAS,QAAA,gBACzBvB,OAAA;gBAAMc,SAAS,EAAC,eAAe;gBAAAS,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5C3B,OAAA;gBAAMc,SAAS,EAAC,gCAAgC;gBAAAS,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACN3B,OAAA;cAAKc,SAAS,EAAC,YAAY;cAAAS,QAAA,gBACzBvB,OAAA;gBAAMc,SAAS,EAAC,eAAe;gBAAAS,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1C3B,OAAA;gBAAMc,SAAS,EAAC,+BAA+B;gBAAAS,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb3B,OAAA;QAAKc,SAAS,EAAC,gEAAgE;QAAAS,QAAA,eAC7EvB,OAAA,CAACsD,MAAM,CAACC,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEI,CAAC,EAAE;UAAG,CAAE;UAC/BH,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEI,CAAC,EAAE;UAAE,CAAE;UAC9B/C,SAAS,EAAC,yCAAyC;UAAAS,QAAA,gBAEnDvB,OAAA;YAAIc,SAAS,EAAC,+CAA+C;YAAAS,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGvE3B,OAAA;YAAKc,SAAS,EAAC,kBAAkB;YAAAS,QAAA,eAC/BvB,OAAA;cACEQ,OAAO,EAAEA,CAAA,KAAMK,KAAK,CAAC,WAAW,CAAE;cAClCC,SAAS,EAAC,0DAA0D;cAAAS,QAAA,EACrE;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN3B,OAAA;YAAKc,SAAS,EAAC,qCAAqC;YAAAS,QAAA,EACjDY,KAAK,CAACqC,GAAG,CAAEC,IAAI,iBACdzE,OAAA,CAACG,WAAW;cAEVC,EAAE,EAAEqE,IAAI,CAACrE,EAAG;cACZC,IAAI,EAAEoE,IAAI,CAACpE,IAAK;cAChBC,KAAK,EAAEmE,IAAI,CAACnE,KAAM;cAClBC,UAAU,EAAE0B,aAAa,CAACgB,QAAQ,CAACwB,IAAI,CAACrE,EAAE,CAAE;cAC5CI,OAAO,EAAEA,CAAA,KAAMqC,eAAe,CAAC4B,IAAI,CAACrE,EAAE;YAAE,GALnCqE,IAAI,CAACrE,EAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMb,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN3B,OAAA,CAACqD,eAAe;QAAA9B,QAAA,EACbU,aAAa,CAACM,MAAM,GAAG,CAAC,iBACvBvC,OAAA,CAACsD,MAAM,CAACC,GAAG;UACTC,OAAO,EAAE;YAAEK,CAAC,EAAE,GAAG;YAAEJ,OAAO,EAAE;UAAE,CAAE;UAChCC,OAAO,EAAE;YAAEG,CAAC,EAAE,CAAC;YAAEJ,OAAO,EAAE;UAAE,CAAE;UAC9BE,IAAI,EAAE;YAAEE,CAAC,EAAE,GAAG;YAAEJ,OAAO,EAAE;UAAE,CAAE;UAC7B3C,SAAS,EAAC,4DAA4D;UAAAS,QAAA,eAEtEvB,OAAA;YAAKc,SAAS,EAAC,wEAAwE;YAAAS,QAAA,gBACrFvB,OAAA,CAACsD,MAAM,CAACe,MAAM;cACZvD,SAAS,EAAC,oNAAoN;cAC9NwD,UAAU,EAAE;gBAAEV,KAAK,EAAE;cAAK,CAAE;cAC5BW,QAAQ,EAAE;gBAAEX,KAAK,EAAE;cAAK,CAAE;cAC1BpD,OAAO,EAAEA,CAAA,KAAM2C,YAAY,CAAC,IAAI,CAAE;cAAA5B,QAAA,EACnC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChB3B,OAAA,CAACsD,MAAM,CAACe,MAAM;cACZvD,SAAS,EAAC,oNAAoN;cAC9NwD,UAAU,EAAE;gBAAEV,KAAK,EAAE;cAAK,CAAE;cAC5BW,QAAQ,EAAE;gBAAEX,KAAK,EAAE;cAAK,CAAE;cAC1BpD,OAAO,EAAEA,CAAA,KAAM2C,YAAY,CAAC,GAAG,CAAE;cAAA5B,QAAA,EAClC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChB3B,OAAA,CAACsD,MAAM,CAACe,MAAM;cACZvD,SAAS,EAAC,oNAAoN;cAC9NwD,UAAU,EAAE;gBAAEV,KAAK,EAAE;cAAK,CAAE;cAC5BW,QAAQ,EAAE;gBAAEX,KAAK,EAAE;cAAK,CAAE;cAC1BpD,OAAO,EAAEA,CAAA,KAAM2C,YAAY,CAAC,GAAG,CAAE;cAAA5B,QAAA,EAClC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChB3B,OAAA,CAACsD,MAAM,CAACe,MAAM;cACZvD,SAAS,EAAC,oNAAoN;cAC9NwD,UAAU,EAAE;gBAAEV,KAAK,EAAE;cAAK,CAAE;cAC5BW,QAAQ,EAAE;gBAAEX,KAAK,EAAE;cAAK,CAAE;cAC1BpD,OAAO,EAAEA,CAAA,KAAM2C,YAAY,CAAC,GAAG,CAAE;cAAA5B,QAAA,EAClC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChB3B,OAAA,CAACsD,MAAM,CAACe,MAAM;cACZvD,SAAS,EAAC,wLAAwL;cAClMwD,UAAU,EAAE;gBAAEV,KAAK,EAAE;cAAK,CAAE;cAC5BW,QAAQ,EAAE;gBAAEX,KAAK,EAAE;cAAK,CAAE;cAC1BpD,OAAO,EAAEA,CAAA,KAAM2C,YAAY,CAAC,IAAI,CAAE;cAAA5B,QAAA,EACnC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC;IAAA,eAClB,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACG,EAAA,CAlNQD,GAAG;AAAA6C,GAAA,GAAH7C,GAAG;AAoNZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAA8C,GAAA;AAAAC,YAAA,CAAA/C,EAAA;AAAA+C,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}