{"ast": null, "code": "export default function potpack(boxes) {\n  // calculate total box area and maximum box width\n  let area = 0;\n  let maxWidth = 0;\n  for (const box of boxes) {\n    area += box.w * box.h;\n    maxWidth = Math.max(maxWidth, box.w);\n  }\n\n  // sort the boxes for insertion by height, descending\n  boxes.sort((a, b) => b.h - a.h);\n\n  // aim for a squarish resulting container,\n  // slightly adjusted for sub-100% space utilization\n  const startWidth = Math.max(Math.ceil(Math.sqrt(area / 0.95)), maxWidth);\n\n  // start with a single empty space, unbounded at the bottom\n  const spaces = [{\n    x: 0,\n    y: 0,\n    w: startWidth,\n    h: Infinity\n  }];\n  let width = 0;\n  let height = 0;\n  for (const box of boxes) {\n    // look through spaces backwards so that we check smaller spaces first\n    for (let i = spaces.length - 1; i >= 0; i--) {\n      const space = spaces[i];\n\n      // look for empty spaces that can accommodate the current box\n      if (box.w > space.w || box.h > space.h) continue;\n\n      // found the space; add the box to its top-left corner\n      // |-------|-------|\n      // |  box  |       |\n      // |_______|       |\n      // |         space |\n      // |_______________|\n      box.x = space.x;\n      box.y = space.y;\n      height = Math.max(height, box.y + box.h);\n      width = Math.max(width, box.x + box.w);\n      if (box.w === space.w && box.h === space.h) {\n        // space matches the box exactly; remove it\n        const last = spaces.pop();\n        if (i < spaces.length) spaces[i] = last;\n      } else if (box.h === space.h) {\n        // space matches the box height; update it accordingly\n        // |-------|---------------|\n        // |  box  | updated space |\n        // |_______|_______________|\n        space.x += box.w;\n        space.w -= box.w;\n      } else if (box.w === space.w) {\n        // space matches the box width; update it accordingly\n        // |---------------|\n        // |      box      |\n        // |_______________|\n        // | updated space |\n        // |_______________|\n        space.y += box.h;\n        space.h -= box.h;\n      } else {\n        // otherwise the box splits the space into two spaces\n        // |-------|-----------|\n        // |  box  | new space |\n        // |_______|___________|\n        // | updated space     |\n        // |___________________|\n        spaces.push({\n          x: space.x + box.w,\n          y: space.y,\n          w: space.w - box.w,\n          h: box.h\n        });\n        space.y += box.h;\n        space.h -= box.h;\n      }\n      break;\n    }\n  }\n  return {\n    w: width,\n    // container width\n    h: height,\n    // container height\n    fill: area / (width * height) || 0 // space utilization\n  };\n}", "map": {"version": 3, "names": ["potpack", "boxes", "area", "max<PERSON><PERSON><PERSON>", "box", "w", "h", "Math", "max", "sort", "a", "b", "startWidth", "ceil", "sqrt", "spaces", "x", "y", "Infinity", "width", "height", "i", "length", "space", "last", "pop", "push", "fill"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/potpack/index.mjs"], "sourcesContent": ["\nexport default function potpack(boxes) {\n\n    // calculate total box area and maximum box width\n    let area = 0;\n    let maxWidth = 0;\n\n    for (const box of boxes) {\n        area += box.w * box.h;\n        maxWidth = Math.max(maxWidth, box.w);\n    }\n\n    // sort the boxes for insertion by height, descending\n    boxes.sort((a, b) => b.h - a.h);\n\n    // aim for a squarish resulting container,\n    // slightly adjusted for sub-100% space utilization\n    const startWidth = Math.max(Math.ceil(Math.sqrt(area / 0.95)), maxWidth);\n\n    // start with a single empty space, unbounded at the bottom\n    const spaces = [{x: 0, y: 0, w: startWidth, h: Infinity}];\n\n    let width = 0;\n    let height = 0;\n\n    for (const box of boxes) {\n        // look through spaces backwards so that we check smaller spaces first\n        for (let i = spaces.length - 1; i >= 0; i--) {\n            const space = spaces[i];\n\n            // look for empty spaces that can accommodate the current box\n            if (box.w > space.w || box.h > space.h) continue;\n\n            // found the space; add the box to its top-left corner\n            // |-------|-------|\n            // |  box  |       |\n            // |_______|       |\n            // |         space |\n            // |_______________|\n            box.x = space.x;\n            box.y = space.y;\n\n            height = Math.max(height, box.y + box.h);\n            width = Math.max(width, box.x + box.w);\n\n            if (box.w === space.w && box.h === space.h) {\n                // space matches the box exactly; remove it\n                const last = spaces.pop();\n                if (i < spaces.length) spaces[i] = last;\n\n            } else if (box.h === space.h) {\n                // space matches the box height; update it accordingly\n                // |-------|---------------|\n                // |  box  | updated space |\n                // |_______|_______________|\n                space.x += box.w;\n                space.w -= box.w;\n\n            } else if (box.w === space.w) {\n                // space matches the box width; update it accordingly\n                // |---------------|\n                // |      box      |\n                // |_______________|\n                // | updated space |\n                // |_______________|\n                space.y += box.h;\n                space.h -= box.h;\n\n            } else {\n                // otherwise the box splits the space into two spaces\n                // |-------|-----------|\n                // |  box  | new space |\n                // |_______|___________|\n                // | updated space     |\n                // |___________________|\n                spaces.push({\n                    x: space.x + box.w,\n                    y: space.y,\n                    w: space.w - box.w,\n                    h: box.h\n                });\n                space.y += box.h;\n                space.h -= box.h;\n            }\n            break;\n        }\n    }\n\n    return {\n        w: width, // container width\n        h: height, // container height\n        fill: (area / (width * height)) || 0 // space utilization\n    };\n}\n"], "mappings": "AACA,eAAe,SAASA,OAAOA,CAACC,KAAK,EAAE;EAEnC;EACA,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIC,QAAQ,GAAG,CAAC;EAEhB,KAAK,MAAMC,GAAG,IAAIH,KAAK,EAAE;IACrBC,IAAI,IAAIE,GAAG,CAACC,CAAC,GAAGD,GAAG,CAACE,CAAC;IACrBH,QAAQ,GAAGI,IAAI,CAACC,GAAG,CAACL,QAAQ,EAAEC,GAAG,CAACC,CAAC,CAAC;EACxC;;EAEA;EACAJ,KAAK,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACL,CAAC,GAAGI,CAAC,CAACJ,CAAC,CAAC;;EAE/B;EACA;EACA,MAAMM,UAAU,GAAGL,IAAI,CAACC,GAAG,CAACD,IAAI,CAACM,IAAI,CAACN,IAAI,CAACO,IAAI,CAACZ,IAAI,GAAG,IAAI,CAAC,CAAC,EAAEC,QAAQ,CAAC;;EAExE;EACA,MAAMY,MAAM,GAAG,CAAC;IAACC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEZ,CAAC,EAAEO,UAAU;IAAEN,CAAC,EAAEY;EAAQ,CAAC,CAAC;EAEzD,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,MAAM,GAAG,CAAC;EAEd,KAAK,MAAMhB,GAAG,IAAIH,KAAK,EAAE;IACrB;IACA,KAAK,IAAIoB,CAAC,GAAGN,MAAM,CAACO,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACzC,MAAME,KAAK,GAAGR,MAAM,CAACM,CAAC,CAAC;;MAEvB;MACA,IAAIjB,GAAG,CAACC,CAAC,GAAGkB,KAAK,CAAClB,CAAC,IAAID,GAAG,CAACE,CAAC,GAAGiB,KAAK,CAACjB,CAAC,EAAE;;MAExC;MACA;MACA;MACA;MACA;MACA;MACAF,GAAG,CAACY,CAAC,GAAGO,KAAK,CAACP,CAAC;MACfZ,GAAG,CAACa,CAAC,GAAGM,KAAK,CAACN,CAAC;MAEfG,MAAM,GAAGb,IAAI,CAACC,GAAG,CAACY,MAAM,EAAEhB,GAAG,CAACa,CAAC,GAAGb,GAAG,CAACE,CAAC,CAAC;MACxCa,KAAK,GAAGZ,IAAI,CAACC,GAAG,CAACW,KAAK,EAAEf,GAAG,CAACY,CAAC,GAAGZ,GAAG,CAACC,CAAC,CAAC;MAEtC,IAAID,GAAG,CAACC,CAAC,KAAKkB,KAAK,CAAClB,CAAC,IAAID,GAAG,CAACE,CAAC,KAAKiB,KAAK,CAACjB,CAAC,EAAE;QACxC;QACA,MAAMkB,IAAI,GAAGT,MAAM,CAACU,GAAG,CAAC,CAAC;QACzB,IAAIJ,CAAC,GAAGN,MAAM,CAACO,MAAM,EAAEP,MAAM,CAACM,CAAC,CAAC,GAAGG,IAAI;MAE3C,CAAC,MAAM,IAAIpB,GAAG,CAACE,CAAC,KAAKiB,KAAK,CAACjB,CAAC,EAAE;QAC1B;QACA;QACA;QACA;QACAiB,KAAK,CAACP,CAAC,IAAIZ,GAAG,CAACC,CAAC;QAChBkB,KAAK,CAAClB,CAAC,IAAID,GAAG,CAACC,CAAC;MAEpB,CAAC,MAAM,IAAID,GAAG,CAACC,CAAC,KAAKkB,KAAK,CAAClB,CAAC,EAAE;QAC1B;QACA;QACA;QACA;QACA;QACA;QACAkB,KAAK,CAACN,CAAC,IAAIb,GAAG,CAACE,CAAC;QAChBiB,KAAK,CAACjB,CAAC,IAAIF,GAAG,CAACE,CAAC;MAEpB,CAAC,MAAM;QACH;QACA;QACA;QACA;QACA;QACA;QACAS,MAAM,CAACW,IAAI,CAAC;UACRV,CAAC,EAAEO,KAAK,CAACP,CAAC,GAAGZ,GAAG,CAACC,CAAC;UAClBY,CAAC,EAAEM,KAAK,CAACN,CAAC;UACVZ,CAAC,EAAEkB,KAAK,CAAClB,CAAC,GAAGD,GAAG,CAACC,CAAC;UAClBC,CAAC,EAAEF,GAAG,CAACE;QACX,CAAC,CAAC;QACFiB,KAAK,CAACN,CAAC,IAAIb,GAAG,CAACE,CAAC;QAChBiB,KAAK,CAACjB,CAAC,IAAIF,GAAG,CAACE,CAAC;MACpB;MACA;IACJ;EACJ;EAEA,OAAO;IACHD,CAAC,EAAEc,KAAK;IAAE;IACVb,CAAC,EAAEc,MAAM;IAAE;IACXO,IAAI,EAAGzB,IAAI,IAAIiB,KAAK,GAAGC,MAAM,CAAC,IAAK,CAAC,CAAC;EACzC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}