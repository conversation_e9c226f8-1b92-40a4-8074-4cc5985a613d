{"ast": null, "code": "import { Loader, CanvasTexture, NearestFilter, FileLoader } from \"three\";\nimport lottie from \"../libs/lottie.js\";\nclass LottieLoader extends Loader {\n  setQuality(value) {\n    this._quality = value;\n  }\n  load(url, onLoad, onProgress, onError) {\n    const quality = this._quality || 1;\n    const texture = new CanvasTexture();\n    texture.minFilter = NearestFilter;\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setWithCredentials(this.withCredentials);\n    loader.load(url, function (text) {\n      const data = JSON.parse(text);\n      const container = document.createElement(\"div\");\n      container.style.width = data.w + \"px\";\n      container.style.height = data.h + \"px\";\n      document.body.appendChild(container);\n      const animation = lottie.loadAnimation({\n        container,\n        animType: \"canvas\",\n        loop: true,\n        autoplay: true,\n        animationData: data,\n        rendererSettings: {\n          dpr: quality\n        }\n      });\n      texture.animation = animation;\n      texture.image = animation.container;\n      animation.addEventListener(\"enterFrame\", function () {\n        texture.needsUpdate = true;\n      });\n      container.style.display = \"none\";\n      if (onLoad !== void 0) {\n        onLoad(texture);\n      }\n    }, onProgress, onError);\n    return texture;\n  }\n}\nexport { LottieLoader };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Loader", "setQuality", "value", "_quality", "load", "url", "onLoad", "onProgress", "onError", "quality", "texture", "CanvasTexture", "minFilter", "NearestFilter", "loader", "<PERSON><PERSON><PERSON><PERSON>", "manager", "set<PERSON>ath", "path", "setWithCredentials", "withCredentials", "text", "data", "JSON", "parse", "container", "document", "createElement", "style", "width", "w", "height", "h", "body", "append<PERSON><PERSON><PERSON>", "animation", "lottie", "loadAnimation", "animType", "loop", "autoplay", "animationData", "rendererSettings", "dpr", "image", "addEventListener", "needsUpdate", "display"], "sources": ["F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\node_modules\\src\\loaders\\LottieLoader.js"], "sourcesContent": ["import { <PERSON><PERSON>oa<PERSON>, Loader, <PERSON>vasTexture, NearestFilter } from 'three'\nimport lottie from '../libs/lottie'\n\nclass LottieLoader extends Loader {\n  setQuality(value) {\n    this._quality = value\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const quality = this._quality || 1\n\n    const texture = new CanvasTexture()\n    texture.minFilter = NearestFilter\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setWithCredentials(this.withCredentials)\n\n    loader.load(\n      url,\n      function (text) {\n        const data = JSON.parse(text)\n\n        // bodymoving uses container.offetWidth and offsetHeight\n        // to define width/height\n\n        const container = document.createElement('div')\n        container.style.width = data.w + 'px'\n        container.style.height = data.h + 'px'\n        document.body.appendChild(container)\n\n        const animation = lottie.loadAnimation({\n          container: container,\n          animType: 'canvas',\n          loop: true,\n          autoplay: true,\n          animationData: data,\n          rendererSettings: { dpr: quality },\n        })\n\n        texture.animation = animation\n        texture.image = animation.container\n\n        animation.addEventListener('enterFrame', function () {\n          texture.needsUpdate = true\n        })\n\n        container.style.display = 'none'\n\n        if (onLoad !== undefined) {\n          onLoad(texture)\n        }\n      },\n      onProgress,\n      onError,\n    )\n\n    return texture\n  }\n}\n\nexport { LottieLoader }\n"], "mappings": ";;AAGA,MAAMA,YAAA,SAAqBC,MAAA,CAAO;EAChCC,WAAWC,KAAA,EAAO;IAChB,KAAKC,QAAA,GAAWD,KAAA;EACjB;EAEDE,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,OAAA,GAAU,KAAKN,QAAA,IAAY;IAEjC,MAAMO,OAAA,GAAU,IAAIC,aAAA,CAAe;IACnCD,OAAA,CAAQE,SAAA,GAAYC,aAAA;IAEpB,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKC,OAAO;IAC1CF,MAAA,CAAOG,OAAA,CAAQ,KAAKC,IAAI;IACxBJ,MAAA,CAAOK,kBAAA,CAAmB,KAAKC,eAAe;IAE9CN,MAAA,CAAOV,IAAA,CACLC,GAAA,EACA,UAAUgB,IAAA,EAAM;MACd,MAAMC,IAAA,GAAOC,IAAA,CAAKC,KAAA,CAAMH,IAAI;MAK5B,MAAMI,SAAA,GAAYC,QAAA,CAASC,aAAA,CAAc,KAAK;MAC9CF,SAAA,CAAUG,KAAA,CAAMC,KAAA,GAAQP,IAAA,CAAKQ,CAAA,GAAI;MACjCL,SAAA,CAAUG,KAAA,CAAMG,MAAA,GAAST,IAAA,CAAKU,CAAA,GAAI;MAClCN,QAAA,CAASO,IAAA,CAAKC,WAAA,CAAYT,SAAS;MAEnC,MAAMU,SAAA,GAAYC,MAAA,CAAOC,aAAA,CAAc;QACrCZ,SAAA;QACAa,QAAA,EAAU;QACVC,IAAA,EAAM;QACNC,QAAA,EAAU;QACVC,aAAA,EAAenB,IAAA;QACfoB,gBAAA,EAAkB;UAAEC,GAAA,EAAKlC;QAAS;MAC5C,CAAS;MAEDC,OAAA,CAAQyB,SAAA,GAAYA,SAAA;MACpBzB,OAAA,CAAQkC,KAAA,GAAQT,SAAA,CAAUV,SAAA;MAE1BU,SAAA,CAAUU,gBAAA,CAAiB,cAAc,YAAY;QACnDnC,OAAA,CAAQoC,WAAA,GAAc;MAChC,CAAS;MAEDrB,SAAA,CAAUG,KAAA,CAAMmB,OAAA,GAAU;MAE1B,IAAIzC,MAAA,KAAW,QAAW;QACxBA,MAAA,CAAOI,OAAO;MACf;IACF,GACDH,UAAA,EACAC,OACD;IAED,OAAOE,OAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}