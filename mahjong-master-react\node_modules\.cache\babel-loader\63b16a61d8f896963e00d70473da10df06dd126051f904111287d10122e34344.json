{"ast": null, "code": "var _jsxFileName = \"F:\\\\= \\u795E\\u706F\\u667A\\u5E93\\\\- AI \\u521B\\u4F5C\\\\AI APP\\\\\\u795E\\u706F\\u9EBB\\u5C06\\u5927\\u5E08\\\\mahjong-master-react\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\n// 简化的麻将牌组件\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction MahjongTile({\n  id,\n  type,\n  value,\n  isSelected,\n  onClick\n}) {\n  const getTileColor = () => {\n    switch (type) {\n      case 'character':\n        return 'bg-red-100 border-red-300 text-red-800';\n      case 'bamboo':\n        return 'bg-green-100 border-green-300 text-green-800';\n      case 'dot':\n        return 'bg-blue-100 border-blue-300 text-blue-800';\n      case 'wind':\n        return 'bg-yellow-100 border-yellow-300 text-yellow-800';\n      case 'dragon':\n        return 'bg-purple-100 border-purple-300 text-purple-800';\n      default:\n        return 'bg-gray-100 border-gray-300 text-gray-800';\n    }\n  };\n  const handleClick = () => {\n    console.log('MahjongTile clicked:', id);\n    alert(`点击了牌: ${id}`);\n    onClick();\n  };\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: `\n        w-12 h-16 rounded-lg border-2 cursor-pointer flex items-center justify-center font-bold text-sm\n        transition-all duration-200 hover:scale-105 hover:shadow-lg\n        ${getTileColor()}\n        ${isSelected ? 'ring-4 ring-yellow-400 transform -translate-y-2' : ''}\n      `,\n    onClick: handleClick,\n    whileHover: {\n      scale: 1.05\n    },\n    whileTap: {\n      scale: 0.95\n    },\n    animate: isSelected ? {\n      y: -8\n    } : {\n      y: 0\n    },\n    children: value\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n}\n_c = MahjongTile;\nfunction App() {\n  _s();\n  const [showWelcome, setShowWelcome] = useState(false); // 直接进入游戏界面进行测试\n  const [selectedTiles, setSelectedTiles] = useState([]);\n\n  // 生成示例麻将牌\n  const [tiles] = useState(() => {\n    const tileTypes = ['character', 'bamboo', 'dot', 'wind', 'dragon'];\n    return Array.from({\n      length: 13\n    }, (_, i) => ({\n      id: `tile-${i}`,\n      type: tileTypes[Math.floor(Math.random() * tileTypes.length)],\n      value: Math.floor(Math.random() * 9) + 1,\n      isSelected: false\n    }));\n  });\n  const handleTileClick = tileId => {\n    console.log('点击了麻将牌:', tileId); // 添加调试信息\n    setSelectedTiles(prev => {\n      const newSelection = prev.includes(tileId) ? prev.filter(id => id !== tileId) : [...prev, tileId];\n      console.log('选中的牌:', newSelection); // 添加调试信息\n      return newSelection;\n    });\n  };\n  const handleAction = action => {\n    console.log(`执行动作: ${action}，选中的牌: ${selectedTiles}`);\n    setSelectedTiles([]);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 opacity-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full h-full bg-gradient-to-br from-yellow-500/10 to-green-500/10\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: showWelcome && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        exit: {\n          opacity: 0\n        },\n        className: \"absolute inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            scale: 0.8,\n            y: 50\n          },\n          animate: {\n            scale: 1,\n            y: 0\n          },\n          exit: {\n            scale: 0.8,\n            y: -50\n          },\n          className: \"text-center text-white\",\n          children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n            className: \"text-6xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent\",\n            animate: {\n              filter: [\"drop-shadow(0 0 20px rgba(245, 158, 11, 0.5))\", \"drop-shadow(0 0 40px rgba(245, 158, 11, 0.8))\", \"drop-shadow(0 0 20px rgba(245, 158, 11, 0.5))\"]\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity\n            },\n            children: \"\\u795E\\u706F\\u9EBB\\u5C06\\u5927\\u5E08\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            className: \"text-xl mb-8 text-gray-300\",\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              delay: 0.5\n            },\n            children: \"\\u8D85\\u8D8A\\u4F20\\u7EDF\\uFF0C\\u91CD\\u65B0\\u5B9A\\u4E49\\u9EBB\\u5C06\\u4F53\\u9A8C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            className: \"bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white font-bold py-3 px-8 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 text-lg\",\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            onClick: () => setShowWelcome(false),\n            children: \"\\u5F00\\u59CB\\u6E38\\u620F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), !showWelcome && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          y: -100\n        },\n        animate: {\n          y: 0\n        },\n        className: \"absolute top-0 left-0 right-0 z-40 bg-black bg-opacity-50 backdrop-blur-sm p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center max-w-7xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-yellow-400\",\n              children: \"\\u795E\\u706F\\u9EBB\\u5C06\\u5927\\u5E08\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-3 h-3 bg-green-500 rounded-full animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u5728\\u7EBF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-400\",\n                children: \"\\u5F53\\u524D\\u5C40\\u6570:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-yellow-400 font-bold\",\n                children: \"\\u7B2C1\\u5C40\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-400\",\n                children: \"\\u98CE\\u5708:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-green-400 font-bold\",\n                children: \"\\u4E1C\\u98CE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center justify-center h-screen pt-20 pb-20\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          className: \"bg-green-800 rounded-3xl p-8 shadow-2xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-white text-xl font-bold mb-6 text-center\",\n            children: \"\\u60A8\\u7684\\u624B\\u724C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2 flex-wrap justify-center\",\n            children: tiles.map(tile => /*#__PURE__*/_jsxDEV(MahjongTile, {\n              id: tile.id,\n              type: tile.type,\n              value: tile.value,\n              isSelected: selectedTiles.includes(tile.id),\n              onClick: () => handleTileClick(tile.id)\n            }, tile.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: selectedTiles.length > 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            y: 100,\n            opacity: 0\n          },\n          animate: {\n            y: 0,\n            opacity: 1\n          },\n          exit: {\n            y: 100,\n            opacity: 0\n          },\n          className: \"absolute bottom-4 left-1/2 transform -translate-x-1/2 z-40\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-black bg-opacity-70 backdrop-blur-sm rounded-2xl p-4 flex space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              className: \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => handleAction('出牌'),\n              children: \"\\u51FA\\u724C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              className: \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => handleAction('吃'),\n              children: \"\\u5403\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              className: \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => handleAction('碰'),\n              children: \"\\u78B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              className: \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => handleAction('杠'),\n              children: \"\\u6760\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              className: \"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-bold py-2 px-6 rounded-lg shadow-lg transform hover:scale-105 transition-all duration-150\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => handleAction('胡牌'),\n              children: \"\\u80E1\\u724C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"r8uTuIMEPIPsU7gw/B1n7DzHFVc=\");\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"MahjongTile\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MahjongTile", "id", "type", "value", "isSelected", "onClick", "getTileColor", "handleClick", "console", "log", "alert", "div", "className", "whileHover", "scale", "whileTap", "animate", "y", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "App", "_s", "showWelcome", "setShowWelcome", "selectedTiles", "setSelectedTiles", "tiles", "tileTypes", "Array", "from", "length", "_", "i", "Math", "floor", "random", "handleTileClick", "tileId", "prev", "newSelection", "includes", "filter", "handleAction", "action", "initial", "opacity", "exit", "h1", "transition", "duration", "repeat", "Infinity", "p", "delay", "button", "map", "tile", "_c2", "$RefreshReg$"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\n// 简化的麻将牌组件\ninterface MahjongTileProps {\n  id: string;\n  type: string;\n  value: string | number;\n  isSelected: boolean;\n  onClick: () => void;\n}\n\nfunction MahjongTile({ id, type, value, isSelected, onClick }: MahjongTileProps) {\n  const getTileColor = () => {\n    switch (type) {\n      case 'character': return 'bg-red-100 border-red-300 text-red-800';\n      case 'bamboo': return 'bg-green-100 border-green-300 text-green-800';\n      case 'dot': return 'bg-blue-100 border-blue-300 text-blue-800';\n      case 'wind': return 'bg-yellow-100 border-yellow-300 text-yellow-800';\n      case 'dragon': return 'bg-purple-100 border-purple-300 text-purple-800';\n      default: return 'bg-gray-100 border-gray-300 text-gray-800';\n    }\n  };\n\n  const handleClick = () => {\n    console.log('MahjongTile clicked:', id);\n    alert(`点击了牌: ${id}`);\n    onClick();\n  };\n\n  return (\n    <motion.div\n      className={`\n        w-12 h-16 rounded-lg border-2 cursor-pointer flex items-center justify-center font-bold text-sm\n        transition-all duration-200 hover:scale-105 hover:shadow-lg\n        ${getTileColor()}\n        ${isSelected ? 'ring-4 ring-yellow-400 transform -translate-y-2' : ''}\n      `}\n      onClick={handleClick}\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n      animate={isSelected ? { y: -8 } : { y: 0 }}\n    >\n      {value}\n    </motion.div>\n  );\n}\n\nfunction App() {\n  const [showWelcome, setShowWelcome] = useState(false); // 直接进入游戏界面进行测试\n  const [selectedTiles, setSelectedTiles] = useState<string[]>([]);\n\n  // 生成示例麻将牌\n  const [tiles] = useState(() => {\n    const tileTypes = ['character', 'bamboo', 'dot', 'wind', 'dragon'];\n    return Array.from({ length: 13 }, (_, i) => ({\n      id: `tile-${i}`,\n      type: tileTypes[Math.floor(Math.random() * tileTypes.length)],\n      value: Math.floor(Math.random() * 9) + 1,\n      isSelected: false\n    }));\n  });\n\n  const handleTileClick = (tileId: string) => {\n    console.log('点击了麻将牌:', tileId); // 添加调试信息\n    setSelectedTiles(prev => {\n      const newSelection = prev.includes(tileId)\n        ? prev.filter(id => id !== tileId)\n        : [...prev, tileId];\n      console.log('选中的牌:', newSelection); // 添加调试信息\n      return newSelection;\n    });\n  };\n\n  const handleAction = (action: string) => {\n    console.log(`执行动作: ${action}，选中的牌: ${selectedTiles}`);\n    setSelectedTiles([]);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden\">\n      {/* 背景装饰 */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"w-full h-full bg-gradient-to-br from-yellow-500/10 to-green-500/10\"></div>\n      </div>\n\n      {/* 欢迎界面 */}\n      <AnimatePresence>\n        {showWelcome && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"absolute inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80\"\n          >\n            <motion.div\n              initial={{ scale: 0.8, y: 50 }}\n              animate={{ scale: 1, y: 0 }}\n              exit={{ scale: 0.8, y: -50 }}\n              className=\"text-center text-white\"\n            >\n              <motion.h1\n                className=\"text-6xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent\"\n                animate={{\n                  filter: [\n                    \"drop-shadow(0 0 20px rgba(245, 158, 11, 0.5))\",\n                    \"drop-shadow(0 0 40px rgba(245, 158, 11, 0.8))\",\n                    \"drop-shadow(0 0 20px rgba(245, 158, 11, 0.5))\"\n                  ]\n                }}\n                transition={{ duration: 2, repeat: Infinity }}\n              >\n                神灯麻将大师\n              </motion.h1>\n              <motion.p\n                className=\"text-xl mb-8 text-gray-300\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.5 }}\n              >\n                超越传统，重新定义麻将体验\n              </motion.p>\n              <motion.button\n                className=\"bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white font-bold py-3 px-8 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 text-lg\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => setShowWelcome(false)}\n              >\n                开始游戏\n              </motion.button>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* 主游戏界面 */}\n      {!showWelcome && (\n        <>\n          {/* 顶部状态栏 */}\n          <motion.div\n            initial={{ y: -100 }}\n            animate={{ y: 0 }}\n            className=\"absolute top-0 left-0 right-0 z-40 bg-black bg-opacity-50 backdrop-blur-sm p-4\"\n          >\n            <div className=\"flex justify-between items-center max-w-7xl mx-auto\">\n              <div className=\"flex items-center space-x-4\">\n                <h2 className=\"text-2xl font-bold text-yellow-400\">神灯麻将大师</h2>\n                <div className=\"flex items-center space-x-2 text-white\">\n                  <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\n                  <span>在线</span>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"text-white\">\n                  <span className=\"text-gray-400\">当前局数:</span>\n                  <span className=\"ml-2 text-yellow-400 font-bold\">第1局</span>\n                </div>\n                <div className=\"text-white\">\n                  <span className=\"text-gray-400\">风圈:</span>\n                  <span className=\"ml-2 text-green-400 font-bold\">东风</span>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* 游戏区域 */}\n          <div className=\"flex flex-col items-center justify-center h-screen pt-20 pb-20\">\n            <motion.div\n              initial={{ opacity: 0, y: 50 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"bg-green-800 rounded-3xl p-8 shadow-2xl\"\n            >\n              <h3 className=\"text-white text-xl font-bold mb-6 text-center\">您的手牌</h3>\n              <div className=\"flex gap-2 flex-wrap justify-center\">\n                {tiles.map((tile) => (\n                  <MahjongTile\n                    key={tile.id}\n                    id={tile.id}\n                    type={tile.type}\n                    value={tile.value}\n                    isSelected={selectedTiles.includes(tile.id)}\n                    onClick={() => handleTileClick(tile.id)}\n                  />\n                ))}\n              </div>\n            </motion.div>\n          </div>\n\n          {/* 底部操作栏 */}\n          <AnimatePresence>\n            {selectedTiles.length > 0 && (\n              <motion.div\n                initial={{ y: 100, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                exit={{ y: 100, opacity: 0 }}\n                className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 z-40\"\n              >\n                <div className=\"bg-black bg-opacity-70 backdrop-blur-sm rounded-2xl p-4 flex space-x-3\">\n                  <motion.button\n                    className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => handleAction('出牌')}\n                  >\n                    出牌\n                  </motion.button>\n                  <motion.button\n                    className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => handleAction('吃')}\n                  >\n                    吃\n                  </motion.button>\n                  <motion.button\n                    className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => handleAction('碰')}\n                  >\n                    碰\n                  </motion.button>\n                  <motion.button\n                    className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => handleAction('杠')}\n                  >\n                    杠\n                  </motion.button>\n                  <motion.button\n                    className=\"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-bold py-2 px-6 rounded-lg shadow-lg transform hover:scale-105 transition-all duration-150\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => handleAction('胡牌')}\n                  >\n                    胡牌\n                  </motion.button>\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </>\n      )}\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;;AAEvD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,SAASC,WAAWA,CAAC;EAAEC,EAAE;EAAEC,IAAI;EAAEC,KAAK;EAAEC,UAAU;EAAEC;AAA0B,CAAC,EAAE;EAC/E,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,QAAQJ,IAAI;MACV,KAAK,WAAW;QAAE,OAAO,wCAAwC;MACjE,KAAK,QAAQ;QAAE,OAAO,8CAA8C;MACpE,KAAK,KAAK;QAAE,OAAO,2CAA2C;MAC9D,KAAK,MAAM;QAAE,OAAO,iDAAiD;MACrE,KAAK,QAAQ;QAAE,OAAO,iDAAiD;MACvE;QAAS,OAAO,2CAA2C;IAC7D;EACF,CAAC;EAED,MAAMK,WAAW,GAAGA,CAAA,KAAM;IACxBC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAER,EAAE,CAAC;IACvCS,KAAK,CAAC,SAAST,EAAE,EAAE,CAAC;IACpBI,OAAO,CAAC,CAAC;EACX,CAAC;EAED,oBACER,OAAA,CAACH,MAAM,CAACiB,GAAG;IACTC,SAAS,EAAE;AACjB;AACA;AACA,UAAUN,YAAY,CAAC,CAAC;AACxB,UAAUF,UAAU,GAAG,iDAAiD,GAAG,EAAE;AAC7E,OAAQ;IACFC,OAAO,EAAEE,WAAY;IACrBM,UAAU,EAAE;MAAEC,KAAK,EAAE;IAAK,CAAE;IAC5BC,QAAQ,EAAE;MAAED,KAAK,EAAE;IAAK,CAAE;IAC1BE,OAAO,EAAEZ,UAAU,GAAG;MAAEa,CAAC,EAAE,CAAC;IAAE,CAAC,GAAG;MAAEA,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,EAE1Cf;EAAK;IAAAgB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB;AAACC,EAAA,GAlCQvB,WAAW;AAoCpB,SAASwB,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACvD,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAW,EAAE,CAAC;;EAEhE;EACA,MAAM,CAACqC,KAAK,CAAC,GAAGrC,QAAQ,CAAC,MAAM;IAC7B,MAAMsC,SAAS,GAAG,CAAC,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC;IAClE,OAAOC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAG,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,MAAM;MAC3CnC,EAAE,EAAE,QAAQmC,CAAC,EAAE;MACflC,IAAI,EAAE6B,SAAS,CAACM,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGR,SAAS,CAACG,MAAM,CAAC,CAAC;MAC7D/B,KAAK,EAAEkC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACxCnC,UAAU,EAAE;IACd,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EAEF,MAAMoC,eAAe,GAAIC,MAAc,IAAK;IAC1CjC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEgC,MAAM,CAAC,CAAC,CAAC;IAChCZ,gBAAgB,CAACa,IAAI,IAAI;MACvB,MAAMC,YAAY,GAAGD,IAAI,CAACE,QAAQ,CAACH,MAAM,CAAC,GACtCC,IAAI,CAACG,MAAM,CAAC5C,EAAE,IAAIA,EAAE,KAAKwC,MAAM,CAAC,GAChC,CAAC,GAAGC,IAAI,EAAED,MAAM,CAAC;MACrBjC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEkC,YAAY,CAAC,CAAC,CAAC;MACpC,OAAOA,YAAY;IACrB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,YAAY,GAAIC,MAAc,IAAK;IACvCvC,OAAO,CAACC,GAAG,CAAC,SAASsC,MAAM,UAAUnB,aAAa,EAAE,CAAC;IACrDC,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,oBACEhC,OAAA;IAAKe,SAAS,EAAC,mGAAmG;IAAAM,QAAA,gBAEhHrB,OAAA;MAAKe,SAAS,EAAC,6BAA6B;MAAAM,QAAA,eAC1CrB,OAAA;QAAKe,SAAS,EAAC;MAAoE;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvF,CAAC,eAGNzB,OAAA,CAACF,eAAe;MAAAuB,QAAA,EACbQ,WAAW,iBACV7B,OAAA,CAACH,MAAM,CAACiB,GAAG;QACTqC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBjC,OAAO,EAAE;UAAEiC,OAAO,EAAE;QAAE,CAAE;QACxBC,IAAI,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACrBrC,SAAS,EAAC,+EAA+E;QAAAM,QAAA,eAEzFrB,OAAA,CAACH,MAAM,CAACiB,GAAG;UACTqC,OAAO,EAAE;YAAElC,KAAK,EAAE,GAAG;YAAEG,CAAC,EAAE;UAAG,CAAE;UAC/BD,OAAO,EAAE;YAAEF,KAAK,EAAE,CAAC;YAAEG,CAAC,EAAE;UAAE,CAAE;UAC5BiC,IAAI,EAAE;YAAEpC,KAAK,EAAE,GAAG;YAAEG,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BL,SAAS,EAAC,wBAAwB;UAAAM,QAAA,gBAElCrB,OAAA,CAACH,MAAM,CAACyD,EAAE;YACRvC,SAAS,EAAC,sGAAsG;YAChHI,OAAO,EAAE;cACP6B,MAAM,EAAE,CACN,+CAA+C,EAC/C,+CAA+C,EAC/C,+CAA+C;YAEnD,CAAE;YACFO,UAAU,EAAE;cAAEC,QAAQ,EAAE,CAAC;cAAEC,MAAM,EAAEC;YAAS,CAAE;YAAArC,QAAA,EAC/C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACZzB,OAAA,CAACH,MAAM,CAAC8D,CAAC;YACP5C,SAAS,EAAC,4BAA4B;YACtCoC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBjC,OAAO,EAAE;cAAEiC,OAAO,EAAE;YAAE,CAAE;YACxBG,UAAU,EAAE;cAAEK,KAAK,EAAE;YAAI,CAAE;YAAAvC,QAAA,EAC5B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACXzB,OAAA,CAACH,MAAM,CAACgE,MAAM;YACZ9C,SAAS,EAAC,4NAA4N;YACtOC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAC1BT,OAAO,EAAEA,CAAA,KAAMsB,cAAc,CAAC,KAAK,CAAE;YAAAT,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,EAGjB,CAACI,WAAW,iBACX7B,OAAA,CAAAE,SAAA;MAAAmB,QAAA,gBAEErB,OAAA,CAACH,MAAM,CAACiB,GAAG;QACTqC,OAAO,EAAE;UAAE/B,CAAC,EAAE,CAAC;QAAI,CAAE;QACrBD,OAAO,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClBL,SAAS,EAAC,gFAAgF;QAAAM,QAAA,eAE1FrB,OAAA;UAAKe,SAAS,EAAC,qDAAqD;UAAAM,QAAA,gBAClErB,OAAA;YAAKe,SAAS,EAAC,6BAA6B;YAAAM,QAAA,gBAC1CrB,OAAA;cAAIe,SAAS,EAAC,oCAAoC;cAAAM,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DzB,OAAA;cAAKe,SAAS,EAAC,wCAAwC;cAAAM,QAAA,gBACrDrB,OAAA;gBAAKe,SAAS,EAAC;cAAiD;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvEzB,OAAA;gBAAAqB,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzB,OAAA;YAAKe,SAAS,EAAC,6BAA6B;YAAAM,QAAA,gBAC1CrB,OAAA;cAAKe,SAAS,EAAC,YAAY;cAAAM,QAAA,gBACzBrB,OAAA;gBAAMe,SAAS,EAAC,eAAe;gBAAAM,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5CzB,OAAA;gBAAMe,SAAS,EAAC,gCAAgC;gBAAAM,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNzB,OAAA;cAAKe,SAAS,EAAC,YAAY;cAAAM,QAAA,gBACzBrB,OAAA;gBAAMe,SAAS,EAAC,eAAe;gBAAAM,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1CzB,OAAA;gBAAMe,SAAS,EAAC,+BAA+B;gBAAAM,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbzB,OAAA;QAAKe,SAAS,EAAC,gEAAgE;QAAAM,QAAA,eAC7ErB,OAAA,CAACH,MAAM,CAACiB,GAAG;UACTqC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEhC,CAAC,EAAE;UAAG,CAAE;UAC/BD,OAAO,EAAE;YAAEiC,OAAO,EAAE,CAAC;YAAEhC,CAAC,EAAE;UAAE,CAAE;UAC9BL,SAAS,EAAC,yCAAyC;UAAAM,QAAA,gBAEnDrB,OAAA;YAAIe,SAAS,EAAC,+CAA+C;YAAAM,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEzB,OAAA;YAAKe,SAAS,EAAC,qCAAqC;YAAAM,QAAA,EACjDY,KAAK,CAAC6B,GAAG,CAAEC,IAAI,iBACd/D,OAAA,CAACG,WAAW;cAEVC,EAAE,EAAE2D,IAAI,CAAC3D,EAAG;cACZC,IAAI,EAAE0D,IAAI,CAAC1D,IAAK;cAChBC,KAAK,EAAEyD,IAAI,CAACzD,KAAM;cAClBC,UAAU,EAAEwB,aAAa,CAACgB,QAAQ,CAACgB,IAAI,CAAC3D,EAAE,CAAE;cAC5CI,OAAO,EAAEA,CAAA,KAAMmC,eAAe,CAACoB,IAAI,CAAC3D,EAAE;YAAE,GALnC2D,IAAI,CAAC3D,EAAE;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMb,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNzB,OAAA,CAACF,eAAe;QAAAuB,QAAA,EACbU,aAAa,CAACM,MAAM,GAAG,CAAC,iBACvBrC,OAAA,CAACH,MAAM,CAACiB,GAAG;UACTqC,OAAO,EAAE;YAAE/B,CAAC,EAAE,GAAG;YAAEgC,OAAO,EAAE;UAAE,CAAE;UAChCjC,OAAO,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEgC,OAAO,EAAE;UAAE,CAAE;UAC9BC,IAAI,EAAE;YAAEjC,CAAC,EAAE,GAAG;YAAEgC,OAAO,EAAE;UAAE,CAAE;UAC7BrC,SAAS,EAAC,4DAA4D;UAAAM,QAAA,eAEtErB,OAAA;YAAKe,SAAS,EAAC,wEAAwE;YAAAM,QAAA,gBACrFrB,OAAA,CAACH,MAAM,CAACgE,MAAM;cACZ9C,SAAS,EAAC,oNAAoN;cAC9NC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BT,OAAO,EAAEA,CAAA,KAAMyC,YAAY,CAAC,IAAI,CAAE;cAAA5B,QAAA,EACnC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChBzB,OAAA,CAACH,MAAM,CAACgE,MAAM;cACZ9C,SAAS,EAAC,oNAAoN;cAC9NC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BT,OAAO,EAAEA,CAAA,KAAMyC,YAAY,CAAC,GAAG,CAAE;cAAA5B,QAAA,EAClC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChBzB,OAAA,CAACH,MAAM,CAACgE,MAAM;cACZ9C,SAAS,EAAC,oNAAoN;cAC9NC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BT,OAAO,EAAEA,CAAA,KAAMyC,YAAY,CAAC,GAAG,CAAE;cAAA5B,QAAA,EAClC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChBzB,OAAA,CAACH,MAAM,CAACgE,MAAM;cACZ9C,SAAS,EAAC,oNAAoN;cAC9NC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BT,OAAO,EAAEA,CAAA,KAAMyC,YAAY,CAAC,GAAG,CAAE;cAAA5B,QAAA,EAClC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChBzB,OAAA,CAACH,MAAM,CAACgE,MAAM;cACZ9C,SAAS,EAAC,wLAAwL;cAClMC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BT,OAAO,EAAEA,CAAA,KAAMyC,YAAY,CAAC,IAAI,CAAE;cAAA5B,QAAA,EACnC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC;IAAA,eAClB,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACG,EAAA,CAvMQD,GAAG;AAAAqC,GAAA,GAAHrC,GAAG;AAyMZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAsC,GAAA;AAAAC,YAAA,CAAAvC,EAAA;AAAAuC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}