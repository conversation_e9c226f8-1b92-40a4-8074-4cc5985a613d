[{"F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\src\\index.tsx": "1", "F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\src\\App.tsx": "2", "F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\src\\reportWebVitals.ts": "3", "F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\src\\types\\game.ts": "4", "F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\src\\store\\gameStore.ts": "5", "F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\src\\components\\3D\\MahjongTable.tsx": "6"}, {"size": 554, "mtime": 1751332478563, "results": "7", "hashOfConfig": "8"}, {"size": 10350, "mtime": 1751337885371, "results": "9", "hashOfConfig": "8"}, {"size": 425, "mtime": 1751332477388, "results": "10", "hashOfConfig": "8"}, {"size": 3158, "mtime": 1751332729433, "results": "11", "hashOfConfig": "8"}, {"size": 4609, "mtime": 1751332762302, "results": "12", "hashOfConfig": "8"}, {"size": 6813, "mtime": 1751332808527, "results": "13", "hashOfConfig": "8"}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "o2a6k5", {"filePath": "17", "messages": "18", "suppressedMessages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\src\\index.tsx", [], [], "F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\src\\App.tsx", [], [], "F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\src\\reportWebVitals.ts", [], [], "F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\src\\types\\game.ts", [], [], "F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\src\\store\\gameStore.ts", ["32", "33"], [], "F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\src\\components\\3D\\MahjongTable.tsx", ["34"], [], {"ruleId": "35", "severity": 1, "message": "36", "line": 3, "column": 29, "nodeType": "37", "messageId": "38", "endLine": 3, "endColumn": 33}, {"ruleId": "35", "severity": 1, "message": "39", "line": 104, "column": 41, "nodeType": "37", "messageId": "38", "endLine": 104, "endColumn": 48}, {"ruleId": "35", "severity": 1, "message": "40", "line": 5, "column": 10, "nodeType": "37", "messageId": "38", "endLine": 5, "endColumn": 16}, "@typescript-eslint/no-unused-vars", "'Tile' is defined but never used.", "Identifier", "unusedVar", "'uiState' is assigned a value but never used.", "'motion' is defined but never used."]