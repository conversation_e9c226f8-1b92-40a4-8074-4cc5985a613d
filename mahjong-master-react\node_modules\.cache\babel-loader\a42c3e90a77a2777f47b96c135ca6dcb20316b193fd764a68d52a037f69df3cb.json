{"ast": null, "code": "import { REVISION } from \"three\";\nconst version = /* @__PURE__ */(() => parseInt(REVISION.replace(/\\D+/g, \"\")))();\nexport { version };", "map": {"version": 3, "names": ["version", "parseInt", "REVISION", "replace"], "sources": ["F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\node_modules\\src\\_polyfill\\constants.ts"], "sourcesContent": ["import { REVISION } from 'three'\n\nexport const version = /* @__PURE__ */ (() => parseInt(REVISION.replace(/\\D+/g, '')))()\n"], "mappings": ";AAEa,MAAAA,OAAA,yBAAiCC,QAAA,CAASC,QAAA,CAASC,OAAA,CAAQ,QAAQ,EAAE,CAAC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}