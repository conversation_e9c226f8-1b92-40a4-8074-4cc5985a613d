{"ast": null, "code": "import { Vector3, Color, EventDispatcher, BufferGeometry, Float32BufferAttribute, MathUtils, Matrix3, Matrix4, Box3, Sphere, BufferAttribute, Vector2, Object3D } from \"three\";\nconst _m1 = /* @__PURE__ */new Matrix4();\nconst _obj = /* @__PURE__ */new Object3D();\nconst _offset = /* @__PURE__ */new Vector3();\nconst Geometry = /* @__PURE__ */(() => {\n  class Geometry2 extends EventDispatcher {\n    static createBufferGeometryFromObject(object) {\n      let buffergeometry = new BufferGeometry();\n      const geometry = object.geometry;\n      if (object.isPoints || object.isLine) {\n        const positions = new Float32BufferAttribute(geometry.vertices.length * 3, 3);\n        const colors = new Float32BufferAttribute(geometry.colors.length * 3, 3);\n        buffergeometry.setAttribute(\"position\", positions.copyVector3sArray(geometry.vertices));\n        buffergeometry.setAttribute(\"color\", colors.copyColorsArray(geometry.colors));\n        if (geometry.lineDistances && geometry.lineDistances.length === geometry.vertices.length) {\n          const lineDistances = new Float32BufferAttribute(geometry.lineDistances.length, 1);\n          buffergeometry.setAttribute(\"lineDistance\", lineDistances.copyArray(geometry.lineDistances));\n        }\n        if (geometry.boundingSphere !== null) {\n          buffergeometry.boundingSphere = geometry.boundingSphere.clone();\n        }\n        if (geometry.boundingBox !== null) {\n          buffergeometry.boundingBox = geometry.boundingBox.clone();\n        }\n      } else if (object.isMesh) {\n        buffergeometry = geometry.toBufferGeometry();\n      }\n      return buffergeometry;\n    }\n    constructor() {\n      super();\n      this.isGeometry = true;\n      this.uuid = MathUtils.generateUUID();\n      this.name = \"\";\n      this.type = \"Geometry\";\n      this.vertices = [];\n      this.colors = [];\n      this.faces = [];\n      this.faceVertexUvs = [[]];\n      this.morphTargets = [];\n      this.morphNormals = [];\n      this.skinWeights = [];\n      this.skinIndices = [];\n      this.lineDistances = [];\n      this.boundingBox = null;\n      this.boundingSphere = null;\n      this.elementsNeedUpdate = false;\n      this.verticesNeedUpdate = false;\n      this.uvsNeedUpdate = false;\n      this.normalsNeedUpdate = false;\n      this.colorsNeedUpdate = false;\n      this.lineDistancesNeedUpdate = false;\n      this.groupsNeedUpdate = false;\n    }\n    applyMatrix4(matrix) {\n      const normalMatrix = new Matrix3().getNormalMatrix(matrix);\n      for (let i = 0, il = this.vertices.length; i < il; i++) {\n        const vertex = this.vertices[i];\n        vertex.applyMatrix4(matrix);\n      }\n      for (let i = 0, il = this.faces.length; i < il; i++) {\n        const face = this.faces[i];\n        face.normal.applyMatrix3(normalMatrix).normalize();\n        for (let j = 0, jl = face.vertexNormals.length; j < jl; j++) {\n          face.vertexNormals[j].applyMatrix3(normalMatrix).normalize();\n        }\n      }\n      if (this.boundingBox !== null) {\n        this.computeBoundingBox();\n      }\n      if (this.boundingSphere !== null) {\n        this.computeBoundingSphere();\n      }\n      this.verticesNeedUpdate = true;\n      this.normalsNeedUpdate = true;\n      return this;\n    }\n    rotateX(angle) {\n      _m1.makeRotationX(angle);\n      this.applyMatrix4(_m1);\n      return this;\n    }\n    rotateY(angle) {\n      _m1.makeRotationY(angle);\n      this.applyMatrix4(_m1);\n      return this;\n    }\n    rotateZ(angle) {\n      _m1.makeRotationZ(angle);\n      this.applyMatrix4(_m1);\n      return this;\n    }\n    translate(x, y, z) {\n      _m1.makeTranslation(x, y, z);\n      this.applyMatrix4(_m1);\n      return this;\n    }\n    scale(x, y, z) {\n      _m1.makeScale(x, y, z);\n      this.applyMatrix4(_m1);\n      return this;\n    }\n    lookAt(vector) {\n      _obj.lookAt(vector);\n      _obj.updateMatrix();\n      this.applyMatrix4(_obj.matrix);\n      return this;\n    }\n    fromBufferGeometry(geometry) {\n      const scope = this;\n      const index = geometry.index !== null ? geometry.index : void 0;\n      const attributes = geometry.attributes;\n      if (attributes.position === void 0) {\n        console.error(\"THREE.Geometry.fromBufferGeometry(): Position attribute required for conversion.\");\n        return this;\n      }\n      const position = attributes.position;\n      const normal = attributes.normal;\n      const color = attributes.color;\n      const uv = attributes.uv;\n      const uv2 = attributes.uv2;\n      if (uv2 !== void 0) this.faceVertexUvs[1] = [];\n      for (let i = 0; i < position.count; i++) {\n        scope.vertices.push(new Vector3().fromBufferAttribute(position, i));\n        if (color !== void 0) {\n          scope.colors.push(new Color().fromBufferAttribute(color, i));\n        }\n      }\n      function addFace(a, b, c, materialIndex) {\n        const vertexColors = color === void 0 ? [] : [scope.colors[a].clone(), scope.colors[b].clone(), scope.colors[c].clone()];\n        const vertexNormals = normal === void 0 ? [] : [new Vector3().fromBufferAttribute(normal, a), new Vector3().fromBufferAttribute(normal, b), new Vector3().fromBufferAttribute(normal, c)];\n        const face = new Face3(a, b, c, vertexNormals, vertexColors, materialIndex);\n        scope.faces.push(face);\n        if (uv !== void 0) {\n          scope.faceVertexUvs[0].push([new Vector2().fromBufferAttribute(uv, a), new Vector2().fromBufferAttribute(uv, b), new Vector2().fromBufferAttribute(uv, c)]);\n        }\n        if (uv2 !== void 0) {\n          scope.faceVertexUvs[1].push([new Vector2().fromBufferAttribute(uv2, a), new Vector2().fromBufferAttribute(uv2, b), new Vector2().fromBufferAttribute(uv2, c)]);\n        }\n      }\n      const groups = geometry.groups;\n      if (groups.length > 0) {\n        for (let i = 0; i < groups.length; i++) {\n          const group = groups[i];\n          const start = group.start;\n          const count = group.count;\n          for (let j = start, jl = start + count; j < jl; j += 3) {\n            if (index !== void 0) {\n              addFace(index.getX(j), index.getX(j + 1), index.getX(j + 2), group.materialIndex);\n            } else {\n              addFace(j, j + 1, j + 2, group.materialIndex);\n            }\n          }\n        }\n      } else {\n        if (index !== void 0) {\n          for (let i = 0; i < index.count; i += 3) {\n            addFace(index.getX(i), index.getX(i + 1), index.getX(i + 2));\n          }\n        } else {\n          for (let i = 0; i < position.count; i += 3) {\n            addFace(i, i + 1, i + 2);\n          }\n        }\n      }\n      this.computeFaceNormals();\n      if (geometry.boundingBox !== null) {\n        this.boundingBox = geometry.boundingBox.clone();\n      }\n      if (geometry.boundingSphere !== null) {\n        this.boundingSphere = geometry.boundingSphere.clone();\n      }\n      return this;\n    }\n    center() {\n      this.computeBoundingBox();\n      this.boundingBox.getCenter(_offset).negate();\n      this.translate(_offset.x, _offset.y, _offset.z);\n      return this;\n    }\n    normalize() {\n      this.computeBoundingSphere();\n      const center = this.boundingSphere.center;\n      const radius = this.boundingSphere.radius;\n      const s = radius === 0 ? 1 : 1 / radius;\n      const matrix = new Matrix4();\n      matrix.set(s, 0, 0, -s * center.x, 0, s, 0, -s * center.y, 0, 0, s, -s * center.z, 0, 0, 0, 1);\n      this.applyMatrix4(matrix);\n      return this;\n    }\n    computeFaceNormals() {\n      const cb = new Vector3(),\n        ab = new Vector3();\n      for (let f = 0, fl = this.faces.length; f < fl; f++) {\n        const face = this.faces[f];\n        const vA = this.vertices[face.a];\n        const vB = this.vertices[face.b];\n        const vC = this.vertices[face.c];\n        cb.subVectors(vC, vB);\n        ab.subVectors(vA, vB);\n        cb.cross(ab);\n        cb.normalize();\n        face.normal.copy(cb);\n      }\n    }\n    computeVertexNormals(areaWeighted = true) {\n      const vertices = new Array(this.vertices.length);\n      for (let v = 0, vl = this.vertices.length; v < vl; v++) {\n        vertices[v] = new Vector3();\n      }\n      if (areaWeighted) {\n        const cb = new Vector3(),\n          ab = new Vector3();\n        for (let f = 0, fl = this.faces.length; f < fl; f++) {\n          const face = this.faces[f];\n          const vA = this.vertices[face.a];\n          const vB = this.vertices[face.b];\n          const vC = this.vertices[face.c];\n          cb.subVectors(vC, vB);\n          ab.subVectors(vA, vB);\n          cb.cross(ab);\n          vertices[face.a].add(cb);\n          vertices[face.b].add(cb);\n          vertices[face.c].add(cb);\n        }\n      } else {\n        this.computeFaceNormals();\n        for (let f = 0, fl = this.faces.length; f < fl; f++) {\n          const face = this.faces[f];\n          vertices[face.a].add(face.normal);\n          vertices[face.b].add(face.normal);\n          vertices[face.c].add(face.normal);\n        }\n      }\n      for (let v = 0, vl = this.vertices.length; v < vl; v++) {\n        vertices[v].normalize();\n      }\n      for (let f = 0, fl = this.faces.length; f < fl; f++) {\n        const face = this.faces[f];\n        const vertexNormals = face.vertexNormals;\n        if (vertexNormals.length === 3) {\n          vertexNormals[0].copy(vertices[face.a]);\n          vertexNormals[1].copy(vertices[face.b]);\n          vertexNormals[2].copy(vertices[face.c]);\n        } else {\n          vertexNormals[0] = vertices[face.a].clone();\n          vertexNormals[1] = vertices[face.b].clone();\n          vertexNormals[2] = vertices[face.c].clone();\n        }\n      }\n      if (this.faces.length > 0) {\n        this.normalsNeedUpdate = true;\n      }\n    }\n    computeFlatVertexNormals() {\n      this.computeFaceNormals();\n      for (let f = 0, fl = this.faces.length; f < fl; f++) {\n        const face = this.faces[f];\n        const vertexNormals = face.vertexNormals;\n        if (vertexNormals.length === 3) {\n          vertexNormals[0].copy(face.normal);\n          vertexNormals[1].copy(face.normal);\n          vertexNormals[2].copy(face.normal);\n        } else {\n          vertexNormals[0] = face.normal.clone();\n          vertexNormals[1] = face.normal.clone();\n          vertexNormals[2] = face.normal.clone();\n        }\n      }\n      if (this.faces.length > 0) {\n        this.normalsNeedUpdate = true;\n      }\n    }\n    computeMorphNormals() {\n      for (let f = 0, fl = this.faces.length; f < fl; f++) {\n        const face = this.faces[f];\n        if (!face.__originalFaceNormal) {\n          face.__originalFaceNormal = face.normal.clone();\n        } else {\n          face.__originalFaceNormal.copy(face.normal);\n        }\n        if (!face.__originalVertexNormals) face.__originalVertexNormals = [];\n        for (let i = 0, il = face.vertexNormals.length; i < il; i++) {\n          if (!face.__originalVertexNormals[i]) {\n            face.__originalVertexNormals[i] = face.vertexNormals[i].clone();\n          } else {\n            face.__originalVertexNormals[i].copy(face.vertexNormals[i]);\n          }\n        }\n      }\n      const tmpGeo = new Geometry2();\n      tmpGeo.faces = this.faces;\n      for (let i = 0, il = this.morphTargets.length; i < il; i++) {\n        if (!this.morphNormals[i]) {\n          this.morphNormals[i] = {};\n          this.morphNormals[i].faceNormals = [];\n          this.morphNormals[i].vertexNormals = [];\n          const dstNormalsFace = this.morphNormals[i].faceNormals;\n          const dstNormalsVertex = this.morphNormals[i].vertexNormals;\n          for (let f = 0, fl = this.faces.length; f < fl; f++) {\n            const faceNormal = new Vector3();\n            const vertexNormals = {\n              a: new Vector3(),\n              b: new Vector3(),\n              c: new Vector3()\n            };\n            dstNormalsFace.push(faceNormal);\n            dstNormalsVertex.push(vertexNormals);\n          }\n        }\n        const morphNormals = this.morphNormals[i];\n        tmpGeo.vertices = this.morphTargets[i].vertices;\n        tmpGeo.computeFaceNormals();\n        tmpGeo.computeVertexNormals();\n        for (let f = 0, fl = this.faces.length; f < fl; f++) {\n          const face = this.faces[f];\n          const faceNormal = morphNormals.faceNormals[f];\n          const vertexNormals = morphNormals.vertexNormals[f];\n          faceNormal.copy(face.normal);\n          vertexNormals.a.copy(face.vertexNormals[0]);\n          vertexNormals.b.copy(face.vertexNormals[1]);\n          vertexNormals.c.copy(face.vertexNormals[2]);\n        }\n      }\n      for (let f = 0, fl = this.faces.length; f < fl; f++) {\n        const face = this.faces[f];\n        face.normal = face.__originalFaceNormal;\n        face.vertexNormals = face.__originalVertexNormals;\n      }\n    }\n    computeBoundingBox() {\n      if (this.boundingBox === null) {\n        this.boundingBox = new Box3();\n      }\n      this.boundingBox.setFromPoints(this.vertices);\n    }\n    computeBoundingSphere() {\n      if (this.boundingSphere === null) {\n        this.boundingSphere = new Sphere();\n      }\n      this.boundingSphere.setFromPoints(this.vertices);\n    }\n    merge(geometry, matrix, materialIndexOffset = 0) {\n      if (!(geometry && geometry.isGeometry)) {\n        console.error(\"THREE.Geometry.merge(): geometry not an instance of THREE.Geometry.\", geometry);\n        return;\n      }\n      let normalMatrix;\n      const vertexOffset = this.vertices.length,\n        vertices1 = this.vertices,\n        vertices2 = geometry.vertices,\n        faces1 = this.faces,\n        faces2 = geometry.faces,\n        colors1 = this.colors,\n        colors2 = geometry.colors;\n      if (matrix !== void 0) {\n        normalMatrix = new Matrix3().getNormalMatrix(matrix);\n      }\n      for (let i = 0, il = vertices2.length; i < il; i++) {\n        const vertex = vertices2[i];\n        const vertexCopy = vertex.clone();\n        if (matrix !== void 0) vertexCopy.applyMatrix4(matrix);\n        vertices1.push(vertexCopy);\n      }\n      for (let i = 0, il = colors2.length; i < il; i++) {\n        colors1.push(colors2[i].clone());\n      }\n      for (let i = 0, il = faces2.length; i < il; i++) {\n        const face = faces2[i];\n        let normal, color;\n        const faceVertexNormals = face.vertexNormals,\n          faceVertexColors = face.vertexColors;\n        const faceCopy = new Face3(face.a + vertexOffset, face.b + vertexOffset, face.c + vertexOffset);\n        faceCopy.normal.copy(face.normal);\n        if (normalMatrix !== void 0) {\n          faceCopy.normal.applyMatrix3(normalMatrix).normalize();\n        }\n        for (let j = 0, jl = faceVertexNormals.length; j < jl; j++) {\n          normal = faceVertexNormals[j].clone();\n          if (normalMatrix !== void 0) {\n            normal.applyMatrix3(normalMatrix).normalize();\n          }\n          faceCopy.vertexNormals.push(normal);\n        }\n        faceCopy.color.copy(face.color);\n        for (let j = 0, jl = faceVertexColors.length; j < jl; j++) {\n          color = faceVertexColors[j];\n          faceCopy.vertexColors.push(color.clone());\n        }\n        faceCopy.materialIndex = face.materialIndex + materialIndexOffset;\n        faces1.push(faceCopy);\n      }\n      for (let i = 0, il = geometry.faceVertexUvs.length; i < il; i++) {\n        const faceVertexUvs2 = geometry.faceVertexUvs[i];\n        if (this.faceVertexUvs[i] === void 0) this.faceVertexUvs[i] = [];\n        for (let j = 0, jl = faceVertexUvs2.length; j < jl; j++) {\n          const uvs2 = faceVertexUvs2[j],\n            uvsCopy = [];\n          for (let k = 0, kl = uvs2.length; k < kl; k++) {\n            uvsCopy.push(uvs2[k].clone());\n          }\n          this.faceVertexUvs[i].push(uvsCopy);\n        }\n      }\n    }\n    mergeMesh(mesh) {\n      if (!(mesh && mesh.isMesh)) {\n        console.error(\"THREE.Geometry.mergeMesh(): mesh not an instance of THREE.Mesh.\", mesh);\n        return;\n      }\n      if (mesh.matrixAutoUpdate) mesh.updateMatrix();\n      this.merge(mesh.geometry, mesh.matrix);\n    }\n    /*\n     * Checks for duplicate vertices with hashmap.\n     * Duplicated vertices are removed\n     * and faces' vertices are updated.\n     */\n    mergeVertices(precisionPoints = 4) {\n      const verticesMap = {};\n      const unique = [],\n        changes = [];\n      const precision = Math.pow(10, precisionPoints);\n      for (let i = 0, il = this.vertices.length; i < il; i++) {\n        const v = this.vertices[i];\n        const key = `${Math.round(v.x * precision)}_${Math.round(v.y * precision)}_${Math.round(v.z * precision)}`;\n        if (verticesMap[key] === void 0) {\n          verticesMap[key] = i;\n          unique.push(this.vertices[i]);\n          changes[i] = unique.length - 1;\n        } else {\n          changes[i] = changes[verticesMap[key]];\n        }\n      }\n      const faceIndicesToRemove = [];\n      for (let i = 0, il = this.faces.length; i < il; i++) {\n        const face = this.faces[i];\n        face.a = changes[face.a];\n        face.b = changes[face.b];\n        face.c = changes[face.c];\n        const indices = [face.a, face.b, face.c];\n        for (let n = 0; n < 3; n++) {\n          if (indices[n] === indices[(n + 1) % 3]) {\n            faceIndicesToRemove.push(i);\n            break;\n          }\n        }\n      }\n      for (let i = faceIndicesToRemove.length - 1; i >= 0; i--) {\n        const idx = faceIndicesToRemove[i];\n        this.faces.splice(idx, 1);\n        for (let j = 0, jl = this.faceVertexUvs.length; j < jl; j++) {\n          this.faceVertexUvs[j].splice(idx, 1);\n        }\n      }\n      const diff = this.vertices.length - unique.length;\n      this.vertices = unique;\n      return diff;\n    }\n    setFromPoints(points) {\n      this.vertices = [];\n      for (let i = 0, l = points.length; i < l; i++) {\n        const point = points[i];\n        this.vertices.push(new Vector3(point.x, point.y, point.z || 0));\n      }\n      return this;\n    }\n    sortFacesByMaterialIndex() {\n      const faces = this.faces;\n      const length = faces.length;\n      for (let i = 0; i < length; i++) {\n        faces[i]._id = i;\n      }\n      function materialIndexSort(a, b) {\n        return a.materialIndex - b.materialIndex;\n      }\n      faces.sort(materialIndexSort);\n      const uvs1 = this.faceVertexUvs[0];\n      const uvs2 = this.faceVertexUvs[1];\n      let newUvs1, newUvs2;\n      if (uvs1 && uvs1.length === length) newUvs1 = [];\n      if (uvs2 && uvs2.length === length) newUvs2 = [];\n      for (let i = 0; i < length; i++) {\n        const id = faces[i]._id;\n        if (newUvs1) newUvs1.push(uvs1[id]);\n        if (newUvs2) newUvs2.push(uvs2[id]);\n      }\n      if (newUvs1) this.faceVertexUvs[0] = newUvs1;\n      if (newUvs2) this.faceVertexUvs[1] = newUvs2;\n    }\n    toJSON() {\n      const data = {\n        metadata: {\n          version: 4.5,\n          type: \"Geometry\",\n          generator: \"Geometry.toJSON\"\n        }\n      };\n      data.uuid = this.uuid;\n      data.type = this.type;\n      if (this.name !== \"\") data.name = this.name;\n      if (this.parameters !== void 0) {\n        const parameters = this.parameters;\n        for (let key in parameters) {\n          if (parameters[key] !== void 0) data[key] = parameters[key];\n        }\n        return data;\n      }\n      const vertices = [];\n      for (let i = 0; i < this.vertices.length; i++) {\n        const vertex = this.vertices[i];\n        vertices.push(vertex.x, vertex.y, vertex.z);\n      }\n      const faces = [];\n      const normals = [];\n      const normalsHash = {};\n      const colors = [];\n      const colorsHash = {};\n      const uvs = [];\n      const uvsHash = {};\n      for (let i = 0; i < this.faces.length; i++) {\n        const face = this.faces[i];\n        const hasMaterial = true;\n        const hasFaceUv = false;\n        const hasFaceVertexUv = this.faceVertexUvs[0][i] !== void 0;\n        const hasFaceNormal = face.normal.length() > 0;\n        const hasFaceVertexNormal = face.vertexNormals.length > 0;\n        const hasFaceColor = face.color.r !== 1 || face.color.g !== 1 || face.color.b !== 1;\n        const hasFaceVertexColor = face.vertexColors.length > 0;\n        let faceType = 0;\n        faceType = setBit(faceType, 0, 0);\n        faceType = setBit(faceType, 1, hasMaterial);\n        faceType = setBit(faceType, 2, hasFaceUv);\n        faceType = setBit(faceType, 3, hasFaceVertexUv);\n        faceType = setBit(faceType, 4, hasFaceNormal);\n        faceType = setBit(faceType, 5, hasFaceVertexNormal);\n        faceType = setBit(faceType, 6, hasFaceColor);\n        faceType = setBit(faceType, 7, hasFaceVertexColor);\n        faces.push(faceType);\n        faces.push(face.a, face.b, face.c);\n        faces.push(face.materialIndex);\n        if (hasFaceVertexUv) {\n          const faceVertexUvs = this.faceVertexUvs[0][i];\n          faces.push(getUvIndex(faceVertexUvs[0]), getUvIndex(faceVertexUvs[1]), getUvIndex(faceVertexUvs[2]));\n        }\n        if (hasFaceNormal) {\n          faces.push(getNormalIndex(face.normal));\n        }\n        if (hasFaceVertexNormal) {\n          const vertexNormals = face.vertexNormals;\n          faces.push(getNormalIndex(vertexNormals[0]), getNormalIndex(vertexNormals[1]), getNormalIndex(vertexNormals[2]));\n        }\n        if (hasFaceColor) {\n          faces.push(getColorIndex(face.color));\n        }\n        if (hasFaceVertexColor) {\n          const vertexColors = face.vertexColors;\n          faces.push(getColorIndex(vertexColors[0]), getColorIndex(vertexColors[1]), getColorIndex(vertexColors[2]));\n        }\n      }\n      function setBit(value, position, enabled) {\n        return enabled ? value | 1 << position : value & ~(1 << position);\n      }\n      function getNormalIndex(normal) {\n        const hash = normal.x.toString() + normal.y.toString() + normal.z.toString();\n        if (normalsHash[hash] !== void 0) {\n          return normalsHash[hash];\n        }\n        normalsHash[hash] = normals.length / 3;\n        normals.push(normal.x, normal.y, normal.z);\n        return normalsHash[hash];\n      }\n      function getColorIndex(color) {\n        const hash = color.r.toString() + color.g.toString() + color.b.toString();\n        if (colorsHash[hash] !== void 0) {\n          return colorsHash[hash];\n        }\n        colorsHash[hash] = colors.length;\n        colors.push(color.getHex());\n        return colorsHash[hash];\n      }\n      function getUvIndex(uv) {\n        const hash = uv.x.toString() + uv.y.toString();\n        if (uvsHash[hash] !== void 0) {\n          return uvsHash[hash];\n        }\n        uvsHash[hash] = uvs.length / 2;\n        uvs.push(uv.x, uv.y);\n        return uvsHash[hash];\n      }\n      data.data = {};\n      data.data.vertices = vertices;\n      data.data.normals = normals;\n      if (colors.length > 0) data.data.colors = colors;\n      if (uvs.length > 0) data.data.uvs = [uvs];\n      data.data.faces = faces;\n      return data;\n    }\n    clone() {\n      return new Geometry2().copy(this);\n    }\n    copy(source) {\n      this.vertices = [];\n      this.colors = [];\n      this.faces = [];\n      this.faceVertexUvs = [[]];\n      this.morphTargets = [];\n      this.morphNormals = [];\n      this.skinWeights = [];\n      this.skinIndices = [];\n      this.lineDistances = [];\n      this.boundingBox = null;\n      this.boundingSphere = null;\n      this.name = source.name;\n      const vertices = source.vertices;\n      for (let i = 0, il = vertices.length; i < il; i++) {\n        this.vertices.push(vertices[i].clone());\n      }\n      const colors = source.colors;\n      for (let i = 0, il = colors.length; i < il; i++) {\n        this.colors.push(colors[i].clone());\n      }\n      const faces = source.faces;\n      for (let i = 0, il = faces.length; i < il; i++) {\n        this.faces.push(faces[i].clone());\n      }\n      for (let i = 0, il = source.faceVertexUvs.length; i < il; i++) {\n        const faceVertexUvs = source.faceVertexUvs[i];\n        if (this.faceVertexUvs[i] === void 0) {\n          this.faceVertexUvs[i] = [];\n        }\n        for (let j = 0, jl = faceVertexUvs.length; j < jl; j++) {\n          const uvs = faceVertexUvs[j],\n            uvsCopy = [];\n          for (let k = 0, kl = uvs.length; k < kl; k++) {\n            const uv = uvs[k];\n            uvsCopy.push(uv.clone());\n          }\n          this.faceVertexUvs[i].push(uvsCopy);\n        }\n      }\n      const morphTargets = source.morphTargets;\n      for (let i = 0, il = morphTargets.length; i < il; i++) {\n        const morphTarget = {};\n        morphTarget.name = morphTargets[i].name;\n        if (morphTargets[i].vertices !== void 0) {\n          morphTarget.vertices = [];\n          for (let j = 0, jl = morphTargets[i].vertices.length; j < jl; j++) {\n            morphTarget.vertices.push(morphTargets[i].vertices[j].clone());\n          }\n        }\n        if (morphTargets[i].normals !== void 0) {\n          morphTarget.normals = [];\n          for (let j = 0, jl = morphTargets[i].normals.length; j < jl; j++) {\n            morphTarget.normals.push(morphTargets[i].normals[j].clone());\n          }\n        }\n        this.morphTargets.push(morphTarget);\n      }\n      const morphNormals = source.morphNormals;\n      for (let i = 0, il = morphNormals.length; i < il; i++) {\n        const morphNormal = {};\n        if (morphNormals[i].vertexNormals !== void 0) {\n          morphNormal.vertexNormals = [];\n          for (let j = 0, jl = morphNormals[i].vertexNormals.length; j < jl; j++) {\n            const srcVertexNormal = morphNormals[i].vertexNormals[j];\n            const destVertexNormal = {};\n            destVertexNormal.a = srcVertexNormal.a.clone();\n            destVertexNormal.b = srcVertexNormal.b.clone();\n            destVertexNormal.c = srcVertexNormal.c.clone();\n            morphNormal.vertexNormals.push(destVertexNormal);\n          }\n        }\n        if (morphNormals[i].faceNormals !== void 0) {\n          morphNormal.faceNormals = [];\n          for (let j = 0, jl = morphNormals[i].faceNormals.length; j < jl; j++) {\n            morphNormal.faceNormals.push(morphNormals[i].faceNormals[j].clone());\n          }\n        }\n        this.morphNormals.push(morphNormal);\n      }\n      const skinWeights = source.skinWeights;\n      for (let i = 0, il = skinWeights.length; i < il; i++) {\n        this.skinWeights.push(skinWeights[i].clone());\n      }\n      const skinIndices = source.skinIndices;\n      for (let i = 0, il = skinIndices.length; i < il; i++) {\n        this.skinIndices.push(skinIndices[i].clone());\n      }\n      const lineDistances = source.lineDistances;\n      for (let i = 0, il = lineDistances.length; i < il; i++) {\n        this.lineDistances.push(lineDistances[i]);\n      }\n      const boundingBox = source.boundingBox;\n      if (boundingBox !== null) {\n        this.boundingBox = boundingBox.clone();\n      }\n      const boundingSphere = source.boundingSphere;\n      if (boundingSphere !== null) {\n        this.boundingSphere = boundingSphere.clone();\n      }\n      this.elementsNeedUpdate = source.elementsNeedUpdate;\n      this.verticesNeedUpdate = source.verticesNeedUpdate;\n      this.uvsNeedUpdate = source.uvsNeedUpdate;\n      this.normalsNeedUpdate = source.normalsNeedUpdate;\n      this.colorsNeedUpdate = source.colorsNeedUpdate;\n      this.lineDistancesNeedUpdate = source.lineDistancesNeedUpdate;\n      this.groupsNeedUpdate = source.groupsNeedUpdate;\n      return this;\n    }\n    toBufferGeometry() {\n      const geometry = new DirectGeometry().fromGeometry(this);\n      const buffergeometry = new BufferGeometry();\n      const positions = new Float32Array(geometry.vertices.length * 3);\n      buffergeometry.setAttribute(\"position\", new BufferAttribute(positions, 3).copyVector3sArray(geometry.vertices));\n      if (geometry.normals.length > 0) {\n        const normals = new Float32Array(geometry.normals.length * 3);\n        buffergeometry.setAttribute(\"normal\", new BufferAttribute(normals, 3).copyVector3sArray(geometry.normals));\n      }\n      if (geometry.colors.length > 0) {\n        const colors = new Float32Array(geometry.colors.length * 3);\n        buffergeometry.setAttribute(\"color\", new BufferAttribute(colors, 3).copyColorsArray(geometry.colors));\n      }\n      if (geometry.uvs.length > 0) {\n        const uvs = new Float32Array(geometry.uvs.length * 2);\n        buffergeometry.setAttribute(\"uv\", new BufferAttribute(uvs, 2).copyVector2sArray(geometry.uvs));\n      }\n      if (geometry.uvs2.length > 0) {\n        const uvs2 = new Float32Array(geometry.uvs2.length * 2);\n        buffergeometry.setAttribute(\"uv2\", new BufferAttribute(uvs2, 2).copyVector2sArray(geometry.uvs2));\n      }\n      buffergeometry.groups = geometry.groups;\n      for (let name in geometry.morphTargets) {\n        const array = [];\n        const morphTargets = geometry.morphTargets[name];\n        for (let i = 0, l = morphTargets.length; i < l; i++) {\n          const morphTarget = morphTargets[i];\n          const attribute = new Float32BufferAttribute(morphTarget.data.length * 3, 3);\n          attribute.name = morphTarget.name;\n          array.push(attribute.copyVector3sArray(morphTarget.data));\n        }\n        buffergeometry.morphAttributes[name] = array;\n      }\n      if (geometry.skinIndices.length > 0) {\n        const skinIndices = new Float32BufferAttribute(geometry.skinIndices.length * 4, 4);\n        buffergeometry.setAttribute(\"skinIndex\", skinIndices.copyVector4sArray(geometry.skinIndices));\n      }\n      if (geometry.skinWeights.length > 0) {\n        const skinWeights = new Float32BufferAttribute(geometry.skinWeights.length * 4, 4);\n        buffergeometry.setAttribute(\"skinWeight\", skinWeights.copyVector4sArray(geometry.skinWeights));\n      }\n      if (geometry.boundingSphere !== null) {\n        buffergeometry.boundingSphere = geometry.boundingSphere.clone();\n      }\n      if (geometry.boundingBox !== null) {\n        buffergeometry.boundingBox = geometry.boundingBox.clone();\n      }\n      return buffergeometry;\n    }\n    computeTangents() {\n      console.error(\"THREE.Geometry: .computeTangents() has been removed.\");\n    }\n    computeLineDistances() {\n      console.error(\"THREE.Geometry: .computeLineDistances() has been removed. Use THREE.Line.computeLineDistances() instead.\");\n    }\n    applyMatrix(matrix) {\n      console.warn(\"THREE.Geometry: .applyMatrix() has been renamed to .applyMatrix4().\");\n      return this.applyMatrix4(matrix);\n    }\n    dispose() {\n      this.dispatchEvent({\n        type: \"dispose\"\n      });\n    }\n  }\n  return Geometry2;\n})();\nclass DirectGeometry {\n  constructor() {\n    this.vertices = [];\n    this.normals = [];\n    this.colors = [];\n    this.uvs = [];\n    this.uvs2 = [];\n    this.groups = [];\n    this.morphTargets = {};\n    this.skinWeights = [];\n    this.skinIndices = [];\n    this.boundingBox = null;\n    this.boundingSphere = null;\n    this.verticesNeedUpdate = false;\n    this.normalsNeedUpdate = false;\n    this.colorsNeedUpdate = false;\n    this.uvsNeedUpdate = false;\n    this.groupsNeedUpdate = false;\n  }\n  computeGroups(geometry) {\n    const groups = [];\n    let group, i;\n    let materialIndex = void 0;\n    const faces = geometry.faces;\n    for (i = 0; i < faces.length; i++) {\n      const face = faces[i];\n      if (face.materialIndex !== materialIndex) {\n        materialIndex = face.materialIndex;\n        if (group !== void 0) {\n          group.count = i * 3 - group.start;\n          groups.push(group);\n        }\n        group = {\n          start: i * 3,\n          materialIndex\n        };\n      }\n    }\n    if (group !== void 0) {\n      group.count = i * 3 - group.start;\n      groups.push(group);\n    }\n    this.groups = groups;\n  }\n  fromGeometry(geometry) {\n    const faces = geometry.faces;\n    const vertices = geometry.vertices;\n    const faceVertexUvs = geometry.faceVertexUvs;\n    const hasFaceVertexUv = faceVertexUvs[0] && faceVertexUvs[0].length > 0;\n    const hasFaceVertexUv2 = faceVertexUvs[1] && faceVertexUvs[1].length > 0;\n    const morphTargets = geometry.morphTargets;\n    const morphTargetsLength = morphTargets.length;\n    let morphTargetsPosition;\n    if (morphTargetsLength > 0) {\n      morphTargetsPosition = [];\n      for (let i = 0; i < morphTargetsLength; i++) {\n        morphTargetsPosition[i] = {\n          name: morphTargets[i].name,\n          data: []\n        };\n      }\n      this.morphTargets.position = morphTargetsPosition;\n    }\n    const morphNormals = geometry.morphNormals;\n    const morphNormalsLength = morphNormals.length;\n    let morphTargetsNormal;\n    if (morphNormalsLength > 0) {\n      morphTargetsNormal = [];\n      for (let i = 0; i < morphNormalsLength; i++) {\n        morphTargetsNormal[i] = {\n          name: morphNormals[i].name,\n          data: []\n        };\n      }\n      this.morphTargets.normal = morphTargetsNormal;\n    }\n    const skinIndices = geometry.skinIndices;\n    const skinWeights = geometry.skinWeights;\n    const hasSkinIndices = skinIndices.length === vertices.length;\n    const hasSkinWeights = skinWeights.length === vertices.length;\n    if (vertices.length > 0 && faces.length === 0) {\n      console.error(\"THREE.DirectGeometry: Faceless geometries are not supported.\");\n    }\n    for (let i = 0; i < faces.length; i++) {\n      const face = faces[i];\n      this.vertices.push(vertices[face.a], vertices[face.b], vertices[face.c]);\n      const vertexNormals = face.vertexNormals;\n      if (vertexNormals.length === 3) {\n        this.normals.push(vertexNormals[0], vertexNormals[1], vertexNormals[2]);\n      } else {\n        const normal = face.normal;\n        this.normals.push(normal, normal, normal);\n      }\n      const vertexColors = face.vertexColors;\n      if (vertexColors.length === 3) {\n        this.colors.push(vertexColors[0], vertexColors[1], vertexColors[2]);\n      } else {\n        const color = face.color;\n        this.colors.push(color, color, color);\n      }\n      if (hasFaceVertexUv === true) {\n        const vertexUvs = faceVertexUvs[0][i];\n        if (vertexUvs !== void 0) {\n          this.uvs.push(vertexUvs[0], vertexUvs[1], vertexUvs[2]);\n        } else {\n          console.warn(\"THREE.DirectGeometry.fromGeometry(): Undefined vertexUv \", i);\n          this.uvs.push(new Vector2(), new Vector2(), new Vector2());\n        }\n      }\n      if (hasFaceVertexUv2 === true) {\n        const vertexUvs = faceVertexUvs[1][i];\n        if (vertexUvs !== void 0) {\n          this.uvs2.push(vertexUvs[0], vertexUvs[1], vertexUvs[2]);\n        } else {\n          console.warn(\"THREE.DirectGeometry.fromGeometry(): Undefined vertexUv2 \", i);\n          this.uvs2.push(new Vector2(), new Vector2(), new Vector2());\n        }\n      }\n      for (let j = 0; j < morphTargetsLength; j++) {\n        const morphTarget = morphTargets[j].vertices;\n        morphTargetsPosition[j].data.push(morphTarget[face.a], morphTarget[face.b], morphTarget[face.c]);\n      }\n      for (let j = 0; j < morphNormalsLength; j++) {\n        const morphNormal = morphNormals[j].vertexNormals[i];\n        morphTargetsNormal[j].data.push(morphNormal.a, morphNormal.b, morphNormal.c);\n      }\n      if (hasSkinIndices) {\n        this.skinIndices.push(skinIndices[face.a], skinIndices[face.b], skinIndices[face.c]);\n      }\n      if (hasSkinWeights) {\n        this.skinWeights.push(skinWeights[face.a], skinWeights[face.b], skinWeights[face.c]);\n      }\n    }\n    this.computeGroups(geometry);\n    this.verticesNeedUpdate = geometry.verticesNeedUpdate;\n    this.normalsNeedUpdate = geometry.normalsNeedUpdate;\n    this.colorsNeedUpdate = geometry.colorsNeedUpdate;\n    this.uvsNeedUpdate = geometry.uvsNeedUpdate;\n    this.groupsNeedUpdate = geometry.groupsNeedUpdate;\n    if (geometry.boundingSphere !== null) {\n      this.boundingSphere = geometry.boundingSphere.clone();\n    }\n    if (geometry.boundingBox !== null) {\n      this.boundingBox = geometry.boundingBox.clone();\n    }\n    return this;\n  }\n}\nclass Face3 {\n  constructor(a, b, c, normal, color, materialIndex = 0) {\n    this.a = a;\n    this.b = b;\n    this.c = c;\n    this.normal = normal && normal.isVector3 ? normal : new Vector3();\n    this.vertexNormals = Array.isArray(normal) ? normal : [];\n    this.color = color && color.isColor ? color : new Color();\n    this.vertexColors = Array.isArray(color) ? color : [];\n    this.materialIndex = materialIndex;\n  }\n  clone() {\n    return new this.constructor().copy(this);\n  }\n  copy(source) {\n    this.a = source.a;\n    this.b = source.b;\n    this.c = source.c;\n    this.normal.copy(source.normal);\n    this.color.copy(source.color);\n    this.materialIndex = source.materialIndex;\n    for (let i = 0, il = source.vertexNormals.length; i < il; i++) {\n      this.vertexNormals[i] = source.vertexNormals[i].clone();\n    }\n    for (let i = 0, il = source.vertexColors.length; i < il; i++) {\n      this.vertexColors[i] = source.vertexColors[i].clone();\n    }\n    return this;\n  }\n}\nexport { Face3, Geometry };", "map": {"version": 3, "names": ["_m1", "Matrix4", "_obj", "Object3D", "_offset", "Vector3", "Geometry", "Geometry2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createBufferGeometryFromObject", "object", "buffergeometry", "BufferGeometry", "geometry", "isPoints", "isLine", "positions", "Float32BufferAttribute", "vertices", "length", "colors", "setAttribute", "copyVector3sArray", "copyColorsArray", "lineDistances", "copyArray", "boundingSphere", "clone", "boundingBox", "<PERSON><PERSON><PERSON>", "toBufferGeometry", "constructor", "isGeometry", "uuid", "MathUtils", "generateUUID", "name", "type", "faces", "faceVertexUvs", "morphTargets", "morphNormals", "skinWeights", "skinIndices", "elementsNeedUpdate", "verticesNeedUpdate", "uvsNeedUpdate", "normalsNeedUpdate", "colorsNeedUpdate", "lineDistancesNeedUpdate", "groupsNeedUpdate", "applyMatrix4", "matrix", "normalMatrix", "Matrix3", "getNormalMatrix", "i", "il", "vertex", "face", "normal", "applyMatrix3", "normalize", "j", "jl", "vertexNormals", "computeBoundingBox", "computeBoundingSphere", "rotateX", "angle", "makeRotationX", "rotateY", "makeRotationY", "rotateZ", "makeRotationZ", "translate", "x", "y", "z", "makeTranslation", "scale", "makeScale", "lookAt", "vector", "updateMatrix", "fromBufferGeometry", "scope", "index", "attributes", "position", "console", "error", "color", "uv", "uv2", "count", "push", "fromBufferAttribute", "Color", "addFace", "a", "b", "c", "materialIndex", "vertexColors", "Face3", "Vector2", "groups", "group", "start", "getX", "computeFaceNormals", "center", "getCenter", "negate", "radius", "s", "set", "cb", "ab", "f", "fl", "vA", "vB", "vC", "subVectors", "cross", "copy", "computeVertexNormals", "areaWeighted", "Array", "v", "vl", "add", "computeFlatVertexNormals", "computeMorphNormals", "__originalFaceNormal", "__originalVertexNormals", "tmpGeo", "faceNormals", "dstNormalsFace", "dstNormalsVertex", "faceNormal", "Box3", "setFromPoints", "Sphere", "merge", "materialIndexOffset", "vertexOffset", "vertices1", "vertices2", "faces1", "faces2", "colors1", "colors2", "vertexCopy", "faceVertexNormals", "faceVertexColors", "faceCopy", "faceVertexUvs2", "uvs2", "uvsCopy", "k", "kl", "<PERSON><PERSON>esh", "mesh", "matrixAutoUpdate", "mergeVertices", "precisionPoints", "verticesMap", "unique", "changes", "precision", "Math", "pow", "key", "round", "faceIndicesToRemove", "indices", "n", "idx", "splice", "diff", "points", "l", "point", "sortFacesByMaterialIndex", "_id", "materialIndexSort", "sort", "uvs1", "newUvs1", "newUvs2", "id", "toJSON", "data", "metadata", "version", "generator", "parameters", "normals", "normalsHash", "colorsHash", "uvs", "uvsHash", "hasMaterial", "hasFaceUv", "hasFaceVertexUv", "hasFaceNormal", "hasFaceVertexNormal", "hasFaceColor", "r", "g", "hasFaceVertexColor", "faceType", "setBit", "getUvIndex", "getNormalIndex", "getColorIndex", "value", "enabled", "hash", "toString", "getHex", "source", "morph<PERSON>arget", "morphNormal", "srcVertexNormal", "destVertexNormal", "DirectGeometry", "fromGeometry", "Float32Array", "BufferAttribute", "copyVector2sArray", "array", "attribute", "morphAttributes", "copyVector4sArray", "computeTangents", "computeLineDistances", "applyMatrix", "warn", "dispose", "dispatchEvent", "computeGroups", "hasFaceVertexUv2", "morphTar<PERSON><PERSON><PERSON><PERSON>", "morphTargetsPosition", "morphNorm<PERSON><PERSON><PERSON>th", "morphTargetsNormal", "hasSkinIndices", "hasSkinWeights", "vertexUvs", "isVector3", "isArray", "isColor"], "sources": ["F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\node_modules\\src\\deprecated\\Geometry.js"], "sourcesContent": ["import {\n  Box3,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>ribute,\n  BufferGeometry,\n  Color,\n  EventDispatcher,\n  Float32BufferAttribute,\n  Matrix3,\n  Matrix4,\n  MathUtils,\n  Object3D,\n  Sphere,\n  Vector2,\n  Vector3,\n} from 'three'\n\nconst _m1 = /* @__PURE__ */ new Matrix4()\nconst _obj = /* @__PURE__ */ new Object3D()\nconst _offset = /* @__PURE__ */ new Vector3()\n\nconst Geometry = /* @__PURE__ */ (() => {\n  class Geometry extends EventDispatcher {\n    static createBufferGeometryFromObject(object) {\n      let buffergeometry = new BufferGeometry()\n\n      const geometry = object.geometry\n\n      if (object.isPoints || object.isLine) {\n        const positions = new Float32BufferAttribute(geometry.vertices.length * 3, 3)\n        const colors = new Float32BufferAttribute(geometry.colors.length * 3, 3)\n\n        buffergeometry.setAttribute('position', positions.copyVector3sArray(geometry.vertices))\n        buffergeometry.setAttribute('color', colors.copyColorsArray(geometry.colors))\n\n        if (geometry.lineDistances && geometry.lineDistances.length === geometry.vertices.length) {\n          const lineDistances = new Float32BufferAttribute(geometry.lineDistances.length, 1)\n\n          buffergeometry.setAttribute('lineDistance', lineDistances.copyArray(geometry.lineDistances))\n        }\n\n        if (geometry.boundingSphere !== null) {\n          buffergeometry.boundingSphere = geometry.boundingSphere.clone()\n        }\n\n        if (geometry.boundingBox !== null) {\n          buffergeometry.boundingBox = geometry.boundingBox.clone()\n        }\n      } else if (object.isMesh) {\n        buffergeometry = geometry.toBufferGeometry()\n      }\n\n      return buffergeometry\n    }\n\n    constructor() {\n      super()\n      this.isGeometry = true\n      this.uuid = MathUtils.generateUUID()\n\n      this.name = ''\n      this.type = 'Geometry'\n\n      this.vertices = []\n      this.colors = []\n      this.faces = []\n      this.faceVertexUvs = [[]]\n\n      this.morphTargets = []\n      this.morphNormals = []\n\n      this.skinWeights = []\n      this.skinIndices = []\n\n      this.lineDistances = []\n\n      this.boundingBox = null\n      this.boundingSphere = null\n\n      // update flags\n\n      this.elementsNeedUpdate = false\n      this.verticesNeedUpdate = false\n      this.uvsNeedUpdate = false\n      this.normalsNeedUpdate = false\n      this.colorsNeedUpdate = false\n      this.lineDistancesNeedUpdate = false\n      this.groupsNeedUpdate = false\n    }\n\n    applyMatrix4(matrix) {\n      const normalMatrix = new Matrix3().getNormalMatrix(matrix)\n\n      for (let i = 0, il = this.vertices.length; i < il; i++) {\n        const vertex = this.vertices[i]\n        vertex.applyMatrix4(matrix)\n      }\n\n      for (let i = 0, il = this.faces.length; i < il; i++) {\n        const face = this.faces[i]\n        face.normal.applyMatrix3(normalMatrix).normalize()\n\n        for (let j = 0, jl = face.vertexNormals.length; j < jl; j++) {\n          face.vertexNormals[j].applyMatrix3(normalMatrix).normalize()\n        }\n      }\n\n      if (this.boundingBox !== null) {\n        this.computeBoundingBox()\n      }\n\n      if (this.boundingSphere !== null) {\n        this.computeBoundingSphere()\n      }\n\n      this.verticesNeedUpdate = true\n      this.normalsNeedUpdate = true\n\n      return this\n    }\n\n    rotateX(angle) {\n      // rotate geometry around world x-axis\n\n      _m1.makeRotationX(angle)\n\n      this.applyMatrix4(_m1)\n\n      return this\n    }\n\n    rotateY(angle) {\n      // rotate geometry around world y-axis\n\n      _m1.makeRotationY(angle)\n\n      this.applyMatrix4(_m1)\n\n      return this\n    }\n\n    rotateZ(angle) {\n      // rotate geometry around world z-axis\n\n      _m1.makeRotationZ(angle)\n\n      this.applyMatrix4(_m1)\n\n      return this\n    }\n\n    translate(x, y, z) {\n      // translate geometry\n\n      _m1.makeTranslation(x, y, z)\n\n      this.applyMatrix4(_m1)\n\n      return this\n    }\n\n    scale(x, y, z) {\n      // scale geometry\n\n      _m1.makeScale(x, y, z)\n\n      this.applyMatrix4(_m1)\n\n      return this\n    }\n\n    lookAt(vector) {\n      _obj.lookAt(vector)\n\n      _obj.updateMatrix()\n\n      this.applyMatrix4(_obj.matrix)\n\n      return this\n    }\n\n    fromBufferGeometry(geometry) {\n      const scope = this\n\n      const index = geometry.index !== null ? geometry.index : undefined\n      const attributes = geometry.attributes\n\n      if (attributes.position === undefined) {\n        console.error('THREE.Geometry.fromBufferGeometry(): Position attribute required for conversion.')\n        return this\n      }\n\n      const position = attributes.position\n      const normal = attributes.normal\n      const color = attributes.color\n      const uv = attributes.uv\n      const uv2 = attributes.uv2\n\n      if (uv2 !== undefined) this.faceVertexUvs[1] = []\n\n      for (let i = 0; i < position.count; i++) {\n        scope.vertices.push(new Vector3().fromBufferAttribute(position, i))\n\n        if (color !== undefined) {\n          scope.colors.push(new Color().fromBufferAttribute(color, i))\n        }\n      }\n\n      function addFace(a, b, c, materialIndex) {\n        const vertexColors =\n          color === undefined ? [] : [scope.colors[a].clone(), scope.colors[b].clone(), scope.colors[c].clone()]\n\n        const vertexNormals =\n          normal === undefined\n            ? []\n            : [\n                new Vector3().fromBufferAttribute(normal, a),\n                new Vector3().fromBufferAttribute(normal, b),\n                new Vector3().fromBufferAttribute(normal, c),\n              ]\n\n        const face = new Face3(a, b, c, vertexNormals, vertexColors, materialIndex)\n\n        scope.faces.push(face)\n\n        if (uv !== undefined) {\n          scope.faceVertexUvs[0].push([\n            new Vector2().fromBufferAttribute(uv, a),\n            new Vector2().fromBufferAttribute(uv, b),\n            new Vector2().fromBufferAttribute(uv, c),\n          ])\n        }\n\n        if (uv2 !== undefined) {\n          scope.faceVertexUvs[1].push([\n            new Vector2().fromBufferAttribute(uv2, a),\n            new Vector2().fromBufferAttribute(uv2, b),\n            new Vector2().fromBufferAttribute(uv2, c),\n          ])\n        }\n      }\n\n      const groups = geometry.groups\n\n      if (groups.length > 0) {\n        for (let i = 0; i < groups.length; i++) {\n          const group = groups[i]\n\n          const start = group.start\n          const count = group.count\n\n          for (let j = start, jl = start + count; j < jl; j += 3) {\n            if (index !== undefined) {\n              addFace(index.getX(j), index.getX(j + 1), index.getX(j + 2), group.materialIndex)\n            } else {\n              addFace(j, j + 1, j + 2, group.materialIndex)\n            }\n          }\n        }\n      } else {\n        if (index !== undefined) {\n          for (let i = 0; i < index.count; i += 3) {\n            addFace(index.getX(i), index.getX(i + 1), index.getX(i + 2))\n          }\n        } else {\n          for (let i = 0; i < position.count; i += 3) {\n            addFace(i, i + 1, i + 2)\n          }\n        }\n      }\n\n      this.computeFaceNormals()\n\n      if (geometry.boundingBox !== null) {\n        this.boundingBox = geometry.boundingBox.clone()\n      }\n\n      if (geometry.boundingSphere !== null) {\n        this.boundingSphere = geometry.boundingSphere.clone()\n      }\n\n      return this\n    }\n\n    center() {\n      this.computeBoundingBox()\n\n      this.boundingBox.getCenter(_offset).negate()\n\n      this.translate(_offset.x, _offset.y, _offset.z)\n\n      return this\n    }\n\n    normalize() {\n      this.computeBoundingSphere()\n\n      const center = this.boundingSphere.center\n      const radius = this.boundingSphere.radius\n\n      const s = radius === 0 ? 1 : 1.0 / radius\n\n      const matrix = new Matrix4()\n      matrix.set(s, 0, 0, -s * center.x, 0, s, 0, -s * center.y, 0, 0, s, -s * center.z, 0, 0, 0, 1)\n\n      this.applyMatrix4(matrix)\n\n      return this\n    }\n\n    computeFaceNormals() {\n      const cb = new Vector3(),\n        ab = new Vector3()\n\n      for (let f = 0, fl = this.faces.length; f < fl; f++) {\n        const face = this.faces[f]\n\n        const vA = this.vertices[face.a]\n        const vB = this.vertices[face.b]\n        const vC = this.vertices[face.c]\n\n        cb.subVectors(vC, vB)\n        ab.subVectors(vA, vB)\n        cb.cross(ab)\n\n        cb.normalize()\n\n        face.normal.copy(cb)\n      }\n    }\n\n    computeVertexNormals(areaWeighted = true) {\n      const vertices = new Array(this.vertices.length)\n\n      for (let v = 0, vl = this.vertices.length; v < vl; v++) {\n        vertices[v] = new Vector3()\n      }\n\n      if (areaWeighted) {\n        // vertex normals weighted by triangle areas\n        // http://www.iquilezles.org/www/articles/normals/normals.htm\n\n        const cb = new Vector3(),\n          ab = new Vector3()\n\n        for (let f = 0, fl = this.faces.length; f < fl; f++) {\n          const face = this.faces[f]\n\n          const vA = this.vertices[face.a]\n          const vB = this.vertices[face.b]\n          const vC = this.vertices[face.c]\n\n          cb.subVectors(vC, vB)\n          ab.subVectors(vA, vB)\n          cb.cross(ab)\n\n          vertices[face.a].add(cb)\n          vertices[face.b].add(cb)\n          vertices[face.c].add(cb)\n        }\n      } else {\n        this.computeFaceNormals()\n\n        for (let f = 0, fl = this.faces.length; f < fl; f++) {\n          const face = this.faces[f]\n\n          vertices[face.a].add(face.normal)\n          vertices[face.b].add(face.normal)\n          vertices[face.c].add(face.normal)\n        }\n      }\n\n      for (let v = 0, vl = this.vertices.length; v < vl; v++) {\n        vertices[v].normalize()\n      }\n\n      for (let f = 0, fl = this.faces.length; f < fl; f++) {\n        const face = this.faces[f]\n\n        const vertexNormals = face.vertexNormals\n\n        if (vertexNormals.length === 3) {\n          vertexNormals[0].copy(vertices[face.a])\n          vertexNormals[1].copy(vertices[face.b])\n          vertexNormals[2].copy(vertices[face.c])\n        } else {\n          vertexNormals[0] = vertices[face.a].clone()\n          vertexNormals[1] = vertices[face.b].clone()\n          vertexNormals[2] = vertices[face.c].clone()\n        }\n      }\n\n      if (this.faces.length > 0) {\n        this.normalsNeedUpdate = true\n      }\n    }\n\n    computeFlatVertexNormals() {\n      this.computeFaceNormals()\n\n      for (let f = 0, fl = this.faces.length; f < fl; f++) {\n        const face = this.faces[f]\n\n        const vertexNormals = face.vertexNormals\n\n        if (vertexNormals.length === 3) {\n          vertexNormals[0].copy(face.normal)\n          vertexNormals[1].copy(face.normal)\n          vertexNormals[2].copy(face.normal)\n        } else {\n          vertexNormals[0] = face.normal.clone()\n          vertexNormals[1] = face.normal.clone()\n          vertexNormals[2] = face.normal.clone()\n        }\n      }\n\n      if (this.faces.length > 0) {\n        this.normalsNeedUpdate = true\n      }\n    }\n\n    computeMorphNormals() {\n      // save original normals\n      // - create temp variables on first access\n      //   otherwise just copy (for faster repeated calls)\n\n      for (let f = 0, fl = this.faces.length; f < fl; f++) {\n        const face = this.faces[f]\n\n        if (!face.__originalFaceNormal) {\n          face.__originalFaceNormal = face.normal.clone()\n        } else {\n          face.__originalFaceNormal.copy(face.normal)\n        }\n\n        if (!face.__originalVertexNormals) face.__originalVertexNormals = []\n\n        for (let i = 0, il = face.vertexNormals.length; i < il; i++) {\n          if (!face.__originalVertexNormals[i]) {\n            face.__originalVertexNormals[i] = face.vertexNormals[i].clone()\n          } else {\n            face.__originalVertexNormals[i].copy(face.vertexNormals[i])\n          }\n        }\n      }\n\n      // use temp geometry to compute face and vertex normals for each morph\n\n      const tmpGeo = new Geometry()\n      tmpGeo.faces = this.faces\n\n      for (let i = 0, il = this.morphTargets.length; i < il; i++) {\n        // create on first access\n\n        if (!this.morphNormals[i]) {\n          this.morphNormals[i] = {}\n          this.morphNormals[i].faceNormals = []\n          this.morphNormals[i].vertexNormals = []\n\n          const dstNormalsFace = this.morphNormals[i].faceNormals\n          const dstNormalsVertex = this.morphNormals[i].vertexNormals\n\n          for (let f = 0, fl = this.faces.length; f < fl; f++) {\n            const faceNormal = new Vector3()\n            const vertexNormals = {\n              a: new Vector3(),\n              b: new Vector3(),\n              c: new Vector3(),\n            }\n\n            dstNormalsFace.push(faceNormal)\n            dstNormalsVertex.push(vertexNormals)\n          }\n        }\n\n        const morphNormals = this.morphNormals[i]\n\n        // set vertices to morph target\n\n        tmpGeo.vertices = this.morphTargets[i].vertices\n\n        // compute morph normals\n\n        tmpGeo.computeFaceNormals()\n        tmpGeo.computeVertexNormals()\n\n        // store morph normals\n\n        for (let f = 0, fl = this.faces.length; f < fl; f++) {\n          const face = this.faces[f]\n\n          const faceNormal = morphNormals.faceNormals[f]\n          const vertexNormals = morphNormals.vertexNormals[f]\n\n          faceNormal.copy(face.normal)\n\n          vertexNormals.a.copy(face.vertexNormals[0])\n          vertexNormals.b.copy(face.vertexNormals[1])\n          vertexNormals.c.copy(face.vertexNormals[2])\n        }\n      }\n\n      // restore original normals\n\n      for (let f = 0, fl = this.faces.length; f < fl; f++) {\n        const face = this.faces[f]\n\n        face.normal = face.__originalFaceNormal\n        face.vertexNormals = face.__originalVertexNormals\n      }\n    }\n\n    computeBoundingBox() {\n      if (this.boundingBox === null) {\n        this.boundingBox = new Box3()\n      }\n\n      this.boundingBox.setFromPoints(this.vertices)\n    }\n\n    computeBoundingSphere() {\n      if (this.boundingSphere === null) {\n        this.boundingSphere = new Sphere()\n      }\n\n      this.boundingSphere.setFromPoints(this.vertices)\n    }\n\n    merge(geometry, matrix, materialIndexOffset = 0) {\n      if (!(geometry && geometry.isGeometry)) {\n        console.error('THREE.Geometry.merge(): geometry not an instance of THREE.Geometry.', geometry)\n        return\n      }\n\n      let normalMatrix\n      const vertexOffset = this.vertices.length,\n        vertices1 = this.vertices,\n        vertices2 = geometry.vertices,\n        faces1 = this.faces,\n        faces2 = geometry.faces,\n        colors1 = this.colors,\n        colors2 = geometry.colors\n\n      if (matrix !== undefined) {\n        normalMatrix = new Matrix3().getNormalMatrix(matrix)\n      }\n\n      // vertices\n\n      for (let i = 0, il = vertices2.length; i < il; i++) {\n        const vertex = vertices2[i]\n\n        const vertexCopy = vertex.clone()\n\n        if (matrix !== undefined) vertexCopy.applyMatrix4(matrix)\n\n        vertices1.push(vertexCopy)\n      }\n\n      // colors\n\n      for (let i = 0, il = colors2.length; i < il; i++) {\n        colors1.push(colors2[i].clone())\n      }\n\n      // faces\n\n      for (let i = 0, il = faces2.length; i < il; i++) {\n        const face = faces2[i]\n        let normal, color\n        const faceVertexNormals = face.vertexNormals,\n          faceVertexColors = face.vertexColors\n\n        const faceCopy = new Face3(face.a + vertexOffset, face.b + vertexOffset, face.c + vertexOffset)\n        faceCopy.normal.copy(face.normal)\n\n        if (normalMatrix !== undefined) {\n          faceCopy.normal.applyMatrix3(normalMatrix).normalize()\n        }\n\n        for (let j = 0, jl = faceVertexNormals.length; j < jl; j++) {\n          normal = faceVertexNormals[j].clone()\n\n          if (normalMatrix !== undefined) {\n            normal.applyMatrix3(normalMatrix).normalize()\n          }\n\n          faceCopy.vertexNormals.push(normal)\n        }\n\n        faceCopy.color.copy(face.color)\n\n        for (let j = 0, jl = faceVertexColors.length; j < jl; j++) {\n          color = faceVertexColors[j]\n          faceCopy.vertexColors.push(color.clone())\n        }\n\n        faceCopy.materialIndex = face.materialIndex + materialIndexOffset\n\n        faces1.push(faceCopy)\n      }\n\n      // uvs\n\n      for (let i = 0, il = geometry.faceVertexUvs.length; i < il; i++) {\n        const faceVertexUvs2 = geometry.faceVertexUvs[i]\n\n        if (this.faceVertexUvs[i] === undefined) this.faceVertexUvs[i] = []\n\n        for (let j = 0, jl = faceVertexUvs2.length; j < jl; j++) {\n          const uvs2 = faceVertexUvs2[j],\n            uvsCopy = []\n\n          for (let k = 0, kl = uvs2.length; k < kl; k++) {\n            uvsCopy.push(uvs2[k].clone())\n          }\n\n          this.faceVertexUvs[i].push(uvsCopy)\n        }\n      }\n    }\n\n    mergeMesh(mesh) {\n      if (!(mesh && mesh.isMesh)) {\n        console.error('THREE.Geometry.mergeMesh(): mesh not an instance of THREE.Mesh.', mesh)\n        return\n      }\n\n      if (mesh.matrixAutoUpdate) mesh.updateMatrix()\n\n      this.merge(mesh.geometry, mesh.matrix)\n    }\n\n    /*\n     * Checks for duplicate vertices with hashmap.\n     * Duplicated vertices are removed\n     * and faces' vertices are updated.\n     */\n\n    mergeVertices(precisionPoints = 4) {\n      const verticesMap = {} // Hashmap for looking up vertices by position coordinates (and making sure they are unique)\n      const unique = [],\n        changes = []\n\n      const precision = Math.pow(10, precisionPoints)\n\n      for (let i = 0, il = this.vertices.length; i < il; i++) {\n        const v = this.vertices[i]\n        const key = `${Math.round(v.x * precision)}_${Math.round(v.y * precision)}_${Math.round(v.z * precision)}`\n\n        if (verticesMap[key] === undefined) {\n          verticesMap[key] = i\n          unique.push(this.vertices[i])\n          changes[i] = unique.length - 1\n        } else {\n          //console.log('Duplicate vertex found. ', i, ' could be using ', verticesMap[key]);\n          changes[i] = changes[verticesMap[key]]\n        }\n      }\n\n      // if faces are completely degenerate after merging vertices, we\n      // have to remove them from the geometry.\n      const faceIndicesToRemove = []\n\n      for (let i = 0, il = this.faces.length; i < il; i++) {\n        const face = this.faces[i]\n\n        face.a = changes[face.a]\n        face.b = changes[face.b]\n        face.c = changes[face.c]\n\n        const indices = [face.a, face.b, face.c]\n\n        // if any duplicate vertices are found in a Face3\n        // we have to remove the face as nothing can be saved\n        for (let n = 0; n < 3; n++) {\n          if (indices[n] === indices[(n + 1) % 3]) {\n            faceIndicesToRemove.push(i)\n            break\n          }\n        }\n      }\n\n      for (let i = faceIndicesToRemove.length - 1; i >= 0; i--) {\n        const idx = faceIndicesToRemove[i]\n\n        this.faces.splice(idx, 1)\n\n        for (let j = 0, jl = this.faceVertexUvs.length; j < jl; j++) {\n          this.faceVertexUvs[j].splice(idx, 1)\n        }\n      }\n\n      // Use unique set of vertices\n\n      const diff = this.vertices.length - unique.length\n      this.vertices = unique\n      return diff\n    }\n\n    setFromPoints(points) {\n      this.vertices = []\n\n      for (let i = 0, l = points.length; i < l; i++) {\n        const point = points[i]\n        this.vertices.push(new Vector3(point.x, point.y, point.z || 0))\n      }\n\n      return this\n    }\n\n    sortFacesByMaterialIndex() {\n      const faces = this.faces\n      const length = faces.length\n\n      // tag faces\n\n      for (let i = 0; i < length; i++) {\n        faces[i]._id = i\n      }\n\n      // sort faces\n\n      function materialIndexSort(a, b) {\n        return a.materialIndex - b.materialIndex\n      }\n\n      faces.sort(materialIndexSort)\n\n      // sort uvs\n\n      const uvs1 = this.faceVertexUvs[0]\n      const uvs2 = this.faceVertexUvs[1]\n\n      let newUvs1, newUvs2\n\n      if (uvs1 && uvs1.length === length) newUvs1 = []\n      if (uvs2 && uvs2.length === length) newUvs2 = []\n\n      for (let i = 0; i < length; i++) {\n        const id = faces[i]._id\n\n        if (newUvs1) newUvs1.push(uvs1[id])\n        if (newUvs2) newUvs2.push(uvs2[id])\n      }\n\n      if (newUvs1) this.faceVertexUvs[0] = newUvs1\n      if (newUvs2) this.faceVertexUvs[1] = newUvs2\n    }\n\n    toJSON() {\n      const data = {\n        metadata: {\n          version: 4.5,\n          type: 'Geometry',\n          generator: 'Geometry.toJSON',\n        },\n      }\n\n      // standard Geometry serialization\n\n      data.uuid = this.uuid\n      data.type = this.type\n      if (this.name !== '') data.name = this.name\n\n      if (this.parameters !== undefined) {\n        const parameters = this.parameters\n\n        for (let key in parameters) {\n          if (parameters[key] !== undefined) data[key] = parameters[key]\n        }\n\n        return data\n      }\n\n      const vertices = []\n\n      for (let i = 0; i < this.vertices.length; i++) {\n        const vertex = this.vertices[i]\n        vertices.push(vertex.x, vertex.y, vertex.z)\n      }\n\n      const faces = []\n      const normals = []\n      const normalsHash = {}\n      const colors = []\n      const colorsHash = {}\n      const uvs = []\n      const uvsHash = {}\n\n      for (let i = 0; i < this.faces.length; i++) {\n        const face = this.faces[i]\n\n        const hasMaterial = true\n        const hasFaceUv = false // deprecated\n        const hasFaceVertexUv = this.faceVertexUvs[0][i] !== undefined\n        const hasFaceNormal = face.normal.length() > 0\n        const hasFaceVertexNormal = face.vertexNormals.length > 0\n        const hasFaceColor = face.color.r !== 1 || face.color.g !== 1 || face.color.b !== 1\n        const hasFaceVertexColor = face.vertexColors.length > 0\n\n        let faceType = 0\n\n        faceType = setBit(faceType, 0, 0) // isQuad\n        faceType = setBit(faceType, 1, hasMaterial)\n        faceType = setBit(faceType, 2, hasFaceUv)\n        faceType = setBit(faceType, 3, hasFaceVertexUv)\n        faceType = setBit(faceType, 4, hasFaceNormal)\n        faceType = setBit(faceType, 5, hasFaceVertexNormal)\n        faceType = setBit(faceType, 6, hasFaceColor)\n        faceType = setBit(faceType, 7, hasFaceVertexColor)\n\n        faces.push(faceType)\n        faces.push(face.a, face.b, face.c)\n        faces.push(face.materialIndex)\n\n        if (hasFaceVertexUv) {\n          const faceVertexUvs = this.faceVertexUvs[0][i]\n\n          faces.push(getUvIndex(faceVertexUvs[0]), getUvIndex(faceVertexUvs[1]), getUvIndex(faceVertexUvs[2]))\n        }\n\n        if (hasFaceNormal) {\n          faces.push(getNormalIndex(face.normal))\n        }\n\n        if (hasFaceVertexNormal) {\n          const vertexNormals = face.vertexNormals\n\n          faces.push(\n            getNormalIndex(vertexNormals[0]),\n            getNormalIndex(vertexNormals[1]),\n            getNormalIndex(vertexNormals[2]),\n          )\n        }\n\n        if (hasFaceColor) {\n          faces.push(getColorIndex(face.color))\n        }\n\n        if (hasFaceVertexColor) {\n          const vertexColors = face.vertexColors\n\n          faces.push(getColorIndex(vertexColors[0]), getColorIndex(vertexColors[1]), getColorIndex(vertexColors[2]))\n        }\n      }\n\n      function setBit(value, position, enabled) {\n        return enabled ? value | (1 << position) : value & ~(1 << position)\n      }\n\n      function getNormalIndex(normal) {\n        const hash = normal.x.toString() + normal.y.toString() + normal.z.toString()\n\n        if (normalsHash[hash] !== undefined) {\n          return normalsHash[hash]\n        }\n\n        normalsHash[hash] = normals.length / 3\n        normals.push(normal.x, normal.y, normal.z)\n\n        return normalsHash[hash]\n      }\n\n      function getColorIndex(color) {\n        const hash = color.r.toString() + color.g.toString() + color.b.toString()\n\n        if (colorsHash[hash] !== undefined) {\n          return colorsHash[hash]\n        }\n\n        colorsHash[hash] = colors.length\n        colors.push(color.getHex())\n\n        return colorsHash[hash]\n      }\n\n      function getUvIndex(uv) {\n        const hash = uv.x.toString() + uv.y.toString()\n\n        if (uvsHash[hash] !== undefined) {\n          return uvsHash[hash]\n        }\n\n        uvsHash[hash] = uvs.length / 2\n        uvs.push(uv.x, uv.y)\n\n        return uvsHash[hash]\n      }\n\n      data.data = {}\n\n      data.data.vertices = vertices\n      data.data.normals = normals\n      if (colors.length > 0) data.data.colors = colors\n      if (uvs.length > 0) data.data.uvs = [uvs] // temporal backward compatibility\n      data.data.faces = faces\n\n      return data\n    }\n\n    clone() {\n      /*\n\t\t // Handle primitives\n\n\t\t const parameters = this.parameters;\n\n\t\t if ( parameters !== undefined ) {\n\n\t\t const values = [];\n\n\t\t for ( const key in parameters ) {\n\n\t\t values.push( parameters[ key ] );\n\n\t\t }\n\n\t\t const geometry = Object.create( this.constructor.prototype );\n\t\t this.constructor.apply( geometry, values );\n\t\t return geometry;\n\n\t\t }\n\n\t\t return new this.constructor().copy( this );\n\t\t */\n\n      return new Geometry().copy(this)\n    }\n\n    copy(source) {\n      // reset\n\n      this.vertices = []\n      this.colors = []\n      this.faces = []\n      this.faceVertexUvs = [[]]\n      this.morphTargets = []\n      this.morphNormals = []\n      this.skinWeights = []\n      this.skinIndices = []\n      this.lineDistances = []\n      this.boundingBox = null\n      this.boundingSphere = null\n\n      // name\n\n      this.name = source.name\n\n      // vertices\n\n      const vertices = source.vertices\n\n      for (let i = 0, il = vertices.length; i < il; i++) {\n        this.vertices.push(vertices[i].clone())\n      }\n\n      // colors\n\n      const colors = source.colors\n\n      for (let i = 0, il = colors.length; i < il; i++) {\n        this.colors.push(colors[i].clone())\n      }\n\n      // faces\n\n      const faces = source.faces\n\n      for (let i = 0, il = faces.length; i < il; i++) {\n        this.faces.push(faces[i].clone())\n      }\n\n      // face vertex uvs\n\n      for (let i = 0, il = source.faceVertexUvs.length; i < il; i++) {\n        const faceVertexUvs = source.faceVertexUvs[i]\n\n        if (this.faceVertexUvs[i] === undefined) {\n          this.faceVertexUvs[i] = []\n        }\n\n        for (let j = 0, jl = faceVertexUvs.length; j < jl; j++) {\n          const uvs = faceVertexUvs[j],\n            uvsCopy = []\n\n          for (let k = 0, kl = uvs.length; k < kl; k++) {\n            const uv = uvs[k]\n\n            uvsCopy.push(uv.clone())\n          }\n\n          this.faceVertexUvs[i].push(uvsCopy)\n        }\n      }\n\n      // morph targets\n\n      const morphTargets = source.morphTargets\n\n      for (let i = 0, il = morphTargets.length; i < il; i++) {\n        const morphTarget = {}\n        morphTarget.name = morphTargets[i].name\n\n        // vertices\n\n        if (morphTargets[i].vertices !== undefined) {\n          morphTarget.vertices = []\n\n          for (let j = 0, jl = morphTargets[i].vertices.length; j < jl; j++) {\n            morphTarget.vertices.push(morphTargets[i].vertices[j].clone())\n          }\n        }\n\n        // normals\n\n        if (morphTargets[i].normals !== undefined) {\n          morphTarget.normals = []\n\n          for (let j = 0, jl = morphTargets[i].normals.length; j < jl; j++) {\n            morphTarget.normals.push(morphTargets[i].normals[j].clone())\n          }\n        }\n\n        this.morphTargets.push(morphTarget)\n      }\n\n      // morph normals\n\n      const morphNormals = source.morphNormals\n\n      for (let i = 0, il = morphNormals.length; i < il; i++) {\n        const morphNormal = {}\n\n        // vertex normals\n\n        if (morphNormals[i].vertexNormals !== undefined) {\n          morphNormal.vertexNormals = []\n\n          for (let j = 0, jl = morphNormals[i].vertexNormals.length; j < jl; j++) {\n            const srcVertexNormal = morphNormals[i].vertexNormals[j]\n            const destVertexNormal = {}\n\n            destVertexNormal.a = srcVertexNormal.a.clone()\n            destVertexNormal.b = srcVertexNormal.b.clone()\n            destVertexNormal.c = srcVertexNormal.c.clone()\n\n            morphNormal.vertexNormals.push(destVertexNormal)\n          }\n        }\n\n        // face normals\n\n        if (morphNormals[i].faceNormals !== undefined) {\n          morphNormal.faceNormals = []\n\n          for (let j = 0, jl = morphNormals[i].faceNormals.length; j < jl; j++) {\n            morphNormal.faceNormals.push(morphNormals[i].faceNormals[j].clone())\n          }\n        }\n\n        this.morphNormals.push(morphNormal)\n      }\n\n      // skin weights\n\n      const skinWeights = source.skinWeights\n\n      for (let i = 0, il = skinWeights.length; i < il; i++) {\n        this.skinWeights.push(skinWeights[i].clone())\n      }\n\n      // skin indices\n\n      const skinIndices = source.skinIndices\n\n      for (let i = 0, il = skinIndices.length; i < il; i++) {\n        this.skinIndices.push(skinIndices[i].clone())\n      }\n\n      // line distances\n\n      const lineDistances = source.lineDistances\n\n      for (let i = 0, il = lineDistances.length; i < il; i++) {\n        this.lineDistances.push(lineDistances[i])\n      }\n\n      // bounding box\n\n      const boundingBox = source.boundingBox\n\n      if (boundingBox !== null) {\n        this.boundingBox = boundingBox.clone()\n      }\n\n      // bounding sphere\n\n      const boundingSphere = source.boundingSphere\n\n      if (boundingSphere !== null) {\n        this.boundingSphere = boundingSphere.clone()\n      }\n\n      // update flags\n\n      this.elementsNeedUpdate = source.elementsNeedUpdate\n      this.verticesNeedUpdate = source.verticesNeedUpdate\n      this.uvsNeedUpdate = source.uvsNeedUpdate\n      this.normalsNeedUpdate = source.normalsNeedUpdate\n      this.colorsNeedUpdate = source.colorsNeedUpdate\n      this.lineDistancesNeedUpdate = source.lineDistancesNeedUpdate\n      this.groupsNeedUpdate = source.groupsNeedUpdate\n\n      return this\n    }\n\n    toBufferGeometry() {\n      const geometry = new DirectGeometry().fromGeometry(this)\n\n      const buffergeometry = new BufferGeometry()\n\n      const positions = new Float32Array(geometry.vertices.length * 3)\n      buffergeometry.setAttribute('position', new BufferAttribute(positions, 3).copyVector3sArray(geometry.vertices))\n\n      if (geometry.normals.length > 0) {\n        const normals = new Float32Array(geometry.normals.length * 3)\n        buffergeometry.setAttribute('normal', new BufferAttribute(normals, 3).copyVector3sArray(geometry.normals))\n      }\n\n      if (geometry.colors.length > 0) {\n        const colors = new Float32Array(geometry.colors.length * 3)\n        buffergeometry.setAttribute('color', new BufferAttribute(colors, 3).copyColorsArray(geometry.colors))\n      }\n\n      if (geometry.uvs.length > 0) {\n        const uvs = new Float32Array(geometry.uvs.length * 2)\n        buffergeometry.setAttribute('uv', new BufferAttribute(uvs, 2).copyVector2sArray(geometry.uvs))\n      }\n\n      if (geometry.uvs2.length > 0) {\n        const uvs2 = new Float32Array(geometry.uvs2.length * 2)\n        buffergeometry.setAttribute('uv2', new BufferAttribute(uvs2, 2).copyVector2sArray(geometry.uvs2))\n      }\n\n      // groups\n\n      buffergeometry.groups = geometry.groups\n\n      // morphs\n\n      for (let name in geometry.morphTargets) {\n        const array = []\n        const morphTargets = geometry.morphTargets[name]\n\n        for (let i = 0, l = morphTargets.length; i < l; i++) {\n          const morphTarget = morphTargets[i]\n\n          const attribute = new Float32BufferAttribute(morphTarget.data.length * 3, 3)\n          attribute.name = morphTarget.name\n\n          array.push(attribute.copyVector3sArray(morphTarget.data))\n        }\n\n        buffergeometry.morphAttributes[name] = array\n      }\n\n      // skinning\n\n      if (geometry.skinIndices.length > 0) {\n        const skinIndices = new Float32BufferAttribute(geometry.skinIndices.length * 4, 4)\n        buffergeometry.setAttribute('skinIndex', skinIndices.copyVector4sArray(geometry.skinIndices))\n      }\n\n      if (geometry.skinWeights.length > 0) {\n        const skinWeights = new Float32BufferAttribute(geometry.skinWeights.length * 4, 4)\n        buffergeometry.setAttribute('skinWeight', skinWeights.copyVector4sArray(geometry.skinWeights))\n      }\n\n      //\n\n      if (geometry.boundingSphere !== null) {\n        buffergeometry.boundingSphere = geometry.boundingSphere.clone()\n      }\n\n      if (geometry.boundingBox !== null) {\n        buffergeometry.boundingBox = geometry.boundingBox.clone()\n      }\n\n      return buffergeometry\n    }\n\n    computeTangents() {\n      console.error('THREE.Geometry: .computeTangents() has been removed.')\n    }\n\n    computeLineDistances() {\n      console.error(\n        'THREE.Geometry: .computeLineDistances() has been removed. Use THREE.Line.computeLineDistances() instead.',\n      )\n    }\n\n    applyMatrix(matrix) {\n      console.warn('THREE.Geometry: .applyMatrix() has been renamed to .applyMatrix4().')\n      return this.applyMatrix4(matrix)\n    }\n\n    dispose() {\n      this.dispatchEvent({ type: 'dispose' })\n    }\n  }\n\n  return Geometry\n})()\n\nclass DirectGeometry {\n  constructor() {\n    this.vertices = []\n    this.normals = []\n    this.colors = []\n    this.uvs = []\n    this.uvs2 = []\n\n    this.groups = []\n\n    this.morphTargets = {}\n\n    this.skinWeights = []\n    this.skinIndices = []\n\n    // this.lineDistances = [];\n\n    this.boundingBox = null\n    this.boundingSphere = null\n\n    // update flags\n\n    this.verticesNeedUpdate = false\n    this.normalsNeedUpdate = false\n    this.colorsNeedUpdate = false\n    this.uvsNeedUpdate = false\n    this.groupsNeedUpdate = false\n  }\n\n  computeGroups(geometry) {\n    const groups = []\n\n    let group, i\n    let materialIndex = undefined\n\n    const faces = geometry.faces\n\n    for (i = 0; i < faces.length; i++) {\n      const face = faces[i]\n\n      // materials\n\n      if (face.materialIndex !== materialIndex) {\n        materialIndex = face.materialIndex\n\n        if (group !== undefined) {\n          group.count = i * 3 - group.start\n          groups.push(group)\n        }\n\n        group = {\n          start: i * 3,\n          materialIndex,\n        }\n      }\n    }\n\n    if (group !== undefined) {\n      group.count = i * 3 - group.start\n      groups.push(group)\n    }\n\n    this.groups = groups\n  }\n\n  fromGeometry(geometry) {\n    const faces = geometry.faces\n    const vertices = geometry.vertices\n    const faceVertexUvs = geometry.faceVertexUvs\n\n    const hasFaceVertexUv = faceVertexUvs[0] && faceVertexUvs[0].length > 0\n    const hasFaceVertexUv2 = faceVertexUvs[1] && faceVertexUvs[1].length > 0\n\n    // morphs\n\n    const morphTargets = geometry.morphTargets\n    const morphTargetsLength = morphTargets.length\n\n    let morphTargetsPosition\n\n    if (morphTargetsLength > 0) {\n      morphTargetsPosition = []\n\n      for (let i = 0; i < morphTargetsLength; i++) {\n        morphTargetsPosition[i] = {\n          name: morphTargets[i].name,\n          data: [],\n        }\n      }\n\n      this.morphTargets.position = morphTargetsPosition\n    }\n\n    const morphNormals = geometry.morphNormals\n    const morphNormalsLength = morphNormals.length\n\n    let morphTargetsNormal\n\n    if (morphNormalsLength > 0) {\n      morphTargetsNormal = []\n\n      for (let i = 0; i < morphNormalsLength; i++) {\n        morphTargetsNormal[i] = {\n          name: morphNormals[i].name,\n          data: [],\n        }\n      }\n\n      this.morphTargets.normal = morphTargetsNormal\n    }\n\n    // skins\n\n    const skinIndices = geometry.skinIndices\n    const skinWeights = geometry.skinWeights\n\n    const hasSkinIndices = skinIndices.length === vertices.length\n    const hasSkinWeights = skinWeights.length === vertices.length\n\n    //\n\n    if (vertices.length > 0 && faces.length === 0) {\n      console.error('THREE.DirectGeometry: Faceless geometries are not supported.')\n    }\n\n    for (let i = 0; i < faces.length; i++) {\n      const face = faces[i]\n\n      this.vertices.push(vertices[face.a], vertices[face.b], vertices[face.c])\n\n      const vertexNormals = face.vertexNormals\n\n      if (vertexNormals.length === 3) {\n        this.normals.push(vertexNormals[0], vertexNormals[1], vertexNormals[2])\n      } else {\n        const normal = face.normal\n\n        this.normals.push(normal, normal, normal)\n      }\n\n      const vertexColors = face.vertexColors\n\n      if (vertexColors.length === 3) {\n        this.colors.push(vertexColors[0], vertexColors[1], vertexColors[2])\n      } else {\n        const color = face.color\n\n        this.colors.push(color, color, color)\n      }\n\n      if (hasFaceVertexUv === true) {\n        const vertexUvs = faceVertexUvs[0][i]\n\n        if (vertexUvs !== undefined) {\n          this.uvs.push(vertexUvs[0], vertexUvs[1], vertexUvs[2])\n        } else {\n          console.warn('THREE.DirectGeometry.fromGeometry(): Undefined vertexUv ', i)\n\n          this.uvs.push(new Vector2(), new Vector2(), new Vector2())\n        }\n      }\n\n      if (hasFaceVertexUv2 === true) {\n        const vertexUvs = faceVertexUvs[1][i]\n\n        if (vertexUvs !== undefined) {\n          this.uvs2.push(vertexUvs[0], vertexUvs[1], vertexUvs[2])\n        } else {\n          console.warn('THREE.DirectGeometry.fromGeometry(): Undefined vertexUv2 ', i)\n\n          this.uvs2.push(new Vector2(), new Vector2(), new Vector2())\n        }\n      }\n\n      // morphs\n\n      for (let j = 0; j < morphTargetsLength; j++) {\n        const morphTarget = morphTargets[j].vertices\n\n        morphTargetsPosition[j].data.push(morphTarget[face.a], morphTarget[face.b], morphTarget[face.c])\n      }\n\n      for (let j = 0; j < morphNormalsLength; j++) {\n        const morphNormal = morphNormals[j].vertexNormals[i]\n\n        morphTargetsNormal[j].data.push(morphNormal.a, morphNormal.b, morphNormal.c)\n      }\n\n      // skins\n\n      if (hasSkinIndices) {\n        this.skinIndices.push(skinIndices[face.a], skinIndices[face.b], skinIndices[face.c])\n      }\n\n      if (hasSkinWeights) {\n        this.skinWeights.push(skinWeights[face.a], skinWeights[face.b], skinWeights[face.c])\n      }\n    }\n\n    this.computeGroups(geometry)\n\n    this.verticesNeedUpdate = geometry.verticesNeedUpdate\n    this.normalsNeedUpdate = geometry.normalsNeedUpdate\n    this.colorsNeedUpdate = geometry.colorsNeedUpdate\n    this.uvsNeedUpdate = geometry.uvsNeedUpdate\n    this.groupsNeedUpdate = geometry.groupsNeedUpdate\n\n    if (geometry.boundingSphere !== null) {\n      this.boundingSphere = geometry.boundingSphere.clone()\n    }\n\n    if (geometry.boundingBox !== null) {\n      this.boundingBox = geometry.boundingBox.clone()\n    }\n\n    return this\n  }\n}\n\nclass Face3 {\n  constructor(a, b, c, normal, color, materialIndex = 0) {\n    this.a = a\n    this.b = b\n    this.c = c\n\n    this.normal = normal && normal.isVector3 ? normal : new Vector3()\n    this.vertexNormals = Array.isArray(normal) ? normal : []\n\n    this.color = color && color.isColor ? color : new Color()\n    this.vertexColors = Array.isArray(color) ? color : []\n\n    this.materialIndex = materialIndex\n  }\n\n  clone() {\n    return new this.constructor().copy(this)\n  }\n\n  copy(source) {\n    this.a = source.a\n    this.b = source.b\n    this.c = source.c\n\n    this.normal.copy(source.normal)\n    this.color.copy(source.color)\n\n    this.materialIndex = source.materialIndex\n\n    for (let i = 0, il = source.vertexNormals.length; i < il; i++) {\n      this.vertexNormals[i] = source.vertexNormals[i].clone()\n    }\n\n    for (let i = 0, il = source.vertexColors.length; i < il; i++) {\n      this.vertexColors[i] = source.vertexColors[i].clone()\n    }\n\n    return this\n  }\n}\n\nexport { Face3, Geometry }\n"], "mappings": ";AAgBA,MAAMA,GAAA,GAAsB,mBAAIC,OAAA,CAAS;AACzC,MAAMC,IAAA,GAAuB,mBAAIC,QAAA,CAAU;AAC3C,MAAMC,OAAA,GAA0B,mBAAIC,OAAA,CAAS;AAExC,MAACC,QAAA,GAA4B,sBAAM;EACtC,MAAMC,SAAA,SAAiBC,eAAA,CAAgB;IACrC,OAAOC,+BAA+BC,MAAA,EAAQ;MAC5C,IAAIC,cAAA,GAAiB,IAAIC,cAAA,CAAgB;MAEzC,MAAMC,QAAA,GAAWH,MAAA,CAAOG,QAAA;MAExB,IAAIH,MAAA,CAAOI,QAAA,IAAYJ,MAAA,CAAOK,MAAA,EAAQ;QACpC,MAAMC,SAAA,GAAY,IAAIC,sBAAA,CAAuBJ,QAAA,CAASK,QAAA,CAASC,MAAA,GAAS,GAAG,CAAC;QAC5E,MAAMC,MAAA,GAAS,IAAIH,sBAAA,CAAuBJ,QAAA,CAASO,MAAA,CAAOD,MAAA,GAAS,GAAG,CAAC;QAEvER,cAAA,CAAeU,YAAA,CAAa,YAAYL,SAAA,CAAUM,iBAAA,CAAkBT,QAAA,CAASK,QAAQ,CAAC;QACtFP,cAAA,CAAeU,YAAA,CAAa,SAASD,MAAA,CAAOG,eAAA,CAAgBV,QAAA,CAASO,MAAM,CAAC;QAE5E,IAAIP,QAAA,CAASW,aAAA,IAAiBX,QAAA,CAASW,aAAA,CAAcL,MAAA,KAAWN,QAAA,CAASK,QAAA,CAASC,MAAA,EAAQ;UACxF,MAAMK,aAAA,GAAgB,IAAIP,sBAAA,CAAuBJ,QAAA,CAASW,aAAA,CAAcL,MAAA,EAAQ,CAAC;UAEjFR,cAAA,CAAeU,YAAA,CAAa,gBAAgBG,aAAA,CAAcC,SAAA,CAAUZ,QAAA,CAASW,aAAa,CAAC;QAC5F;QAED,IAAIX,QAAA,CAASa,cAAA,KAAmB,MAAM;UACpCf,cAAA,CAAee,cAAA,GAAiBb,QAAA,CAASa,cAAA,CAAeC,KAAA,CAAO;QAChE;QAED,IAAId,QAAA,CAASe,WAAA,KAAgB,MAAM;UACjCjB,cAAA,CAAeiB,WAAA,GAAcf,QAAA,CAASe,WAAA,CAAYD,KAAA,CAAO;QAC1D;MACT,WAAiBjB,MAAA,CAAOmB,MAAA,EAAQ;QACxBlB,cAAA,GAAiBE,QAAA,CAASiB,gBAAA,CAAkB;MAC7C;MAED,OAAOnB,cAAA;IACR;IAEDoB,YAAA,EAAc;MACZ,MAAO;MACP,KAAKC,UAAA,GAAa;MAClB,KAAKC,IAAA,GAAOC,SAAA,CAAUC,YAAA,CAAc;MAEpC,KAAKC,IAAA,GAAO;MACZ,KAAKC,IAAA,GAAO;MAEZ,KAAKnB,QAAA,GAAW,EAAE;MAClB,KAAKE,MAAA,GAAS,EAAE;MAChB,KAAKkB,KAAA,GAAQ,EAAE;MACf,KAAKC,aAAA,GAAgB,CAAC,EAAE;MAExB,KAAKC,YAAA,GAAe,EAAE;MACtB,KAAKC,YAAA,GAAe,EAAE;MAEtB,KAAKC,WAAA,GAAc,EAAE;MACrB,KAAKC,WAAA,GAAc,EAAE;MAErB,KAAKnB,aAAA,GAAgB,EAAE;MAEvB,KAAKI,WAAA,GAAc;MACnB,KAAKF,cAAA,GAAiB;MAItB,KAAKkB,kBAAA,GAAqB;MAC1B,KAAKC,kBAAA,GAAqB;MAC1B,KAAKC,aAAA,GAAgB;MACrB,KAAKC,iBAAA,GAAoB;MACzB,KAAKC,gBAAA,GAAmB;MACxB,KAAKC,uBAAA,GAA0B;MAC/B,KAAKC,gBAAA,GAAmB;IACzB;IAEDC,aAAaC,MAAA,EAAQ;MACnB,MAAMC,YAAA,GAAe,IAAIC,OAAA,GAAUC,eAAA,CAAgBH,MAAM;MAEzD,SAASI,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKvC,QAAA,CAASC,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACtD,MAAME,MAAA,GAAS,KAAKxC,QAAA,CAASsC,CAAC;QAC9BE,MAAA,CAAOP,YAAA,CAAaC,MAAM;MAC3B;MAED,SAASI,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKnB,KAAA,CAAMnB,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACnD,MAAMG,IAAA,GAAO,KAAKrB,KAAA,CAAMkB,CAAC;QACzBG,IAAA,CAAKC,MAAA,CAAOC,YAAA,CAAaR,YAAY,EAAES,SAAA,CAAW;QAElD,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKL,IAAA,CAAKM,aAAA,CAAc9C,MAAA,EAAQ4C,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UAC3DJ,IAAA,CAAKM,aAAA,CAAcF,CAAC,EAAEF,YAAA,CAAaR,YAAY,EAAES,SAAA,CAAW;QAC7D;MACF;MAED,IAAI,KAAKlC,WAAA,KAAgB,MAAM;QAC7B,KAAKsC,kBAAA,CAAoB;MAC1B;MAED,IAAI,KAAKxC,cAAA,KAAmB,MAAM;QAChC,KAAKyC,qBAAA,CAAuB;MAC7B;MAED,KAAKtB,kBAAA,GAAqB;MAC1B,KAAKE,iBAAA,GAAoB;MAEzB,OAAO;IACR;IAEDqB,QAAQC,KAAA,EAAO;MAGbrE,GAAA,CAAIsE,aAAA,CAAcD,KAAK;MAEvB,KAAKlB,YAAA,CAAanD,GAAG;MAErB,OAAO;IACR;IAEDuE,QAAQF,KAAA,EAAO;MAGbrE,GAAA,CAAIwE,aAAA,CAAcH,KAAK;MAEvB,KAAKlB,YAAA,CAAanD,GAAG;MAErB,OAAO;IACR;IAEDyE,QAAQJ,KAAA,EAAO;MAGbrE,GAAA,CAAI0E,aAAA,CAAcL,KAAK;MAEvB,KAAKlB,YAAA,CAAanD,GAAG;MAErB,OAAO;IACR;IAED2E,UAAUC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;MAGjB9E,GAAA,CAAI+E,eAAA,CAAgBH,CAAA,EAAGC,CAAA,EAAGC,CAAC;MAE3B,KAAK3B,YAAA,CAAanD,GAAG;MAErB,OAAO;IACR;IAEDgF,MAAMJ,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;MAGb9E,GAAA,CAAIiF,SAAA,CAAUL,CAAA,EAAGC,CAAA,EAAGC,CAAC;MAErB,KAAK3B,YAAA,CAAanD,GAAG;MAErB,OAAO;IACR;IAEDkF,OAAOC,MAAA,EAAQ;MACbjF,IAAA,CAAKgF,MAAA,CAAOC,MAAM;MAElBjF,IAAA,CAAKkF,YAAA,CAAc;MAEnB,KAAKjC,YAAA,CAAajD,IAAA,CAAKkD,MAAM;MAE7B,OAAO;IACR;IAEDiC,mBAAmBxE,QAAA,EAAU;MAC3B,MAAMyE,KAAA,GAAQ;MAEd,MAAMC,KAAA,GAAQ1E,QAAA,CAAS0E,KAAA,KAAU,OAAO1E,QAAA,CAAS0E,KAAA,GAAQ;MACzD,MAAMC,UAAA,GAAa3E,QAAA,CAAS2E,UAAA;MAE5B,IAAIA,UAAA,CAAWC,QAAA,KAAa,QAAW;QACrCC,OAAA,CAAQC,KAAA,CAAM,kFAAkF;QAChG,OAAO;MACR;MAED,MAAMF,QAAA,GAAWD,UAAA,CAAWC,QAAA;MAC5B,MAAM7B,MAAA,GAAS4B,UAAA,CAAW5B,MAAA;MAC1B,MAAMgC,KAAA,GAAQJ,UAAA,CAAWI,KAAA;MACzB,MAAMC,EAAA,GAAKL,UAAA,CAAWK,EAAA;MACtB,MAAMC,GAAA,GAAMN,UAAA,CAAWM,GAAA;MAEvB,IAAIA,GAAA,KAAQ,QAAW,KAAKvD,aAAA,CAAc,CAAC,IAAI,EAAE;MAEjD,SAASiB,CAAA,GAAI,GAAGA,CAAA,GAAIiC,QAAA,CAASM,KAAA,EAAOvC,CAAA,IAAK;QACvC8B,KAAA,CAAMpE,QAAA,CAAS8E,IAAA,CAAK,IAAI3F,OAAA,CAAS,EAAC4F,mBAAA,CAAoBR,QAAA,EAAUjC,CAAC,CAAC;QAElE,IAAIoC,KAAA,KAAU,QAAW;UACvBN,KAAA,CAAMlE,MAAA,CAAO4E,IAAA,CAAK,IAAIE,KAAA,CAAO,EAACD,mBAAA,CAAoBL,KAAA,EAAOpC,CAAC,CAAC;QAC5D;MACF;MAED,SAAS2C,QAAQC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGC,aAAA,EAAe;QACvC,MAAMC,YAAA,GACJZ,KAAA,KAAU,SAAY,KAAK,CAACN,KAAA,CAAMlE,MAAA,CAAOgF,CAAC,EAAEzE,KAAA,IAAS2D,KAAA,CAAMlE,MAAA,CAAOiF,CAAC,EAAE1E,KAAA,IAAS2D,KAAA,CAAMlE,MAAA,CAAOkF,CAAC,EAAE3E,KAAA,EAAO;QAEvG,MAAMsC,aAAA,GACJL,MAAA,KAAW,SACP,EAAE,GACF,CACE,IAAIvD,OAAA,CAAS,EAAC4F,mBAAA,CAAoBrC,MAAA,EAAQwC,CAAC,GAC3C,IAAI/F,OAAA,CAAS,EAAC4F,mBAAA,CAAoBrC,MAAA,EAAQyC,CAAC,GAC3C,IAAIhG,OAAA,CAAS,EAAC4F,mBAAA,CAAoBrC,MAAA,EAAQ0C,CAAC,EAC5C;QAEP,MAAM3C,IAAA,GAAO,IAAI8C,KAAA,CAAML,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAGrC,aAAA,EAAeuC,YAAA,EAAcD,aAAa;QAE1EjB,KAAA,CAAMhD,KAAA,CAAM0D,IAAA,CAAKrC,IAAI;QAErB,IAAIkC,EAAA,KAAO,QAAW;UACpBP,KAAA,CAAM/C,aAAA,CAAc,CAAC,EAAEyD,IAAA,CAAK,CAC1B,IAAIU,OAAA,CAAS,EAACT,mBAAA,CAAoBJ,EAAA,EAAIO,CAAC,GACvC,IAAIM,OAAA,CAAS,EAACT,mBAAA,CAAoBJ,EAAA,EAAIQ,CAAC,GACvC,IAAIK,OAAA,CAAS,EAACT,mBAAA,CAAoBJ,EAAA,EAAIS,CAAC,EACxC;QACF;QAED,IAAIR,GAAA,KAAQ,QAAW;UACrBR,KAAA,CAAM/C,aAAA,CAAc,CAAC,EAAEyD,IAAA,CAAK,CAC1B,IAAIU,OAAA,CAAS,EAACT,mBAAA,CAAoBH,GAAA,EAAKM,CAAC,GACxC,IAAIM,OAAA,CAAS,EAACT,mBAAA,CAAoBH,GAAA,EAAKO,CAAC,GACxC,IAAIK,OAAA,CAAS,EAACT,mBAAA,CAAoBH,GAAA,EAAKQ,CAAC,EACzC;QACF;MACF;MAED,MAAMK,MAAA,GAAS9F,QAAA,CAAS8F,MAAA;MAExB,IAAIA,MAAA,CAAOxF,MAAA,GAAS,GAAG;QACrB,SAASqC,CAAA,GAAI,GAAGA,CAAA,GAAImD,MAAA,CAAOxF,MAAA,EAAQqC,CAAA,IAAK;UACtC,MAAMoD,KAAA,GAAQD,MAAA,CAAOnD,CAAC;UAEtB,MAAMqD,KAAA,GAAQD,KAAA,CAAMC,KAAA;UACpB,MAAMd,KAAA,GAAQa,KAAA,CAAMb,KAAA;UAEpB,SAAShC,CAAA,GAAI8C,KAAA,EAAO7C,EAAA,GAAK6C,KAAA,GAAQd,KAAA,EAAOhC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;YACtD,IAAIwB,KAAA,KAAU,QAAW;cACvBY,OAAA,CAAQZ,KAAA,CAAMuB,IAAA,CAAK/C,CAAC,GAAGwB,KAAA,CAAMuB,IAAA,CAAK/C,CAAA,GAAI,CAAC,GAAGwB,KAAA,CAAMuB,IAAA,CAAK/C,CAAA,GAAI,CAAC,GAAG6C,KAAA,CAAML,aAAa;YAC9F,OAAmB;cACLJ,OAAA,CAAQpC,CAAA,EAAGA,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAG6C,KAAA,CAAML,aAAa;YAC7C;UACF;QACF;MACT,OAAa;QACL,IAAIhB,KAAA,KAAU,QAAW;UACvB,SAAS/B,CAAA,GAAI,GAAGA,CAAA,GAAI+B,KAAA,CAAMQ,KAAA,EAAOvC,CAAA,IAAK,GAAG;YACvC2C,OAAA,CAAQZ,KAAA,CAAMuB,IAAA,CAAKtD,CAAC,GAAG+B,KAAA,CAAMuB,IAAA,CAAKtD,CAAA,GAAI,CAAC,GAAG+B,KAAA,CAAMuB,IAAA,CAAKtD,CAAA,GAAI,CAAC,CAAC;UAC5D;QACX,OAAe;UACL,SAASA,CAAA,GAAI,GAAGA,CAAA,GAAIiC,QAAA,CAASM,KAAA,EAAOvC,CAAA,IAAK,GAAG;YAC1C2C,OAAA,CAAQ3C,CAAA,EAAGA,CAAA,GAAI,GAAGA,CAAA,GAAI,CAAC;UACxB;QACF;MACF;MAED,KAAKuD,kBAAA,CAAoB;MAEzB,IAAIlG,QAAA,CAASe,WAAA,KAAgB,MAAM;QACjC,KAAKA,WAAA,GAAcf,QAAA,CAASe,WAAA,CAAYD,KAAA,CAAO;MAChD;MAED,IAAId,QAAA,CAASa,cAAA,KAAmB,MAAM;QACpC,KAAKA,cAAA,GAAiBb,QAAA,CAASa,cAAA,CAAeC,KAAA,CAAO;MACtD;MAED,OAAO;IACR;IAEDqF,OAAA,EAAS;MACP,KAAK9C,kBAAA,CAAoB;MAEzB,KAAKtC,WAAA,CAAYqF,SAAA,CAAU7G,OAAO,EAAE8G,MAAA,CAAQ;MAE5C,KAAKvC,SAAA,CAAUvE,OAAA,CAAQwE,CAAA,EAAGxE,OAAA,CAAQyE,CAAA,EAAGzE,OAAA,CAAQ0E,CAAC;MAE9C,OAAO;IACR;IAEDhB,UAAA,EAAY;MACV,KAAKK,qBAAA,CAAuB;MAE5B,MAAM6C,MAAA,GAAS,KAAKtF,cAAA,CAAesF,MAAA;MACnC,MAAMG,MAAA,GAAS,KAAKzF,cAAA,CAAeyF,MAAA;MAEnC,MAAMC,CAAA,GAAID,MAAA,KAAW,IAAI,IAAI,IAAMA,MAAA;MAEnC,MAAM/D,MAAA,GAAS,IAAInD,OAAA,CAAS;MAC5BmD,MAAA,CAAOiE,GAAA,CAAID,CAAA,EAAG,GAAG,GAAG,CAACA,CAAA,GAAIJ,MAAA,CAAOpC,CAAA,EAAG,GAAGwC,CAAA,EAAG,GAAG,CAACA,CAAA,GAAIJ,MAAA,CAAOnC,CAAA,EAAG,GAAG,GAAGuC,CAAA,EAAG,CAACA,CAAA,GAAIJ,MAAA,CAAOlC,CAAA,EAAG,GAAG,GAAG,GAAG,CAAC;MAE7F,KAAK3B,YAAA,CAAaC,MAAM;MAExB,OAAO;IACR;IAED2D,mBAAA,EAAqB;MACnB,MAAMO,EAAA,GAAK,IAAIjH,OAAA,CAAS;QACtBkH,EAAA,GAAK,IAAIlH,OAAA,CAAS;MAEpB,SAASmH,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKnF,KAAA,CAAMnB,MAAA,EAAQqG,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACnD,MAAM7D,IAAA,GAAO,KAAKrB,KAAA,CAAMkF,CAAC;QAEzB,MAAME,EAAA,GAAK,KAAKxG,QAAA,CAASyC,IAAA,CAAKyC,CAAC;QAC/B,MAAMuB,EAAA,GAAK,KAAKzG,QAAA,CAASyC,IAAA,CAAK0C,CAAC;QAC/B,MAAMuB,EAAA,GAAK,KAAK1G,QAAA,CAASyC,IAAA,CAAK2C,CAAC;QAE/BgB,EAAA,CAAGO,UAAA,CAAWD,EAAA,EAAID,EAAE;QACpBJ,EAAA,CAAGM,UAAA,CAAWH,EAAA,EAAIC,EAAE;QACpBL,EAAA,CAAGQ,KAAA,CAAMP,EAAE;QAEXD,EAAA,CAAGxD,SAAA,CAAW;QAEdH,IAAA,CAAKC,MAAA,CAAOmE,IAAA,CAAKT,EAAE;MACpB;IACF;IAEDU,qBAAqBC,YAAA,GAAe,MAAM;MACxC,MAAM/G,QAAA,GAAW,IAAIgH,KAAA,CAAM,KAAKhH,QAAA,CAASC,MAAM;MAE/C,SAASgH,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKlH,QAAA,CAASC,MAAA,EAAQgH,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACtDjH,QAAA,CAASiH,CAAC,IAAI,IAAI9H,OAAA,CAAS;MAC5B;MAED,IAAI4H,YAAA,EAAc;QAIhB,MAAMX,EAAA,GAAK,IAAIjH,OAAA,CAAS;UACtBkH,EAAA,GAAK,IAAIlH,OAAA,CAAS;QAEpB,SAASmH,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKnF,KAAA,CAAMnB,MAAA,EAAQqG,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACnD,MAAM7D,IAAA,GAAO,KAAKrB,KAAA,CAAMkF,CAAC;UAEzB,MAAME,EAAA,GAAK,KAAKxG,QAAA,CAASyC,IAAA,CAAKyC,CAAC;UAC/B,MAAMuB,EAAA,GAAK,KAAKzG,QAAA,CAASyC,IAAA,CAAK0C,CAAC;UAC/B,MAAMuB,EAAA,GAAK,KAAK1G,QAAA,CAASyC,IAAA,CAAK2C,CAAC;UAE/BgB,EAAA,CAAGO,UAAA,CAAWD,EAAA,EAAID,EAAE;UACpBJ,EAAA,CAAGM,UAAA,CAAWH,EAAA,EAAIC,EAAE;UACpBL,EAAA,CAAGQ,KAAA,CAAMP,EAAE;UAEXrG,QAAA,CAASyC,IAAA,CAAKyC,CAAC,EAAEiC,GAAA,CAAIf,EAAE;UACvBpG,QAAA,CAASyC,IAAA,CAAK0C,CAAC,EAAEgC,GAAA,CAAIf,EAAE;UACvBpG,QAAA,CAASyC,IAAA,CAAK2C,CAAC,EAAE+B,GAAA,CAAIf,EAAE;QACxB;MACT,OAAa;QACL,KAAKP,kBAAA,CAAoB;QAEzB,SAASS,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKnF,KAAA,CAAMnB,MAAA,EAAQqG,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACnD,MAAM7D,IAAA,GAAO,KAAKrB,KAAA,CAAMkF,CAAC;UAEzBtG,QAAA,CAASyC,IAAA,CAAKyC,CAAC,EAAEiC,GAAA,CAAI1E,IAAA,CAAKC,MAAM;UAChC1C,QAAA,CAASyC,IAAA,CAAK0C,CAAC,EAAEgC,GAAA,CAAI1E,IAAA,CAAKC,MAAM;UAChC1C,QAAA,CAASyC,IAAA,CAAK2C,CAAC,EAAE+B,GAAA,CAAI1E,IAAA,CAAKC,MAAM;QACjC;MACF;MAED,SAASuE,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKlH,QAAA,CAASC,MAAA,EAAQgH,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACtDjH,QAAA,CAASiH,CAAC,EAAErE,SAAA,CAAW;MACxB;MAED,SAAS0D,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKnF,KAAA,CAAMnB,MAAA,EAAQqG,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACnD,MAAM7D,IAAA,GAAO,KAAKrB,KAAA,CAAMkF,CAAC;QAEzB,MAAMvD,aAAA,GAAgBN,IAAA,CAAKM,aAAA;QAE3B,IAAIA,aAAA,CAAc9C,MAAA,KAAW,GAAG;UAC9B8C,aAAA,CAAc,CAAC,EAAE8D,IAAA,CAAK7G,QAAA,CAASyC,IAAA,CAAKyC,CAAC,CAAC;UACtCnC,aAAA,CAAc,CAAC,EAAE8D,IAAA,CAAK7G,QAAA,CAASyC,IAAA,CAAK0C,CAAC,CAAC;UACtCpC,aAAA,CAAc,CAAC,EAAE8D,IAAA,CAAK7G,QAAA,CAASyC,IAAA,CAAK2C,CAAC,CAAC;QAChD,OAAe;UACLrC,aAAA,CAAc,CAAC,IAAI/C,QAAA,CAASyC,IAAA,CAAKyC,CAAC,EAAEzE,KAAA,CAAO;UAC3CsC,aAAA,CAAc,CAAC,IAAI/C,QAAA,CAASyC,IAAA,CAAK0C,CAAC,EAAE1E,KAAA,CAAO;UAC3CsC,aAAA,CAAc,CAAC,IAAI/C,QAAA,CAASyC,IAAA,CAAK2C,CAAC,EAAE3E,KAAA,CAAO;QAC5C;MACF;MAED,IAAI,KAAKW,KAAA,CAAMnB,MAAA,GAAS,GAAG;QACzB,KAAK4B,iBAAA,GAAoB;MAC1B;IACF;IAEDuF,yBAAA,EAA2B;MACzB,KAAKvB,kBAAA,CAAoB;MAEzB,SAASS,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKnF,KAAA,CAAMnB,MAAA,EAAQqG,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACnD,MAAM7D,IAAA,GAAO,KAAKrB,KAAA,CAAMkF,CAAC;QAEzB,MAAMvD,aAAA,GAAgBN,IAAA,CAAKM,aAAA;QAE3B,IAAIA,aAAA,CAAc9C,MAAA,KAAW,GAAG;UAC9B8C,aAAA,CAAc,CAAC,EAAE8D,IAAA,CAAKpE,IAAA,CAAKC,MAAM;UACjCK,aAAA,CAAc,CAAC,EAAE8D,IAAA,CAAKpE,IAAA,CAAKC,MAAM;UACjCK,aAAA,CAAc,CAAC,EAAE8D,IAAA,CAAKpE,IAAA,CAAKC,MAAM;QAC3C,OAAe;UACLK,aAAA,CAAc,CAAC,IAAIN,IAAA,CAAKC,MAAA,CAAOjC,KAAA,CAAO;UACtCsC,aAAA,CAAc,CAAC,IAAIN,IAAA,CAAKC,MAAA,CAAOjC,KAAA,CAAO;UACtCsC,aAAA,CAAc,CAAC,IAAIN,IAAA,CAAKC,MAAA,CAAOjC,KAAA,CAAO;QACvC;MACF;MAED,IAAI,KAAKW,KAAA,CAAMnB,MAAA,GAAS,GAAG;QACzB,KAAK4B,iBAAA,GAAoB;MAC1B;IACF;IAEDwF,oBAAA,EAAsB;MAKpB,SAASf,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKnF,KAAA,CAAMnB,MAAA,EAAQqG,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACnD,MAAM7D,IAAA,GAAO,KAAKrB,KAAA,CAAMkF,CAAC;QAEzB,IAAI,CAAC7D,IAAA,CAAK6E,oBAAA,EAAsB;UAC9B7E,IAAA,CAAK6E,oBAAA,GAAuB7E,IAAA,CAAKC,MAAA,CAAOjC,KAAA,CAAO;QACzD,OAAe;UACLgC,IAAA,CAAK6E,oBAAA,CAAqBT,IAAA,CAAKpE,IAAA,CAAKC,MAAM;QAC3C;QAED,IAAI,CAACD,IAAA,CAAK8E,uBAAA,EAAyB9E,IAAA,CAAK8E,uBAAA,GAA0B,EAAE;QAEpE,SAASjF,CAAA,GAAI,GAAGC,EAAA,GAAKE,IAAA,CAAKM,aAAA,CAAc9C,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UAC3D,IAAI,CAACG,IAAA,CAAK8E,uBAAA,CAAwBjF,CAAC,GAAG;YACpCG,IAAA,CAAK8E,uBAAA,CAAwBjF,CAAC,IAAIG,IAAA,CAAKM,aAAA,CAAcT,CAAC,EAAE7B,KAAA,CAAO;UAC3E,OAAiB;YACLgC,IAAA,CAAK8E,uBAAA,CAAwBjF,CAAC,EAAEuE,IAAA,CAAKpE,IAAA,CAAKM,aAAA,CAAcT,CAAC,CAAC;UAC3D;QACF;MACF;MAID,MAAMkF,MAAA,GAAS,IAAInI,SAAA,CAAU;MAC7BmI,MAAA,CAAOpG,KAAA,GAAQ,KAAKA,KAAA;MAEpB,SAASkB,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKjB,YAAA,CAAarB,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAG1D,IAAI,CAAC,KAAKf,YAAA,CAAae,CAAC,GAAG;UACzB,KAAKf,YAAA,CAAae,CAAC,IAAI,CAAE;UACzB,KAAKf,YAAA,CAAae,CAAC,EAAEmF,WAAA,GAAc,EAAE;UACrC,KAAKlG,YAAA,CAAae,CAAC,EAAES,aAAA,GAAgB,EAAE;UAEvC,MAAM2E,cAAA,GAAiB,KAAKnG,YAAA,CAAae,CAAC,EAAEmF,WAAA;UAC5C,MAAME,gBAAA,GAAmB,KAAKpG,YAAA,CAAae,CAAC,EAAES,aAAA;UAE9C,SAASuD,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKnF,KAAA,CAAMnB,MAAA,EAAQqG,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;YACnD,MAAMsB,UAAA,GAAa,IAAIzI,OAAA,CAAS;YAChC,MAAM4D,aAAA,GAAgB;cACpBmC,CAAA,EAAG,IAAI/F,OAAA,CAAS;cAChBgG,CAAA,EAAG,IAAIhG,OAAA,CAAS;cAChBiG,CAAA,EAAG,IAAIjG,OAAA,CAAS;YACjB;YAEDuI,cAAA,CAAe5C,IAAA,CAAK8C,UAAU;YAC9BD,gBAAA,CAAiB7C,IAAA,CAAK/B,aAAa;UACpC;QACF;QAED,MAAMxB,YAAA,GAAe,KAAKA,YAAA,CAAae,CAAC;QAIxCkF,MAAA,CAAOxH,QAAA,GAAW,KAAKsB,YAAA,CAAagB,CAAC,EAAEtC,QAAA;QAIvCwH,MAAA,CAAO3B,kBAAA,CAAoB;QAC3B2B,MAAA,CAAOV,oBAAA,CAAsB;QAI7B,SAASR,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKnF,KAAA,CAAMnB,MAAA,EAAQqG,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACnD,MAAM7D,IAAA,GAAO,KAAKrB,KAAA,CAAMkF,CAAC;UAEzB,MAAMsB,UAAA,GAAarG,YAAA,CAAakG,WAAA,CAAYnB,CAAC;UAC7C,MAAMvD,aAAA,GAAgBxB,YAAA,CAAawB,aAAA,CAAcuD,CAAC;UAElDsB,UAAA,CAAWf,IAAA,CAAKpE,IAAA,CAAKC,MAAM;UAE3BK,aAAA,CAAcmC,CAAA,CAAE2B,IAAA,CAAKpE,IAAA,CAAKM,aAAA,CAAc,CAAC,CAAC;UAC1CA,aAAA,CAAcoC,CAAA,CAAE0B,IAAA,CAAKpE,IAAA,CAAKM,aAAA,CAAc,CAAC,CAAC;UAC1CA,aAAA,CAAcqC,CAAA,CAAEyB,IAAA,CAAKpE,IAAA,CAAKM,aAAA,CAAc,CAAC,CAAC;QAC3C;MACF;MAID,SAASuD,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKnF,KAAA,CAAMnB,MAAA,EAAQqG,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACnD,MAAM7D,IAAA,GAAO,KAAKrB,KAAA,CAAMkF,CAAC;QAEzB7D,IAAA,CAAKC,MAAA,GAASD,IAAA,CAAK6E,oBAAA;QACnB7E,IAAA,CAAKM,aAAA,GAAgBN,IAAA,CAAK8E,uBAAA;MAC3B;IACF;IAEDvE,mBAAA,EAAqB;MACnB,IAAI,KAAKtC,WAAA,KAAgB,MAAM;QAC7B,KAAKA,WAAA,GAAc,IAAImH,IAAA,CAAM;MAC9B;MAED,KAAKnH,WAAA,CAAYoH,aAAA,CAAc,KAAK9H,QAAQ;IAC7C;IAEDiD,sBAAA,EAAwB;MACtB,IAAI,KAAKzC,cAAA,KAAmB,MAAM;QAChC,KAAKA,cAAA,GAAiB,IAAIuH,MAAA,CAAQ;MACnC;MAED,KAAKvH,cAAA,CAAesH,aAAA,CAAc,KAAK9H,QAAQ;IAChD;IAEDgI,MAAMrI,QAAA,EAAUuC,MAAA,EAAQ+F,mBAAA,GAAsB,GAAG;MAC/C,IAAI,EAAEtI,QAAA,IAAYA,QAAA,CAASmB,UAAA,GAAa;QACtC0D,OAAA,CAAQC,KAAA,CAAM,uEAAuE9E,QAAQ;QAC7F;MACD;MAED,IAAIwC,YAAA;MACJ,MAAM+F,YAAA,GAAe,KAAKlI,QAAA,CAASC,MAAA;QACjCkI,SAAA,GAAY,KAAKnI,QAAA;QACjBoI,SAAA,GAAYzI,QAAA,CAASK,QAAA;QACrBqI,MAAA,GAAS,KAAKjH,KAAA;QACdkH,MAAA,GAAS3I,QAAA,CAASyB,KAAA;QAClBmH,OAAA,GAAU,KAAKrI,MAAA;QACfsI,OAAA,GAAU7I,QAAA,CAASO,MAAA;MAErB,IAAIgC,MAAA,KAAW,QAAW;QACxBC,YAAA,GAAe,IAAIC,OAAA,GAAUC,eAAA,CAAgBH,MAAM;MACpD;MAID,SAASI,CAAA,GAAI,GAAGC,EAAA,GAAK6F,SAAA,CAAUnI,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAClD,MAAME,MAAA,GAAS4F,SAAA,CAAU9F,CAAC;QAE1B,MAAMmG,UAAA,GAAajG,MAAA,CAAO/B,KAAA,CAAO;QAEjC,IAAIyB,MAAA,KAAW,QAAWuG,UAAA,CAAWxG,YAAA,CAAaC,MAAM;QAExDiG,SAAA,CAAUrD,IAAA,CAAK2D,UAAU;MAC1B;MAID,SAASnG,CAAA,GAAI,GAAGC,EAAA,GAAKiG,OAAA,CAAQvI,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAChDiG,OAAA,CAAQzD,IAAA,CAAK0D,OAAA,CAAQlG,CAAC,EAAE7B,KAAA,CAAK,CAAE;MAChC;MAID,SAAS6B,CAAA,GAAI,GAAGC,EAAA,GAAK+F,MAAA,CAAOrI,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC/C,MAAMG,IAAA,GAAO6F,MAAA,CAAOhG,CAAC;QACrB,IAAII,MAAA,EAAQgC,KAAA;QACZ,MAAMgE,iBAAA,GAAoBjG,IAAA,CAAKM,aAAA;UAC7B4F,gBAAA,GAAmBlG,IAAA,CAAK6C,YAAA;QAE1B,MAAMsD,QAAA,GAAW,IAAIrD,KAAA,CAAM9C,IAAA,CAAKyC,CAAA,GAAIgD,YAAA,EAAczF,IAAA,CAAK0C,CAAA,GAAI+C,YAAA,EAAczF,IAAA,CAAK2C,CAAA,GAAI8C,YAAY;QAC9FU,QAAA,CAASlG,MAAA,CAAOmE,IAAA,CAAKpE,IAAA,CAAKC,MAAM;QAEhC,IAAIP,YAAA,KAAiB,QAAW;UAC9ByG,QAAA,CAASlG,MAAA,CAAOC,YAAA,CAAaR,YAAY,EAAES,SAAA,CAAW;QACvD;QAED,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAK4F,iBAAA,CAAkBzI,MAAA,EAAQ4C,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UAC1DH,MAAA,GAASgG,iBAAA,CAAkB7F,CAAC,EAAEpC,KAAA,CAAO;UAErC,IAAI0B,YAAA,KAAiB,QAAW;YAC9BO,MAAA,CAAOC,YAAA,CAAaR,YAAY,EAAES,SAAA,CAAW;UAC9C;UAEDgG,QAAA,CAAS7F,aAAA,CAAc+B,IAAA,CAAKpC,MAAM;QACnC;QAEDkG,QAAA,CAASlE,KAAA,CAAMmC,IAAA,CAAKpE,IAAA,CAAKiC,KAAK;QAE9B,SAAS7B,CAAA,GAAI,GAAGC,EAAA,GAAK6F,gBAAA,CAAiB1I,MAAA,EAAQ4C,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACzD6B,KAAA,GAAQiE,gBAAA,CAAiB9F,CAAC;UAC1B+F,QAAA,CAAStD,YAAA,CAAaR,IAAA,CAAKJ,KAAA,CAAMjE,KAAA,CAAK,CAAE;QACzC;QAEDmI,QAAA,CAASvD,aAAA,GAAgB5C,IAAA,CAAK4C,aAAA,GAAgB4C,mBAAA;QAE9CI,MAAA,CAAOvD,IAAA,CAAK8D,QAAQ;MACrB;MAID,SAAStG,CAAA,GAAI,GAAGC,EAAA,GAAK5C,QAAA,CAAS0B,aAAA,CAAcpB,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC/D,MAAMuG,cAAA,GAAiBlJ,QAAA,CAAS0B,aAAA,CAAciB,CAAC;QAE/C,IAAI,KAAKjB,aAAA,CAAciB,CAAC,MAAM,QAAW,KAAKjB,aAAA,CAAciB,CAAC,IAAI,EAAE;QAEnE,SAASO,CAAA,GAAI,GAAGC,EAAA,GAAK+F,cAAA,CAAe5I,MAAA,EAAQ4C,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACvD,MAAMiG,IAAA,GAAOD,cAAA,CAAehG,CAAC;YAC3BkG,OAAA,GAAU,EAAE;UAEd,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKH,IAAA,CAAK7I,MAAA,EAAQ+I,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;YAC7CD,OAAA,CAAQjE,IAAA,CAAKgE,IAAA,CAAKE,CAAC,EAAEvI,KAAA,CAAK,CAAE;UAC7B;UAED,KAAKY,aAAA,CAAciB,CAAC,EAAEwC,IAAA,CAAKiE,OAAO;QACnC;MACF;IACF;IAEDG,UAAUC,IAAA,EAAM;MACd,IAAI,EAAEA,IAAA,IAAQA,IAAA,CAAKxI,MAAA,GAAS;QAC1B6D,OAAA,CAAQC,KAAA,CAAM,mEAAmE0E,IAAI;QACrF;MACD;MAED,IAAIA,IAAA,CAAKC,gBAAA,EAAkBD,IAAA,CAAKjF,YAAA,CAAc;MAE9C,KAAK8D,KAAA,CAAMmB,IAAA,CAAKxJ,QAAA,EAAUwJ,IAAA,CAAKjH,MAAM;IACtC;IAAA;AAAA;AAAA;AAAA;AAAA;IAQDmH,cAAcC,eAAA,GAAkB,GAAG;MACjC,MAAMC,WAAA,GAAc,CAAE;MACtB,MAAMC,MAAA,GAAS,EAAE;QACfC,OAAA,GAAU,EAAE;MAEd,MAAMC,SAAA,GAAYC,IAAA,CAAKC,GAAA,CAAI,IAAIN,eAAe;MAE9C,SAAShH,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKvC,QAAA,CAASC,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACtD,MAAM2E,CAAA,GAAI,KAAKjH,QAAA,CAASsC,CAAC;QACzB,MAAMuH,GAAA,GAAM,GAAGF,IAAA,CAAKG,KAAA,CAAM7C,CAAA,CAAEvD,CAAA,GAAIgG,SAAS,KAAKC,IAAA,CAAKG,KAAA,CAAM7C,CAAA,CAAEtD,CAAA,GAAI+F,SAAS,KAAKC,IAAA,CAAKG,KAAA,CAAM7C,CAAA,CAAErD,CAAA,GAAI8F,SAAS;QAEvG,IAAIH,WAAA,CAAYM,GAAG,MAAM,QAAW;UAClCN,WAAA,CAAYM,GAAG,IAAIvH,CAAA;UACnBkH,MAAA,CAAO1E,IAAA,CAAK,KAAK9E,QAAA,CAASsC,CAAC,CAAC;UAC5BmH,OAAA,CAAQnH,CAAC,IAAIkH,MAAA,CAAOvJ,MAAA,GAAS;QACvC,OAAe;UAELwJ,OAAA,CAAQnH,CAAC,IAAImH,OAAA,CAAQF,WAAA,CAAYM,GAAG,CAAC;QACtC;MACF;MAID,MAAME,mBAAA,GAAsB,EAAE;MAE9B,SAASzH,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKnB,KAAA,CAAMnB,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACnD,MAAMG,IAAA,GAAO,KAAKrB,KAAA,CAAMkB,CAAC;QAEzBG,IAAA,CAAKyC,CAAA,GAAIuE,OAAA,CAAQhH,IAAA,CAAKyC,CAAC;QACvBzC,IAAA,CAAK0C,CAAA,GAAIsE,OAAA,CAAQhH,IAAA,CAAK0C,CAAC;QACvB1C,IAAA,CAAK2C,CAAA,GAAIqE,OAAA,CAAQhH,IAAA,CAAK2C,CAAC;QAEvB,MAAM4E,OAAA,GAAU,CAACvH,IAAA,CAAKyC,CAAA,EAAGzC,IAAA,CAAK0C,CAAA,EAAG1C,IAAA,CAAK2C,CAAC;QAIvC,SAAS6E,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;UAC1B,IAAID,OAAA,CAAQC,CAAC,MAAMD,OAAA,EAASC,CAAA,GAAI,KAAK,CAAC,GAAG;YACvCF,mBAAA,CAAoBjF,IAAA,CAAKxC,CAAC;YAC1B;UACD;QACF;MACF;MAED,SAASA,CAAA,GAAIyH,mBAAA,CAAoB9J,MAAA,GAAS,GAAGqC,CAAA,IAAK,GAAGA,CAAA,IAAK;QACxD,MAAM4H,GAAA,GAAMH,mBAAA,CAAoBzH,CAAC;QAEjC,KAAKlB,KAAA,CAAM+I,MAAA,CAAOD,GAAA,EAAK,CAAC;QAExB,SAASrH,CAAA,GAAI,GAAGC,EAAA,GAAK,KAAKzB,aAAA,CAAcpB,MAAA,EAAQ4C,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UAC3D,KAAKxB,aAAA,CAAcwB,CAAC,EAAEsH,MAAA,CAAOD,GAAA,EAAK,CAAC;QACpC;MACF;MAID,MAAME,IAAA,GAAO,KAAKpK,QAAA,CAASC,MAAA,GAASuJ,MAAA,CAAOvJ,MAAA;MAC3C,KAAKD,QAAA,GAAWwJ,MAAA;MAChB,OAAOY,IAAA;IACR;IAEDtC,cAAcuC,MAAA,EAAQ;MACpB,KAAKrK,QAAA,GAAW,EAAE;MAElB,SAASsC,CAAA,GAAI,GAAGgI,CAAA,GAAID,MAAA,CAAOpK,MAAA,EAAQqC,CAAA,GAAIgI,CAAA,EAAGhI,CAAA,IAAK;QAC7C,MAAMiI,KAAA,GAAQF,MAAA,CAAO/H,CAAC;QACtB,KAAKtC,QAAA,CAAS8E,IAAA,CAAK,IAAI3F,OAAA,CAAQoL,KAAA,CAAM7G,CAAA,EAAG6G,KAAA,CAAM5G,CAAA,EAAG4G,KAAA,CAAM3G,CAAA,IAAK,CAAC,CAAC;MAC/D;MAED,OAAO;IACR;IAED4G,yBAAA,EAA2B;MACzB,MAAMpJ,KAAA,GAAQ,KAAKA,KAAA;MACnB,MAAMnB,MAAA,GAASmB,KAAA,CAAMnB,MAAA;MAIrB,SAASqC,CAAA,GAAI,GAAGA,CAAA,GAAIrC,MAAA,EAAQqC,CAAA,IAAK;QAC/BlB,KAAA,CAAMkB,CAAC,EAAEmI,GAAA,GAAMnI,CAAA;MAChB;MAID,SAASoI,kBAAkBxF,CAAA,EAAGC,CAAA,EAAG;QAC/B,OAAOD,CAAA,CAAEG,aAAA,GAAgBF,CAAA,CAAEE,aAAA;MAC5B;MAEDjE,KAAA,CAAMuJ,IAAA,CAAKD,iBAAiB;MAI5B,MAAME,IAAA,GAAO,KAAKvJ,aAAA,CAAc,CAAC;MACjC,MAAMyH,IAAA,GAAO,KAAKzH,aAAA,CAAc,CAAC;MAEjC,IAAIwJ,OAAA,EAASC,OAAA;MAEb,IAAIF,IAAA,IAAQA,IAAA,CAAK3K,MAAA,KAAWA,MAAA,EAAQ4K,OAAA,GAAU,EAAE;MAChD,IAAI/B,IAAA,IAAQA,IAAA,CAAK7I,MAAA,KAAWA,MAAA,EAAQ6K,OAAA,GAAU,EAAE;MAEhD,SAASxI,CAAA,GAAI,GAAGA,CAAA,GAAIrC,MAAA,EAAQqC,CAAA,IAAK;QAC/B,MAAMyI,EAAA,GAAK3J,KAAA,CAAMkB,CAAC,EAAEmI,GAAA;QAEpB,IAAII,OAAA,EAASA,OAAA,CAAQ/F,IAAA,CAAK8F,IAAA,CAAKG,EAAE,CAAC;QAClC,IAAID,OAAA,EAASA,OAAA,CAAQhG,IAAA,CAAKgE,IAAA,CAAKiC,EAAE,CAAC;MACnC;MAED,IAAIF,OAAA,EAAS,KAAKxJ,aAAA,CAAc,CAAC,IAAIwJ,OAAA;MACrC,IAAIC,OAAA,EAAS,KAAKzJ,aAAA,CAAc,CAAC,IAAIyJ,OAAA;IACtC;IAEDE,OAAA,EAAS;MACP,MAAMC,IAAA,GAAO;QACXC,QAAA,EAAU;UACRC,OAAA,EAAS;UACThK,IAAA,EAAM;UACNiK,SAAA,EAAW;QACZ;MACF;MAIDH,IAAA,CAAKlK,IAAA,GAAO,KAAKA,IAAA;MACjBkK,IAAA,CAAK9J,IAAA,GAAO,KAAKA,IAAA;MACjB,IAAI,KAAKD,IAAA,KAAS,IAAI+J,IAAA,CAAK/J,IAAA,GAAO,KAAKA,IAAA;MAEvC,IAAI,KAAKmK,UAAA,KAAe,QAAW;QACjC,MAAMA,UAAA,GAAa,KAAKA,UAAA;QAExB,SAASxB,GAAA,IAAOwB,UAAA,EAAY;UAC1B,IAAIA,UAAA,CAAWxB,GAAG,MAAM,QAAWoB,IAAA,CAAKpB,GAAG,IAAIwB,UAAA,CAAWxB,GAAG;QAC9D;QAED,OAAOoB,IAAA;MACR;MAED,MAAMjL,QAAA,GAAW,EAAE;MAEnB,SAASsC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKtC,QAAA,CAASC,MAAA,EAAQqC,CAAA,IAAK;QAC7C,MAAME,MAAA,GAAS,KAAKxC,QAAA,CAASsC,CAAC;QAC9BtC,QAAA,CAAS8E,IAAA,CAAKtC,MAAA,CAAOkB,CAAA,EAAGlB,MAAA,CAAOmB,CAAA,EAAGnB,MAAA,CAAOoB,CAAC;MAC3C;MAED,MAAMxC,KAAA,GAAQ,EAAE;MAChB,MAAMkK,OAAA,GAAU,EAAE;MAClB,MAAMC,WAAA,GAAc,CAAE;MACtB,MAAMrL,MAAA,GAAS,EAAE;MACjB,MAAMsL,UAAA,GAAa,CAAE;MACrB,MAAMC,GAAA,GAAM,EAAE;MACd,MAAMC,OAAA,GAAU,CAAE;MAElB,SAASpJ,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKlB,KAAA,CAAMnB,MAAA,EAAQqC,CAAA,IAAK;QAC1C,MAAMG,IAAA,GAAO,KAAKrB,KAAA,CAAMkB,CAAC;QAEzB,MAAMqJ,WAAA,GAAc;QACpB,MAAMC,SAAA,GAAY;QAClB,MAAMC,eAAA,GAAkB,KAAKxK,aAAA,CAAc,CAAC,EAAEiB,CAAC,MAAM;QACrD,MAAMwJ,aAAA,GAAgBrJ,IAAA,CAAKC,MAAA,CAAOzC,MAAA,CAAQ,IAAG;QAC7C,MAAM8L,mBAAA,GAAsBtJ,IAAA,CAAKM,aAAA,CAAc9C,MAAA,GAAS;QACxD,MAAM+L,YAAA,GAAevJ,IAAA,CAAKiC,KAAA,CAAMuH,CAAA,KAAM,KAAKxJ,IAAA,CAAKiC,KAAA,CAAMwH,CAAA,KAAM,KAAKzJ,IAAA,CAAKiC,KAAA,CAAMS,CAAA,KAAM;QAClF,MAAMgH,kBAAA,GAAqB1J,IAAA,CAAK6C,YAAA,CAAarF,MAAA,GAAS;QAEtD,IAAImM,QAAA,GAAW;QAEfA,QAAA,GAAWC,MAAA,CAAOD,QAAA,EAAU,GAAG,CAAC;QAChCA,QAAA,GAAWC,MAAA,CAAOD,QAAA,EAAU,GAAGT,WAAW;QAC1CS,QAAA,GAAWC,MAAA,CAAOD,QAAA,EAAU,GAAGR,SAAS;QACxCQ,QAAA,GAAWC,MAAA,CAAOD,QAAA,EAAU,GAAGP,eAAe;QAC9CO,QAAA,GAAWC,MAAA,CAAOD,QAAA,EAAU,GAAGN,aAAa;QAC5CM,QAAA,GAAWC,MAAA,CAAOD,QAAA,EAAU,GAAGL,mBAAmB;QAClDK,QAAA,GAAWC,MAAA,CAAOD,QAAA,EAAU,GAAGJ,YAAY;QAC3CI,QAAA,GAAWC,MAAA,CAAOD,QAAA,EAAU,GAAGD,kBAAkB;QAEjD/K,KAAA,CAAM0D,IAAA,CAAKsH,QAAQ;QACnBhL,KAAA,CAAM0D,IAAA,CAAKrC,IAAA,CAAKyC,CAAA,EAAGzC,IAAA,CAAK0C,CAAA,EAAG1C,IAAA,CAAK2C,CAAC;QACjChE,KAAA,CAAM0D,IAAA,CAAKrC,IAAA,CAAK4C,aAAa;QAE7B,IAAIwG,eAAA,EAAiB;UACnB,MAAMxK,aAAA,GAAgB,KAAKA,aAAA,CAAc,CAAC,EAAEiB,CAAC;UAE7ClB,KAAA,CAAM0D,IAAA,CAAKwH,UAAA,CAAWjL,aAAA,CAAc,CAAC,CAAC,GAAGiL,UAAA,CAAWjL,aAAA,CAAc,CAAC,CAAC,GAAGiL,UAAA,CAAWjL,aAAA,CAAc,CAAC,CAAC,CAAC;QACpG;QAED,IAAIyK,aAAA,EAAe;UACjB1K,KAAA,CAAM0D,IAAA,CAAKyH,cAAA,CAAe9J,IAAA,CAAKC,MAAM,CAAC;QACvC;QAED,IAAIqJ,mBAAA,EAAqB;UACvB,MAAMhJ,aAAA,GAAgBN,IAAA,CAAKM,aAAA;UAE3B3B,KAAA,CAAM0D,IAAA,CACJyH,cAAA,CAAexJ,aAAA,CAAc,CAAC,CAAC,GAC/BwJ,cAAA,CAAexJ,aAAA,CAAc,CAAC,CAAC,GAC/BwJ,cAAA,CAAexJ,aAAA,CAAc,CAAC,CAAC,CAChC;QACF;QAED,IAAIiJ,YAAA,EAAc;UAChB5K,KAAA,CAAM0D,IAAA,CAAK0H,aAAA,CAAc/J,IAAA,CAAKiC,KAAK,CAAC;QACrC;QAED,IAAIyH,kBAAA,EAAoB;UACtB,MAAM7G,YAAA,GAAe7C,IAAA,CAAK6C,YAAA;UAE1BlE,KAAA,CAAM0D,IAAA,CAAK0H,aAAA,CAAclH,YAAA,CAAa,CAAC,CAAC,GAAGkH,aAAA,CAAclH,YAAA,CAAa,CAAC,CAAC,GAAGkH,aAAA,CAAclH,YAAA,CAAa,CAAC,CAAC,CAAC;QAC1G;MACF;MAED,SAAS+G,OAAOI,KAAA,EAAOlI,QAAA,EAAUmI,OAAA,EAAS;QACxC,OAAOA,OAAA,GAAUD,KAAA,GAAS,KAAKlI,QAAA,GAAYkI,KAAA,GAAQ,EAAE,KAAKlI,QAAA;MAC3D;MAED,SAASgI,eAAe7J,MAAA,EAAQ;QAC9B,MAAMiK,IAAA,GAAOjK,MAAA,CAAOgB,CAAA,CAAEkJ,QAAA,CAAQ,IAAKlK,MAAA,CAAOiB,CAAA,CAAEiJ,QAAA,CAAU,IAAGlK,MAAA,CAAOkB,CAAA,CAAEgJ,QAAA,CAAU;QAE5E,IAAIrB,WAAA,CAAYoB,IAAI,MAAM,QAAW;UACnC,OAAOpB,WAAA,CAAYoB,IAAI;QACxB;QAEDpB,WAAA,CAAYoB,IAAI,IAAIrB,OAAA,CAAQrL,MAAA,GAAS;QACrCqL,OAAA,CAAQxG,IAAA,CAAKpC,MAAA,CAAOgB,CAAA,EAAGhB,MAAA,CAAOiB,CAAA,EAAGjB,MAAA,CAAOkB,CAAC;QAEzC,OAAO2H,WAAA,CAAYoB,IAAI;MACxB;MAED,SAASH,cAAc9H,KAAA,EAAO;QAC5B,MAAMiI,IAAA,GAAOjI,KAAA,CAAMuH,CAAA,CAAEW,QAAA,CAAQ,IAAKlI,KAAA,CAAMwH,CAAA,CAAEU,QAAA,CAAU,IAAGlI,KAAA,CAAMS,CAAA,CAAEyH,QAAA,CAAU;QAEzE,IAAIpB,UAAA,CAAWmB,IAAI,MAAM,QAAW;UAClC,OAAOnB,UAAA,CAAWmB,IAAI;QACvB;QAEDnB,UAAA,CAAWmB,IAAI,IAAIzM,MAAA,CAAOD,MAAA;QAC1BC,MAAA,CAAO4E,IAAA,CAAKJ,KAAA,CAAMmI,MAAA,EAAQ;QAE1B,OAAOrB,UAAA,CAAWmB,IAAI;MACvB;MAED,SAASL,WAAW3H,EAAA,EAAI;QACtB,MAAMgI,IAAA,GAAOhI,EAAA,CAAGjB,CAAA,CAAEkJ,QAAA,CAAQ,IAAKjI,EAAA,CAAGhB,CAAA,CAAEiJ,QAAA,CAAU;QAE9C,IAAIlB,OAAA,CAAQiB,IAAI,MAAM,QAAW;UAC/B,OAAOjB,OAAA,CAAQiB,IAAI;QACpB;QAEDjB,OAAA,CAAQiB,IAAI,IAAIlB,GAAA,CAAIxL,MAAA,GAAS;QAC7BwL,GAAA,CAAI3G,IAAA,CAAKH,EAAA,CAAGjB,CAAA,EAAGiB,EAAA,CAAGhB,CAAC;QAEnB,OAAO+H,OAAA,CAAQiB,IAAI;MACpB;MAED1B,IAAA,CAAKA,IAAA,GAAO,CAAE;MAEdA,IAAA,CAAKA,IAAA,CAAKjL,QAAA,GAAWA,QAAA;MACrBiL,IAAA,CAAKA,IAAA,CAAKK,OAAA,GAAUA,OAAA;MACpB,IAAIpL,MAAA,CAAOD,MAAA,GAAS,GAAGgL,IAAA,CAAKA,IAAA,CAAK/K,MAAA,GAASA,MAAA;MAC1C,IAAIuL,GAAA,CAAIxL,MAAA,GAAS,GAAGgL,IAAA,CAAKA,IAAA,CAAKQ,GAAA,GAAM,CAACA,GAAG;MACxCR,IAAA,CAAKA,IAAA,CAAK7J,KAAA,GAAQA,KAAA;MAElB,OAAO6J,IAAA;IACR;IAEDxK,MAAA,EAAQ;MAyBN,OAAO,IAAIpB,SAAA,CAAQ,EAAGwH,IAAA,CAAK,IAAI;IAChC;IAEDA,KAAKiG,MAAA,EAAQ;MAGX,KAAK9M,QAAA,GAAW,EAAE;MAClB,KAAKE,MAAA,GAAS,EAAE;MAChB,KAAKkB,KAAA,GAAQ,EAAE;MACf,KAAKC,aAAA,GAAgB,CAAC,EAAE;MACxB,KAAKC,YAAA,GAAe,EAAE;MACtB,KAAKC,YAAA,GAAe,EAAE;MACtB,KAAKC,WAAA,GAAc,EAAE;MACrB,KAAKC,WAAA,GAAc,EAAE;MACrB,KAAKnB,aAAA,GAAgB,EAAE;MACvB,KAAKI,WAAA,GAAc;MACnB,KAAKF,cAAA,GAAiB;MAItB,KAAKU,IAAA,GAAO4L,MAAA,CAAO5L,IAAA;MAInB,MAAMlB,QAAA,GAAW8M,MAAA,CAAO9M,QAAA;MAExB,SAASsC,CAAA,GAAI,GAAGC,EAAA,GAAKvC,QAAA,CAASC,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACjD,KAAKtC,QAAA,CAAS8E,IAAA,CAAK9E,QAAA,CAASsC,CAAC,EAAE7B,KAAA,EAAO;MACvC;MAID,MAAMP,MAAA,GAAS4M,MAAA,CAAO5M,MAAA;MAEtB,SAASoC,CAAA,GAAI,GAAGC,EAAA,GAAKrC,MAAA,CAAOD,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC/C,KAAKpC,MAAA,CAAO4E,IAAA,CAAK5E,MAAA,CAAOoC,CAAC,EAAE7B,KAAA,EAAO;MACnC;MAID,MAAMW,KAAA,GAAQ0L,MAAA,CAAO1L,KAAA;MAErB,SAASkB,CAAA,GAAI,GAAGC,EAAA,GAAKnB,KAAA,CAAMnB,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC9C,KAAKlB,KAAA,CAAM0D,IAAA,CAAK1D,KAAA,CAAMkB,CAAC,EAAE7B,KAAA,EAAO;MACjC;MAID,SAAS6B,CAAA,GAAI,GAAGC,EAAA,GAAKuK,MAAA,CAAOzL,aAAA,CAAcpB,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QAC7D,MAAMjB,aAAA,GAAgByL,MAAA,CAAOzL,aAAA,CAAciB,CAAC;QAE5C,IAAI,KAAKjB,aAAA,CAAciB,CAAC,MAAM,QAAW;UACvC,KAAKjB,aAAA,CAAciB,CAAC,IAAI,EAAE;QAC3B;QAED,SAASO,CAAA,GAAI,GAAGC,EAAA,GAAKzB,aAAA,CAAcpB,MAAA,EAAQ4C,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;UACtD,MAAM4I,GAAA,GAAMpK,aAAA,CAAcwB,CAAC;YACzBkG,OAAA,GAAU,EAAE;UAEd,SAASC,CAAA,GAAI,GAAGC,EAAA,GAAKwC,GAAA,CAAIxL,MAAA,EAAQ+I,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;YAC5C,MAAMrE,EAAA,GAAK8G,GAAA,CAAIzC,CAAC;YAEhBD,OAAA,CAAQjE,IAAA,CAAKH,EAAA,CAAGlE,KAAA,EAAO;UACxB;UAED,KAAKY,aAAA,CAAciB,CAAC,EAAEwC,IAAA,CAAKiE,OAAO;QACnC;MACF;MAID,MAAMzH,YAAA,GAAewL,MAAA,CAAOxL,YAAA;MAE5B,SAASgB,CAAA,GAAI,GAAGC,EAAA,GAAKjB,YAAA,CAAarB,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACrD,MAAMyK,WAAA,GAAc,CAAE;QACtBA,WAAA,CAAY7L,IAAA,GAAOI,YAAA,CAAagB,CAAC,EAAEpB,IAAA;QAInC,IAAII,YAAA,CAAagB,CAAC,EAAEtC,QAAA,KAAa,QAAW;UAC1C+M,WAAA,CAAY/M,QAAA,GAAW,EAAE;UAEzB,SAAS6C,CAAA,GAAI,GAAGC,EAAA,GAAKxB,YAAA,CAAagB,CAAC,EAAEtC,QAAA,CAASC,MAAA,EAAQ4C,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;YACjEkK,WAAA,CAAY/M,QAAA,CAAS8E,IAAA,CAAKxD,YAAA,CAAagB,CAAC,EAAEtC,QAAA,CAAS6C,CAAC,EAAEpC,KAAA,EAAO;UAC9D;QACF;QAID,IAAIa,YAAA,CAAagB,CAAC,EAAEgJ,OAAA,KAAY,QAAW;UACzCyB,WAAA,CAAYzB,OAAA,GAAU,EAAE;UAExB,SAASzI,CAAA,GAAI,GAAGC,EAAA,GAAKxB,YAAA,CAAagB,CAAC,EAAEgJ,OAAA,CAAQrL,MAAA,EAAQ4C,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;YAChEkK,WAAA,CAAYzB,OAAA,CAAQxG,IAAA,CAAKxD,YAAA,CAAagB,CAAC,EAAEgJ,OAAA,CAAQzI,CAAC,EAAEpC,KAAA,EAAO;UAC5D;QACF;QAED,KAAKa,YAAA,CAAawD,IAAA,CAAKiI,WAAW;MACnC;MAID,MAAMxL,YAAA,GAAeuL,MAAA,CAAOvL,YAAA;MAE5B,SAASe,CAAA,GAAI,GAAGC,EAAA,GAAKhB,YAAA,CAAatB,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACrD,MAAM0K,WAAA,GAAc,CAAE;QAItB,IAAIzL,YAAA,CAAae,CAAC,EAAES,aAAA,KAAkB,QAAW;UAC/CiK,WAAA,CAAYjK,aAAA,GAAgB,EAAE;UAE9B,SAASF,CAAA,GAAI,GAAGC,EAAA,GAAKvB,YAAA,CAAae,CAAC,EAAES,aAAA,CAAc9C,MAAA,EAAQ4C,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;YACtE,MAAMoK,eAAA,GAAkB1L,YAAA,CAAae,CAAC,EAAES,aAAA,CAAcF,CAAC;YACvD,MAAMqK,gBAAA,GAAmB,CAAE;YAE3BA,gBAAA,CAAiBhI,CAAA,GAAI+H,eAAA,CAAgB/H,CAAA,CAAEzE,KAAA,CAAO;YAC9CyM,gBAAA,CAAiB/H,CAAA,GAAI8H,eAAA,CAAgB9H,CAAA,CAAE1E,KAAA,CAAO;YAC9CyM,gBAAA,CAAiB9H,CAAA,GAAI6H,eAAA,CAAgB7H,CAAA,CAAE3E,KAAA,CAAO;YAE9CuM,WAAA,CAAYjK,aAAA,CAAc+B,IAAA,CAAKoI,gBAAgB;UAChD;QACF;QAID,IAAI3L,YAAA,CAAae,CAAC,EAAEmF,WAAA,KAAgB,QAAW;UAC7CuF,WAAA,CAAYvF,WAAA,GAAc,EAAE;UAE5B,SAAS5E,CAAA,GAAI,GAAGC,EAAA,GAAKvB,YAAA,CAAae,CAAC,EAAEmF,WAAA,CAAYxH,MAAA,EAAQ4C,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;YACpEmK,WAAA,CAAYvF,WAAA,CAAY3C,IAAA,CAAKvD,YAAA,CAAae,CAAC,EAAEmF,WAAA,CAAY5E,CAAC,EAAEpC,KAAA,EAAO;UACpE;QACF;QAED,KAAKc,YAAA,CAAauD,IAAA,CAAKkI,WAAW;MACnC;MAID,MAAMxL,WAAA,GAAcsL,MAAA,CAAOtL,WAAA;MAE3B,SAASc,CAAA,GAAI,GAAGC,EAAA,GAAKf,WAAA,CAAYvB,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACpD,KAAKd,WAAA,CAAYsD,IAAA,CAAKtD,WAAA,CAAYc,CAAC,EAAE7B,KAAA,EAAO;MAC7C;MAID,MAAMgB,WAAA,GAAcqL,MAAA,CAAOrL,WAAA;MAE3B,SAASa,CAAA,GAAI,GAAGC,EAAA,GAAKd,WAAA,CAAYxB,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACpD,KAAKb,WAAA,CAAYqD,IAAA,CAAKrD,WAAA,CAAYa,CAAC,EAAE7B,KAAA,EAAO;MAC7C;MAID,MAAMH,aAAA,GAAgBwM,MAAA,CAAOxM,aAAA;MAE7B,SAASgC,CAAA,GAAI,GAAGC,EAAA,GAAKjC,aAAA,CAAcL,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;QACtD,KAAKhC,aAAA,CAAcwE,IAAA,CAAKxE,aAAA,CAAcgC,CAAC,CAAC;MACzC;MAID,MAAM5B,WAAA,GAAcoM,MAAA,CAAOpM,WAAA;MAE3B,IAAIA,WAAA,KAAgB,MAAM;QACxB,KAAKA,WAAA,GAAcA,WAAA,CAAYD,KAAA,CAAO;MACvC;MAID,MAAMD,cAAA,GAAiBsM,MAAA,CAAOtM,cAAA;MAE9B,IAAIA,cAAA,KAAmB,MAAM;QAC3B,KAAKA,cAAA,GAAiBA,cAAA,CAAeC,KAAA,CAAO;MAC7C;MAID,KAAKiB,kBAAA,GAAqBoL,MAAA,CAAOpL,kBAAA;MACjC,KAAKC,kBAAA,GAAqBmL,MAAA,CAAOnL,kBAAA;MACjC,KAAKC,aAAA,GAAgBkL,MAAA,CAAOlL,aAAA;MAC5B,KAAKC,iBAAA,GAAoBiL,MAAA,CAAOjL,iBAAA;MAChC,KAAKC,gBAAA,GAAmBgL,MAAA,CAAOhL,gBAAA;MAC/B,KAAKC,uBAAA,GAA0B+K,MAAA,CAAO/K,uBAAA;MACtC,KAAKC,gBAAA,GAAmB8K,MAAA,CAAO9K,gBAAA;MAE/B,OAAO;IACR;IAEDpB,iBAAA,EAAmB;MACjB,MAAMjB,QAAA,GAAW,IAAIwN,cAAA,GAAiBC,YAAA,CAAa,IAAI;MAEvD,MAAM3N,cAAA,GAAiB,IAAIC,cAAA,CAAgB;MAE3C,MAAMI,SAAA,GAAY,IAAIuN,YAAA,CAAa1N,QAAA,CAASK,QAAA,CAASC,MAAA,GAAS,CAAC;MAC/DR,cAAA,CAAeU,YAAA,CAAa,YAAY,IAAImN,eAAA,CAAgBxN,SAAA,EAAW,CAAC,EAAEM,iBAAA,CAAkBT,QAAA,CAASK,QAAQ,CAAC;MAE9G,IAAIL,QAAA,CAAS2L,OAAA,CAAQrL,MAAA,GAAS,GAAG;QAC/B,MAAMqL,OAAA,GAAU,IAAI+B,YAAA,CAAa1N,QAAA,CAAS2L,OAAA,CAAQrL,MAAA,GAAS,CAAC;QAC5DR,cAAA,CAAeU,YAAA,CAAa,UAAU,IAAImN,eAAA,CAAgBhC,OAAA,EAAS,CAAC,EAAElL,iBAAA,CAAkBT,QAAA,CAAS2L,OAAO,CAAC;MAC1G;MAED,IAAI3L,QAAA,CAASO,MAAA,CAAOD,MAAA,GAAS,GAAG;QAC9B,MAAMC,MAAA,GAAS,IAAImN,YAAA,CAAa1N,QAAA,CAASO,MAAA,CAAOD,MAAA,GAAS,CAAC;QAC1DR,cAAA,CAAeU,YAAA,CAAa,SAAS,IAAImN,eAAA,CAAgBpN,MAAA,EAAQ,CAAC,EAAEG,eAAA,CAAgBV,QAAA,CAASO,MAAM,CAAC;MACrG;MAED,IAAIP,QAAA,CAAS8L,GAAA,CAAIxL,MAAA,GAAS,GAAG;QAC3B,MAAMwL,GAAA,GAAM,IAAI4B,YAAA,CAAa1N,QAAA,CAAS8L,GAAA,CAAIxL,MAAA,GAAS,CAAC;QACpDR,cAAA,CAAeU,YAAA,CAAa,MAAM,IAAImN,eAAA,CAAgB7B,GAAA,EAAK,CAAC,EAAE8B,iBAAA,CAAkB5N,QAAA,CAAS8L,GAAG,CAAC;MAC9F;MAED,IAAI9L,QAAA,CAASmJ,IAAA,CAAK7I,MAAA,GAAS,GAAG;QAC5B,MAAM6I,IAAA,GAAO,IAAIuE,YAAA,CAAa1N,QAAA,CAASmJ,IAAA,CAAK7I,MAAA,GAAS,CAAC;QACtDR,cAAA,CAAeU,YAAA,CAAa,OAAO,IAAImN,eAAA,CAAgBxE,IAAA,EAAM,CAAC,EAAEyE,iBAAA,CAAkB5N,QAAA,CAASmJ,IAAI,CAAC;MACjG;MAIDrJ,cAAA,CAAegG,MAAA,GAAS9F,QAAA,CAAS8F,MAAA;MAIjC,SAASvE,IAAA,IAAQvB,QAAA,CAAS2B,YAAA,EAAc;QACtC,MAAMkM,KAAA,GAAQ,EAAE;QAChB,MAAMlM,YAAA,GAAe3B,QAAA,CAAS2B,YAAA,CAAaJ,IAAI;QAE/C,SAASoB,CAAA,GAAI,GAAGgI,CAAA,GAAIhJ,YAAA,CAAarB,MAAA,EAAQqC,CAAA,GAAIgI,CAAA,EAAGhI,CAAA,IAAK;UACnD,MAAMyK,WAAA,GAAczL,YAAA,CAAagB,CAAC;UAElC,MAAMmL,SAAA,GAAY,IAAI1N,sBAAA,CAAuBgN,WAAA,CAAY9B,IAAA,CAAKhL,MAAA,GAAS,GAAG,CAAC;UAC3EwN,SAAA,CAAUvM,IAAA,GAAO6L,WAAA,CAAY7L,IAAA;UAE7BsM,KAAA,CAAM1I,IAAA,CAAK2I,SAAA,CAAUrN,iBAAA,CAAkB2M,WAAA,CAAY9B,IAAI,CAAC;QACzD;QAEDxL,cAAA,CAAeiO,eAAA,CAAgBxM,IAAI,IAAIsM,KAAA;MACxC;MAID,IAAI7N,QAAA,CAAS8B,WAAA,CAAYxB,MAAA,GAAS,GAAG;QACnC,MAAMwB,WAAA,GAAc,IAAI1B,sBAAA,CAAuBJ,QAAA,CAAS8B,WAAA,CAAYxB,MAAA,GAAS,GAAG,CAAC;QACjFR,cAAA,CAAeU,YAAA,CAAa,aAAasB,WAAA,CAAYkM,iBAAA,CAAkBhO,QAAA,CAAS8B,WAAW,CAAC;MAC7F;MAED,IAAI9B,QAAA,CAAS6B,WAAA,CAAYvB,MAAA,GAAS,GAAG;QACnC,MAAMuB,WAAA,GAAc,IAAIzB,sBAAA,CAAuBJ,QAAA,CAAS6B,WAAA,CAAYvB,MAAA,GAAS,GAAG,CAAC;QACjFR,cAAA,CAAeU,YAAA,CAAa,cAAcqB,WAAA,CAAYmM,iBAAA,CAAkBhO,QAAA,CAAS6B,WAAW,CAAC;MAC9F;MAID,IAAI7B,QAAA,CAASa,cAAA,KAAmB,MAAM;QACpCf,cAAA,CAAee,cAAA,GAAiBb,QAAA,CAASa,cAAA,CAAeC,KAAA,CAAO;MAChE;MAED,IAAId,QAAA,CAASe,WAAA,KAAgB,MAAM;QACjCjB,cAAA,CAAeiB,WAAA,GAAcf,QAAA,CAASe,WAAA,CAAYD,KAAA,CAAO;MAC1D;MAED,OAAOhB,cAAA;IACR;IAEDmO,gBAAA,EAAkB;MAChBpJ,OAAA,CAAQC,KAAA,CAAM,sDAAsD;IACrE;IAEDoJ,qBAAA,EAAuB;MACrBrJ,OAAA,CAAQC,KAAA,CACN,0GACD;IACF;IAEDqJ,YAAY5L,MAAA,EAAQ;MAClBsC,OAAA,CAAQuJ,IAAA,CAAK,qEAAqE;MAClF,OAAO,KAAK9L,YAAA,CAAaC,MAAM;IAChC;IAED8L,QAAA,EAAU;MACR,KAAKC,aAAA,CAAc;QAAE9M,IAAA,EAAM;MAAS,CAAE;IACvC;EACF;EAED,OAAO9B,SAAA;AACT,GAAI;AAEJ,MAAM8N,cAAA,CAAe;EACnBtM,YAAA,EAAc;IACZ,KAAKb,QAAA,GAAW,EAAE;IAClB,KAAKsL,OAAA,GAAU,EAAE;IACjB,KAAKpL,MAAA,GAAS,EAAE;IAChB,KAAKuL,GAAA,GAAM,EAAE;IACb,KAAK3C,IAAA,GAAO,EAAE;IAEd,KAAKrD,MAAA,GAAS,EAAE;IAEhB,KAAKnE,YAAA,GAAe,CAAE;IAEtB,KAAKE,WAAA,GAAc,EAAE;IACrB,KAAKC,WAAA,GAAc,EAAE;IAIrB,KAAKf,WAAA,GAAc;IACnB,KAAKF,cAAA,GAAiB;IAItB,KAAKmB,kBAAA,GAAqB;IAC1B,KAAKE,iBAAA,GAAoB;IACzB,KAAKC,gBAAA,GAAmB;IACxB,KAAKF,aAAA,GAAgB;IACrB,KAAKI,gBAAA,GAAmB;EACzB;EAEDkM,cAAcvO,QAAA,EAAU;IACtB,MAAM8F,MAAA,GAAS,EAAE;IAEjB,IAAIC,KAAA,EAAOpD,CAAA;IACX,IAAI+C,aAAA,GAAgB;IAEpB,MAAMjE,KAAA,GAAQzB,QAAA,CAASyB,KAAA;IAEvB,KAAKkB,CAAA,GAAI,GAAGA,CAAA,GAAIlB,KAAA,CAAMnB,MAAA,EAAQqC,CAAA,IAAK;MACjC,MAAMG,IAAA,GAAOrB,KAAA,CAAMkB,CAAC;MAIpB,IAAIG,IAAA,CAAK4C,aAAA,KAAkBA,aAAA,EAAe;QACxCA,aAAA,GAAgB5C,IAAA,CAAK4C,aAAA;QAErB,IAAIK,KAAA,KAAU,QAAW;UACvBA,KAAA,CAAMb,KAAA,GAAQvC,CAAA,GAAI,IAAIoD,KAAA,CAAMC,KAAA;UAC5BF,MAAA,CAAOX,IAAA,CAAKY,KAAK;QAClB;QAEDA,KAAA,GAAQ;UACNC,KAAA,EAAOrD,CAAA,GAAI;UACX+C;QACD;MACF;IACF;IAED,IAAIK,KAAA,KAAU,QAAW;MACvBA,KAAA,CAAMb,KAAA,GAAQvC,CAAA,GAAI,IAAIoD,KAAA,CAAMC,KAAA;MAC5BF,MAAA,CAAOX,IAAA,CAAKY,KAAK;IAClB;IAED,KAAKD,MAAA,GAASA,MAAA;EACf;EAED2H,aAAazN,QAAA,EAAU;IACrB,MAAMyB,KAAA,GAAQzB,QAAA,CAASyB,KAAA;IACvB,MAAMpB,QAAA,GAAWL,QAAA,CAASK,QAAA;IAC1B,MAAMqB,aAAA,GAAgB1B,QAAA,CAAS0B,aAAA;IAE/B,MAAMwK,eAAA,GAAkBxK,aAAA,CAAc,CAAC,KAAKA,aAAA,CAAc,CAAC,EAAEpB,MAAA,GAAS;IACtE,MAAMkO,gBAAA,GAAmB9M,aAAA,CAAc,CAAC,KAAKA,aAAA,CAAc,CAAC,EAAEpB,MAAA,GAAS;IAIvE,MAAMqB,YAAA,GAAe3B,QAAA,CAAS2B,YAAA;IAC9B,MAAM8M,kBAAA,GAAqB9M,YAAA,CAAarB,MAAA;IAExC,IAAIoO,oBAAA;IAEJ,IAAID,kBAAA,GAAqB,GAAG;MAC1BC,oBAAA,GAAuB,EAAE;MAEzB,SAAS/L,CAAA,GAAI,GAAGA,CAAA,GAAI8L,kBAAA,EAAoB9L,CAAA,IAAK;QAC3C+L,oBAAA,CAAqB/L,CAAC,IAAI;UACxBpB,IAAA,EAAMI,YAAA,CAAagB,CAAC,EAAEpB,IAAA;UACtB+J,IAAA,EAAM;QACP;MACF;MAED,KAAK3J,YAAA,CAAaiD,QAAA,GAAW8J,oBAAA;IAC9B;IAED,MAAM9M,YAAA,GAAe5B,QAAA,CAAS4B,YAAA;IAC9B,MAAM+M,kBAAA,GAAqB/M,YAAA,CAAatB,MAAA;IAExC,IAAIsO,kBAAA;IAEJ,IAAID,kBAAA,GAAqB,GAAG;MAC1BC,kBAAA,GAAqB,EAAE;MAEvB,SAASjM,CAAA,GAAI,GAAGA,CAAA,GAAIgM,kBAAA,EAAoBhM,CAAA,IAAK;QAC3CiM,kBAAA,CAAmBjM,CAAC,IAAI;UACtBpB,IAAA,EAAMK,YAAA,CAAae,CAAC,EAAEpB,IAAA;UACtB+J,IAAA,EAAM;QACP;MACF;MAED,KAAK3J,YAAA,CAAaoB,MAAA,GAAS6L,kBAAA;IAC5B;IAID,MAAM9M,WAAA,GAAc9B,QAAA,CAAS8B,WAAA;IAC7B,MAAMD,WAAA,GAAc7B,QAAA,CAAS6B,WAAA;IAE7B,MAAMgN,cAAA,GAAiB/M,WAAA,CAAYxB,MAAA,KAAWD,QAAA,CAASC,MAAA;IACvD,MAAMwO,cAAA,GAAiBjN,WAAA,CAAYvB,MAAA,KAAWD,QAAA,CAASC,MAAA;IAIvD,IAAID,QAAA,CAASC,MAAA,GAAS,KAAKmB,KAAA,CAAMnB,MAAA,KAAW,GAAG;MAC7CuE,OAAA,CAAQC,KAAA,CAAM,8DAA8D;IAC7E;IAED,SAASnC,CAAA,GAAI,GAAGA,CAAA,GAAIlB,KAAA,CAAMnB,MAAA,EAAQqC,CAAA,IAAK;MACrC,MAAMG,IAAA,GAAOrB,KAAA,CAAMkB,CAAC;MAEpB,KAAKtC,QAAA,CAAS8E,IAAA,CAAK9E,QAAA,CAASyC,IAAA,CAAKyC,CAAC,GAAGlF,QAAA,CAASyC,IAAA,CAAK0C,CAAC,GAAGnF,QAAA,CAASyC,IAAA,CAAK2C,CAAC,CAAC;MAEvE,MAAMrC,aAAA,GAAgBN,IAAA,CAAKM,aAAA;MAE3B,IAAIA,aAAA,CAAc9C,MAAA,KAAW,GAAG;QAC9B,KAAKqL,OAAA,CAAQxG,IAAA,CAAK/B,aAAA,CAAc,CAAC,GAAGA,aAAA,CAAc,CAAC,GAAGA,aAAA,CAAc,CAAC,CAAC;MAC9E,OAAa;QACL,MAAML,MAAA,GAASD,IAAA,CAAKC,MAAA;QAEpB,KAAK4I,OAAA,CAAQxG,IAAA,CAAKpC,MAAA,EAAQA,MAAA,EAAQA,MAAM;MACzC;MAED,MAAM4C,YAAA,GAAe7C,IAAA,CAAK6C,YAAA;MAE1B,IAAIA,YAAA,CAAarF,MAAA,KAAW,GAAG;QAC7B,KAAKC,MAAA,CAAO4E,IAAA,CAAKQ,YAAA,CAAa,CAAC,GAAGA,YAAA,CAAa,CAAC,GAAGA,YAAA,CAAa,CAAC,CAAC;MAC1E,OAAa;QACL,MAAMZ,KAAA,GAAQjC,IAAA,CAAKiC,KAAA;QAEnB,KAAKxE,MAAA,CAAO4E,IAAA,CAAKJ,KAAA,EAAOA,KAAA,EAAOA,KAAK;MACrC;MAED,IAAImH,eAAA,KAAoB,MAAM;QAC5B,MAAM6C,SAAA,GAAYrN,aAAA,CAAc,CAAC,EAAEiB,CAAC;QAEpC,IAAIoM,SAAA,KAAc,QAAW;UAC3B,KAAKjD,GAAA,CAAI3G,IAAA,CAAK4J,SAAA,CAAU,CAAC,GAAGA,SAAA,CAAU,CAAC,GAAGA,SAAA,CAAU,CAAC,CAAC;QAChE,OAAe;UACLlK,OAAA,CAAQuJ,IAAA,CAAK,4DAA4DzL,CAAC;UAE1E,KAAKmJ,GAAA,CAAI3G,IAAA,CAAK,IAAIU,OAAA,CAAS,GAAE,IAAIA,OAAA,CAAO,GAAI,IAAIA,OAAA,EAAS;QAC1D;MACF;MAED,IAAI2I,gBAAA,KAAqB,MAAM;QAC7B,MAAMO,SAAA,GAAYrN,aAAA,CAAc,CAAC,EAAEiB,CAAC;QAEpC,IAAIoM,SAAA,KAAc,QAAW;UAC3B,KAAK5F,IAAA,CAAKhE,IAAA,CAAK4J,SAAA,CAAU,CAAC,GAAGA,SAAA,CAAU,CAAC,GAAGA,SAAA,CAAU,CAAC,CAAC;QACjE,OAAe;UACLlK,OAAA,CAAQuJ,IAAA,CAAK,6DAA6DzL,CAAC;UAE3E,KAAKwG,IAAA,CAAKhE,IAAA,CAAK,IAAIU,OAAA,CAAS,GAAE,IAAIA,OAAA,CAAO,GAAI,IAAIA,OAAA,EAAS;QAC3D;MACF;MAID,SAAS3C,CAAA,GAAI,GAAGA,CAAA,GAAIuL,kBAAA,EAAoBvL,CAAA,IAAK;QAC3C,MAAMkK,WAAA,GAAczL,YAAA,CAAauB,CAAC,EAAE7C,QAAA;QAEpCqO,oBAAA,CAAqBxL,CAAC,EAAEoI,IAAA,CAAKnG,IAAA,CAAKiI,WAAA,CAAYtK,IAAA,CAAKyC,CAAC,GAAG6H,WAAA,CAAYtK,IAAA,CAAK0C,CAAC,GAAG4H,WAAA,CAAYtK,IAAA,CAAK2C,CAAC,CAAC;MAChG;MAED,SAASvC,CAAA,GAAI,GAAGA,CAAA,GAAIyL,kBAAA,EAAoBzL,CAAA,IAAK;QAC3C,MAAMmK,WAAA,GAAczL,YAAA,CAAasB,CAAC,EAAEE,aAAA,CAAcT,CAAC;QAEnDiM,kBAAA,CAAmB1L,CAAC,EAAEoI,IAAA,CAAKnG,IAAA,CAAKkI,WAAA,CAAY9H,CAAA,EAAG8H,WAAA,CAAY7H,CAAA,EAAG6H,WAAA,CAAY5H,CAAC;MAC5E;MAID,IAAIoJ,cAAA,EAAgB;QAClB,KAAK/M,WAAA,CAAYqD,IAAA,CAAKrD,WAAA,CAAYgB,IAAA,CAAKyC,CAAC,GAAGzD,WAAA,CAAYgB,IAAA,CAAK0C,CAAC,GAAG1D,WAAA,CAAYgB,IAAA,CAAK2C,CAAC,CAAC;MACpF;MAED,IAAIqJ,cAAA,EAAgB;QAClB,KAAKjN,WAAA,CAAYsD,IAAA,CAAKtD,WAAA,CAAYiB,IAAA,CAAKyC,CAAC,GAAG1D,WAAA,CAAYiB,IAAA,CAAK0C,CAAC,GAAG3D,WAAA,CAAYiB,IAAA,CAAK2C,CAAC,CAAC;MACpF;IACF;IAED,KAAK8I,aAAA,CAAcvO,QAAQ;IAE3B,KAAKgC,kBAAA,GAAqBhC,QAAA,CAASgC,kBAAA;IACnC,KAAKE,iBAAA,GAAoBlC,QAAA,CAASkC,iBAAA;IAClC,KAAKC,gBAAA,GAAmBnC,QAAA,CAASmC,gBAAA;IACjC,KAAKF,aAAA,GAAgBjC,QAAA,CAASiC,aAAA;IAC9B,KAAKI,gBAAA,GAAmBrC,QAAA,CAASqC,gBAAA;IAEjC,IAAIrC,QAAA,CAASa,cAAA,KAAmB,MAAM;MACpC,KAAKA,cAAA,GAAiBb,QAAA,CAASa,cAAA,CAAeC,KAAA,CAAO;IACtD;IAED,IAAId,QAAA,CAASe,WAAA,KAAgB,MAAM;MACjC,KAAKA,WAAA,GAAcf,QAAA,CAASe,WAAA,CAAYD,KAAA,CAAO;IAChD;IAED,OAAO;EACR;AACH;AAEA,MAAM8E,KAAA,CAAM;EACV1E,YAAYqE,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG1C,MAAA,EAAQgC,KAAA,EAAOW,aAAA,GAAgB,GAAG;IACrD,KAAKH,CAAA,GAAIA,CAAA;IACT,KAAKC,CAAA,GAAIA,CAAA;IACT,KAAKC,CAAA,GAAIA,CAAA;IAET,KAAK1C,MAAA,GAASA,MAAA,IAAUA,MAAA,CAAOiM,SAAA,GAAYjM,MAAA,GAAS,IAAIvD,OAAA,CAAS;IACjE,KAAK4D,aAAA,GAAgBiE,KAAA,CAAM4H,OAAA,CAAQlM,MAAM,IAAIA,MAAA,GAAS,EAAE;IAExD,KAAKgC,KAAA,GAAQA,KAAA,IAASA,KAAA,CAAMmK,OAAA,GAAUnK,KAAA,GAAQ,IAAIM,KAAA,CAAO;IACzD,KAAKM,YAAA,GAAe0B,KAAA,CAAM4H,OAAA,CAAQlK,KAAK,IAAIA,KAAA,GAAQ,EAAE;IAErD,KAAKW,aAAA,GAAgBA,aAAA;EACtB;EAED5E,MAAA,EAAQ;IACN,OAAO,IAAI,KAAKI,WAAA,GAAcgG,IAAA,CAAK,IAAI;EACxC;EAEDA,KAAKiG,MAAA,EAAQ;IACX,KAAK5H,CAAA,GAAI4H,MAAA,CAAO5H,CAAA;IAChB,KAAKC,CAAA,GAAI2H,MAAA,CAAO3H,CAAA;IAChB,KAAKC,CAAA,GAAI0H,MAAA,CAAO1H,CAAA;IAEhB,KAAK1C,MAAA,CAAOmE,IAAA,CAAKiG,MAAA,CAAOpK,MAAM;IAC9B,KAAKgC,KAAA,CAAMmC,IAAA,CAAKiG,MAAA,CAAOpI,KAAK;IAE5B,KAAKW,aAAA,GAAgByH,MAAA,CAAOzH,aAAA;IAE5B,SAAS/C,CAAA,GAAI,GAAGC,EAAA,GAAKuK,MAAA,CAAO/J,aAAA,CAAc9C,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MAC7D,KAAKS,aAAA,CAAcT,CAAC,IAAIwK,MAAA,CAAO/J,aAAA,CAAcT,CAAC,EAAE7B,KAAA,CAAO;IACxD;IAED,SAAS6B,CAAA,GAAI,GAAGC,EAAA,GAAKuK,MAAA,CAAOxH,YAAA,CAAarF,MAAA,EAAQqC,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;MAC5D,KAAKgD,YAAA,CAAahD,CAAC,IAAIwK,MAAA,CAAOxH,YAAA,CAAahD,CAAC,EAAE7B,KAAA,CAAO;IACtD;IAED,OAAO;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}