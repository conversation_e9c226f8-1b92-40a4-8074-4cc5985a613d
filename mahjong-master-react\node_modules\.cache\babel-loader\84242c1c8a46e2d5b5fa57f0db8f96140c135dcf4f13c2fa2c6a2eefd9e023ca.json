{"ast": null, "code": "import { MotionValueState } from '../MotionValueState.mjs';\nfunction createEffect(addValue) {\n  const stateCache = new WeakMap();\n  const subscriptions = [];\n  return (subject, values) => {\n    var _stateCache$get;\n    const state = (_stateCache$get = stateCache.get(subject)) !== null && _stateCache$get !== void 0 ? _stateCache$get : new MotionValueState();\n    stateCache.set(subject, state);\n    for (const key in values) {\n      const value = values[key];\n      const remove = addValue(subject, state, key, value);\n      subscriptions.push(remove);\n    }\n    return () => {\n      for (const cancel of subscriptions) cancel();\n    };\n  };\n}\nexport { createEffect };", "map": {"version": 3, "names": ["MotionValueState", "createEffect", "addValue", "stateCache", "WeakMap", "subscriptions", "subject", "values", "_stateCache$get", "state", "get", "set", "key", "value", "remove", "push", "cancel"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/motion-dom/dist/es/effects/utils/create-effect.mjs"], "sourcesContent": ["import { MotionValueState } from '../MotionValueState.mjs';\n\nfunction createEffect(addValue) {\n    const stateCache = new WeakMap();\n    const subscriptions = [];\n    return (subject, values) => {\n        const state = stateCache.get(subject) ?? new MotionValueState();\n        stateCache.set(subject, state);\n        for (const key in values) {\n            const value = values[key];\n            const remove = addValue(subject, state, key, value);\n            subscriptions.push(remove);\n        }\n        return () => {\n            for (const cancel of subscriptions)\n                cancel();\n        };\n    };\n}\n\nexport { createEffect };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,yBAAyB;AAE1D,SAASC,YAAYA,CAACC,QAAQ,EAAE;EAC5B,MAAMC,UAAU,GAAG,IAAIC,OAAO,CAAC,CAAC;EAChC,MAAMC,aAAa,GAAG,EAAE;EACxB,OAAO,CAACC,OAAO,EAAEC,MAAM,KAAK;IAAA,IAAAC,eAAA;IACxB,MAAMC,KAAK,IAAAD,eAAA,GAAGL,UAAU,CAACO,GAAG,CAACJ,OAAO,CAAC,cAAAE,eAAA,cAAAA,eAAA,GAAI,IAAIR,gBAAgB,CAAC,CAAC;IAC/DG,UAAU,CAACQ,GAAG,CAACL,OAAO,EAAEG,KAAK,CAAC;IAC9B,KAAK,MAAMG,GAAG,IAAIL,MAAM,EAAE;MACtB,MAAMM,KAAK,GAAGN,MAAM,CAACK,GAAG,CAAC;MACzB,MAAME,MAAM,GAAGZ,QAAQ,CAACI,OAAO,EAAEG,KAAK,EAAEG,GAAG,EAAEC,KAAK,CAAC;MACnDR,aAAa,CAACU,IAAI,CAACD,MAAM,CAAC;IAC9B;IACA,OAAO,MAAM;MACT,KAAK,MAAME,MAAM,IAAIX,aAAa,EAC9BW,MAAM,CAAC,CAAC;IAChB,CAAC;EACL,CAAC;AACL;AAEA,SAASf,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}