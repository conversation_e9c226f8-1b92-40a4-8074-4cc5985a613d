{"ast": null, "code": "import { CompressedTextureLoader, RGBAFormat, RGB_ETC1_Format, RGBA_S3TC_DXT5_Format, RGBA_S3TC_DXT3_Format, RGB_S3TC_DXT1_Format } from \"three\";\nclass DDSLoader extends CompressedTextureLoader {\n  constructor(manager) {\n    super(manager);\n  }\n  parse(buffer, loadMipmaps) {\n    const dds = {\n      mipmaps: [],\n      width: 0,\n      height: 0,\n      format: null,\n      mipmapCount: 1\n    };\n    const DDS_MAGIC = 542327876;\n    const DDSD_MIPMAPCOUNT = 131072;\n    const DDSCAPS2_CUBEMAP = 512;\n    const DDSCAPS2_CUBEMAP_POSITIVEX = 1024;\n    const DDSCAPS2_CUBEMAP_NEGATIVEX = 2048;\n    const DDSCAPS2_CUBEMAP_POSITIVEY = 4096;\n    const DDSCAPS2_CUBEMAP_NEGATIVEY = 8192;\n    const DDSCAPS2_CUBEMAP_POSITIVEZ = 16384;\n    const DDSCAPS2_CUBEMAP_NEGATIVEZ = 32768;\n    const DDPF_FOURCC = 4;\n    function fourCCToInt32(value) {\n      return value.charCodeAt(0) + (value.charCodeAt(1) << 8) + (value.charCodeAt(2) << 16) + (value.charCodeAt(3) << 24);\n    }\n    function int32ToFourCC(value) {\n      return String.fromCharCode(value & 255, value >> 8 & 255, value >> 16 & 255, value >> 24 & 255);\n    }\n    function loadARGBMip(buffer2, dataOffset2, width, height) {\n      const dataLength = width * height * 4;\n      const srcBuffer = new Uint8Array(buffer2, dataOffset2, dataLength);\n      const byteArray = new Uint8Array(dataLength);\n      let dst = 0;\n      let src = 0;\n      for (let y = 0; y < height; y++) {\n        for (let x = 0; x < width; x++) {\n          const b = srcBuffer[src];\n          src++;\n          const g = srcBuffer[src];\n          src++;\n          const r = srcBuffer[src];\n          src++;\n          const a = srcBuffer[src];\n          src++;\n          byteArray[dst] = r;\n          dst++;\n          byteArray[dst] = g;\n          dst++;\n          byteArray[dst] = b;\n          dst++;\n          byteArray[dst] = a;\n          dst++;\n        }\n      }\n      return byteArray;\n    }\n    const FOURCC_DXT1 = fourCCToInt32(\"DXT1\");\n    const FOURCC_DXT3 = fourCCToInt32(\"DXT3\");\n    const FOURCC_DXT5 = fourCCToInt32(\"DXT5\");\n    const FOURCC_ETC1 = fourCCToInt32(\"ETC1\");\n    const headerLengthInt = 31;\n    const off_magic = 0;\n    const off_size = 1;\n    const off_flags = 2;\n    const off_height = 3;\n    const off_width = 4;\n    const off_mipmapCount = 7;\n    const off_pfFlags = 20;\n    const off_pfFourCC = 21;\n    const off_RGBBitCount = 22;\n    const off_RBitMask = 23;\n    const off_GBitMask = 24;\n    const off_BBitMask = 25;\n    const off_ABitMask = 26;\n    const off_caps2 = 28;\n    const header = new Int32Array(buffer, 0, headerLengthInt);\n    if (header[off_magic] !== DDS_MAGIC) {\n      console.error(\"THREE.DDSLoader.parse: Invalid magic number in DDS header.\");\n      return dds;\n    }\n    if (!header[off_pfFlags] & DDPF_FOURCC) {\n      console.error(\"THREE.DDSLoader.parse: Unsupported format, must contain a FourCC code.\");\n      return dds;\n    }\n    let blockBytes;\n    const fourCC = header[off_pfFourCC];\n    let isRGBAUncompressed = false;\n    switch (fourCC) {\n      case FOURCC_DXT1:\n        blockBytes = 8;\n        dds.format = RGB_S3TC_DXT1_Format;\n        break;\n      case FOURCC_DXT3:\n        blockBytes = 16;\n        dds.format = RGBA_S3TC_DXT3_Format;\n        break;\n      case FOURCC_DXT5:\n        blockBytes = 16;\n        dds.format = RGBA_S3TC_DXT5_Format;\n        break;\n      case FOURCC_ETC1:\n        blockBytes = 8;\n        dds.format = RGB_ETC1_Format;\n        break;\n      default:\n        if (header[off_RGBBitCount] === 32 && header[off_RBitMask] & 16711680 && header[off_GBitMask] & 65280 && header[off_BBitMask] & 255 && header[off_ABitMask] & 4278190080) {\n          isRGBAUncompressed = true;\n          blockBytes = 64;\n          dds.format = RGBAFormat;\n        } else {\n          console.error(\"THREE.DDSLoader.parse: Unsupported FourCC code \", int32ToFourCC(fourCC));\n          return dds;\n        }\n    }\n    dds.mipmapCount = 1;\n    if (header[off_flags] & DDSD_MIPMAPCOUNT && loadMipmaps !== false) {\n      dds.mipmapCount = Math.max(1, header[off_mipmapCount]);\n    }\n    const caps2 = header[off_caps2];\n    dds.isCubemap = caps2 & DDSCAPS2_CUBEMAP ? true : false;\n    if (dds.isCubemap && (!(caps2 & DDSCAPS2_CUBEMAP_POSITIVEX) || !(caps2 & DDSCAPS2_CUBEMAP_NEGATIVEX) || !(caps2 & DDSCAPS2_CUBEMAP_POSITIVEY) || !(caps2 & DDSCAPS2_CUBEMAP_NEGATIVEY) || !(caps2 & DDSCAPS2_CUBEMAP_POSITIVEZ) || !(caps2 & DDSCAPS2_CUBEMAP_NEGATIVEZ))) {\n      console.error(\"THREE.DDSLoader.parse: Incomplete cubemap faces\");\n      return dds;\n    }\n    dds.width = header[off_width];\n    dds.height = header[off_height];\n    let dataOffset = header[off_size] + 4;\n    const faces = dds.isCubemap ? 6 : 1;\n    for (let face = 0; face < faces; face++) {\n      let width = dds.width;\n      let height = dds.height;\n      for (let i = 0; i < dds.mipmapCount; i++) {\n        let byteArray, dataLength;\n        if (isRGBAUncompressed) {\n          byteArray = loadARGBMip(buffer, dataOffset, width, height);\n          dataLength = byteArray.length;\n        } else {\n          dataLength = Math.max(4, width) / 4 * Math.max(4, height) / 4 * blockBytes;\n          byteArray = new Uint8Array(buffer, dataOffset, dataLength);\n        }\n        const mipmap = {\n          data: byteArray,\n          width,\n          height\n        };\n        dds.mipmaps.push(mipmap);\n        dataOffset += dataLength;\n        width = Math.max(width >> 1, 1);\n        height = Math.max(height >> 1, 1);\n      }\n    }\n    return dds;\n  }\n}\nexport { DDSLoader };", "map": {"version": 3, "names": ["DDSLoader", "CompressedTextureLoader", "constructor", "manager", "parse", "buffer", "loadMipmaps", "dds", "mipmaps", "width", "height", "format", "mipmapCount", "DDS_MAGIC", "DDSD_MIPMAPCOUNT", "DDSCAPS2_CUBEMAP", "DDSCAPS2_CUBEMAP_POSITIVEX", "DDSCAPS2_CUBEMAP_NEGATIVEX", "DDSCAPS2_CUBEMAP_POSITIVEY", "DDSCAPS2_CUBEMAP_NEGATIVEY", "DDSCAPS2_CUBEMAP_POSITIVEZ", "DDSCAPS2_CUBEMAP_NEGATIVEZ", "DDPF_FOURCC", "fourCCToInt32", "value", "charCodeAt", "int32ToFourCC", "String", "fromCharCode", "loadARGBMip", "buffer2", "dataOffset2", "dataLength", "src<PERSON>uffer", "Uint8Array", "byteArray", "dst", "src", "y", "x", "b", "g", "r", "a", "FOURCC_DXT1", "FOURCC_DXT3", "FOURCC_DXT5", "FOURCC_ETC1", "headerLengthInt", "off_magic", "off_size", "off_flags", "off_height", "off_width", "off_mipmapCount", "off_pfFlags", "off_pfFourCC", "off_RGBBitCount", "off_RBitMask", "off_GBitMask", "off_BBitMask", "off_ABitMask", "off_caps2", "header", "Int32Array", "console", "error", "blockBytes", "fourCC", "isRGBAUncompressed", "RGB_S3TC_DXT1_Format", "RGBA_S3TC_DXT3_Format", "RGBA_S3TC_DXT5_Format", "RGB_ETC1_Format", "RGBAFormat", "Math", "max", "caps2", "isCubemap", "dataOffset", "faces", "face", "i", "length", "mipmap", "data", "push"], "sources": ["F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\node_modules\\src\\loaders\\DDSLoader.js"], "sourcesContent": ["import {\n  CompressedTextureLoader,\n  RGBAFormat,\n  RGBA_S3TC_DXT3_Format,\n  RGBA_S3TC_DXT5_Format,\n  RGB_ETC1_Format,\n  RGB_S3TC_DXT1_Format,\n} from 'three'\n\nclass DDSLoader extends CompressedTextureLoader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  parse(buffer, loadMipmaps) {\n    const dds = { mipmaps: [], width: 0, height: 0, format: null, mipmapCount: 1 }\n\n    // Adapted from @toji's DDS utils\n    // https://github.com/toji/webgl-texture-utils/blob/master/texture-util/dds.js\n\n    // All values and structures referenced from:\n    // http://msdn.microsoft.com/en-us/library/bb943991.aspx/\n\n    const DDS_MAGIC = 0x20534444\n\n    // let DDSD_CAPS = 0x1;\n    // let DDSD_HEIGHT = 0x2;\n    // let DDSD_WIDTH = 0x4;\n    // let DDSD_PITCH = 0x8;\n    // let DDSD_PIXELFORMAT = 0x1000;\n    const DDSD_MIPMAPCOUNT = 0x20000\n    // let DDSD_LINEARSIZE = 0x80000;\n    // let DDSD_DEPTH = 0x800000;\n\n    // let DDSCAPS_COMPLEX = 0x8;\n    // let DDSCAPS_MIPMAP = 0x400000;\n    // let DDSCAPS_TEXTURE = 0x1000;\n\n    const DDSCAPS2_CUBEMAP = 0x200\n    const DDSCAPS2_CUBEMAP_POSITIVEX = 0x400\n    const DDSCAPS2_CUBEMAP_NEGATIVEX = 0x800\n    const DDSCAPS2_CUBEMAP_POSITIVEY = 0x1000\n    const DDSCAPS2_CUBEMAP_NEGATIVEY = 0x2000\n    const DDSCAPS2_CUBEMAP_POSITIVEZ = 0x4000\n    const DDSCAPS2_CUBEMAP_NEGATIVEZ = 0x8000\n    // let DDSCAPS2_VOLUME = 0x200000;\n\n    // let DDPF_ALPHAPIXELS = 0x1;\n    // let DDPF_ALPHA = 0x2;\n    const DDPF_FOURCC = 0x4\n    // let DDPF_RGB = 0x40;\n    // let DDPF_YUV = 0x200;\n    // let DDPF_LUMINANCE = 0x20000;\n\n    function fourCCToInt32(value) {\n      return (\n        value.charCodeAt(0) + (value.charCodeAt(1) << 8) + (value.charCodeAt(2) << 16) + (value.charCodeAt(3) << 24)\n      )\n    }\n\n    function int32ToFourCC(value) {\n      return String.fromCharCode(value & 0xff, (value >> 8) & 0xff, (value >> 16) & 0xff, (value >> 24) & 0xff)\n    }\n\n    function loadARGBMip(buffer, dataOffset, width, height) {\n      const dataLength = width * height * 4\n      const srcBuffer = new Uint8Array(buffer, dataOffset, dataLength)\n      const byteArray = new Uint8Array(dataLength)\n      let dst = 0\n      let src = 0\n      for (let y = 0; y < height; y++) {\n        for (let x = 0; x < width; x++) {\n          const b = srcBuffer[src]\n          src++\n          const g = srcBuffer[src]\n          src++\n          const r = srcBuffer[src]\n          src++\n          const a = srcBuffer[src]\n          src++\n          byteArray[dst] = r\n          dst++ //r\n          byteArray[dst] = g\n          dst++ //g\n          byteArray[dst] = b\n          dst++ //b\n          byteArray[dst] = a\n          dst++ //a\n        }\n      }\n\n      return byteArray\n    }\n\n    const FOURCC_DXT1 = fourCCToInt32('DXT1')\n    const FOURCC_DXT3 = fourCCToInt32('DXT3')\n    const FOURCC_DXT5 = fourCCToInt32('DXT5')\n    const FOURCC_ETC1 = fourCCToInt32('ETC1')\n\n    const headerLengthInt = 31 // The header length in 32 bit ints\n\n    // Offsets into the header array\n\n    const off_magic = 0\n\n    const off_size = 1\n    const off_flags = 2\n    const off_height = 3\n    const off_width = 4\n\n    const off_mipmapCount = 7\n\n    const off_pfFlags = 20\n    const off_pfFourCC = 21\n    const off_RGBBitCount = 22\n    const off_RBitMask = 23\n    const off_GBitMask = 24\n    const off_BBitMask = 25\n    const off_ABitMask = 26\n\n    // let off_caps = 27;\n    const off_caps2 = 28\n    // let off_caps3 = 29;\n    // let off_caps4 = 30;\n\n    // Parse header\n\n    const header = new Int32Array(buffer, 0, headerLengthInt)\n\n    if (header[off_magic] !== DDS_MAGIC) {\n      console.error('THREE.DDSLoader.parse: Invalid magic number in DDS header.')\n      return dds\n    }\n\n    if (!header[off_pfFlags] & DDPF_FOURCC) {\n      console.error('THREE.DDSLoader.parse: Unsupported format, must contain a FourCC code.')\n      return dds\n    }\n\n    let blockBytes\n\n    const fourCC = header[off_pfFourCC]\n\n    let isRGBAUncompressed = false\n\n    switch (fourCC) {\n      case FOURCC_DXT1:\n        blockBytes = 8\n        dds.format = RGB_S3TC_DXT1_Format\n        break\n\n      case FOURCC_DXT3:\n        blockBytes = 16\n        dds.format = RGBA_S3TC_DXT3_Format\n        break\n\n      case FOURCC_DXT5:\n        blockBytes = 16\n        dds.format = RGBA_S3TC_DXT5_Format\n        break\n\n      case FOURCC_ETC1:\n        blockBytes = 8\n        dds.format = RGB_ETC1_Format\n        break\n\n      default:\n        if (\n          header[off_RGBBitCount] === 32 &&\n          header[off_RBitMask] & 0xff0000 &&\n          header[off_GBitMask] & 0xff00 &&\n          header[off_BBitMask] & 0xff &&\n          header[off_ABitMask] & 0xff000000\n        ) {\n          isRGBAUncompressed = true\n          blockBytes = 64\n          dds.format = RGBAFormat\n        } else {\n          console.error('THREE.DDSLoader.parse: Unsupported FourCC code ', int32ToFourCC(fourCC))\n          return dds\n        }\n    }\n\n    dds.mipmapCount = 1\n\n    if (header[off_flags] & DDSD_MIPMAPCOUNT && loadMipmaps !== false) {\n      dds.mipmapCount = Math.max(1, header[off_mipmapCount])\n    }\n\n    const caps2 = header[off_caps2]\n    dds.isCubemap = caps2 & DDSCAPS2_CUBEMAP ? true : false\n    if (\n      dds.isCubemap &&\n      (!(caps2 & DDSCAPS2_CUBEMAP_POSITIVEX) ||\n        !(caps2 & DDSCAPS2_CUBEMAP_NEGATIVEX) ||\n        !(caps2 & DDSCAPS2_CUBEMAP_POSITIVEY) ||\n        !(caps2 & DDSCAPS2_CUBEMAP_NEGATIVEY) ||\n        !(caps2 & DDSCAPS2_CUBEMAP_POSITIVEZ) ||\n        !(caps2 & DDSCAPS2_CUBEMAP_NEGATIVEZ))\n    ) {\n      console.error('THREE.DDSLoader.parse: Incomplete cubemap faces')\n      return dds\n    }\n\n    dds.width = header[off_width]\n    dds.height = header[off_height]\n\n    let dataOffset = header[off_size] + 4\n\n    // Extract mipmaps buffers\n\n    const faces = dds.isCubemap ? 6 : 1\n\n    for (let face = 0; face < faces; face++) {\n      let width = dds.width\n      let height = dds.height\n\n      for (let i = 0; i < dds.mipmapCount; i++) {\n        let byteArray, dataLength\n\n        if (isRGBAUncompressed) {\n          byteArray = loadARGBMip(buffer, dataOffset, width, height)\n          dataLength = byteArray.length\n        } else {\n          dataLength = (((Math.max(4, width) / 4) * Math.max(4, height)) / 4) * blockBytes\n          byteArray = new Uint8Array(buffer, dataOffset, dataLength)\n        }\n\n        const mipmap = { data: byteArray, width: width, height: height }\n        dds.mipmaps.push(mipmap)\n\n        dataOffset += dataLength\n\n        width = Math.max(width >> 1, 1)\n        height = Math.max(height >> 1, 1)\n      }\n    }\n\n    return dds\n  }\n}\n\nexport { DDSLoader }\n"], "mappings": ";AASA,MAAMA,SAAA,SAAkBC,uBAAA,CAAwB;EAC9CC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;EACd;EAEDC,MAAMC,MAAA,EAAQC,WAAA,EAAa;IACzB,MAAMC,GAAA,GAAM;MAAEC,OAAA,EAAS;MAAIC,KAAA,EAAO;MAAGC,MAAA,EAAQ;MAAGC,MAAA,EAAQ;MAAMC,WAAA,EAAa;IAAG;IAQ9E,MAAMC,SAAA,GAAY;IAOlB,MAAMC,gBAAA,GAAmB;IAQzB,MAAMC,gBAAA,GAAmB;IACzB,MAAMC,0BAAA,GAA6B;IACnC,MAAMC,0BAAA,GAA6B;IACnC,MAAMC,0BAAA,GAA6B;IACnC,MAAMC,0BAAA,GAA6B;IACnC,MAAMC,0BAAA,GAA6B;IACnC,MAAMC,0BAAA,GAA6B;IAKnC,MAAMC,WAAA,GAAc;IAKpB,SAASC,cAAcC,KAAA,EAAO;MAC5B,OACEA,KAAA,CAAMC,UAAA,CAAW,CAAC,KAAKD,KAAA,CAAMC,UAAA,CAAW,CAAC,KAAK,MAAMD,KAAA,CAAMC,UAAA,CAAW,CAAC,KAAK,OAAOD,KAAA,CAAMC,UAAA,CAAW,CAAC,KAAK;IAE5G;IAED,SAASC,cAAcF,KAAA,EAAO;MAC5B,OAAOG,MAAA,CAAOC,YAAA,CAAaJ,KAAA,GAAQ,KAAOA,KAAA,IAAS,IAAK,KAAOA,KAAA,IAAS,KAAM,KAAOA,KAAA,IAAS,KAAM,GAAI;IACzG;IAED,SAASK,YAAYC,OAAA,EAAQC,WAAA,EAAYtB,KAAA,EAAOC,MAAA,EAAQ;MACtD,MAAMsB,UAAA,GAAavB,KAAA,GAAQC,MAAA,GAAS;MACpC,MAAMuB,SAAA,GAAY,IAAIC,UAAA,CAAWJ,OAAA,EAAQC,WAAA,EAAYC,UAAU;MAC/D,MAAMG,SAAA,GAAY,IAAID,UAAA,CAAWF,UAAU;MAC3C,IAAII,GAAA,GAAM;MACV,IAAIC,GAAA,GAAM;MACV,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI5B,MAAA,EAAQ4B,CAAA,IAAK;QAC/B,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI9B,KAAA,EAAO8B,CAAA,IAAK;UAC9B,MAAMC,CAAA,GAAIP,SAAA,CAAUI,GAAG;UACvBA,GAAA;UACA,MAAMI,CAAA,GAAIR,SAAA,CAAUI,GAAG;UACvBA,GAAA;UACA,MAAMK,CAAA,GAAIT,SAAA,CAAUI,GAAG;UACvBA,GAAA;UACA,MAAMM,CAAA,GAAIV,SAAA,CAAUI,GAAG;UACvBA,GAAA;UACAF,SAAA,CAAUC,GAAG,IAAIM,CAAA;UACjBN,GAAA;UACAD,SAAA,CAAUC,GAAG,IAAIK,CAAA;UACjBL,GAAA;UACAD,SAAA,CAAUC,GAAG,IAAII,CAAA;UACjBJ,GAAA;UACAD,SAAA,CAAUC,GAAG,IAAIO,CAAA;UACjBP,GAAA;QACD;MACF;MAED,OAAOD,SAAA;IACR;IAED,MAAMS,WAAA,GAAcrB,aAAA,CAAc,MAAM;IACxC,MAAMsB,WAAA,GAActB,aAAA,CAAc,MAAM;IACxC,MAAMuB,WAAA,GAAcvB,aAAA,CAAc,MAAM;IACxC,MAAMwB,WAAA,GAAcxB,aAAA,CAAc,MAAM;IAExC,MAAMyB,eAAA,GAAkB;IAIxB,MAAMC,SAAA,GAAY;IAElB,MAAMC,QAAA,GAAW;IACjB,MAAMC,SAAA,GAAY;IAClB,MAAMC,UAAA,GAAa;IACnB,MAAMC,SAAA,GAAY;IAElB,MAAMC,eAAA,GAAkB;IAExB,MAAMC,WAAA,GAAc;IACpB,MAAMC,YAAA,GAAe;IACrB,MAAMC,eAAA,GAAkB;IACxB,MAAMC,YAAA,GAAe;IACrB,MAAMC,YAAA,GAAe;IACrB,MAAMC,YAAA,GAAe;IACrB,MAAMC,YAAA,GAAe;IAGrB,MAAMC,SAAA,GAAY;IAMlB,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAW3D,MAAA,EAAQ,GAAG2C,eAAe;IAExD,IAAIe,MAAA,CAAOd,SAAS,MAAMpC,SAAA,EAAW;MACnCoD,OAAA,CAAQC,KAAA,CAAM,4DAA4D;MAC1E,OAAO3D,GAAA;IACR;IAED,IAAI,CAACwD,MAAA,CAAOR,WAAW,IAAIjC,WAAA,EAAa;MACtC2C,OAAA,CAAQC,KAAA,CAAM,wEAAwE;MACtF,OAAO3D,GAAA;IACR;IAED,IAAI4D,UAAA;IAEJ,MAAMC,MAAA,GAASL,MAAA,CAAOP,YAAY;IAElC,IAAIa,kBAAA,GAAqB;IAEzB,QAAQD,MAAA;MACN,KAAKxB,WAAA;QACHuB,UAAA,GAAa;QACb5D,GAAA,CAAII,MAAA,GAAS2D,oBAAA;QACb;MAEF,KAAKzB,WAAA;QACHsB,UAAA,GAAa;QACb5D,GAAA,CAAII,MAAA,GAAS4D,qBAAA;QACb;MAEF,KAAKzB,WAAA;QACHqB,UAAA,GAAa;QACb5D,GAAA,CAAII,MAAA,GAAS6D,qBAAA;QACb;MAEF,KAAKzB,WAAA;QACHoB,UAAA,GAAa;QACb5D,GAAA,CAAII,MAAA,GAAS8D,eAAA;QACb;MAEF;QACE,IACEV,MAAA,CAAON,eAAe,MAAM,MAC5BM,MAAA,CAAOL,YAAY,IAAI,YACvBK,MAAA,CAAOJ,YAAY,IAAI,SACvBI,MAAA,CAAOH,YAAY,IAAI,OACvBG,MAAA,CAAOF,YAAY,IAAI,YACvB;UACAQ,kBAAA,GAAqB;UACrBF,UAAA,GAAa;UACb5D,GAAA,CAAII,MAAA,GAAS+D,UAAA;QACvB,OAAe;UACLT,OAAA,CAAQC,KAAA,CAAM,mDAAmDxC,aAAA,CAAc0C,MAAM,CAAC;UACtF,OAAO7D,GAAA;QACR;IACJ;IAEDA,GAAA,CAAIK,WAAA,GAAc;IAElB,IAAImD,MAAA,CAAOZ,SAAS,IAAIrC,gBAAA,IAAoBR,WAAA,KAAgB,OAAO;MACjEC,GAAA,CAAIK,WAAA,GAAc+D,IAAA,CAAKC,GAAA,CAAI,GAAGb,MAAA,CAAOT,eAAe,CAAC;IACtD;IAED,MAAMuB,KAAA,GAAQd,MAAA,CAAOD,SAAS;IAC9BvD,GAAA,CAAIuE,SAAA,GAAYD,KAAA,GAAQ9D,gBAAA,GAAmB,OAAO;IAClD,IACER,GAAA,CAAIuE,SAAA,KACH,EAAED,KAAA,GAAQ7D,0BAAA,KACT,EAAE6D,KAAA,GAAQ5D,0BAAA,KACV,EAAE4D,KAAA,GAAQ3D,0BAAA,KACV,EAAE2D,KAAA,GAAQ1D,0BAAA,KACV,EAAE0D,KAAA,GAAQzD,0BAAA,KACV,EAAEyD,KAAA,GAAQxD,0BAAA,IACZ;MACA4C,OAAA,CAAQC,KAAA,CAAM,iDAAiD;MAC/D,OAAO3D,GAAA;IACR;IAEDA,GAAA,CAAIE,KAAA,GAAQsD,MAAA,CAAOV,SAAS;IAC5B9C,GAAA,CAAIG,MAAA,GAASqD,MAAA,CAAOX,UAAU;IAE9B,IAAI2B,UAAA,GAAahB,MAAA,CAAOb,QAAQ,IAAI;IAIpC,MAAM8B,KAAA,GAAQzE,GAAA,CAAIuE,SAAA,GAAY,IAAI;IAElC,SAASG,IAAA,GAAO,GAAGA,IAAA,GAAOD,KAAA,EAAOC,IAAA,IAAQ;MACvC,IAAIxE,KAAA,GAAQF,GAAA,CAAIE,KAAA;MAChB,IAAIC,MAAA,GAASH,GAAA,CAAIG,MAAA;MAEjB,SAASwE,CAAA,GAAI,GAAGA,CAAA,GAAI3E,GAAA,CAAIK,WAAA,EAAasE,CAAA,IAAK;QACxC,IAAI/C,SAAA,EAAWH,UAAA;QAEf,IAAIqC,kBAAA,EAAoB;UACtBlC,SAAA,GAAYN,WAAA,CAAYxB,MAAA,EAAQ0E,UAAA,EAAYtE,KAAA,EAAOC,MAAM;UACzDsB,UAAA,GAAaG,SAAA,CAAUgD,MAAA;QACjC,OAAe;UACLnD,UAAA,GAAgB2C,IAAA,CAAKC,GAAA,CAAI,GAAGnE,KAAK,IAAI,IAAKkE,IAAA,CAAKC,GAAA,CAAI,GAAGlE,MAAM,IAAK,IAAKyD,UAAA;UACtEhC,SAAA,GAAY,IAAID,UAAA,CAAW7B,MAAA,EAAQ0E,UAAA,EAAY/C,UAAU;QAC1D;QAED,MAAMoD,MAAA,GAAS;UAAEC,IAAA,EAAMlD,SAAA;UAAW1B,KAAA;UAAcC;QAAgB;QAChEH,GAAA,CAAIC,OAAA,CAAQ8E,IAAA,CAAKF,MAAM;QAEvBL,UAAA,IAAc/C,UAAA;QAEdvB,KAAA,GAAQkE,IAAA,CAAKC,GAAA,CAAInE,KAAA,IAAS,GAAG,CAAC;QAC9BC,MAAA,GAASiE,IAAA,CAAKC,GAAA,CAAIlE,MAAA,IAAU,GAAG,CAAC;MACjC;IACF;IAED,OAAOH,GAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}