{"ast": null, "code": "import { Vector3, Vector2, Plane, Line3 } from 'three';\nexport const closestPointLineToLine = function () {\n  // https://github.com/juj/MathGeoLib/blob/master/src/Geometry/Line.cpp#L56\n  const dir1 = new Vector3();\n  const dir2 = new Vector3();\n  const v02 = new Vector3();\n  return function closestPointLineToLine(l1, l2, result) {\n    const v0 = l1.start;\n    const v10 = dir1;\n    const v2 = l2.start;\n    const v32 = dir2;\n    v02.subVectors(v0, v2);\n    dir1.subVectors(l1.end, l1.start);\n    dir2.subVectors(l2.end, l2.start);\n\n    // float d0232 = v02.Dot(v32);\n    const d0232 = v02.dot(v32);\n\n    // float d3210 = v32.Dot(v10);\n    const d3210 = v32.dot(v10);\n\n    // float d3232 = v32.Dot(v32);\n    const d3232 = v32.dot(v32);\n\n    // float d0210 = v02.Dot(v10);\n    const d0210 = v02.dot(v10);\n\n    // float d1010 = v10.Dot(v10);\n    const d1010 = v10.dot(v10);\n\n    // float denom = d1010*d3232 - d3210*d3210;\n    const denom = d1010 * d3232 - d3210 * d3210;\n    let d, d2;\n    if (denom !== 0) {\n      d = (d0232 * d3210 - d0210 * d3232) / denom;\n    } else {\n      d = 0;\n    }\n    d2 = (d0232 + d * d3210) / d3232;\n    result.x = d;\n    result.y = d2;\n  };\n}();\nexport const closestPointsSegmentToSegment = function () {\n  // https://github.com/juj/MathGeoLib/blob/master/src/Geometry/LineSegment.cpp#L187\n  const paramResult = new Vector2();\n  const temp1 = new Vector3();\n  const temp2 = new Vector3();\n  return function closestPointsSegmentToSegment(l1, l2, target1, target2) {\n    closestPointLineToLine(l1, l2, paramResult);\n    let d = paramResult.x;\n    let d2 = paramResult.y;\n    if (d >= 0 && d <= 1 && d2 >= 0 && d2 <= 1) {\n      l1.at(d, target1);\n      l2.at(d2, target2);\n      return;\n    } else if (d >= 0 && d <= 1) {\n      // Only d2 is out of bounds.\n      if (d2 < 0) {\n        l2.at(0, target2);\n      } else {\n        l2.at(1, target2);\n      }\n      l1.closestPointToPoint(target2, true, target1);\n      return;\n    } else if (d2 >= 0 && d2 <= 1) {\n      // Only d is out of bounds.\n      if (d < 0) {\n        l1.at(0, target1);\n      } else {\n        l1.at(1, target1);\n      }\n      l2.closestPointToPoint(target1, true, target2);\n      return;\n    } else {\n      // Both u and u2 are out of bounds.\n      let p;\n      if (d < 0) {\n        p = l1.start;\n      } else {\n        p = l1.end;\n      }\n      let p2;\n      if (d2 < 0) {\n        p2 = l2.start;\n      } else {\n        p2 = l2.end;\n      }\n      const closestPoint = temp1;\n      const closestPoint2 = temp2;\n      l1.closestPointToPoint(p2, true, temp1);\n      l2.closestPointToPoint(p, true, temp2);\n      if (closestPoint.distanceToSquared(p2) <= closestPoint2.distanceToSquared(p)) {\n        target1.copy(closestPoint);\n        target2.copy(p2);\n        return;\n      } else {\n        target1.copy(p);\n        target2.copy(closestPoint2);\n        return;\n      }\n    }\n  };\n}();\nexport const sphereIntersectTriangle = function () {\n  // https://stackoverflow.com/questions/34043955/detect-collision-between-sphere-and-triangle-in-three-js\n  const closestPointTemp = new Vector3();\n  const projectedPointTemp = new Vector3();\n  const planeTemp = new Plane();\n  const lineTemp = new Line3();\n  return function sphereIntersectTriangle(sphere, triangle) {\n    const {\n      radius,\n      center\n    } = sphere;\n    const {\n      a,\n      b,\n      c\n    } = triangle;\n\n    // phase 1\n    lineTemp.start = a;\n    lineTemp.end = b;\n    const closestPoint1 = lineTemp.closestPointToPoint(center, true, closestPointTemp);\n    if (closestPoint1.distanceTo(center) <= radius) return true;\n    lineTemp.start = a;\n    lineTemp.end = c;\n    const closestPoint2 = lineTemp.closestPointToPoint(center, true, closestPointTemp);\n    if (closestPoint2.distanceTo(center) <= radius) return true;\n    lineTemp.start = b;\n    lineTemp.end = c;\n    const closestPoint3 = lineTemp.closestPointToPoint(center, true, closestPointTemp);\n    if (closestPoint3.distanceTo(center) <= radius) return true;\n\n    // phase 2\n    const plane = triangle.getPlane(planeTemp);\n    const dp = Math.abs(plane.distanceToPoint(center));\n    if (dp <= radius) {\n      const pp = plane.projectPoint(center, projectedPointTemp);\n      const cp = triangle.containsPoint(pp);\n      if (cp) return true;\n    }\n    return false;\n  };\n}();", "map": {"version": 3, "names": ["Vector3", "Vector2", "Plane", "Line3", "closestPointLineToLine", "dir1", "dir2", "v02", "l1", "l2", "result", "v0", "start", "v10", "v2", "v32", "subVectors", "end", "d0232", "dot", "d3210", "d3232", "d0210", "d1010", "denom", "d", "d2", "x", "y", "closestPointsSegmentToSegment", "paramResult", "temp1", "temp2", "target1", "target2", "at", "closestPointToPoint", "p", "p2", "closestPoint", "closestPoint2", "distanceToSquared", "copy", "sphereIntersectTriangle", "closestPointTemp", "projectedPointTemp", "planeTemp", "lineTemp", "sphere", "triangle", "radius", "center", "a", "b", "c", "closestPoint1", "distanceTo", "closestPoint3", "plane", "getPlane", "dp", "Math", "abs", "distanceToPoint", "pp", "projectPoint", "cp", "containsPoint"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/three-mesh-bvh/src/math/MathUtilities.js"], "sourcesContent": ["import { Vector3, Vector2, Plane, Line3 } from 'three';\n\nexport const closestPointLineToLine = ( function () {\n\n\t// https://github.com/juj/MathGeoLib/blob/master/src/Geometry/Line.cpp#L56\n\tconst dir1 = new Vector3();\n\tconst dir2 = new Vector3();\n\tconst v02 = new Vector3();\n\treturn function closestPointLineToLine( l1, l2, result ) {\n\n\t\tconst v0 = l1.start;\n\t\tconst v10 = dir1;\n\t\tconst v2 = l2.start;\n\t\tconst v32 = dir2;\n\n\t\tv02.subVectors( v0, v2 );\n\t\tdir1.subVectors( l1.end, l1.start );\n\t\tdir2.subVectors( l2.end, l2.start );\n\n\t\t// float d0232 = v02.Dot(v32);\n\t\tconst d0232 = v02.dot( v32 );\n\n\t\t// float d3210 = v32.Dot(v10);\n\t\tconst d3210 = v32.dot( v10 );\n\n\t\t// float d3232 = v32.Dot(v32);\n\t\tconst d3232 = v32.dot( v32 );\n\n\t\t// float d0210 = v02.Dot(v10);\n\t\tconst d0210 = v02.dot( v10 );\n\n\t\t// float d1010 = v10.Dot(v10);\n\t\tconst d1010 = v10.dot( v10 );\n\n\t\t// float denom = d1010*d3232 - d3210*d3210;\n\t\tconst denom = d1010 * d3232 - d3210 * d3210;\n\n\t\tlet d, d2;\n\t\tif ( denom !== 0 ) {\n\n\t\t\td = ( d0232 * d3210 - d0210 * d3232 ) / denom;\n\n\t\t} else {\n\n\t\t\td = 0;\n\n\t\t}\n\n\t\td2 = ( d0232 + d * d3210 ) / d3232;\n\n\t\tresult.x = d;\n\t\tresult.y = d2;\n\n\t};\n\n} )();\n\nexport const closestPointsSegmentToSegment = ( function () {\n\n\t// https://github.com/juj/MathGeoLib/blob/master/src/Geometry/LineSegment.cpp#L187\n\tconst paramResult = new Vector2();\n\tconst temp1 = new Vector3();\n\tconst temp2 = new Vector3();\n\treturn function closestPointsSegmentToSegment( l1, l2, target1, target2 ) {\n\n\t\tclosestPointLineToLine( l1, l2, paramResult );\n\n\t\tlet d = paramResult.x;\n\t\tlet d2 = paramResult.y;\n\t\tif ( d >= 0 && d <= 1 && d2 >= 0 && d2 <= 1 ) {\n\n\t\t\tl1.at( d, target1 );\n\t\t\tl2.at( d2, target2 );\n\n\t\t\treturn;\n\n\t\t} else if ( d >= 0 && d <= 1 ) {\n\n\t\t\t// Only d2 is out of bounds.\n\t\t\tif ( d2 < 0 ) {\n\n\t\t\t\tl2.at( 0, target2 );\n\n\t\t\t} else {\n\n\t\t\t\tl2.at( 1, target2 );\n\n\t\t\t}\n\n\t\t\tl1.closestPointToPoint( target2, true, target1 );\n\t\t\treturn;\n\n\t\t} else if ( d2 >= 0 && d2 <= 1 ) {\n\n\t\t\t// Only d is out of bounds.\n\t\t\tif ( d < 0 ) {\n\n\t\t\t\tl1.at( 0, target1 );\n\n\t\t\t} else {\n\n\t\t\t\tl1.at( 1, target1 );\n\n\t\t\t}\n\n\t\t\tl2.closestPointToPoint( target1, true, target2 );\n\t\t\treturn;\n\n\t\t} else {\n\n\t\t\t// Both u and u2 are out of bounds.\n\t\t\tlet p;\n\t\t\tif ( d < 0 ) {\n\n\t\t\t\tp = l1.start;\n\n\t\t\t} else {\n\n\t\t\t\tp = l1.end;\n\n\t\t\t}\n\n\t\t\tlet p2;\n\t\t\tif ( d2 < 0 ) {\n\n\t\t\t\tp2 = l2.start;\n\n\t\t\t} else {\n\n\t\t\t\tp2 = l2.end;\n\n\t\t\t}\n\n\t\t\tconst closestPoint = temp1;\n\t\t\tconst closestPoint2 = temp2;\n\t\t\tl1.closestPointToPoint( p2, true, temp1 );\n\t\t\tl2.closestPointToPoint( p, true, temp2 );\n\n\t\t\tif ( closestPoint.distanceToSquared( p2 ) <= closestPoint2.distanceToSquared( p ) ) {\n\n\t\t\t\ttarget1.copy( closestPoint );\n\t\t\t\ttarget2.copy( p2 );\n\t\t\t\treturn;\n\n\t\t\t} else {\n\n\t\t\t\ttarget1.copy( p );\n\t\t\t\ttarget2.copy( closestPoint2 );\n\t\t\t\treturn;\n\n\t\t\t}\n\n\t\t}\n\n\t};\n\n} )();\n\n\nexport const sphereIntersectTriangle = ( function () {\n\n\t// https://stackoverflow.com/questions/34043955/detect-collision-between-sphere-and-triangle-in-three-js\n\tconst closestPointTemp = new Vector3();\n\tconst projectedPointTemp = new Vector3();\n\tconst planeTemp = new Plane();\n\tconst lineTemp = new Line3();\n\treturn function sphereIntersectTriangle( sphere, triangle ) {\n\n\t\tconst { radius, center } = sphere;\n\t\tconst { a, b, c } = triangle;\n\n\t\t// phase 1\n\t\tlineTemp.start = a;\n\t\tlineTemp.end = b;\n\t\tconst closestPoint1 = lineTemp.closestPointToPoint( center, true, closestPointTemp );\n\t\tif ( closestPoint1.distanceTo( center ) <= radius ) return true;\n\n\t\tlineTemp.start = a;\n\t\tlineTemp.end = c;\n\t\tconst closestPoint2 = lineTemp.closestPointToPoint( center, true, closestPointTemp );\n\t\tif ( closestPoint2.distanceTo( center ) <= radius ) return true;\n\n\t\tlineTemp.start = b;\n\t\tlineTemp.end = c;\n\t\tconst closestPoint3 = lineTemp.closestPointToPoint( center, true, closestPointTemp );\n\t\tif ( closestPoint3.distanceTo( center ) <= radius ) return true;\n\n\t\t// phase 2\n\t\tconst plane = triangle.getPlane( planeTemp );\n\t\tconst dp = Math.abs( plane.distanceToPoint( center ) );\n\t\tif ( dp <= radius ) {\n\n\t\t\tconst pp = plane.projectPoint( center, projectedPointTemp );\n\t\t\tconst cp = triangle.containsPoint( pp );\n\t\t\tif ( cp ) return true;\n\n\t\t}\n\n\t\treturn false;\n\n\t};\n\n} )();\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,OAAO;AAEtD,OAAO,MAAMC,sBAAsB,GAAK,YAAY;EAEnD;EACA,MAAMC,IAAI,GAAG,IAAIL,OAAO,CAAC,CAAC;EAC1B,MAAMM,IAAI,GAAG,IAAIN,OAAO,CAAC,CAAC;EAC1B,MAAMO,GAAG,GAAG,IAAIP,OAAO,CAAC,CAAC;EACzB,OAAO,SAASI,sBAAsBA,CAAEI,EAAE,EAAEC,EAAE,EAAEC,MAAM,EAAG;IAExD,MAAMC,EAAE,GAAGH,EAAE,CAACI,KAAK;IACnB,MAAMC,GAAG,GAAGR,IAAI;IAChB,MAAMS,EAAE,GAAGL,EAAE,CAACG,KAAK;IACnB,MAAMG,GAAG,GAAGT,IAAI;IAEhBC,GAAG,CAACS,UAAU,CAAEL,EAAE,EAAEG,EAAG,CAAC;IACxBT,IAAI,CAACW,UAAU,CAAER,EAAE,CAACS,GAAG,EAAET,EAAE,CAACI,KAAM,CAAC;IACnCN,IAAI,CAACU,UAAU,CAAEP,EAAE,CAACQ,GAAG,EAAER,EAAE,CAACG,KAAM,CAAC;;IAEnC;IACA,MAAMM,KAAK,GAAGX,GAAG,CAACY,GAAG,CAAEJ,GAAI,CAAC;;IAE5B;IACA,MAAMK,KAAK,GAAGL,GAAG,CAACI,GAAG,CAAEN,GAAI,CAAC;;IAE5B;IACA,MAAMQ,KAAK,GAAGN,GAAG,CAACI,GAAG,CAAEJ,GAAI,CAAC;;IAE5B;IACA,MAAMO,KAAK,GAAGf,GAAG,CAACY,GAAG,CAAEN,GAAI,CAAC;;IAE5B;IACA,MAAMU,KAAK,GAAGV,GAAG,CAACM,GAAG,CAAEN,GAAI,CAAC;;IAE5B;IACA,MAAMW,KAAK,GAAGD,KAAK,GAAGF,KAAK,GAAGD,KAAK,GAAGA,KAAK;IAE3C,IAAIK,CAAC,EAAEC,EAAE;IACT,IAAKF,KAAK,KAAK,CAAC,EAAG;MAElBC,CAAC,GAAG,CAAEP,KAAK,GAAGE,KAAK,GAAGE,KAAK,GAAGD,KAAK,IAAKG,KAAK;IAE9C,CAAC,MAAM;MAENC,CAAC,GAAG,CAAC;IAEN;IAEAC,EAAE,GAAG,CAAER,KAAK,GAAGO,CAAC,GAAGL,KAAK,IAAKC,KAAK;IAElCX,MAAM,CAACiB,CAAC,GAAGF,CAAC;IACZf,MAAM,CAACkB,CAAC,GAAGF,EAAE;EAEd,CAAC;AAEF,CAAC,CAAG,CAAC;AAEL,OAAO,MAAMG,6BAA6B,GAAK,YAAY;EAE1D;EACA,MAAMC,WAAW,GAAG,IAAI7B,OAAO,CAAC,CAAC;EACjC,MAAM8B,KAAK,GAAG,IAAI/B,OAAO,CAAC,CAAC;EAC3B,MAAMgC,KAAK,GAAG,IAAIhC,OAAO,CAAC,CAAC;EAC3B,OAAO,SAAS6B,6BAA6BA,CAAErB,EAAE,EAAEC,EAAE,EAAEwB,OAAO,EAAEC,OAAO,EAAG;IAEzE9B,sBAAsB,CAAEI,EAAE,EAAEC,EAAE,EAAEqB,WAAY,CAAC;IAE7C,IAAIL,CAAC,GAAGK,WAAW,CAACH,CAAC;IACrB,IAAID,EAAE,GAAGI,WAAW,CAACF,CAAC;IACtB,IAAKH,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,IAAIC,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAG;MAE7ClB,EAAE,CAAC2B,EAAE,CAAEV,CAAC,EAAEQ,OAAQ,CAAC;MACnBxB,EAAE,CAAC0B,EAAE,CAAET,EAAE,EAAEQ,OAAQ,CAAC;MAEpB;IAED,CAAC,MAAM,IAAKT,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,EAAG;MAE9B;MACA,IAAKC,EAAE,GAAG,CAAC,EAAG;QAEbjB,EAAE,CAAC0B,EAAE,CAAE,CAAC,EAAED,OAAQ,CAAC;MAEpB,CAAC,MAAM;QAENzB,EAAE,CAAC0B,EAAE,CAAE,CAAC,EAAED,OAAQ,CAAC;MAEpB;MAEA1B,EAAE,CAAC4B,mBAAmB,CAAEF,OAAO,EAAE,IAAI,EAAED,OAAQ,CAAC;MAChD;IAED,CAAC,MAAM,IAAKP,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAG;MAEhC;MACA,IAAKD,CAAC,GAAG,CAAC,EAAG;QAEZjB,EAAE,CAAC2B,EAAE,CAAE,CAAC,EAAEF,OAAQ,CAAC;MAEpB,CAAC,MAAM;QAENzB,EAAE,CAAC2B,EAAE,CAAE,CAAC,EAAEF,OAAQ,CAAC;MAEpB;MAEAxB,EAAE,CAAC2B,mBAAmB,CAAEH,OAAO,EAAE,IAAI,EAAEC,OAAQ,CAAC;MAChD;IAED,CAAC,MAAM;MAEN;MACA,IAAIG,CAAC;MACL,IAAKZ,CAAC,GAAG,CAAC,EAAG;QAEZY,CAAC,GAAG7B,EAAE,CAACI,KAAK;MAEb,CAAC,MAAM;QAENyB,CAAC,GAAG7B,EAAE,CAACS,GAAG;MAEX;MAEA,IAAIqB,EAAE;MACN,IAAKZ,EAAE,GAAG,CAAC,EAAG;QAEbY,EAAE,GAAG7B,EAAE,CAACG,KAAK;MAEd,CAAC,MAAM;QAEN0B,EAAE,GAAG7B,EAAE,CAACQ,GAAG;MAEZ;MAEA,MAAMsB,YAAY,GAAGR,KAAK;MAC1B,MAAMS,aAAa,GAAGR,KAAK;MAC3BxB,EAAE,CAAC4B,mBAAmB,CAAEE,EAAE,EAAE,IAAI,EAAEP,KAAM,CAAC;MACzCtB,EAAE,CAAC2B,mBAAmB,CAAEC,CAAC,EAAE,IAAI,EAAEL,KAAM,CAAC;MAExC,IAAKO,YAAY,CAACE,iBAAiB,CAAEH,EAAG,CAAC,IAAIE,aAAa,CAACC,iBAAiB,CAAEJ,CAAE,CAAC,EAAG;QAEnFJ,OAAO,CAACS,IAAI,CAAEH,YAAa,CAAC;QAC5BL,OAAO,CAACQ,IAAI,CAAEJ,EAAG,CAAC;QAClB;MAED,CAAC,MAAM;QAENL,OAAO,CAACS,IAAI,CAAEL,CAAE,CAAC;QACjBH,OAAO,CAACQ,IAAI,CAAEF,aAAc,CAAC;QAC7B;MAED;IAED;EAED,CAAC;AAEF,CAAC,CAAG,CAAC;AAGL,OAAO,MAAMG,uBAAuB,GAAK,YAAY;EAEpD;EACA,MAAMC,gBAAgB,GAAG,IAAI5C,OAAO,CAAC,CAAC;EACtC,MAAM6C,kBAAkB,GAAG,IAAI7C,OAAO,CAAC,CAAC;EACxC,MAAM8C,SAAS,GAAG,IAAI5C,KAAK,CAAC,CAAC;EAC7B,MAAM6C,QAAQ,GAAG,IAAI5C,KAAK,CAAC,CAAC;EAC5B,OAAO,SAASwC,uBAAuBA,CAAEK,MAAM,EAAEC,QAAQ,EAAG;IAE3D,MAAM;MAAEC,MAAM;MAAEC;IAAO,CAAC,GAAGH,MAAM;IACjC,MAAM;MAAEI,CAAC;MAAEC,CAAC;MAAEC;IAAE,CAAC,GAAGL,QAAQ;;IAE5B;IACAF,QAAQ,CAACnC,KAAK,GAAGwC,CAAC;IAClBL,QAAQ,CAAC9B,GAAG,GAAGoC,CAAC;IAChB,MAAME,aAAa,GAAGR,QAAQ,CAACX,mBAAmB,CAAEe,MAAM,EAAE,IAAI,EAAEP,gBAAiB,CAAC;IACpF,IAAKW,aAAa,CAACC,UAAU,CAAEL,MAAO,CAAC,IAAID,MAAM,EAAG,OAAO,IAAI;IAE/DH,QAAQ,CAACnC,KAAK,GAAGwC,CAAC;IAClBL,QAAQ,CAAC9B,GAAG,GAAGqC,CAAC;IAChB,MAAMd,aAAa,GAAGO,QAAQ,CAACX,mBAAmB,CAAEe,MAAM,EAAE,IAAI,EAAEP,gBAAiB,CAAC;IACpF,IAAKJ,aAAa,CAACgB,UAAU,CAAEL,MAAO,CAAC,IAAID,MAAM,EAAG,OAAO,IAAI;IAE/DH,QAAQ,CAACnC,KAAK,GAAGyC,CAAC;IAClBN,QAAQ,CAAC9B,GAAG,GAAGqC,CAAC;IAChB,MAAMG,aAAa,GAAGV,QAAQ,CAACX,mBAAmB,CAAEe,MAAM,EAAE,IAAI,EAAEP,gBAAiB,CAAC;IACpF,IAAKa,aAAa,CAACD,UAAU,CAAEL,MAAO,CAAC,IAAID,MAAM,EAAG,OAAO,IAAI;;IAE/D;IACA,MAAMQ,KAAK,GAAGT,QAAQ,CAACU,QAAQ,CAAEb,SAAU,CAAC;IAC5C,MAAMc,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAEJ,KAAK,CAACK,eAAe,CAAEZ,MAAO,CAAE,CAAC;IACtD,IAAKS,EAAE,IAAIV,MAAM,EAAG;MAEnB,MAAMc,EAAE,GAAGN,KAAK,CAACO,YAAY,CAAEd,MAAM,EAAEN,kBAAmB,CAAC;MAC3D,MAAMqB,EAAE,GAAGjB,QAAQ,CAACkB,aAAa,CAAEH,EAAG,CAAC;MACvC,IAAKE,EAAE,EAAG,OAAO,IAAI;IAEtB;IAEA,OAAO,KAAK;EAEb,CAAC;AAEF,CAAC,CAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}