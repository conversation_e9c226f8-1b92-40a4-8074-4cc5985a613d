{"ast": null, "code": "const isNotNull = value => value !== null;\nfunction getFinalKeyframe(keyframes, _ref, finalKeyframe) {\n  let {\n    repeat,\n    repeatType = \"loop\"\n  } = _ref;\n  let speed = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;\n  const resolvedKeyframes = keyframes.filter(isNotNull);\n  const useFirstKeyframe = speed < 0 || repeat && repeatType !== \"loop\" && repeat % 2 === 1;\n  const index = useFirstKeyframe ? 0 : resolvedKeyframes.length - 1;\n  return !index || finalKeyframe === undefined ? resolvedKeyframes[index] : finalKeyframe;\n}\nexport { getFinalKeyframe };", "map": {"version": 3, "names": ["isNotNull", "value", "getFinalKeyframe", "keyframes", "_ref", "finalKeyframe", "repeat", "repeatType", "speed", "arguments", "length", "undefined", "resolvedKeyframes", "filter", "useFirstKeyframe", "index"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/motion-dom/dist/es/animation/keyframes/get-final.mjs"], "sourcesContent": ["const isNotNull = (value) => value !== null;\nfunction getFinalKeyframe(keyframes, { repeat, repeatType = \"loop\" }, finalKeyframe, speed = 1) {\n    const resolvedKeyframes = keyframes.filter(isNotNull);\n    const useFirstKeyframe = speed < 0 || (repeat && repeatType !== \"loop\" && repeat % 2 === 1);\n    const index = useFirstKeyframe ? 0 : resolvedKeyframes.length - 1;\n    return !index || finalKeyframe === undefined\n        ? resolvedKeyframes[index]\n        : finalKeyframe;\n}\n\nexport { getFinalKeyframe };\n"], "mappings": "AAAA,MAAMA,SAAS,GAAIC,KAAK,IAAKA,KAAK,KAAK,IAAI;AAC3C,SAASC,gBAAgBA,CAACC,SAAS,EAAAC,IAAA,EAAmCC,aAAa,EAAa;EAAA,IAA3D;IAAEC,MAAM;IAAEC,UAAU,GAAG;EAAO,CAAC,GAAAH,IAAA;EAAA,IAAiBI,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAC1F,MAAMG,iBAAiB,GAAGT,SAAS,CAACU,MAAM,CAACb,SAAS,CAAC;EACrD,MAAMc,gBAAgB,GAAGN,KAAK,GAAG,CAAC,IAAKF,MAAM,IAAIC,UAAU,KAAK,MAAM,IAAID,MAAM,GAAG,CAAC,KAAK,CAAE;EAC3F,MAAMS,KAAK,GAAGD,gBAAgB,GAAG,CAAC,GAAGF,iBAAiB,CAACF,MAAM,GAAG,CAAC;EACjE,OAAO,CAACK,KAAK,IAAIV,aAAa,KAAKM,SAAS,GACtCC,iBAAiB,CAACG,KAAK,CAAC,GACxBV,aAAa;AACvB;AAEA,SAASH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}