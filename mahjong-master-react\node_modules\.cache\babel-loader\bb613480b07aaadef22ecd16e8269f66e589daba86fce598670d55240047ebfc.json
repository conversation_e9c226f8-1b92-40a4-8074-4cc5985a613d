{"ast": null, "code": "const pxValues = new Set([\n// Border props\n\"borderWidth\", \"borderTopWidth\", \"borderRightWidth\", \"borderBottomWidth\", \"borderLeftWidth\", \"borderRadius\", \"radius\", \"borderTopLeftRadius\", \"borderTopRightRadius\", \"borderBottomRightRadius\", \"borderBottomLeftRadius\",\n// Positioning props\n\"width\", \"maxWidth\", \"height\", \"maxHeight\", \"top\", \"right\", \"bottom\", \"left\",\n// Spacing props\n\"padding\", \"paddingTop\", \"paddingRight\", \"paddingBottom\", \"paddingLeft\", \"margin\", \"marginTop\", \"marginRight\", \"marginBottom\", \"marginLeft\",\n// Misc\n\"backgroundPositionX\", \"backgroundPositionY\"]);\nexport { pxValues };", "map": {"version": 3, "names": ["px<PERSON><PERSON><PERSON>", "Set"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/motion-dom/dist/es/animation/waapi/utils/px-values.mjs"], "sourcesContent": ["const pxValues = new Set([\n    // Border props\n    \"borderWidth\",\n    \"borderTopWidth\",\n    \"borderRightWidth\",\n    \"borderBottomWidth\",\n    \"borderLeftWidth\",\n    \"borderRadius\",\n    \"radius\",\n    \"borderTopLeftRadius\",\n    \"borderTopRightRadius\",\n    \"borderBottomRightRadius\",\n    \"borderBottomLeftRadius\",\n    // Positioning props\n    \"width\",\n    \"maxWidth\",\n    \"height\",\n    \"maxHeight\",\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\",\n    // Spacing props\n    \"padding\",\n    \"paddingTop\",\n    \"paddingRight\",\n    \"paddingBottom\",\n    \"paddingLeft\",\n    \"margin\",\n    \"marginTop\",\n    \"marginRight\",\n    \"marginBottom\",\n    \"marginLeft\",\n    // Misc\n    \"backgroundPositionX\",\n    \"backgroundPositionY\",\n]);\n\nexport { pxValues };\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAG,IAAIC,GAAG,CAAC;AACrB;AACA,aAAa,EACb,gBAAgB,EAChB,kBAAkB,EAClB,mBAAmB,EACnB,iBAAiB,EACjB,cAAc,EACd,QAAQ,EACR,qBAAqB,EACrB,sBAAsB,EACtB,yBAAyB,EACzB,wBAAwB;AACxB;AACA,OAAO,EACP,UAAU,EACV,QAAQ,EACR,WAAW,EACX,KAAK,EACL,OAAO,EACP,QAAQ,EACR,MAAM;AACN;AACA,SAAS,EACT,YAAY,EACZ,cAAc,EACd,eAAe,EACf,aAAa,EACb,QAAQ,EACR,WAAW,EACX,aAAa,EACb,cAAc,EACd,YAAY;AACZ;AACA,qBAAqB,EACrB,qBAAqB,CACxB,CAAC;AAEF,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}