{"ast": null, "code": "import { createStore } from 'zustand/vanilla';\nexport * from 'zustand/vanilla';\nimport ReactExports from 'react';\nimport useSyncExternalStoreExports from 'use-sync-external-store/shim/with-selector.js';\nconst {\n  useDebugValue\n} = ReactExports;\nconst {\n  useSyncExternalStoreWithSelector\n} = useSyncExternalStoreExports;\nlet didWarnAboutEqualityFn = false;\nconst identity = arg => arg;\nfunction useStore(api, selector = identity, equalityFn) {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && equalityFn && !didWarnAboutEqualityFn) {\n    console.warn(\"[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937\");\n    didWarnAboutEqualityFn = true;\n  }\n  const slice = useSyncExternalStoreWithSelector(api.subscribe, api.getState, api.getServerState || api.getInitialState, selector, equalityFn);\n  useDebugValue(slice);\n  return slice;\n}\nconst createImpl = createState => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && typeof createState !== \"function\") {\n    console.warn(\"[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.\");\n  }\n  const api = typeof createState === \"function\" ? createStore(createState) : createState;\n  const useBoundStore = (selector, equalityFn) => useStore(api, selector, equalityFn);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = createState => createState ? createImpl(createState) : createImpl;\nvar react = createState => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\"[DEPRECATED] Default export is deprecated. Instead use `import { create } from 'zustand'`.\");\n  }\n  return create(createState);\n};\nexport { create, react as default, useStore };", "map": {"version": 3, "names": ["createStore", "ReactExports", "useSyncExternalStoreExports", "useDebugValue", "useSyncExternalStoreWithSelector", "didWarnAboutEqualityFn", "identity", "arg", "useStore", "api", "selector", "equalityFn", "import", "meta", "env", "MODE", "console", "warn", "slice", "subscribe", "getState", "getServerState", "getInitialState", "createImpl", "createState", "useBoundStore", "Object", "assign", "create", "react", "default"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/tunnel-rat/node_modules/zustand/esm/index.mjs"], "sourcesContent": ["import { createStore } from 'zustand/vanilla';\nexport * from 'zustand/vanilla';\nimport ReactExports from 'react';\nimport useSyncExternalStoreExports from 'use-sync-external-store/shim/with-selector.js';\n\nconst { useDebugValue } = ReactExports;\nconst { useSyncExternalStoreWithSelector } = useSyncExternalStoreExports;\nlet didWarnAboutEqualityFn = false;\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity, equalityFn) {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && equalityFn && !didWarnAboutEqualityFn) {\n    console.warn(\n      \"[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937\"\n    );\n    didWarnAboutEqualityFn = true;\n  }\n  const slice = useSyncExternalStoreWithSelector(\n    api.subscribe,\n    api.getState,\n    api.getServerState || api.getInitialState,\n    selector,\n    equalityFn\n  );\n  useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && typeof createState !== \"function\") {\n    console.warn(\n      \"[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.\"\n    );\n  }\n  const api = typeof createState === \"function\" ? createStore(createState) : createState;\n  const useBoundStore = (selector, equalityFn) => useStore(api, selector, equalityFn);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\nvar react = (createState) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use `import { create } from 'zustand'`.\"\n    );\n  }\n  return create(createState);\n};\n\nexport { create, react as default, useStore };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,iBAAiB;AAC7C,cAAc,iBAAiB;AAC/B,OAAOC,YAAY,MAAM,OAAO;AAChC,OAAOC,2BAA2B,MAAM,+CAA+C;AAEvF,MAAM;EAAEC;AAAc,CAAC,GAAGF,YAAY;AACtC,MAAM;EAAEG;AAAiC,CAAC,GAAGF,2BAA2B;AACxE,IAAIG,sBAAsB,GAAG,KAAK;AAClC,MAAMC,QAAQ,GAAIC,GAAG,IAAKA,GAAG;AAC7B,SAASC,QAAQA,CAACC,GAAG,EAAEC,QAAQ,GAAGJ,QAAQ,EAAEK,UAAU,EAAE;EACtD,IAAI,CAACC,MAAM,CAACC,IAAI,CAACC,GAAG,GAAGF,MAAM,CAACC,IAAI,CAACC,GAAG,CAACC,IAAI,GAAG,KAAK,CAAC,MAAM,YAAY,IAAIJ,UAAU,IAAI,CAACN,sBAAsB,EAAE;IAC/GW,OAAO,CAACC,IAAI,CACV,wNACF,CAAC;IACDZ,sBAAsB,GAAG,IAAI;EAC/B;EACA,MAAMa,KAAK,GAAGd,gCAAgC,CAC5CK,GAAG,CAACU,SAAS,EACbV,GAAG,CAACW,QAAQ,EACZX,GAAG,CAACY,cAAc,IAAIZ,GAAG,CAACa,eAAe,EACzCZ,QAAQ,EACRC,UACF,CAAC;EACDR,aAAa,CAACe,KAAK,CAAC;EACpB,OAAOA,KAAK;AACd;AACA,MAAMK,UAAU,GAAIC,WAAW,IAAK;EAClC,IAAI,CAACZ,MAAM,CAACC,IAAI,CAACC,GAAG,GAAGF,MAAM,CAACC,IAAI,CAACC,GAAG,CAACC,IAAI,GAAG,KAAK,CAAC,MAAM,YAAY,IAAI,OAAOS,WAAW,KAAK,UAAU,EAAE;IAC3GR,OAAO,CAACC,IAAI,CACV,iIACF,CAAC;EACH;EACA,MAAMR,GAAG,GAAG,OAAOe,WAAW,KAAK,UAAU,GAAGxB,WAAW,CAACwB,WAAW,CAAC,GAAGA,WAAW;EACtF,MAAMC,aAAa,GAAGA,CAACf,QAAQ,EAAEC,UAAU,KAAKH,QAAQ,CAACC,GAAG,EAAEC,QAAQ,EAAEC,UAAU,CAAC;EACnFe,MAAM,CAACC,MAAM,CAACF,aAAa,EAAEhB,GAAG,CAAC;EACjC,OAAOgB,aAAa;AACtB,CAAC;AACD,MAAMG,MAAM,GAAIJ,WAAW,IAAKA,WAAW,GAAGD,UAAU,CAACC,WAAW,CAAC,GAAGD,UAAU;AAClF,IAAIM,KAAK,GAAIL,WAAW,IAAK;EAC3B,IAAI,CAACZ,MAAM,CAACC,IAAI,CAACC,GAAG,GAAGF,MAAM,CAACC,IAAI,CAACC,GAAG,CAACC,IAAI,GAAG,KAAK,CAAC,MAAM,YAAY,EAAE;IACtEC,OAAO,CAACC,IAAI,CACV,4FACF,CAAC;EACH;EACA,OAAOW,MAAM,CAACJ,WAAW,CAAC;AAC5B,CAAC;AAED,SAASI,MAAM,EAAEC,KAAK,IAAIC,OAAO,EAAEtB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}