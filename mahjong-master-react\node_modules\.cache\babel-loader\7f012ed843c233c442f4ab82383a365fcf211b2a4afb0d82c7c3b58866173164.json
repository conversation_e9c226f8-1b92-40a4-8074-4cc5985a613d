{"ast": null, "code": "import { intersectTri } from '../../utils/ThreeRayIntersectUtilities.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\n\n/*************************************************************/\n/* This file is generated from \"iterationUtils.template.js\". */\n/*************************************************************/\n/* eslint-disable indent */\n\nfunction intersectTris(bvh, side, ray, offset, count, intersections, near, far) {\n  const {\n    geometry,\n    _indirectBuffer\n  } = bvh;\n  for (let i = offset, end = offset + count; i < end; i++) {\n    intersectTri(geometry, side, ray, i, intersections, near, far);\n  }\n}\nfunction intersectClosestTri(bvh, side, ray, offset, count, near, far) {\n  const {\n    geometry,\n    _indirectBuffer\n  } = bvh;\n  let dist = Infinity;\n  let res = null;\n  for (let i = offset, end = offset + count; i < end; i++) {\n    let intersection;\n    intersection = intersectTri(geometry, side, ray, i, null, near, far);\n    if (intersection && intersection.distance < dist) {\n      res = intersection;\n      dist = intersection.distance;\n    }\n  }\n  return res;\n}\nfunction iterateOverTriangles(offset, count, bvh, intersectsTriangleFunc, contained, depth, triangle) {\n  const {\n    geometry\n  } = bvh;\n  const {\n    index\n  } = geometry;\n  const pos = geometry.attributes.position;\n  for (let i = offset, l = count + offset; i < l; i++) {\n    let tri;\n    tri = i;\n    setTriangle(triangle, tri * 3, index, pos);\n    triangle.needsUpdate = true;\n    if (intersectsTriangleFunc(triangle, tri, contained, depth)) {\n      return true;\n    }\n  }\n  return false;\n}\nexport { intersectClosestTri, intersectTris, iterateOverTriangles };", "map": {"version": 3, "names": ["intersectTri", "set<PERSON>riangle", "intersectTris", "bvh", "side", "ray", "offset", "count", "intersections", "near", "far", "geometry", "_<PERSON><PERSON><PERSON>er", "i", "end", "intersectClosestTri", "dist", "Infinity", "res", "intersection", "distance", "iterateOverTriangles", "intersectsTriangleFunc", "contained", "depth", "triangle", "index", "pos", "attributes", "position", "l", "tri", "needsUpdate"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/three-mesh-bvh/src/core/utils/iterationUtils.generated.js"], "sourcesContent": ["import { intersectTri } from '../../utils/ThreeRayIntersectUtilities.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\n\n/*************************************************************/\n/* This file is generated from \"iterationUtils.template.js\". */\n/*************************************************************/\n/* eslint-disable indent */\n\nfunction intersectTris( bvh, side, ray, offset, count, intersections, near, far ) {\n\n\tconst { geometry, _indirectBuffer } = bvh;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\n\t\tintersectTri( geometry, side, ray, i, intersections, near, far );\n\n\n\t}\n\n}\n\nfunction intersectClosestTri( bvh, side, ray, offset, count, near, far ) {\n\n\tconst { geometry, _indirectBuffer } = bvh;\n\tlet dist = Infinity;\n\tlet res = null;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tlet intersection;\n\n\t\tintersection = intersectTri( geometry, side, ray, i, null, near, far );\n\n\n\t\tif ( intersection && intersection.distance < dist ) {\n\n\t\t\tres = intersection;\n\t\t\tdist = intersection.distance;\n\n\t\t}\n\n\t}\n\n\treturn res;\n\n}\n\nfunction iterateOverTriangles(\n\toffset,\n\tcount,\n\tbvh,\n\tintersectsTriangleFunc,\n\tcontained,\n\tdepth,\n\ttriangle\n) {\n\n\tconst { geometry } = bvh;\n\tconst { index } = geometry;\n\tconst pos = geometry.attributes.position;\n\tfor ( let i = offset, l = count + offset; i < l; i ++ ) {\n\n\t\tlet tri;\n\n\t\ttri = i;\n\n\t\tsetTriangle( triangle, tri * 3, index, pos );\n\t\ttriangle.needsUpdate = true;\n\n\t\tif ( intersectsTriangleFunc( triangle, tri, contained, depth ) ) {\n\n\t\t\treturn true;\n\n\t\t}\n\n\t}\n\n\treturn false;\n\n}\n\nexport { intersectClosestTri, intersectTris, iterateOverTriangles };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,2CAA2C;AACxE,SAASC,WAAW,QAAQ,kCAAkC;;AAE9D;AACA;AACA;AACA;;AAEA,SAASC,aAAaA,CAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,aAAa,EAAEC,IAAI,EAAEC,GAAG,EAAG;EAEjF,MAAM;IAAEC,QAAQ;IAAEC;EAAgB,CAAC,GAAGT,GAAG;EACzC,KAAM,IAAIU,CAAC,GAAGP,MAAM,EAAEQ,GAAG,GAAGR,MAAM,GAAGC,KAAK,EAAEM,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAG,EAAG;IAG3Db,YAAY,CAAEW,QAAQ,EAAEP,IAAI,EAAEC,GAAG,EAAEQ,CAAC,EAAEL,aAAa,EAAEC,IAAI,EAAEC,GAAI,CAAC;EAGjE;AAED;AAEA,SAASK,mBAAmBA,CAAEZ,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEE,IAAI,EAAEC,GAAG,EAAG;EAExE,MAAM;IAAEC,QAAQ;IAAEC;EAAgB,CAAC,GAAGT,GAAG;EACzC,IAAIa,IAAI,GAAGC,QAAQ;EACnB,IAAIC,GAAG,GAAG,IAAI;EACd,KAAM,IAAIL,CAAC,GAAGP,MAAM,EAAEQ,GAAG,GAAGR,MAAM,GAAGC,KAAK,EAAEM,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAG,EAAG;IAE3D,IAAIM,YAAY;IAEhBA,YAAY,GAAGnB,YAAY,CAAEW,QAAQ,EAAEP,IAAI,EAAEC,GAAG,EAAEQ,CAAC,EAAE,IAAI,EAAEJ,IAAI,EAAEC,GAAI,CAAC;IAGtE,IAAKS,YAAY,IAAIA,YAAY,CAACC,QAAQ,GAAGJ,IAAI,EAAG;MAEnDE,GAAG,GAAGC,YAAY;MAClBH,IAAI,GAAGG,YAAY,CAACC,QAAQ;IAE7B;EAED;EAEA,OAAOF,GAAG;AAEX;AAEA,SAASG,oBAAoBA,CAC5Bf,MAAM,EACNC,KAAK,EACLJ,GAAG,EACHmB,sBAAsB,EACtBC,SAAS,EACTC,KAAK,EACLC,QAAQ,EACP;EAED,MAAM;IAAEd;EAAS,CAAC,GAAGR,GAAG;EACxB,MAAM;IAAEuB;EAAM,CAAC,GAAGf,QAAQ;EAC1B,MAAMgB,GAAG,GAAGhB,QAAQ,CAACiB,UAAU,CAACC,QAAQ;EACxC,KAAM,IAAIhB,CAAC,GAAGP,MAAM,EAAEwB,CAAC,GAAGvB,KAAK,GAAGD,MAAM,EAAEO,CAAC,GAAGiB,CAAC,EAAEjB,CAAC,EAAG,EAAG;IAEvD,IAAIkB,GAAG;IAEPA,GAAG,GAAGlB,CAAC;IAEPZ,WAAW,CAAEwB,QAAQ,EAAEM,GAAG,GAAG,CAAC,EAAEL,KAAK,EAAEC,GAAI,CAAC;IAC5CF,QAAQ,CAACO,WAAW,GAAG,IAAI;IAE3B,IAAKV,sBAAsB,CAAEG,QAAQ,EAAEM,GAAG,EAAER,SAAS,EAAEC,KAAM,CAAC,EAAG;MAEhE,OAAO,IAAI;IAEZ;EAED;EAEA,OAAO,KAAK;AAEb;AAEA,SAAST,mBAAmB,EAAEb,aAAa,EAAEmB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}