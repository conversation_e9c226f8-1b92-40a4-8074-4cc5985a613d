{"ast": null, "code": "import { isSVGElement } from '../utils/is-svg-element.mjs';\nimport { resolveElements } from '../utils/resolve-elements.mjs';\nconst resizeHandlers = new WeakMap();\nlet observer;\nconst getSize = (borderBoxAxis, svgAxis, htmlAxis) => (target, borderBoxSize) => {\n  if (borderBoxSize && borderBoxSize[0]) {\n    return borderBoxSize[0][borderBoxAxis + \"Size\"];\n  } else if (isSVGElement(target) && \"getBBox\" in target) {\n    return target.getBBox()[svgAxis];\n  } else {\n    return target[htmlAxis];\n  }\n};\nconst getWidth = /*@__PURE__*/getSize(\"inline\", \"width\", \"offsetWidth\");\nconst getHeight = /*@__PURE__*/getSize(\"block\", \"height\", \"offsetHeight\");\nfunction notifyTarget(_ref) {\n  var _resizeHandlers$get;\n  let {\n    target,\n    borderBoxSize\n  } = _ref;\n  (_resizeHandlers$get = resizeHandlers.get(target)) === null || _resizeHandlers$get === void 0 || _resizeHandlers$get.forEach(handler => {\n    handler(target, {\n      get width() {\n        return getWidth(target, borderBoxSize);\n      },\n      get height() {\n        return getHeight(target, borderBoxSize);\n      }\n    });\n  });\n}\nfunction notifyAll(entries) {\n  entries.forEach(notifyTarget);\n}\nfunction createResizeObserver() {\n  if (typeof ResizeObserver === \"undefined\") return;\n  observer = new ResizeObserver(notifyAll);\n}\nfunction resizeElement(target, handler) {\n  if (!observer) createResizeObserver();\n  const elements = resolveElements(target);\n  elements.forEach(element => {\n    var _observer;\n    let elementHandlers = resizeHandlers.get(element);\n    if (!elementHandlers) {\n      elementHandlers = new Set();\n      resizeHandlers.set(element, elementHandlers);\n    }\n    elementHandlers.add(handler);\n    (_observer = observer) === null || _observer === void 0 || _observer.observe(element);\n  });\n  return () => {\n    elements.forEach(element => {\n      const elementHandlers = resizeHandlers.get(element);\n      elementHandlers === null || elementHandlers === void 0 || elementHandlers.delete(handler);\n      if (!(elementHandlers !== null && elementHandlers !== void 0 && elementHandlers.size)) {\n        var _observer2;\n        (_observer2 = observer) === null || _observer2 === void 0 || _observer2.unobserve(element);\n      }\n    });\n  };\n}\nexport { resizeElement };", "map": {"version": 3, "names": ["isSVGElement", "resolveElements", "resizeHandlers", "WeakMap", "observer", "getSize", "borderBoxAxis", "svgAxis", "htmlAxis", "target", "borderBoxSize", "getBBox", "getWidth", "getHeight", "notify<PERSON><PERSON><PERSON>", "_ref", "_resizeHandlers$get", "get", "for<PERSON>ach", "handler", "width", "height", "notifyAll", "entries", "createResizeObserver", "ResizeObserver", "resizeElement", "elements", "element", "_observer", "elementHandlers", "Set", "set", "add", "observe", "delete", "size", "_observer2", "unobserve"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/motion-dom/dist/es/resize/handle-element.mjs"], "sourcesContent": ["import { isSVGElement } from '../utils/is-svg-element.mjs';\nimport { resolveElements } from '../utils/resolve-elements.mjs';\n\nconst resizeHandlers = new WeakMap();\nlet observer;\nconst getSize = (borderBoxAxis, svgAxis, htmlAxis) => (target, borderBoxSize) => {\n    if (borderBoxSize && borderBoxSize[0]) {\n        return borderBoxSize[0][(borderBoxAxis + \"Size\")];\n    }\n    else if (isSVGElement(target) && \"getBBox\" in target) {\n        return target.getBBox()[svgAxis];\n    }\n    else {\n        return target[htmlAxis];\n    }\n};\nconst getWidth = /*@__PURE__*/ getSize(\"inline\", \"width\", \"offsetWidth\");\nconst getHeight = /*@__PURE__*/ getSize(\"block\", \"height\", \"offsetHeight\");\nfunction notifyTarget({ target, borderBoxSize }) {\n    resizeHandlers.get(target)?.forEach((handler) => {\n        handler(target, {\n            get width() {\n                return getWidth(target, borderBoxSize);\n            },\n            get height() {\n                return getHeight(target, borderBoxSize);\n            },\n        });\n    });\n}\nfunction notifyAll(entries) {\n    entries.forEach(notifyTarget);\n}\nfunction createResizeObserver() {\n    if (typeof ResizeObserver === \"undefined\")\n        return;\n    observer = new ResizeObserver(notifyAll);\n}\nfunction resizeElement(target, handler) {\n    if (!observer)\n        createResizeObserver();\n    const elements = resolveElements(target);\n    elements.forEach((element) => {\n        let elementHandlers = resizeHandlers.get(element);\n        if (!elementHandlers) {\n            elementHandlers = new Set();\n            resizeHandlers.set(element, elementHandlers);\n        }\n        elementHandlers.add(handler);\n        observer?.observe(element);\n    });\n    return () => {\n        elements.forEach((element) => {\n            const elementHandlers = resizeHandlers.get(element);\n            elementHandlers?.delete(handler);\n            if (!elementHandlers?.size) {\n                observer?.unobserve(element);\n            }\n        });\n    };\n}\n\nexport { resizeElement };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,cAAc,GAAG,IAAIC,OAAO,CAAC,CAAC;AACpC,IAAIC,QAAQ;AACZ,MAAMC,OAAO,GAAGA,CAACC,aAAa,EAAEC,OAAO,EAAEC,QAAQ,KAAK,CAACC,MAAM,EAAEC,aAAa,KAAK;EAC7E,IAAIA,aAAa,IAAIA,aAAa,CAAC,CAAC,CAAC,EAAE;IACnC,OAAOA,aAAa,CAAC,CAAC,CAAC,CAAEJ,aAAa,GAAG,MAAM,CAAE;EACrD,CAAC,MACI,IAAIN,YAAY,CAACS,MAAM,CAAC,IAAI,SAAS,IAAIA,MAAM,EAAE;IAClD,OAAOA,MAAM,CAACE,OAAO,CAAC,CAAC,CAACJ,OAAO,CAAC;EACpC,CAAC,MACI;IACD,OAAOE,MAAM,CAACD,QAAQ,CAAC;EAC3B;AACJ,CAAC;AACD,MAAMI,QAAQ,GAAG,aAAcP,OAAO,CAAC,QAAQ,EAAE,OAAO,EAAE,aAAa,CAAC;AACxE,MAAMQ,SAAS,GAAG,aAAcR,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,cAAc,CAAC;AAC1E,SAASS,YAAYA,CAAAC,IAAA,EAA4B;EAAA,IAAAC,mBAAA;EAAA,IAA3B;IAAEP,MAAM;IAAEC;EAAc,CAAC,GAAAK,IAAA;EAC3C,CAAAC,mBAAA,GAAAd,cAAc,CAACe,GAAG,CAACR,MAAM,CAAC,cAAAO,mBAAA,eAA1BA,mBAAA,CAA4BE,OAAO,CAAEC,OAAO,IAAK;IAC7CA,OAAO,CAACV,MAAM,EAAE;MACZ,IAAIW,KAAKA,CAAA,EAAG;QACR,OAAOR,QAAQ,CAACH,MAAM,EAAEC,aAAa,CAAC;MAC1C,CAAC;MACD,IAAIW,MAAMA,CAAA,EAAG;QACT,OAAOR,SAAS,CAACJ,MAAM,EAAEC,aAAa,CAAC;MAC3C;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,SAASY,SAASA,CAACC,OAAO,EAAE;EACxBA,OAAO,CAACL,OAAO,CAACJ,YAAY,CAAC;AACjC;AACA,SAASU,oBAAoBA,CAAA,EAAG;EAC5B,IAAI,OAAOC,cAAc,KAAK,WAAW,EACrC;EACJrB,QAAQ,GAAG,IAAIqB,cAAc,CAACH,SAAS,CAAC;AAC5C;AACA,SAASI,aAAaA,CAACjB,MAAM,EAAEU,OAAO,EAAE;EACpC,IAAI,CAACf,QAAQ,EACToB,oBAAoB,CAAC,CAAC;EAC1B,MAAMG,QAAQ,GAAG1B,eAAe,CAACQ,MAAM,CAAC;EACxCkB,QAAQ,CAACT,OAAO,CAAEU,OAAO,IAAK;IAAA,IAAAC,SAAA;IAC1B,IAAIC,eAAe,GAAG5B,cAAc,CAACe,GAAG,CAACW,OAAO,CAAC;IACjD,IAAI,CAACE,eAAe,EAAE;MAClBA,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC3B7B,cAAc,CAAC8B,GAAG,CAACJ,OAAO,EAAEE,eAAe,CAAC;IAChD;IACAA,eAAe,CAACG,GAAG,CAACd,OAAO,CAAC;IAC5B,CAAAU,SAAA,GAAAzB,QAAQ,cAAAyB,SAAA,eAARA,SAAA,CAAUK,OAAO,CAACN,OAAO,CAAC;EAC9B,CAAC,CAAC;EACF,OAAO,MAAM;IACTD,QAAQ,CAACT,OAAO,CAAEU,OAAO,IAAK;MAC1B,MAAME,eAAe,GAAG5B,cAAc,CAACe,GAAG,CAACW,OAAO,CAAC;MACnDE,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEK,MAAM,CAAChB,OAAO,CAAC;MAChC,IAAI,EAACW,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEM,IAAI,GAAE;QAAA,IAAAC,UAAA;QACxB,CAAAA,UAAA,GAAAjC,QAAQ,cAAAiC,UAAA,eAARA,UAAA,CAAUC,SAAS,CAACV,OAAO,CAAC;MAChC;IACJ,CAAC,CAAC;EACN,CAAC;AACL;AAEA,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}