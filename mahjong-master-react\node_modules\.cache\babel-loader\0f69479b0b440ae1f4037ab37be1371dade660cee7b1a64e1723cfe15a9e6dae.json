{"ast": null, "code": "import { Vector4, Vector3 } from \"three\";\nfunction findSpan(p, u, U) {\n  const n = U.length - p - 1;\n  if (u >= U[n]) {\n    return n - 1;\n  }\n  if (u <= U[p]) {\n    return p;\n  }\n  let low = p;\n  let high = n;\n  let mid = Math.floor((low + high) / 2);\n  while (u < U[mid] || u >= U[mid + 1]) {\n    if (u < U[mid]) {\n      high = mid;\n    } else {\n      low = mid;\n    }\n    mid = Math.floor((low + high) / 2);\n  }\n  return mid;\n}\nfunction calcBasisFunctions(span, u, p, U) {\n  const N = [];\n  const left = [];\n  const right = [];\n  N[0] = 1;\n  for (let j = 1; j <= p; ++j) {\n    left[j] = u - U[span + 1 - j];\n    right[j] = U[span + j] - u;\n    let saved = 0;\n    for (let r = 0; r < j; ++r) {\n      const rv = right[r + 1];\n      const lv = left[j - r];\n      const temp = N[r] / (rv + lv);\n      N[r] = saved + rv * temp;\n      saved = lv * temp;\n    }\n    N[j] = saved;\n  }\n  return N;\n}\nfunction calcBSplinePoint(p, U, P, u) {\n  const span = findSpan(p, u, U);\n  const N = calcBasisFunctions(span, u, p, U);\n  const C = new Vector4(0, 0, 0, 0);\n  for (let j = 0; j <= p; ++j) {\n    const point = P[span - p + j];\n    const Nj = N[j];\n    const wNj = point.w * Nj;\n    C.x += point.x * wNj;\n    C.y += point.y * wNj;\n    C.z += point.z * wNj;\n    C.w += point.w * Nj;\n  }\n  return C;\n}\nfunction calcBasisFunctionDerivatives(span, u, p, n, U) {\n  const zeroArr = [];\n  for (let i = 0; i <= p; ++i) zeroArr[i] = 0;\n  const ders = [];\n  for (let i = 0; i <= n; ++i) ders[i] = zeroArr.slice(0);\n  const ndu = [];\n  for (let i = 0; i <= p; ++i) ndu[i] = zeroArr.slice(0);\n  ndu[0][0] = 1;\n  const left = zeroArr.slice(0);\n  const right = zeroArr.slice(0);\n  for (let j = 1; j <= p; ++j) {\n    left[j] = u - U[span + 1 - j];\n    right[j] = U[span + j] - u;\n    let saved = 0;\n    for (let r2 = 0; r2 < j; ++r2) {\n      const rv = right[r2 + 1];\n      const lv = left[j - r2];\n      ndu[j][r2] = rv + lv;\n      const temp = ndu[r2][j - 1] / ndu[j][r2];\n      ndu[r2][j] = saved + rv * temp;\n      saved = lv * temp;\n    }\n    ndu[j][j] = saved;\n  }\n  for (let j = 0; j <= p; ++j) {\n    ders[0][j] = ndu[j][p];\n  }\n  for (let r2 = 0; r2 <= p; ++r2) {\n    let s1 = 0;\n    let s2 = 1;\n    const a = [];\n    for (let i = 0; i <= p; ++i) {\n      a[i] = zeroArr.slice(0);\n    }\n    a[0][0] = 1;\n    for (let k = 1; k <= n; ++k) {\n      let d = 0;\n      const rk = r2 - k;\n      const pk = p - k;\n      if (r2 >= k) {\n        a[s2][0] = a[s1][0] / ndu[pk + 1][rk];\n        d = a[s2][0] * ndu[rk][pk];\n      }\n      const j1 = rk >= -1 ? 1 : -rk;\n      const j2 = r2 - 1 <= pk ? k - 1 : p - r2;\n      for (let j3 = j1; j3 <= j2; ++j3) {\n        a[s2][j3] = (a[s1][j3] - a[s1][j3 - 1]) / ndu[pk + 1][rk + j3];\n        d += a[s2][j3] * ndu[rk + j3][pk];\n      }\n      if (r2 <= pk) {\n        a[s2][k] = -a[s1][k - 1] / ndu[pk + 1][r2];\n        d += a[s2][k] * ndu[r2][pk];\n      }\n      ders[k][r2] = d;\n      const j = s1;\n      s1 = s2;\n      s2 = j;\n    }\n  }\n  let r = p;\n  for (let k = 1; k <= n; ++k) {\n    for (let j = 0; j <= p; ++j) {\n      ders[k][j] *= r;\n    }\n    r *= p - k;\n  }\n  return ders;\n}\nfunction calcBSplineDerivatives(p, U, P, u, nd) {\n  const du = nd < p ? nd : p;\n  const CK = [];\n  const span = findSpan(p, u, U);\n  const nders = calcBasisFunctionDerivatives(span, u, p, du, U);\n  const Pw = [];\n  for (let i = 0; i < P.length; ++i) {\n    const point = P[i].clone();\n    const w = point.w;\n    point.x *= w;\n    point.y *= w;\n    point.z *= w;\n    Pw[i] = point;\n  }\n  for (let k = 0; k <= du; ++k) {\n    const point = Pw[span - p].clone().multiplyScalar(nders[k][0]);\n    for (let j = 1; j <= p; ++j) {\n      point.add(Pw[span - p + j].clone().multiplyScalar(nders[k][j]));\n    }\n    CK[k] = point;\n  }\n  for (let k = du + 1; k <= nd + 1; ++k) {\n    CK[k] = new Vector4(0, 0, 0);\n  }\n  return CK;\n}\nfunction calcKoverI(k, i) {\n  let nom = 1;\n  for (let j = 2; j <= k; ++j) {\n    nom *= j;\n  }\n  let denom = 1;\n  for (let j = 2; j <= i; ++j) {\n    denom *= j;\n  }\n  for (let j = 2; j <= k - i; ++j) {\n    denom *= j;\n  }\n  return nom / denom;\n}\nfunction calcRationalCurveDerivatives(Pders) {\n  const nd = Pders.length;\n  const Aders = [];\n  const wders = [];\n  for (let i = 0; i < nd; ++i) {\n    const point = Pders[i];\n    Aders[i] = new Vector3(point.x, point.y, point.z);\n    wders[i] = point.w;\n  }\n  const CK = [];\n  for (let k = 0; k < nd; ++k) {\n    const v = Aders[k].clone();\n    for (let i = 1; i <= k; ++i) {\n      v.sub(CK[k - i].clone().multiplyScalar(calcKoverI(k, i) * wders[i]));\n    }\n    CK[k] = v.divideScalar(wders[0]);\n  }\n  return CK;\n}\nfunction calcNURBSDerivatives(p, U, P, u, nd) {\n  const Pders = calcBSplineDerivatives(p, U, P, u, nd);\n  return calcRationalCurveDerivatives(Pders);\n}\nfunction calcSurfacePoint(p, q, U, V, P, u, v, target) {\n  const uspan = findSpan(p, u, U);\n  const vspan = findSpan(q, v, V);\n  const Nu = calcBasisFunctions(uspan, u, p, U);\n  const Nv = calcBasisFunctions(vspan, v, q, V);\n  const temp = [];\n  for (let l = 0; l <= q; ++l) {\n    temp[l] = new Vector4(0, 0, 0, 0);\n    for (let k = 0; k <= p; ++k) {\n      const point = P[uspan - p + k][vspan - q + l].clone();\n      const w = point.w;\n      point.x *= w;\n      point.y *= w;\n      point.z *= w;\n      temp[l].add(point.multiplyScalar(Nu[k]));\n    }\n  }\n  const Sw = new Vector4(0, 0, 0, 0);\n  for (let l = 0; l <= q; ++l) {\n    Sw.add(temp[l].multiplyScalar(Nv[l]));\n  }\n  Sw.divideScalar(Sw.w);\n  target.set(Sw.x, Sw.y, Sw.z);\n}\nexport { calcBSplineDerivatives, calcBSplinePoint, calcBasisFunctionDerivatives, calcBasisFunctions, calcKoverI, calcNURBSDerivatives, calcRationalCurveDerivatives, calcSurfacePoint, findSpan };", "map": {"version": 3, "names": ["findSpan", "p", "u", "U", "n", "length", "low", "high", "mid", "Math", "floor", "calcBasisFunctions", "span", "N", "left", "right", "j", "saved", "r", "rv", "lv", "temp", "calcBSplinePoint", "P", "C", "Vector4", "point", "Nj", "wNj", "w", "x", "y", "z", "calcBasisFunctionDerivatives", "zeroArr", "i", "ders", "slice", "ndu", "r2", "s1", "s2", "a", "k", "d", "rk", "pk", "j1", "j2", "j3", "calcBSplineDerivatives", "nd", "du", "CK", "nders", "Pw", "clone", "multiplyScalar", "add", "calcKoverI", "nom", "denom", "calcRationalCurveDerivatives", "Pders", "<PERSON><PERSON>", "wders", "Vector3", "v", "sub", "divideScalar", "calcNURBSDerivatives", "calcSurfacePoint", "q", "V", "target", "uspan", "vspan", "<PERSON>u", "Nv", "l", "Sw", "set"], "sources": ["F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\node_modules\\src\\curves\\NURBSUtils.js"], "sourcesContent": ["import { Vector3, Vector4 } from 'three'\n\n/**\n * NURBS utils\n *\n * See NURBSCurve and NURBSSurface.\n **/\n\n/**************************************************************\n *\tNURBS Utils\n **************************************************************/\n\n/*\nFinds knot vector span.\n\np : degree\nu : parametric value\nU : knot vector\n\nreturns the span\n*/\nfunction findSpan(p, u, U) {\n  const n = U.length - p - 1\n\n  if (u >= U[n]) {\n    return n - 1\n  }\n\n  if (u <= U[p]) {\n    return p\n  }\n\n  let low = p\n  let high = n\n  let mid = Math.floor((low + high) / 2)\n\n  while (u < U[mid] || u >= U[mid + 1]) {\n    if (u < U[mid]) {\n      high = mid\n    } else {\n      low = mid\n    }\n\n    mid = Math.floor((low + high) / 2)\n  }\n\n  return mid\n}\n\n/*\nCalculate basis functions. See The NURBS Book, page 70, algorithm A2.2\n\nspan : span in which u lies\nu    : parametric point\np    : degree\nU    : knot vector\n\nreturns array[p+1] with basis functions values.\n*/\nfunction calcBasisFunctions(span, u, p, U) {\n  const N = []\n  const left = []\n  const right = []\n  N[0] = 1.0\n\n  for (let j = 1; j <= p; ++j) {\n    left[j] = u - U[span + 1 - j]\n    right[j] = U[span + j] - u\n\n    let saved = 0.0\n\n    for (let r = 0; r < j; ++r) {\n      const rv = right[r + 1]\n      const lv = left[j - r]\n      const temp = N[r] / (rv + lv)\n      N[r] = saved + rv * temp\n      saved = lv * temp\n    }\n\n    N[j] = saved\n  }\n\n  return N\n}\n\n/*\nCalculate B-Spline curve points. See The NURBS Book, page 82, algorithm A3.1.\n\np : degree of B-Spline\nU : knot vector\nP : control points (x, y, z, w)\nu : parametric point\n\nreturns point for given u\n*/\nfunction calcBSplinePoint(p, U, P, u) {\n  const span = findSpan(p, u, U)\n  const N = calcBasisFunctions(span, u, p, U)\n  const C = new Vector4(0, 0, 0, 0)\n\n  for (let j = 0; j <= p; ++j) {\n    const point = P[span - p + j]\n    const Nj = N[j]\n    const wNj = point.w * Nj\n    C.x += point.x * wNj\n    C.y += point.y * wNj\n    C.z += point.z * wNj\n    C.w += point.w * Nj\n  }\n\n  return C\n}\n\n/*\nCalculate basis functions derivatives. See The NURBS Book, page 72, algorithm A2.3.\n\nspan : span in which u lies\nu    : parametric point\np    : degree\nn    : number of derivatives to calculate\nU    : knot vector\n\nreturns array[n+1][p+1] with basis functions derivatives\n*/\nfunction calcBasisFunctionDerivatives(span, u, p, n, U) {\n  const zeroArr = []\n  for (let i = 0; i <= p; ++i) zeroArr[i] = 0.0\n\n  const ders = []\n\n  for (let i = 0; i <= n; ++i) ders[i] = zeroArr.slice(0)\n\n  const ndu = []\n\n  for (let i = 0; i <= p; ++i) ndu[i] = zeroArr.slice(0)\n\n  ndu[0][0] = 1.0\n\n  const left = zeroArr.slice(0)\n  const right = zeroArr.slice(0)\n\n  for (let j = 1; j <= p; ++j) {\n    left[j] = u - U[span + 1 - j]\n    right[j] = U[span + j] - u\n\n    let saved = 0.0\n\n    for (let r = 0; r < j; ++r) {\n      const rv = right[r + 1]\n      const lv = left[j - r]\n      ndu[j][r] = rv + lv\n\n      const temp = ndu[r][j - 1] / ndu[j][r]\n      ndu[r][j] = saved + rv * temp\n      saved = lv * temp\n    }\n\n    ndu[j][j] = saved\n  }\n\n  for (let j = 0; j <= p; ++j) {\n    ders[0][j] = ndu[j][p]\n  }\n\n  for (let r = 0; r <= p; ++r) {\n    let s1 = 0\n    let s2 = 1\n\n    const a = []\n    for (let i = 0; i <= p; ++i) {\n      a[i] = zeroArr.slice(0)\n    }\n\n    a[0][0] = 1.0\n\n    for (let k = 1; k <= n; ++k) {\n      let d = 0.0\n      const rk = r - k\n      const pk = p - k\n\n      if (r >= k) {\n        a[s2][0] = a[s1][0] / ndu[pk + 1][rk]\n        d = a[s2][0] * ndu[rk][pk]\n      }\n\n      const j1 = rk >= -1 ? 1 : -rk\n      const j2 = r - 1 <= pk ? k - 1 : p - r\n\n      for (let j = j1; j <= j2; ++j) {\n        a[s2][j] = (a[s1][j] - a[s1][j - 1]) / ndu[pk + 1][rk + j]\n        d += a[s2][j] * ndu[rk + j][pk]\n      }\n\n      if (r <= pk) {\n        a[s2][k] = -a[s1][k - 1] / ndu[pk + 1][r]\n        d += a[s2][k] * ndu[r][pk]\n      }\n\n      ders[k][r] = d\n\n      const j = s1\n      s1 = s2\n      s2 = j\n    }\n  }\n\n  let r = p\n\n  for (let k = 1; k <= n; ++k) {\n    for (let j = 0; j <= p; ++j) {\n      ders[k][j] *= r\n    }\n\n    r *= p - k\n  }\n\n  return ders\n}\n\n/*\n\tCalculate derivatives of a B-Spline. See The NURBS Book, page 93, algorithm A3.2.\n\n\tp  : degree\n\tU  : knot vector\n\tP  : control points\n\tu  : Parametric points\n\tnd : number of derivatives\n\n\treturns array[d+1] with derivatives\n\t*/\nfunction calcBSplineDerivatives(p, U, P, u, nd) {\n  const du = nd < p ? nd : p\n  const CK = []\n  const span = findSpan(p, u, U)\n  const nders = calcBasisFunctionDerivatives(span, u, p, du, U)\n  const Pw = []\n\n  for (let i = 0; i < P.length; ++i) {\n    const point = P[i].clone()\n    const w = point.w\n\n    point.x *= w\n    point.y *= w\n    point.z *= w\n\n    Pw[i] = point\n  }\n\n  for (let k = 0; k <= du; ++k) {\n    const point = Pw[span - p].clone().multiplyScalar(nders[k][0])\n\n    for (let j = 1; j <= p; ++j) {\n      point.add(Pw[span - p + j].clone().multiplyScalar(nders[k][j]))\n    }\n\n    CK[k] = point\n  }\n\n  for (let k = du + 1; k <= nd + 1; ++k) {\n    CK[k] = new Vector4(0, 0, 0)\n  }\n\n  return CK\n}\n\n/*\nCalculate \"K over I\"\n\nreturns k!/(i!(k-i)!)\n*/\nfunction calcKoverI(k, i) {\n  let nom = 1\n\n  for (let j = 2; j <= k; ++j) {\n    nom *= j\n  }\n\n  let denom = 1\n\n  for (let j = 2; j <= i; ++j) {\n    denom *= j\n  }\n\n  for (let j = 2; j <= k - i; ++j) {\n    denom *= j\n  }\n\n  return nom / denom\n}\n\n/*\nCalculate derivatives (0-nd) of rational curve. See The NURBS Book, page 127, algorithm A4.2.\n\nPders : result of function calcBSplineDerivatives\n\nreturns array with derivatives for rational curve.\n*/\nfunction calcRationalCurveDerivatives(Pders) {\n  const nd = Pders.length\n  const Aders = []\n  const wders = []\n\n  for (let i = 0; i < nd; ++i) {\n    const point = Pders[i]\n    Aders[i] = new Vector3(point.x, point.y, point.z)\n    wders[i] = point.w\n  }\n\n  const CK = []\n\n  for (let k = 0; k < nd; ++k) {\n    const v = Aders[k].clone()\n\n    for (let i = 1; i <= k; ++i) {\n      v.sub(CK[k - i].clone().multiplyScalar(calcKoverI(k, i) * wders[i]))\n    }\n\n    CK[k] = v.divideScalar(wders[0])\n  }\n\n  return CK\n}\n\n/*\nCalculate NURBS curve derivatives. See The NURBS Book, page 127, algorithm A4.2.\n\np  : degree\nU  : knot vector\nP  : control points in homogeneous space\nu  : parametric points\nnd : number of derivatives\n\nreturns array with derivatives.\n*/\nfunction calcNURBSDerivatives(p, U, P, u, nd) {\n  const Pders = calcBSplineDerivatives(p, U, P, u, nd)\n  return calcRationalCurveDerivatives(Pders)\n}\n\n/*\nCalculate rational B-Spline surface point. See The NURBS Book, page 134, algorithm A4.3.\n\np1, p2 : degrees of B-Spline surface\nU1, U2 : knot vectors\nP      : control points (x, y, z, w)\nu, v   : parametric values\n\nreturns point for given (u, v)\n*/\nfunction calcSurfacePoint(p, q, U, V, P, u, v, target) {\n  const uspan = findSpan(p, u, U)\n  const vspan = findSpan(q, v, V)\n  const Nu = calcBasisFunctions(uspan, u, p, U)\n  const Nv = calcBasisFunctions(vspan, v, q, V)\n  const temp = []\n\n  for (let l = 0; l <= q; ++l) {\n    temp[l] = new Vector4(0, 0, 0, 0)\n    for (let k = 0; k <= p; ++k) {\n      const point = P[uspan - p + k][vspan - q + l].clone()\n      const w = point.w\n      point.x *= w\n      point.y *= w\n      point.z *= w\n      temp[l].add(point.multiplyScalar(Nu[k]))\n    }\n  }\n\n  const Sw = new Vector4(0, 0, 0, 0)\n  for (let l = 0; l <= q; ++l) {\n    Sw.add(temp[l].multiplyScalar(Nv[l]))\n  }\n\n  Sw.divideScalar(Sw.w)\n  target.set(Sw.x, Sw.y, Sw.z)\n}\n\nexport {\n  findSpan,\n  calcBasisFunctions,\n  calcBSplinePoint,\n  calcBasisFunctionDerivatives,\n  calcBSplineDerivatives,\n  calcKoverI,\n  calcRationalCurveDerivatives,\n  calcNURBSDerivatives,\n  calcSurfacePoint,\n}\n"], "mappings": ";AAqBA,SAASA,SAASC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;EACzB,MAAMC,CAAA,GAAID,CAAA,CAAEE,MAAA,GAASJ,CAAA,GAAI;EAEzB,IAAIC,CAAA,IAAKC,CAAA,CAAEC,CAAC,GAAG;IACb,OAAOA,CAAA,GAAI;EACZ;EAED,IAAIF,CAAA,IAAKC,CAAA,CAAEF,CAAC,GAAG;IACb,OAAOA,CAAA;EACR;EAED,IAAIK,GAAA,GAAML,CAAA;EACV,IAAIM,IAAA,GAAOH,CAAA;EACX,IAAII,GAAA,GAAMC,IAAA,CAAKC,KAAA,EAAOJ,GAAA,GAAMC,IAAA,IAAQ,CAAC;EAErC,OAAOL,CAAA,GAAIC,CAAA,CAAEK,GAAG,KAAKN,CAAA,IAAKC,CAAA,CAAEK,GAAA,GAAM,CAAC,GAAG;IACpC,IAAIN,CAAA,GAAIC,CAAA,CAAEK,GAAG,GAAG;MACdD,IAAA,GAAOC,GAAA;IACb,OAAW;MACLF,GAAA,GAAME,GAAA;IACP;IAEDA,GAAA,GAAMC,IAAA,CAAKC,KAAA,EAAOJ,GAAA,GAAMC,IAAA,IAAQ,CAAC;EAClC;EAED,OAAOC,GAAA;AACT;AAYA,SAASG,mBAAmBC,IAAA,EAAMV,CAAA,EAAGD,CAAA,EAAGE,CAAA,EAAG;EACzC,MAAMU,CAAA,GAAI,EAAE;EACZ,MAAMC,IAAA,GAAO,EAAE;EACf,MAAMC,KAAA,GAAQ,EAAE;EAChBF,CAAA,CAAE,CAAC,IAAI;EAEP,SAASG,CAAA,GAAI,GAAGA,CAAA,IAAKf,CAAA,EAAG,EAAEe,CAAA,EAAG;IAC3BF,IAAA,CAAKE,CAAC,IAAId,CAAA,GAAIC,CAAA,CAAES,IAAA,GAAO,IAAII,CAAC;IAC5BD,KAAA,CAAMC,CAAC,IAAIb,CAAA,CAAES,IAAA,GAAOI,CAAC,IAAId,CAAA;IAEzB,IAAIe,KAAA,GAAQ;IAEZ,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIF,CAAA,EAAG,EAAEE,CAAA,EAAG;MAC1B,MAAMC,EAAA,GAAKJ,KAAA,CAAMG,CAAA,GAAI,CAAC;MACtB,MAAME,EAAA,GAAKN,IAAA,CAAKE,CAAA,GAAIE,CAAC;MACrB,MAAMG,IAAA,GAAOR,CAAA,CAAEK,CAAC,KAAKC,EAAA,GAAKC,EAAA;MAC1BP,CAAA,CAAEK,CAAC,IAAID,KAAA,GAAQE,EAAA,GAAKE,IAAA;MACpBJ,KAAA,GAAQG,EAAA,GAAKC,IAAA;IACd;IAEDR,CAAA,CAAEG,CAAC,IAAIC,KAAA;EACR;EAED,OAAOJ,CAAA;AACT;AAYA,SAASS,iBAAiBrB,CAAA,EAAGE,CAAA,EAAGoB,CAAA,EAAGrB,CAAA,EAAG;EACpC,MAAMU,IAAA,GAAOZ,QAAA,CAASC,CAAA,EAAGC,CAAA,EAAGC,CAAC;EAC7B,MAAMU,CAAA,GAAIF,kBAAA,CAAmBC,IAAA,EAAMV,CAAA,EAAGD,CAAA,EAAGE,CAAC;EAC1C,MAAMqB,CAAA,GAAI,IAAIC,OAAA,CAAQ,GAAG,GAAG,GAAG,CAAC;EAEhC,SAAST,CAAA,GAAI,GAAGA,CAAA,IAAKf,CAAA,EAAG,EAAEe,CAAA,EAAG;IAC3B,MAAMU,KAAA,GAAQH,CAAA,CAAEX,IAAA,GAAOX,CAAA,GAAIe,CAAC;IAC5B,MAAMW,EAAA,GAAKd,CAAA,CAAEG,CAAC;IACd,MAAMY,GAAA,GAAMF,KAAA,CAAMG,CAAA,GAAIF,EAAA;IACtBH,CAAA,CAAEM,CAAA,IAAKJ,KAAA,CAAMI,CAAA,GAAIF,GAAA;IACjBJ,CAAA,CAAEO,CAAA,IAAKL,KAAA,CAAMK,CAAA,GAAIH,GAAA;IACjBJ,CAAA,CAAEQ,CAAA,IAAKN,KAAA,CAAMM,CAAA,GAAIJ,GAAA;IACjBJ,CAAA,CAAEK,CAAA,IAAKH,KAAA,CAAMG,CAAA,GAAIF,EAAA;EAClB;EAED,OAAOH,CAAA;AACT;AAaA,SAASS,6BAA6BrB,IAAA,EAAMV,CAAA,EAAGD,CAAA,EAAGG,CAAA,EAAGD,CAAA,EAAG;EACtD,MAAM+B,OAAA,GAAU,EAAE;EAClB,SAASC,CAAA,GAAI,GAAGA,CAAA,IAAKlC,CAAA,EAAG,EAAEkC,CAAA,EAAGD,OAAA,CAAQC,CAAC,IAAI;EAE1C,MAAMC,IAAA,GAAO,EAAE;EAEf,SAASD,CAAA,GAAI,GAAGA,CAAA,IAAK/B,CAAA,EAAG,EAAE+B,CAAA,EAAGC,IAAA,CAAKD,CAAC,IAAID,OAAA,CAAQG,KAAA,CAAM,CAAC;EAEtD,MAAMC,GAAA,GAAM,EAAE;EAEd,SAASH,CAAA,GAAI,GAAGA,CAAA,IAAKlC,CAAA,EAAG,EAAEkC,CAAA,EAAGG,GAAA,CAAIH,CAAC,IAAID,OAAA,CAAQG,KAAA,CAAM,CAAC;EAErDC,GAAA,CAAI,CAAC,EAAE,CAAC,IAAI;EAEZ,MAAMxB,IAAA,GAAOoB,OAAA,CAAQG,KAAA,CAAM,CAAC;EAC5B,MAAMtB,KAAA,GAAQmB,OAAA,CAAQG,KAAA,CAAM,CAAC;EAE7B,SAASrB,CAAA,GAAI,GAAGA,CAAA,IAAKf,CAAA,EAAG,EAAEe,CAAA,EAAG;IAC3BF,IAAA,CAAKE,CAAC,IAAId,CAAA,GAAIC,CAAA,CAAES,IAAA,GAAO,IAAII,CAAC;IAC5BD,KAAA,CAAMC,CAAC,IAAIb,CAAA,CAAES,IAAA,GAAOI,CAAC,IAAId,CAAA;IAEzB,IAAIe,KAAA,GAAQ;IAEZ,SAASsB,EAAA,GAAI,GAAGA,EAAA,GAAIvB,CAAA,EAAG,EAAEuB,EAAA,EAAG;MAC1B,MAAMpB,EAAA,GAAKJ,KAAA,CAAMwB,EAAA,GAAI,CAAC;MACtB,MAAMnB,EAAA,GAAKN,IAAA,CAAKE,CAAA,GAAIuB,EAAC;MACrBD,GAAA,CAAItB,CAAC,EAAEuB,EAAC,IAAIpB,EAAA,GAAKC,EAAA;MAEjB,MAAMC,IAAA,GAAOiB,GAAA,CAAIC,EAAC,EAAEvB,CAAA,GAAI,CAAC,IAAIsB,GAAA,CAAItB,CAAC,EAAEuB,EAAC;MACrCD,GAAA,CAAIC,EAAC,EAAEvB,CAAC,IAAIC,KAAA,GAAQE,EAAA,GAAKE,IAAA;MACzBJ,KAAA,GAAQG,EAAA,GAAKC,IAAA;IACd;IAEDiB,GAAA,CAAItB,CAAC,EAAEA,CAAC,IAAIC,KAAA;EACb;EAED,SAASD,CAAA,GAAI,GAAGA,CAAA,IAAKf,CAAA,EAAG,EAAEe,CAAA,EAAG;IAC3BoB,IAAA,CAAK,CAAC,EAAEpB,CAAC,IAAIsB,GAAA,CAAItB,CAAC,EAAEf,CAAC;EACtB;EAED,SAASsC,EAAA,GAAI,GAAGA,EAAA,IAAKtC,CAAA,EAAG,EAAEsC,EAAA,EAAG;IAC3B,IAAIC,EAAA,GAAK;IACT,IAAIC,EAAA,GAAK;IAET,MAAMC,CAAA,GAAI,EAAE;IACZ,SAASP,CAAA,GAAI,GAAGA,CAAA,IAAKlC,CAAA,EAAG,EAAEkC,CAAA,EAAG;MAC3BO,CAAA,CAAEP,CAAC,IAAID,OAAA,CAAQG,KAAA,CAAM,CAAC;IACvB;IAEDK,CAAA,CAAE,CAAC,EAAE,CAAC,IAAI;IAEV,SAASC,CAAA,GAAI,GAAGA,CAAA,IAAKvC,CAAA,EAAG,EAAEuC,CAAA,EAAG;MAC3B,IAAIC,CAAA,GAAI;MACR,MAAMC,EAAA,GAAKN,EAAA,GAAII,CAAA;MACf,MAAMG,EAAA,GAAK7C,CAAA,GAAI0C,CAAA;MAEf,IAAIJ,EAAA,IAAKI,CAAA,EAAG;QACVD,CAAA,CAAED,EAAE,EAAE,CAAC,IAAIC,CAAA,CAAEF,EAAE,EAAE,CAAC,IAAIF,GAAA,CAAIQ,EAAA,GAAK,CAAC,EAAED,EAAE;QACpCD,CAAA,GAAIF,CAAA,CAAED,EAAE,EAAE,CAAC,IAAIH,GAAA,CAAIO,EAAE,EAAEC,EAAE;MAC1B;MAED,MAAMC,EAAA,GAAKF,EAAA,IAAM,KAAK,IAAI,CAACA,EAAA;MAC3B,MAAMG,EAAA,GAAKT,EAAA,GAAI,KAAKO,EAAA,GAAKH,CAAA,GAAI,IAAI1C,CAAA,GAAIsC,EAAA;MAErC,SAASU,EAAA,GAAIF,EAAA,EAAIE,EAAA,IAAKD,EAAA,EAAI,EAAEC,EAAA,EAAG;QAC7BP,CAAA,CAAED,EAAE,EAAEQ,EAAC,KAAKP,CAAA,CAAEF,EAAE,EAAES,EAAC,IAAIP,CAAA,CAAEF,EAAE,EAAES,EAAA,GAAI,CAAC,KAAKX,GAAA,CAAIQ,EAAA,GAAK,CAAC,EAAED,EAAA,GAAKI,EAAC;QACzDL,CAAA,IAAKF,CAAA,CAAED,EAAE,EAAEQ,EAAC,IAAIX,GAAA,CAAIO,EAAA,GAAKI,EAAC,EAAEH,EAAE;MAC/B;MAED,IAAIP,EAAA,IAAKO,EAAA,EAAI;QACXJ,CAAA,CAAED,EAAE,EAAEE,CAAC,IAAI,CAACD,CAAA,CAAEF,EAAE,EAAEG,CAAA,GAAI,CAAC,IAAIL,GAAA,CAAIQ,EAAA,GAAK,CAAC,EAAEP,EAAC;QACxCK,CAAA,IAAKF,CAAA,CAAED,EAAE,EAAEE,CAAC,IAAIL,GAAA,CAAIC,EAAC,EAAEO,EAAE;MAC1B;MAEDV,IAAA,CAAKO,CAAC,EAAEJ,EAAC,IAAIK,CAAA;MAEb,MAAM5B,CAAA,GAAIwB,EAAA;MACVA,EAAA,GAAKC,EAAA;MACLA,EAAA,GAAKzB,CAAA;IACN;EACF;EAED,IAAIE,CAAA,GAAIjB,CAAA;EAER,SAAS0C,CAAA,GAAI,GAAGA,CAAA,IAAKvC,CAAA,EAAG,EAAEuC,CAAA,EAAG;IAC3B,SAAS3B,CAAA,GAAI,GAAGA,CAAA,IAAKf,CAAA,EAAG,EAAEe,CAAA,EAAG;MAC3BoB,IAAA,CAAKO,CAAC,EAAE3B,CAAC,KAAKE,CAAA;IACf;IAEDA,CAAA,IAAKjB,CAAA,GAAI0C,CAAA;EACV;EAED,OAAOP,IAAA;AACT;AAaA,SAASc,uBAAuBjD,CAAA,EAAGE,CAAA,EAAGoB,CAAA,EAAGrB,CAAA,EAAGiD,EAAA,EAAI;EAC9C,MAAMC,EAAA,GAAKD,EAAA,GAAKlD,CAAA,GAAIkD,EAAA,GAAKlD,CAAA;EACzB,MAAMoD,EAAA,GAAK,EAAE;EACb,MAAMzC,IAAA,GAAOZ,QAAA,CAASC,CAAA,EAAGC,CAAA,EAAGC,CAAC;EAC7B,MAAMmD,KAAA,GAAQrB,4BAAA,CAA6BrB,IAAA,EAAMV,CAAA,EAAGD,CAAA,EAAGmD,EAAA,EAAIjD,CAAC;EAC5D,MAAMoD,EAAA,GAAK,EAAE;EAEb,SAASpB,CAAA,GAAI,GAAGA,CAAA,GAAIZ,CAAA,CAAElB,MAAA,EAAQ,EAAE8B,CAAA,EAAG;IACjC,MAAMT,KAAA,GAAQH,CAAA,CAAEY,CAAC,EAAEqB,KAAA,CAAO;IAC1B,MAAM3B,CAAA,GAAIH,KAAA,CAAMG,CAAA;IAEhBH,KAAA,CAAMI,CAAA,IAAKD,CAAA;IACXH,KAAA,CAAMK,CAAA,IAAKF,CAAA;IACXH,KAAA,CAAMM,CAAA,IAAKH,CAAA;IAEX0B,EAAA,CAAGpB,CAAC,IAAIT,KAAA;EACT;EAED,SAASiB,CAAA,GAAI,GAAGA,CAAA,IAAKS,EAAA,EAAI,EAAET,CAAA,EAAG;IAC5B,MAAMjB,KAAA,GAAQ6B,EAAA,CAAG3C,IAAA,GAAOX,CAAC,EAAEuD,KAAA,GAAQC,cAAA,CAAeH,KAAA,CAAMX,CAAC,EAAE,CAAC,CAAC;IAE7D,SAAS3B,CAAA,GAAI,GAAGA,CAAA,IAAKf,CAAA,EAAG,EAAEe,CAAA,EAAG;MAC3BU,KAAA,CAAMgC,GAAA,CAAIH,EAAA,CAAG3C,IAAA,GAAOX,CAAA,GAAIe,CAAC,EAAEwC,KAAA,CAAO,EAACC,cAAA,CAAeH,KAAA,CAAMX,CAAC,EAAE3B,CAAC,CAAC,CAAC;IAC/D;IAEDqC,EAAA,CAAGV,CAAC,IAAIjB,KAAA;EACT;EAED,SAASiB,CAAA,GAAIS,EAAA,GAAK,GAAGT,CAAA,IAAKQ,EAAA,GAAK,GAAG,EAAER,CAAA,EAAG;IACrCU,EAAA,CAAGV,CAAC,IAAI,IAAIlB,OAAA,CAAQ,GAAG,GAAG,CAAC;EAC5B;EAED,OAAO4B,EAAA;AACT;AAOA,SAASM,WAAWhB,CAAA,EAAGR,CAAA,EAAG;EACxB,IAAIyB,GAAA,GAAM;EAEV,SAAS5C,CAAA,GAAI,GAAGA,CAAA,IAAK2B,CAAA,EAAG,EAAE3B,CAAA,EAAG;IAC3B4C,GAAA,IAAO5C,CAAA;EACR;EAED,IAAI6C,KAAA,GAAQ;EAEZ,SAAS7C,CAAA,GAAI,GAAGA,CAAA,IAAKmB,CAAA,EAAG,EAAEnB,CAAA,EAAG;IAC3B6C,KAAA,IAAS7C,CAAA;EACV;EAED,SAASA,CAAA,GAAI,GAAGA,CAAA,IAAK2B,CAAA,GAAIR,CAAA,EAAG,EAAEnB,CAAA,EAAG;IAC/B6C,KAAA,IAAS7C,CAAA;EACV;EAED,OAAO4C,GAAA,GAAMC,KAAA;AACf;AASA,SAASC,6BAA6BC,KAAA,EAAO;EAC3C,MAAMZ,EAAA,GAAKY,KAAA,CAAM1D,MAAA;EACjB,MAAM2D,KAAA,GAAQ,EAAE;EAChB,MAAMC,KAAA,GAAQ,EAAE;EAEhB,SAAS9B,CAAA,GAAI,GAAGA,CAAA,GAAIgB,EAAA,EAAI,EAAEhB,CAAA,EAAG;IAC3B,MAAMT,KAAA,GAAQqC,KAAA,CAAM5B,CAAC;IACrB6B,KAAA,CAAM7B,CAAC,IAAI,IAAI+B,OAAA,CAAQxC,KAAA,CAAMI,CAAA,EAAGJ,KAAA,CAAMK,CAAA,EAAGL,KAAA,CAAMM,CAAC;IAChDiC,KAAA,CAAM9B,CAAC,IAAIT,KAAA,CAAMG,CAAA;EAClB;EAED,MAAMwB,EAAA,GAAK,EAAE;EAEb,SAASV,CAAA,GAAI,GAAGA,CAAA,GAAIQ,EAAA,EAAI,EAAER,CAAA,EAAG;IAC3B,MAAMwB,CAAA,GAAIH,KAAA,CAAMrB,CAAC,EAAEa,KAAA,CAAO;IAE1B,SAASrB,CAAA,GAAI,GAAGA,CAAA,IAAKQ,CAAA,EAAG,EAAER,CAAA,EAAG;MAC3BgC,CAAA,CAAEC,GAAA,CAAIf,EAAA,CAAGV,CAAA,GAAIR,CAAC,EAAEqB,KAAA,CAAO,EAACC,cAAA,CAAeE,UAAA,CAAWhB,CAAA,EAAGR,CAAC,IAAI8B,KAAA,CAAM9B,CAAC,CAAC,CAAC;IACpE;IAEDkB,EAAA,CAAGV,CAAC,IAAIwB,CAAA,CAAEE,YAAA,CAAaJ,KAAA,CAAM,CAAC,CAAC;EAChC;EAED,OAAOZ,EAAA;AACT;AAaA,SAASiB,qBAAqBrE,CAAA,EAAGE,CAAA,EAAGoB,CAAA,EAAGrB,CAAA,EAAGiD,EAAA,EAAI;EAC5C,MAAMY,KAAA,GAAQb,sBAAA,CAAuBjD,CAAA,EAAGE,CAAA,EAAGoB,CAAA,EAAGrB,CAAA,EAAGiD,EAAE;EACnD,OAAOW,4BAAA,CAA6BC,KAAK;AAC3C;AAYA,SAASQ,iBAAiBtE,CAAA,EAAGuE,CAAA,EAAGrE,CAAA,EAAGsE,CAAA,EAAGlD,CAAA,EAAGrB,CAAA,EAAGiE,CAAA,EAAGO,MAAA,EAAQ;EACrD,MAAMC,KAAA,GAAQ3E,QAAA,CAASC,CAAA,EAAGC,CAAA,EAAGC,CAAC;EAC9B,MAAMyE,KAAA,GAAQ5E,QAAA,CAASwE,CAAA,EAAGL,CAAA,EAAGM,CAAC;EAC9B,MAAMI,EAAA,GAAKlE,kBAAA,CAAmBgE,KAAA,EAAOzE,CAAA,EAAGD,CAAA,EAAGE,CAAC;EAC5C,MAAM2E,EAAA,GAAKnE,kBAAA,CAAmBiE,KAAA,EAAOT,CAAA,EAAGK,CAAA,EAAGC,CAAC;EAC5C,MAAMpD,IAAA,GAAO,EAAE;EAEf,SAAS0D,CAAA,GAAI,GAAGA,CAAA,IAAKP,CAAA,EAAG,EAAEO,CAAA,EAAG;IAC3B1D,IAAA,CAAK0D,CAAC,IAAI,IAAItD,OAAA,CAAQ,GAAG,GAAG,GAAG,CAAC;IAChC,SAASkB,CAAA,GAAI,GAAGA,CAAA,IAAK1C,CAAA,EAAG,EAAE0C,CAAA,EAAG;MAC3B,MAAMjB,KAAA,GAAQH,CAAA,CAAEoD,KAAA,GAAQ1E,CAAA,GAAI0C,CAAC,EAAEiC,KAAA,GAAQJ,CAAA,GAAIO,CAAC,EAAEvB,KAAA,CAAO;MACrD,MAAM3B,CAAA,GAAIH,KAAA,CAAMG,CAAA;MAChBH,KAAA,CAAMI,CAAA,IAAKD,CAAA;MACXH,KAAA,CAAMK,CAAA,IAAKF,CAAA;MACXH,KAAA,CAAMM,CAAA,IAAKH,CAAA;MACXR,IAAA,CAAK0D,CAAC,EAAErB,GAAA,CAAIhC,KAAA,CAAM+B,cAAA,CAAeoB,EAAA,CAAGlC,CAAC,CAAC,CAAC;IACxC;EACF;EAED,MAAMqC,EAAA,GAAK,IAAIvD,OAAA,CAAQ,GAAG,GAAG,GAAG,CAAC;EACjC,SAASsD,CAAA,GAAI,GAAGA,CAAA,IAAKP,CAAA,EAAG,EAAEO,CAAA,EAAG;IAC3BC,EAAA,CAAGtB,GAAA,CAAIrC,IAAA,CAAK0D,CAAC,EAAEtB,cAAA,CAAeqB,EAAA,CAAGC,CAAC,CAAC,CAAC;EACrC;EAEDC,EAAA,CAAGX,YAAA,CAAaW,EAAA,CAAGnD,CAAC;EACpB6C,MAAA,CAAOO,GAAA,CAAID,EAAA,CAAGlD,CAAA,EAAGkD,EAAA,CAAGjD,CAAA,EAAGiD,EAAA,CAAGhD,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}