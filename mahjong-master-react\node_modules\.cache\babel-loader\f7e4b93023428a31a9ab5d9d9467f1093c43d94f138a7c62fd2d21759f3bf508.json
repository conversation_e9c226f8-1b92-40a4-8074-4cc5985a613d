{"ast": null, "code": "export class MeshBVHNode {\n  constructor() {\n    // internal nodes have boundingData, left, right, and splitAxis\n    // leaf nodes have offset and count (referring to primitives in the mesh geometry)\n\n    this.boundingData = new Float32Array(6);\n  }\n}", "map": {"version": 3, "names": ["MeshBVHNode", "constructor", "boundingData", "Float32Array"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/three-mesh-bvh/src/core/MeshBVHNode.js"], "sourcesContent": ["export class MeshBVHNode {\n\n\tconstructor() {\n\n\t\t// internal nodes have boundingData, left, right, and splitAxis\n\t\t// leaf nodes have offset and count (referring to primitives in the mesh geometry)\n\n\t\tthis.boundingData = new Float32Array( 6 );\n\n\t}\n\n}\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,CAAC;EAExBC,WAAWA,CAAA,EAAG;IAEb;IACA;;IAEA,IAAI,CAACC,YAAY,GAAG,IAAIC,YAAY,CAAE,CAAE,CAAC;EAE1C;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}