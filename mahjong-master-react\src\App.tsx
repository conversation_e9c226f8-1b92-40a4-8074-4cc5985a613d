import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// 简化的麻将牌组件
interface MahjongTileProps {
  id: string;
  type: string;
  value: string | number;
  isSelected: boolean;
  onClick: () => void;
}

function MahjongTile({ id, type, value, isSelected, onClick }: MahjongTileProps) {
  const getTileColor = () => {
    switch (type) {
      case 'character': return 'bg-red-100 border-red-300 text-red-800';
      case 'bamboo': return 'bg-green-100 border-green-300 text-green-800';
      case 'dot': return 'bg-blue-100 border-blue-300 text-blue-800';
      case 'wind': return 'bg-yellow-100 border-yellow-300 text-yellow-800';
      case 'dragon': return 'bg-purple-100 border-purple-300 text-purple-800';
      default: return 'bg-gray-100 border-gray-300 text-gray-800';
    }
  };

  return (
    <motion.div
      className={`
        w-12 h-16 rounded-lg border-2 cursor-pointer flex items-center justify-center font-bold text-sm
        transition-all duration-200 hover:scale-105 hover:shadow-lg
        ${getTileColor()}
        ${isSelected ? 'ring-4 ring-yellow-400 transform -translate-y-2' : ''}
      `}
      onClick={onClick}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      animate={isSelected ? { y: -8 } : { y: 0 }}
    >
      {value}
    </motion.div>
  );
}

function App() {
  const [showWelcome, setShowWelcome] = useState(true);
  const [selectedTiles, setSelectedTiles] = useState<string[]>([]);

  // 生成示例麻将牌
  const [tiles] = useState(() => {
    const tileTypes = ['character', 'bamboo', 'dot', 'wind', 'dragon'];
    return Array.from({ length: 13 }, (_, i) => ({
      id: `tile-${i}`,
      type: tileTypes[Math.floor(Math.random() * tileTypes.length)],
      value: Math.floor(Math.random() * 9) + 1,
      isSelected: false
    }));
  });

  const handleTileClick = (tileId: string) => {
    setSelectedTiles(prev => {
      if (prev.includes(tileId)) {
        return prev.filter(id => id !== tileId);
      } else {
        return [...prev, tileId];
      }
    });
  };

  const handleAction = (action: string) => {
    console.log(`执行动作: ${action}，选中的牌: ${selectedTiles}`);
    setSelectedTiles([]);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 opacity-20">
        <div className="w-full h-full bg-gradient-to-br from-yellow-500/10 to-green-500/10"></div>
      </div>

      {/* 欢迎界面 */}
      <AnimatePresence>
        {showWelcome && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80"
          >
            <motion.div
              initial={{ scale: 0.8, y: 50 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.8, y: -50 }}
              className="text-center text-white"
            >
              <motion.h1
                className="text-6xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent"
                animate={{
                  filter: [
                    "drop-shadow(0 0 20px rgba(245, 158, 11, 0.5))",
                    "drop-shadow(0 0 40px rgba(245, 158, 11, 0.8))",
                    "drop-shadow(0 0 20px rgba(245, 158, 11, 0.5))"
                  ]
                }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                神灯麻将大师
              </motion.h1>
              <motion.p
                className="text-xl mb-8 text-gray-300"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
              >
                超越传统，重新定义麻将体验
              </motion.p>
              <motion.button
                className="bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white font-bold py-3 px-8 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 text-lg"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setShowWelcome(false)}
              >
                开始游戏
              </motion.button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 主游戏界面 */}
      {!showWelcome && (
        <>
          {/* 顶部状态栏 */}
          <motion.div
            initial={{ y: -100 }}
            animate={{ y: 0 }}
            className="absolute top-0 left-0 right-0 z-40 bg-black bg-opacity-50 backdrop-blur-sm p-4"
          >
            <div className="flex justify-between items-center max-w-7xl mx-auto">
              <div className="flex items-center space-x-4">
                <h2 className="text-2xl font-bold text-yellow-400">神灯麻将大师</h2>
                <div className="flex items-center space-x-2 text-white">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <span>在线</span>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="text-white">
                  <span className="text-gray-400">当前局数:</span>
                  <span className="ml-2 text-yellow-400 font-bold">第1局</span>
                </div>
                <div className="text-white">
                  <span className="text-gray-400">风圈:</span>
                  <span className="ml-2 text-green-400 font-bold">东风</span>
                </div>
              </div>
            </div>
          </motion.div>

          {/* 游戏区域 */}
          <div className="flex flex-col items-center justify-center h-screen pt-20 pb-20">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-green-800 rounded-3xl p-8 shadow-2xl"
            >
              <h3 className="text-white text-xl font-bold mb-6 text-center">您的手牌</h3>
              <div className="flex gap-2 flex-wrap justify-center">
                {tiles.map((tile) => (
                  <MahjongTile
                    key={tile.id}
                    id={tile.id}
                    type={tile.type}
                    value={tile.value}
                    isSelected={selectedTiles.includes(tile.id)}
                    onClick={() => handleTileClick(tile.id)}
                  />
                ))}
              </div>
            </motion.div>
          </div>

          {/* 底部操作栏 */}
          <AnimatePresence>
            {selectedTiles.length > 0 && (
              <motion.div
                initial={{ y: 100, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                exit={{ y: 100, opacity: 0 }}
                className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-40"
              >
                <div className="bg-black bg-opacity-70 backdrop-blur-sm rounded-2xl p-4 flex space-x-3">
                  <motion.button
                    className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleAction('出牌')}
                  >
                    出牌
                  </motion.button>
                  <motion.button
                    className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleAction('吃')}
                  >
                    吃
                  </motion.button>
                  <motion.button
                    className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleAction('碰')}
                  >
                    碰
                  </motion.button>
                  <motion.button
                    className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleAction('杠')}
                  >
                    杠
                  </motion.button>
                  <motion.button
                    className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-bold py-2 px-6 rounded-lg shadow-lg transform hover:scale-105 transition-all duration-150"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleAction('胡牌')}
                  >
                    胡牌
                  </motion.button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </>
      )}
    </div>
  );
}

export default App;
