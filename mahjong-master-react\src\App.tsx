import React, { useState } from 'react';

// 简化的麻将牌组件
interface MahjongTileProps {
  id: string;
  type: string;
  value: string | number;
  isSelected: boolean;
  onClick: () => void;
}

function MahjongTile({ id, type, value, isSelected, onClick }: MahjongTileProps) {
  const getTileColor = () => {
    switch (type) {
      case 'character': return 'bg-red-100 border-red-300 text-red-800';
      case 'bamboo': return 'bg-green-100 border-green-300 text-green-800';
      case 'dot': return 'bg-blue-100 border-blue-300 text-blue-800';
      case 'wind': return 'bg-yellow-100 border-yellow-300 text-yellow-800';
      case 'dragon': return 'bg-purple-100 border-purple-300 text-purple-800';
      default: return 'bg-gray-100 border-gray-300 text-gray-800';
    }
  };

  const handleClick = () => {
    console.log('MahjongTile clicked:', id);
    alert('点击了牌: ' + id + ' - 值: ' + value);
    onClick();
  };

  return (
    <div
      className={'mahjong-tile ' + getTileColor() + (isSelected ? ' selected' : '')}
      onClick={handleClick}
      style={{
        width: '48px',
        height: '64px',
        borderRadius: '8px',
        border: '2px solid #ccc',
        cursor: 'pointer',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontWeight: 'bold',
        fontSize: '14px',
        userSelect: 'none',
        WebkitUserSelect: 'none',
        MozUserSelect: 'none',
        msUserSelect: 'none'
      }}
    >
      {value}
    </div>
  );
}

function App() {
  const [showWelcome, setShowWelcome] = useState(false); // 直接进入游戏界面进行测试
  const [selectedTiles, setSelectedTiles] = useState<string[]>([]);

  // 生成示例麻将牌
  const [tiles] = useState(() => {
    const tileTypes = ['character', 'bamboo', 'dot', 'wind', 'dragon'];
    return Array.from({ length: 13 }, (_, i) => ({
      id: `tile-${i}`,
      type: tileTypes[Math.floor(Math.random() * tileTypes.length)],
      value: Math.floor(Math.random() * 9) + 1,
      isSelected: false
    }));
  });

  const handleTileClick = (tileId: string) => {
    console.log('点击了麻将牌:', tileId); // 添加调试信息
    setSelectedTiles(prev => {
      const newSelection = prev.includes(tileId)
        ? prev.filter(id => id !== tileId)
        : [...prev, tileId];
      console.log('选中的牌:', newSelection); // 添加调试信息
      return newSelection;
    });
  };

  const handleAction = (action: string) => {
    console.log(`执行动作: ${action}，选中的牌: ${selectedTiles}`);
    setSelectedTiles([]);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 opacity-20">
        <div className="w-full h-full bg-gradient-to-br from-yellow-500/10 to-green-500/10"></div>
      </div>

      {/* 欢迎界面 */}
      {showWelcome && (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80">
          <div className="text-center text-white">
            <h1 className="text-6xl font-bold mb-4 text-yellow-400">
              神灯麻将大师
            </h1>
            <p className="text-xl mb-8 text-gray-300">
              超越传统，重新定义麻将体验
            </p>
            <button
              className="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-3 px-8 rounded-xl text-lg"
              onClick={() => setShowWelcome(false)}
            >
              开始游戏
            </button>
          </div>
        </div>
      )}

      {/* 主游戏界面 */}
      {!showWelcome && (
        <>
          {/* 顶部状态栏 */}
          <div className="absolute top-0 left-0 right-0 z-40 bg-black bg-opacity-50 p-4">
            <div className="flex justify-between items-center max-w-7xl mx-auto">
              <div className="flex items-center space-x-4">
                <h2 className="text-2xl font-bold text-yellow-400">神灯麻将大师</h2>
                <div className="flex items-center space-x-2 text-white">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <span>在线</span>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="text-white">
                  <span className="text-gray-400">当前局数:</span>
                  <span className="ml-2 text-yellow-400 font-bold">第1局</span>
                </div>
                <div className="text-white">
                  <span className="text-gray-400">风圈:</span>
                  <span className="ml-2 text-green-400 font-bold">东风</span>
                </div>
              </div>
            </div>
          </div>

          {/* 游戏区域 */}
          <div className="flex flex-col items-center justify-center h-screen pt-20 pb-20">
            <div className="bg-green-800 rounded-3xl p-8 shadow-2xl">
              <h3 className="text-white text-xl font-bold mb-6 text-center">您的手牌</h3>

              {/* 测试按钮 */}
              <div className="mb-4 text-center">
                <button
                  onClick={() => alert('测试按钮工作正常！')}
                  className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
                >
                  测试点击
                </button>
              </div>

              <div className="flex gap-2 flex-wrap justify-center">
                {tiles.map((tile) => (
                  <MahjongTile
                    key={tile.id}
                    id={tile.id}
                    type={tile.type}
                    value={tile.value}
                    isSelected={selectedTiles.includes(tile.id)}
                    onClick={() => handleTileClick(tile.id)}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* 底部操作栏 */}
          {selectedTiles.length > 0 && (
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-40">
              <div className="bg-black bg-opacity-70 rounded-2xl p-4 flex space-x-3">
                <button
                  className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg"
                  onClick={() => handleAction('出牌')}
                >
                  出牌
                </button>
                <button
                  className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg"
                  onClick={() => handleAction('吃')}
                >
                  吃
                </button>
                <button
                  className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg"
                  onClick={() => handleAction('碰')}
                >
                  碰
                </button>
                <button
                  className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg"
                  onClick={() => handleAction('杠')}
                >
                  杠
                </button>
                <button
                  className="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-6 rounded-lg"
                  onClick={() => handleAction('胡牌')}
                >
                  胡牌
                </button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}

export default App;
