{"ast": null, "code": "import { color } from '../color/index.mjs';\nimport { colorRegex } from '../utils/color-regex.mjs';\nimport { floatRegex } from '../utils/float-regex.mjs';\nimport { sanitize } from '../utils/sanitize.mjs';\nfunction test(v) {\n  var _v$match, _v$match2;\n  return isNaN(v) && typeof v === \"string\" && (((_v$match = v.match(floatRegex)) === null || _v$match === void 0 ? void 0 : _v$match.length) || 0) + (((_v$match2 = v.match(colorRegex)) === null || _v$match2 === void 0 ? void 0 : _v$match2.length) || 0) > 0;\n}\nconst NUMBER_TOKEN = \"number\";\nconst COLOR_TOKEN = \"color\";\nconst VAR_TOKEN = \"var\";\nconst VAR_FUNCTION_TOKEN = \"var(\";\nconst SPLIT_TOKEN = \"${}\";\n// this regex consists of the `singleCssVariableRegex|rgbHSLValueRegex|digitRegex`\nconst complexRegex = /var[\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*\\([\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*--(?:[\\x2D0-9A-Z_a-z\\u017F\\u212A]+[\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*|[\\x2D0-9A-Z_a-z\\u017F\\u212A]+[\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*,(?:[\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*(?:(?![\\t-\\r \\(\\)\\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uD800-\\uDFFF\\uFEFF])[^]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF])|[\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*\\((?:(?:(?![\\(\\)\\uD800-\\uDFFF])[^]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF])|\\((?:(?![\\(\\)\\uD800-\\uDFFF])[^]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF])*\\))*\\))+[\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*)\\)|#[0-9a-f]{3,8}|(?:rgb|h[s\\u017F]l)a?\\((?:-?[\\.0-9]+%?[\\t-\\r ,\\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]+){2}-?[\\.0-9]+%?[\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*(?:[,\\/][\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*)?(?:\\b[0-9]+(?:\\.[0-9]+)?|\\.[0-9]+)?%?\\)|-?(?:[0-9]+(?:\\.[0-9]+)?|\\.[0-9]+)/gi;\nfunction analyseComplexValue(value) {\n  const originalValue = value.toString();\n  const values = [];\n  const indexes = {\n    color: [],\n    number: [],\n    var: []\n  };\n  const types = [];\n  let i = 0;\n  const tokenised = originalValue.replace(complexRegex, parsedValue => {\n    if (color.test(parsedValue)) {\n      indexes.color.push(i);\n      types.push(COLOR_TOKEN);\n      values.push(color.parse(parsedValue));\n    } else if (parsedValue.startsWith(VAR_FUNCTION_TOKEN)) {\n      indexes.var.push(i);\n      types.push(VAR_TOKEN);\n      values.push(parsedValue);\n    } else {\n      indexes.number.push(i);\n      types.push(NUMBER_TOKEN);\n      values.push(parseFloat(parsedValue));\n    }\n    ++i;\n    return SPLIT_TOKEN;\n  });\n  const split = tokenised.split(SPLIT_TOKEN);\n  return {\n    values,\n    split,\n    indexes,\n    types\n  };\n}\nfunction parseComplexValue(v) {\n  return analyseComplexValue(v).values;\n}\nfunction createTransformer(source) {\n  const {\n    split,\n    types\n  } = analyseComplexValue(source);\n  const numSections = split.length;\n  return v => {\n    let output = \"\";\n    for (let i = 0; i < numSections; i++) {\n      output += split[i];\n      if (v[i] !== undefined) {\n        const type = types[i];\n        if (type === NUMBER_TOKEN) {\n          output += sanitize(v[i]);\n        } else if (type === COLOR_TOKEN) {\n          output += color.transform(v[i]);\n        } else {\n          output += v[i];\n        }\n      }\n    }\n    return output;\n  };\n}\nconst convertNumbersToZero = v => typeof v === \"number\" ? 0 : color.test(v) ? color.getAnimatableNone(v) : v;\nfunction getAnimatableNone(v) {\n  const parsed = parseComplexValue(v);\n  const transformer = createTransformer(v);\n  return transformer(parsed.map(convertNumbersToZero));\n}\nconst complex = {\n  test,\n  parse: parseComplexValue,\n  createTransformer,\n  getAnimatableNone\n};\nexport { analyseComplexValue, complex };", "map": {"version": 3, "names": ["color", "colorRegex", "floatRegex", "sanitize", "test", "v", "_v$match", "_v$match2", "isNaN", "match", "length", "NUMBER_TOKEN", "COLOR_TOKEN", "VAR_TOKEN", "VAR_FUNCTION_TOKEN", "SPLIT_TOKEN", "complexRegex", "analyseComplexValue", "value", "originalValue", "toString", "values", "indexes", "number", "var", "types", "i", "tokenised", "replace", "parsedValue", "push", "parse", "startsWith", "parseFloat", "split", "parseComplexValue", "createTransformer", "source", "numSections", "output", "undefined", "type", "transform", "convertNumbersToZero", "getAnimatableNone", "parsed", "transformer", "map", "complex"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/motion-dom/dist/es/value/types/complex/index.mjs"], "sourcesContent": ["import { color } from '../color/index.mjs';\nimport { colorRegex } from '../utils/color-regex.mjs';\nimport { floatRegex } from '../utils/float-regex.mjs';\nimport { sanitize } from '../utils/sanitize.mjs';\n\nfunction test(v) {\n    return (isNaN(v) &&\n        typeof v === \"string\" &&\n        (v.match(floatRegex)?.length || 0) +\n            (v.match(colorRegex)?.length || 0) >\n            0);\n}\nconst NUMBER_TOKEN = \"number\";\nconst COLOR_TOKEN = \"color\";\nconst VAR_TOKEN = \"var\";\nconst VAR_FUNCTION_TOKEN = \"var(\";\nconst SPLIT_TOKEN = \"${}\";\n// this regex consists of the `singleCssVariableRegex|rgbHSLValueRegex|digitRegex`\nconst complexRegex = /var\\s*\\(\\s*--(?:[\\w-]+\\s*|[\\w-]+\\s*,(?:\\s*[^)(\\s]|\\s*\\((?:[^)(]|\\([^)(]*\\))*\\))+\\s*)\\)|#[\\da-f]{3,8}|(?:rgb|hsl)a?\\((?:-?[\\d.]+%?[,\\s]+){2}-?[\\d.]+%?\\s*(?:[,/]\\s*)?(?:\\b\\d+(?:\\.\\d+)?|\\.\\d+)?%?\\)|-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/giu;\nfunction analyseComplexValue(value) {\n    const originalValue = value.toString();\n    const values = [];\n    const indexes = {\n        color: [],\n        number: [],\n        var: [],\n    };\n    const types = [];\n    let i = 0;\n    const tokenised = originalValue.replace(complexRegex, (parsedValue) => {\n        if (color.test(parsedValue)) {\n            indexes.color.push(i);\n            types.push(COLOR_TOKEN);\n            values.push(color.parse(parsedValue));\n        }\n        else if (parsedValue.startsWith(VAR_FUNCTION_TOKEN)) {\n            indexes.var.push(i);\n            types.push(VAR_TOKEN);\n            values.push(parsedValue);\n        }\n        else {\n            indexes.number.push(i);\n            types.push(NUMBER_TOKEN);\n            values.push(parseFloat(parsedValue));\n        }\n        ++i;\n        return SPLIT_TOKEN;\n    });\n    const split = tokenised.split(SPLIT_TOKEN);\n    return { values, split, indexes, types };\n}\nfunction parseComplexValue(v) {\n    return analyseComplexValue(v).values;\n}\nfunction createTransformer(source) {\n    const { split, types } = analyseComplexValue(source);\n    const numSections = split.length;\n    return (v) => {\n        let output = \"\";\n        for (let i = 0; i < numSections; i++) {\n            output += split[i];\n            if (v[i] !== undefined) {\n                const type = types[i];\n                if (type === NUMBER_TOKEN) {\n                    output += sanitize(v[i]);\n                }\n                else if (type === COLOR_TOKEN) {\n                    output += color.transform(v[i]);\n                }\n                else {\n                    output += v[i];\n                }\n            }\n        }\n        return output;\n    };\n}\nconst convertNumbersToZero = (v) => typeof v === \"number\" ? 0 : color.test(v) ? color.getAnimatableNone(v) : v;\nfunction getAnimatableNone(v) {\n    const parsed = parseComplexValue(v);\n    const transformer = createTransformer(v);\n    return transformer(parsed.map(convertNumbersToZero));\n}\nconst complex = {\n    test,\n    parse: parseComplexValue,\n    createTransformer,\n    getAnimatableNone,\n};\n\nexport { analyseComplexValue, complex };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,QAAQ,QAAQ,uBAAuB;AAEhD,SAASC,IAAIA,CAACC,CAAC,EAAE;EAAA,IAAAC,QAAA,EAAAC,SAAA;EACb,OAAQC,KAAK,CAACH,CAAC,CAAC,IACZ,OAAOA,CAAC,KAAK,QAAQ,IACrB,CAAC,EAAAC,QAAA,GAAAD,CAAC,CAACI,KAAK,CAACP,UAAU,CAAC,cAAAI,QAAA,uBAAnBA,QAAA,CAAqBI,MAAM,KAAI,CAAC,KAC5B,EAAAH,SAAA,GAAAF,CAAC,CAACI,KAAK,CAACR,UAAU,CAAC,cAAAM,SAAA,uBAAnBA,SAAA,CAAqBG,MAAM,KAAI,CAAC,CAAC,GAClC,CAAC;AACb;AACA,MAAMC,YAAY,GAAG,QAAQ;AAC7B,MAAMC,WAAW,GAAG,OAAO;AAC3B,MAAMC,SAAS,GAAG,KAAK;AACvB,MAAMC,kBAAkB,GAAG,MAAM;AACjC,MAAMC,WAAW,GAAG,KAAK;AACzB;AACA,MAAMC,YAAY,GAAG,6pCAAiO;AACtP,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAChC,MAAMC,aAAa,GAAGD,KAAK,CAACE,QAAQ,CAAC,CAAC;EACtC,MAAMC,MAAM,GAAG,EAAE;EACjB,MAAMC,OAAO,GAAG;IACZtB,KAAK,EAAE,EAAE;IACTuB,MAAM,EAAE,EAAE;IACVC,GAAG,EAAE;EACT,CAAC;EACD,MAAMC,KAAK,GAAG,EAAE;EAChB,IAAIC,CAAC,GAAG,CAAC;EACT,MAAMC,SAAS,GAAGR,aAAa,CAACS,OAAO,CAACZ,YAAY,EAAGa,WAAW,IAAK;IACnE,IAAI7B,KAAK,CAACI,IAAI,CAACyB,WAAW,CAAC,EAAE;MACzBP,OAAO,CAACtB,KAAK,CAAC8B,IAAI,CAACJ,CAAC,CAAC;MACrBD,KAAK,CAACK,IAAI,CAAClB,WAAW,CAAC;MACvBS,MAAM,CAACS,IAAI,CAAC9B,KAAK,CAAC+B,KAAK,CAACF,WAAW,CAAC,CAAC;IACzC,CAAC,MACI,IAAIA,WAAW,CAACG,UAAU,CAAClB,kBAAkB,CAAC,EAAE;MACjDQ,OAAO,CAACE,GAAG,CAACM,IAAI,CAACJ,CAAC,CAAC;MACnBD,KAAK,CAACK,IAAI,CAACjB,SAAS,CAAC;MACrBQ,MAAM,CAACS,IAAI,CAACD,WAAW,CAAC;IAC5B,CAAC,MACI;MACDP,OAAO,CAACC,MAAM,CAACO,IAAI,CAACJ,CAAC,CAAC;MACtBD,KAAK,CAACK,IAAI,CAACnB,YAAY,CAAC;MACxBU,MAAM,CAACS,IAAI,CAACG,UAAU,CAACJ,WAAW,CAAC,CAAC;IACxC;IACA,EAAEH,CAAC;IACH,OAAOX,WAAW;EACtB,CAAC,CAAC;EACF,MAAMmB,KAAK,GAAGP,SAAS,CAACO,KAAK,CAACnB,WAAW,CAAC;EAC1C,OAAO;IAAEM,MAAM;IAAEa,KAAK;IAAEZ,OAAO;IAAEG;EAAM,CAAC;AAC5C;AACA,SAASU,iBAAiBA,CAAC9B,CAAC,EAAE;EAC1B,OAAOY,mBAAmB,CAACZ,CAAC,CAAC,CAACgB,MAAM;AACxC;AACA,SAASe,iBAAiBA,CAACC,MAAM,EAAE;EAC/B,MAAM;IAAEH,KAAK;IAAET;EAAM,CAAC,GAAGR,mBAAmB,CAACoB,MAAM,CAAC;EACpD,MAAMC,WAAW,GAAGJ,KAAK,CAACxB,MAAM;EAChC,OAAQL,CAAC,IAAK;IACV,IAAIkC,MAAM,GAAG,EAAE;IACf,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,WAAW,EAAEZ,CAAC,EAAE,EAAE;MAClCa,MAAM,IAAIL,KAAK,CAACR,CAAC,CAAC;MAClB,IAAIrB,CAAC,CAACqB,CAAC,CAAC,KAAKc,SAAS,EAAE;QACpB,MAAMC,IAAI,GAAGhB,KAAK,CAACC,CAAC,CAAC;QACrB,IAAIe,IAAI,KAAK9B,YAAY,EAAE;UACvB4B,MAAM,IAAIpC,QAAQ,CAACE,CAAC,CAACqB,CAAC,CAAC,CAAC;QAC5B,CAAC,MACI,IAAIe,IAAI,KAAK7B,WAAW,EAAE;UAC3B2B,MAAM,IAAIvC,KAAK,CAAC0C,SAAS,CAACrC,CAAC,CAACqB,CAAC,CAAC,CAAC;QACnC,CAAC,MACI;UACDa,MAAM,IAAIlC,CAAC,CAACqB,CAAC,CAAC;QAClB;MACJ;IACJ;IACA,OAAOa,MAAM;EACjB,CAAC;AACL;AACA,MAAMI,oBAAoB,GAAItC,CAAC,IAAK,OAAOA,CAAC,KAAK,QAAQ,GAAG,CAAC,GAAGL,KAAK,CAACI,IAAI,CAACC,CAAC,CAAC,GAAGL,KAAK,CAAC4C,iBAAiB,CAACvC,CAAC,CAAC,GAAGA,CAAC;AAC9G,SAASuC,iBAAiBA,CAACvC,CAAC,EAAE;EAC1B,MAAMwC,MAAM,GAAGV,iBAAiB,CAAC9B,CAAC,CAAC;EACnC,MAAMyC,WAAW,GAAGV,iBAAiB,CAAC/B,CAAC,CAAC;EACxC,OAAOyC,WAAW,CAACD,MAAM,CAACE,GAAG,CAACJ,oBAAoB,CAAC,CAAC;AACxD;AACA,MAAMK,OAAO,GAAG;EACZ5C,IAAI;EACJ2B,KAAK,EAAEI,iBAAiB;EACxBC,iBAAiB;EACjBQ;AACJ,CAAC;AAED,SAAS3B,mBAAmB,EAAE+B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}