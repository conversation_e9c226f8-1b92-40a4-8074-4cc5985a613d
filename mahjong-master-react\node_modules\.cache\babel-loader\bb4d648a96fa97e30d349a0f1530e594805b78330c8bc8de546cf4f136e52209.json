{"ast": null, "code": "import { Loader, LoaderUtils, FileLoader, Vector3, Vector2, TextureLoader, Scene, Object3D, Group, SphereGeometry, MeshBasicMaterial, BackSide, Mesh, PointsMaterial, Points, LineBasicMaterial, LineSegments, FrontSide, DoubleSide, MeshPhongMaterial, Color, DataTexture, BufferGeometry, Float32BufferAttribute, BoxGeometry, ConeGeometry, CylinderGeometry, Quaternion, ShapeUtils, BufferAttribute, RepeatWrapping, ClampToEdgeWrapping } from \"three\";\nimport { createToken, Lexer, CstParser } from \"../libs/chevrotain.js\";\nclass VRMLLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const path = scope.path === \"\" ? LoaderUtils.extractUrlBase(url) : scope.path;\n    const loader = new FileLoader(scope.manager);\n    loader.setPath(scope.path);\n    loader.setRequestHeader(scope.requestHeader);\n    loader.setWithCredentials(scope.withCredentials);\n    loader.load(url, function (text) {\n      try {\n        onLoad(scope.parse(text, path));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(data, path) {\n    const nodeMap = {};\n    function generateVRMLTree(data2) {\n      const tokenData = createTokens();\n      const lexer = new VRMLLexer(tokenData.tokens);\n      const parser = new VRMLParser(tokenData.tokenVocabulary);\n      const visitor = createVisitor(parser.getBaseCstVisitorConstructor());\n      const lexingResult = lexer.lex(data2);\n      parser.input = lexingResult.tokens;\n      const cstOutput = parser.vrml();\n      if (parser.errors.length > 0) {\n        console.error(parser.errors);\n        throw Error(\"THREE.VRMLLoader: Parsing errors detected.\");\n      }\n      const ast = visitor.visit(cstOutput);\n      return ast;\n    }\n    function createTokens() {\n      const RouteIdentifier = createToken({\n        name: \"RouteIdentifier\",\n        pattern: /[^\\x30-\\x39\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d][^\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d]*[\\.][^\\x30-\\x39\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d][^\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d]*/\n      });\n      const Identifier = createToken({\n        name: \"Identifier\",\n        pattern: /[^\\x30-\\x39\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d][^\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d]*/,\n        longer_alt: RouteIdentifier\n      });\n      const nodeTypes = [\"Anchor\", \"Billboard\", \"Collision\", \"Group\", \"Transform\",\n      // grouping nodes\n      \"Inline\", \"LOD\", \"Switch\",\n      // special groups\n      \"AudioClip\", \"DirectionalLight\", \"PointLight\", \"Script\", \"Shape\", \"Sound\", \"SpotLight\", \"WorldInfo\",\n      // common nodes\n      \"CylinderSensor\", \"PlaneSensor\", \"ProximitySensor\", \"SphereSensor\", \"TimeSensor\", \"TouchSensor\", \"VisibilitySensor\",\n      // sensors\n      \"Box\", \"Cone\", \"Cylinder\", \"ElevationGrid\", \"Extrusion\", \"IndexedFaceSet\", \"IndexedLineSet\", \"PointSet\", \"Sphere\",\n      // geometries\n      \"Color\", \"Coordinate\", \"Normal\", \"TextureCoordinate\",\n      // geometric properties\n      \"Appearance\", \"FontStyle\", \"ImageTexture\", \"Material\", \"MovieTexture\", \"PixelTexture\", \"TextureTransform\",\n      // appearance\n      \"ColorInterpolator\", \"CoordinateInterpolator\", \"NormalInterpolator\", \"OrientationInterpolator\", \"PositionInterpolator\", \"ScalarInterpolator\",\n      // interpolators\n      \"Background\", \"Fog\", \"NavigationInfo\", \"Viewpoint\",\n      // bindable nodes\n      \"Text\"\n      // Text must be placed at the end of the regex so there are no matches for TextureTransform and TextureCoordinate\n      ];\n      const Version = createToken({\n        name: \"Version\",\n        pattern: /#VRML.*/,\n        longer_alt: Identifier\n      });\n      const NodeName = createToken({\n        name: \"NodeName\",\n        pattern: new RegExp(nodeTypes.join(\"|\")),\n        longer_alt: Identifier\n      });\n      const DEF = createToken({\n        name: \"DEF\",\n        pattern: /DEF/,\n        longer_alt: Identifier\n      });\n      const USE = createToken({\n        name: \"USE\",\n        pattern: /USE/,\n        longer_alt: Identifier\n      });\n      const ROUTE = createToken({\n        name: \"ROUTE\",\n        pattern: /ROUTE/,\n        longer_alt: Identifier\n      });\n      const TO = createToken({\n        name: \"TO\",\n        pattern: /TO/,\n        longer_alt: Identifier\n      });\n      const StringLiteral = createToken({\n        name: \"StringLiteral\",\n        pattern: /\"(?:[^\\\\\"\\n\\r]|\\\\[bfnrtv\"\\\\/]|\\\\u[0-9a-fA-F][0-9a-fA-F][0-9a-fA-F][0-9a-fA-F])*\"/\n      });\n      const HexLiteral = createToken({\n        name: \"HexLiteral\",\n        pattern: /0[xX][0-9a-fA-F]+/\n      });\n      const NumberLiteral = createToken({\n        name: \"NumberLiteral\",\n        pattern: /[-+]?[0-9]*\\.?[0-9]+([eE][-+]?[0-9]+)?/\n      });\n      const TrueLiteral = createToken({\n        name: \"TrueLiteral\",\n        pattern: /TRUE/\n      });\n      const FalseLiteral = createToken({\n        name: \"FalseLiteral\",\n        pattern: /FALSE/\n      });\n      const NullLiteral = createToken({\n        name: \"NullLiteral\",\n        pattern: /NULL/\n      });\n      const LSquare = createToken({\n        name: \"LSquare\",\n        pattern: /\\[/\n      });\n      const RSquare = createToken({\n        name: \"RSquare\",\n        pattern: /]/\n      });\n      const LCurly = createToken({\n        name: \"LCurly\",\n        pattern: /{/\n      });\n      const RCurly = createToken({\n        name: \"RCurly\",\n        pattern: /}/\n      });\n      const Comment = createToken({\n        name: \"Comment\",\n        pattern: /#.*/,\n        group: Lexer.SKIPPED\n      });\n      const WhiteSpace = createToken({\n        name: \"WhiteSpace\",\n        pattern: /[ ,\\s]/,\n        group: Lexer.SKIPPED\n      });\n      const tokens = [WhiteSpace,\n      // keywords appear before the Identifier\n      NodeName, DEF, USE, ROUTE, TO, TrueLiteral, FalseLiteral, NullLiteral,\n      // the Identifier must appear after the keywords because all keywords are valid identifiers\n      Version, Identifier, RouteIdentifier, StringLiteral, HexLiteral, NumberLiteral, LSquare, RSquare, LCurly, RCurly, Comment];\n      const tokenVocabulary = {};\n      for (let i = 0, l = tokens.length; i < l; i++) {\n        const token = tokens[i];\n        tokenVocabulary[token.name] = token;\n      }\n      return {\n        tokens,\n        tokenVocabulary\n      };\n    }\n    function createVisitor(BaseVRMLVisitor) {\n      function VRMLToASTVisitor() {\n        BaseVRMLVisitor.call(this);\n        this.validateVisitor();\n      }\n      VRMLToASTVisitor.prototype = Object.assign(Object.create(BaseVRMLVisitor.prototype), {\n        constructor: VRMLToASTVisitor,\n        vrml: function (ctx) {\n          const data2 = {\n            version: this.visit(ctx.version),\n            nodes: [],\n            routes: []\n          };\n          for (let i = 0, l = ctx.node.length; i < l; i++) {\n            const node = ctx.node[i];\n            data2.nodes.push(this.visit(node));\n          }\n          if (ctx.route) {\n            for (let i = 0, l = ctx.route.length; i < l; i++) {\n              const route = ctx.route[i];\n              data2.routes.push(this.visit(route));\n            }\n          }\n          return data2;\n        },\n        version: function (ctx) {\n          return ctx.Version[0].image;\n        },\n        node: function (ctx) {\n          const data2 = {\n            name: ctx.NodeName[0].image,\n            fields: []\n          };\n          if (ctx.field) {\n            for (let i = 0, l = ctx.field.length; i < l; i++) {\n              const field = ctx.field[i];\n              data2.fields.push(this.visit(field));\n            }\n          }\n          if (ctx.def) {\n            data2.DEF = this.visit(ctx.def[0]);\n          }\n          return data2;\n        },\n        field: function (ctx) {\n          const data2 = {\n            name: ctx.Identifier[0].image,\n            type: null,\n            values: null\n          };\n          let result;\n          if (ctx.singleFieldValue) {\n            result = this.visit(ctx.singleFieldValue[0]);\n          }\n          if (ctx.multiFieldValue) {\n            result = this.visit(ctx.multiFieldValue[0]);\n          }\n          data2.type = result.type;\n          data2.values = result.values;\n          return data2;\n        },\n        def: function (ctx) {\n          return (ctx.Identifier || ctx.NodeName)[0].image;\n        },\n        use: function (ctx) {\n          return {\n            USE: (ctx.Identifier || ctx.NodeName)[0].image\n          };\n        },\n        singleFieldValue: function (ctx) {\n          return processField(this, ctx);\n        },\n        multiFieldValue: function (ctx) {\n          return processField(this, ctx);\n        },\n        route: function (ctx) {\n          const data2 = {\n            FROM: ctx.RouteIdentifier[0].image,\n            TO: ctx.RouteIdentifier[1].image\n          };\n          return data2;\n        }\n      });\n      function processField(scope, ctx) {\n        const field = {\n          type: null,\n          values: []\n        };\n        if (ctx.node) {\n          field.type = \"node\";\n          for (let i = 0, l = ctx.node.length; i < l; i++) {\n            const node = ctx.node[i];\n            field.values.push(scope.visit(node));\n          }\n        }\n        if (ctx.use) {\n          field.type = \"use\";\n          for (let i = 0, l = ctx.use.length; i < l; i++) {\n            const use = ctx.use[i];\n            field.values.push(scope.visit(use));\n          }\n        }\n        if (ctx.StringLiteral) {\n          field.type = \"string\";\n          for (let i = 0, l = ctx.StringLiteral.length; i < l; i++) {\n            const stringLiteral = ctx.StringLiteral[i];\n            field.values.push(stringLiteral.image.replace(/'|\"/g, \"\"));\n          }\n        }\n        if (ctx.NumberLiteral) {\n          field.type = \"number\";\n          for (let i = 0, l = ctx.NumberLiteral.length; i < l; i++) {\n            const numberLiteral = ctx.NumberLiteral[i];\n            field.values.push(parseFloat(numberLiteral.image));\n          }\n        }\n        if (ctx.HexLiteral) {\n          field.type = \"hex\";\n          for (let i = 0, l = ctx.HexLiteral.length; i < l; i++) {\n            const hexLiteral = ctx.HexLiteral[i];\n            field.values.push(hexLiteral.image);\n          }\n        }\n        if (ctx.TrueLiteral) {\n          field.type = \"boolean\";\n          for (let i = 0, l = ctx.TrueLiteral.length; i < l; i++) {\n            const trueLiteral = ctx.TrueLiteral[i];\n            if (trueLiteral.image === \"TRUE\") field.values.push(true);\n          }\n        }\n        if (ctx.FalseLiteral) {\n          field.type = \"boolean\";\n          for (let i = 0, l = ctx.FalseLiteral.length; i < l; i++) {\n            const falseLiteral = ctx.FalseLiteral[i];\n            if (falseLiteral.image === \"FALSE\") field.values.push(false);\n          }\n        }\n        if (ctx.NullLiteral) {\n          field.type = \"null\";\n          ctx.NullLiteral.forEach(function () {\n            field.values.push(null);\n          });\n        }\n        return field;\n      }\n      return new VRMLToASTVisitor();\n    }\n    function parseTree(tree2) {\n      const nodes = tree2.nodes;\n      const scene2 = new Scene();\n      for (let i = 0, l = nodes.length; i < l; i++) {\n        const node = nodes[i];\n        buildNodeMap(node);\n      }\n      for (let i = 0, l = nodes.length; i < l; i++) {\n        const node = nodes[i];\n        const object = getNode(node);\n        if (object instanceof Object3D) scene2.add(object);\n        if (node.name === \"WorldInfo\") scene2.userData.worldInfo = object;\n      }\n      return scene2;\n    }\n    function buildNodeMap(node) {\n      if (node.DEF) {\n        nodeMap[node.DEF] = node;\n      }\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        if (field.type === \"node\") {\n          const fieldValues = field.values;\n          for (let j = 0, jl = fieldValues.length; j < jl; j++) {\n            buildNodeMap(fieldValues[j]);\n          }\n        }\n      }\n    }\n    function getNode(node) {\n      if (node.USE) {\n        return resolveUSE(node.USE);\n      }\n      if (node.build !== void 0) return node.build;\n      node.build = buildNode(node);\n      return node.build;\n    }\n    function buildNode(node) {\n      const nodeName = node.name;\n      let build;\n      switch (nodeName) {\n        case \"Group\":\n        case \"Transform\":\n        case \"Collision\":\n          build = buildGroupingNode(node);\n          break;\n        case \"Background\":\n          build = buildBackgroundNode(node);\n          break;\n        case \"Shape\":\n          build = buildShapeNode(node);\n          break;\n        case \"Appearance\":\n          build = buildAppearanceNode(node);\n          break;\n        case \"Material\":\n          build = buildMaterialNode(node);\n          break;\n        case \"ImageTexture\":\n          build = buildImageTextureNode(node);\n          break;\n        case \"PixelTexture\":\n          build = buildPixelTextureNode(node);\n          break;\n        case \"TextureTransform\":\n          build = buildTextureTransformNode(node);\n          break;\n        case \"IndexedFaceSet\":\n          build = buildIndexedFaceSetNode(node);\n          break;\n        case \"IndexedLineSet\":\n          build = buildIndexedLineSetNode(node);\n          break;\n        case \"PointSet\":\n          build = buildPointSetNode(node);\n          break;\n        case \"Box\":\n          build = buildBoxNode(node);\n          break;\n        case \"Cone\":\n          build = buildConeNode(node);\n          break;\n        case \"Cylinder\":\n          build = buildCylinderNode(node);\n          break;\n        case \"Sphere\":\n          build = buildSphereNode(node);\n          break;\n        case \"ElevationGrid\":\n          build = buildElevationGridNode(node);\n          break;\n        case \"Extrusion\":\n          build = buildExtrusionNode(node);\n          break;\n        case \"Color\":\n        case \"Coordinate\":\n        case \"Normal\":\n        case \"TextureCoordinate\":\n          build = buildGeometricNode(node);\n          break;\n        case \"WorldInfo\":\n          build = buildWorldInfoNode(node);\n          break;\n        case \"Anchor\":\n        case \"Billboard\":\n        case \"Inline\":\n        case \"LOD\":\n        case \"Switch\":\n        case \"AudioClip\":\n        case \"DirectionalLight\":\n        case \"PointLight\":\n        case \"Script\":\n        case \"Sound\":\n        case \"SpotLight\":\n        case \"CylinderSensor\":\n        case \"PlaneSensor\":\n        case \"ProximitySensor\":\n        case \"SphereSensor\":\n        case \"TimeSensor\":\n        case \"TouchSensor\":\n        case \"VisibilitySensor\":\n        case \"Text\":\n        case \"FontStyle\":\n        case \"MovieTexture\":\n        case \"ColorInterpolator\":\n        case \"CoordinateInterpolator\":\n        case \"NormalInterpolator\":\n        case \"OrientationInterpolator\":\n        case \"PositionInterpolator\":\n        case \"ScalarInterpolator\":\n        case \"Fog\":\n        case \"NavigationInfo\":\n        case \"Viewpoint\":\n          break;\n        default:\n          console.warn(\"THREE.VRMLLoader: Unknown node:\", nodeName);\n          break;\n      }\n      if (build !== void 0 && node.DEF !== void 0 && build.hasOwnProperty(\"name\") === true) {\n        build.name = node.DEF;\n      }\n      return build;\n    }\n    function buildGroupingNode(node) {\n      const object = new Group();\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"bboxCenter\":\n            break;\n          case \"bboxSize\":\n            break;\n          case \"center\":\n            break;\n          case \"children\":\n            parseFieldChildren(fieldValues, object);\n            break;\n          case \"collide\":\n            break;\n          case \"rotation\":\n            const axis = new Vector3(fieldValues[0], fieldValues[1], fieldValues[2]).normalize();\n            const angle = fieldValues[3];\n            object.quaternion.setFromAxisAngle(axis, angle);\n            break;\n          case \"scale\":\n            object.scale.set(fieldValues[0], fieldValues[1], fieldValues[2]);\n            break;\n          case \"scaleOrientation\":\n            break;\n          case \"translation\":\n            object.position.set(fieldValues[0], fieldValues[1], fieldValues[2]);\n            break;\n          case \"proxy\":\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      return object;\n    }\n    function buildBackgroundNode(node) {\n      const group = new Group();\n      let groundAngle, groundColor;\n      let skyAngle, skyColor;\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"groundAngle\":\n            groundAngle = fieldValues;\n            break;\n          case \"groundColor\":\n            groundColor = fieldValues;\n            break;\n          case \"backUrl\":\n            break;\n          case \"bottomUrl\":\n            break;\n          case \"frontUrl\":\n            break;\n          case \"leftUrl\":\n            break;\n          case \"rightUrl\":\n            break;\n          case \"topUrl\":\n            break;\n          case \"skyAngle\":\n            skyAngle = fieldValues;\n            break;\n          case \"skyColor\":\n            skyColor = fieldValues;\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      const radius = 1e4;\n      if (skyColor) {\n        const skyGeometry = new SphereGeometry(radius, 32, 16);\n        const skyMaterial = new MeshBasicMaterial({\n          fog: false,\n          side: BackSide,\n          depthWrite: false,\n          depthTest: false\n        });\n        if (skyColor.length > 3) {\n          paintFaces(skyGeometry, radius, skyAngle, toColorArray(skyColor), true);\n          skyMaterial.vertexColors = true;\n        } else {\n          skyMaterial.color.setRGB(skyColor[0], skyColor[1], skyColor[2]);\n        }\n        const sky = new Mesh(skyGeometry, skyMaterial);\n        group.add(sky);\n      }\n      if (groundColor) {\n        if (groundColor.length > 0) {\n          const groundGeometry = new SphereGeometry(radius, 32, 16, 0, 2 * Math.PI, 0.5 * Math.PI, 1.5 * Math.PI);\n          const groundMaterial = new MeshBasicMaterial({\n            fog: false,\n            side: BackSide,\n            vertexColors: true,\n            depthWrite: false,\n            depthTest: false\n          });\n          paintFaces(groundGeometry, radius, groundAngle, toColorArray(groundColor), false);\n          const ground = new Mesh(groundGeometry, groundMaterial);\n          group.add(ground);\n        }\n      }\n      group.renderOrder = -Infinity;\n      return group;\n    }\n    function buildShapeNode(node) {\n      const fields = node.fields;\n      let material = new MeshBasicMaterial({\n        color: 0\n      });\n      let geometry;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"appearance\":\n            if (fieldValues[0] !== null) {\n              material = getNode(fieldValues[0]);\n            }\n            break;\n          case \"geometry\":\n            if (fieldValues[0] !== null) {\n              geometry = getNode(fieldValues[0]);\n            }\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      let object;\n      if (geometry && geometry.attributes.position) {\n        const type = geometry._type;\n        if (type === \"points\") {\n          const pointsMaterial = new PointsMaterial({\n            color: 16777215\n          });\n          if (geometry.attributes.color !== void 0) {\n            pointsMaterial.vertexColors = true;\n          } else {\n            if (material.isMeshPhongMaterial) {\n              pointsMaterial.color.copy(material.emissive);\n            }\n          }\n          object = new Points(geometry, pointsMaterial);\n        } else if (type === \"line\") {\n          const lineMaterial = new LineBasicMaterial({\n            color: 16777215\n          });\n          if (geometry.attributes.color !== void 0) {\n            lineMaterial.vertexColors = true;\n          } else {\n            if (material.isMeshPhongMaterial) {\n              lineMaterial.color.copy(material.emissive);\n            }\n          }\n          object = new LineSegments(geometry, lineMaterial);\n        } else {\n          if (geometry._solid !== void 0) {\n            material.side = geometry._solid ? FrontSide : DoubleSide;\n          }\n          if (geometry.attributes.color !== void 0) {\n            material.vertexColors = true;\n          }\n          object = new Mesh(geometry, material);\n        }\n      } else {\n        object = new Object3D();\n        object.visible = false;\n      }\n      return object;\n    }\n    function buildAppearanceNode(node) {\n      let material = new MeshPhongMaterial();\n      let transformData;\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"material\":\n            if (fieldValues[0] !== null) {\n              const materialData = getNode(fieldValues[0]);\n              if (materialData.diffuseColor) material.color.copy(materialData.diffuseColor);\n              if (materialData.emissiveColor) material.emissive.copy(materialData.emissiveColor);\n              if (materialData.shininess) material.shininess = materialData.shininess;\n              if (materialData.specularColor) material.specular.copy(materialData.specularColor);\n              if (materialData.transparency) material.opacity = 1 - materialData.transparency;\n              if (materialData.transparency > 0) material.transparent = true;\n            } else {\n              material = new MeshBasicMaterial({\n                color: 0\n              });\n            }\n            break;\n          case \"texture\":\n            const textureNode = fieldValues[0];\n            if (textureNode !== null) {\n              if (textureNode.name === \"ImageTexture\" || textureNode.name === \"PixelTexture\") {\n                material.map = getNode(textureNode);\n              }\n            }\n            break;\n          case \"textureTransform\":\n            if (fieldValues[0] !== null) {\n              transformData = getNode(fieldValues[0]);\n            }\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      if (material.map) {\n        if (material.map.__type) {\n          switch (material.map.__type) {\n            case TEXTURE_TYPE.INTENSITY_ALPHA:\n              material.opacity = 1;\n              break;\n            case TEXTURE_TYPE.RGB:\n              material.color.set(16777215);\n              break;\n            case TEXTURE_TYPE.RGBA:\n              material.color.set(16777215);\n              material.opacity = 1;\n              break;\n          }\n          delete material.map.__type;\n        }\n        if (transformData) {\n          material.map.center.copy(transformData.center);\n          material.map.rotation = transformData.rotation;\n          material.map.repeat.copy(transformData.scale);\n          material.map.offset.copy(transformData.translation);\n        }\n      }\n      return material;\n    }\n    function buildMaterialNode(node) {\n      const materialData = {};\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"ambientIntensity\":\n            break;\n          case \"diffuseColor\":\n            materialData.diffuseColor = new Color(fieldValues[0], fieldValues[1], fieldValues[2]);\n            break;\n          case \"emissiveColor\":\n            materialData.emissiveColor = new Color(fieldValues[0], fieldValues[1], fieldValues[2]);\n            break;\n          case \"shininess\":\n            materialData.shininess = fieldValues[0];\n            break;\n          case \"specularColor\":\n            materialData.emissiveColor = new Color(fieldValues[0], fieldValues[1], fieldValues[2]);\n            break;\n          case \"transparency\":\n            materialData.transparency = fieldValues[0];\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      return materialData;\n    }\n    function parseHexColor(hex, textureType, color) {\n      let value;\n      switch (textureType) {\n        case TEXTURE_TYPE.INTENSITY:\n          value = parseInt(hex);\n          color.r = value;\n          color.g = value;\n          color.b = value;\n          color.a = 1;\n          break;\n        case TEXTURE_TYPE.INTENSITY_ALPHA:\n          value = parseInt(\"0x\" + hex.substring(2, 4));\n          color.r = value;\n          color.g = value;\n          color.b = value;\n          color.a = parseInt(\"0x\" + hex.substring(4, 6));\n          break;\n        case TEXTURE_TYPE.RGB:\n          color.r = parseInt(\"0x\" + hex.substring(2, 4));\n          color.g = parseInt(\"0x\" + hex.substring(4, 6));\n          color.b = parseInt(\"0x\" + hex.substring(6, 8));\n          color.a = 1;\n          break;\n        case TEXTURE_TYPE.RGBA:\n          color.r = parseInt(\"0x\" + hex.substring(2, 4));\n          color.g = parseInt(\"0x\" + hex.substring(4, 6));\n          color.b = parseInt(\"0x\" + hex.substring(6, 8));\n          color.a = parseInt(\"0x\" + hex.substring(8, 10));\n          break;\n      }\n    }\n    function getTextureType(num_components) {\n      let type;\n      switch (num_components) {\n        case 1:\n          type = TEXTURE_TYPE.INTENSITY;\n          break;\n        case 2:\n          type = TEXTURE_TYPE.INTENSITY_ALPHA;\n          break;\n        case 3:\n          type = TEXTURE_TYPE.RGB;\n          break;\n        case 4:\n          type = TEXTURE_TYPE.RGBA;\n          break;\n      }\n      return type;\n    }\n    function buildPixelTextureNode(node) {\n      let texture;\n      let wrapS = RepeatWrapping;\n      let wrapT = RepeatWrapping;\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"image\":\n            const width = fieldValues[0];\n            const height = fieldValues[1];\n            const num_components = fieldValues[2];\n            const textureType = getTextureType(num_components);\n            const data2 = new Uint8Array(4 * width * height);\n            const color = {\n              r: 0,\n              g: 0,\n              b: 0,\n              a: 0\n            };\n            for (let j = 3, k = 0, jl = fieldValues.length; j < jl; j++, k++) {\n              parseHexColor(fieldValues[j], textureType, color);\n              const stride = k * 4;\n              data2[stride + 0] = color.r;\n              data2[stride + 1] = color.g;\n              data2[stride + 2] = color.b;\n              data2[stride + 3] = color.a;\n            }\n            texture = new DataTexture(data2, width, height);\n            texture.needsUpdate = true;\n            texture.__type = textureType;\n            break;\n          case \"repeatS\":\n            if (fieldValues[0] === false) wrapS = ClampToEdgeWrapping;\n            break;\n          case \"repeatT\":\n            if (fieldValues[0] === false) wrapT = ClampToEdgeWrapping;\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      if (texture) {\n        texture.wrapS = wrapS;\n        texture.wrapT = wrapT;\n      }\n      return texture;\n    }\n    function buildImageTextureNode(node) {\n      let texture;\n      let wrapS = RepeatWrapping;\n      let wrapT = RepeatWrapping;\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"url\":\n            const url = fieldValues[0];\n            if (url) texture = textureLoader.load(url);\n            break;\n          case \"repeatS\":\n            if (fieldValues[0] === false) wrapS = ClampToEdgeWrapping;\n            break;\n          case \"repeatT\":\n            if (fieldValues[0] === false) wrapT = ClampToEdgeWrapping;\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      if (texture) {\n        texture.wrapS = wrapS;\n        texture.wrapT = wrapT;\n      }\n      return texture;\n    }\n    function buildTextureTransformNode(node) {\n      const transformData = {\n        center: new Vector2(),\n        rotation: new Vector2(),\n        scale: new Vector2(),\n        translation: new Vector2()\n      };\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"center\":\n            transformData.center.set(fieldValues[0], fieldValues[1]);\n            break;\n          case \"rotation\":\n            transformData.rotation = fieldValues[0];\n            break;\n          case \"scale\":\n            transformData.scale.set(fieldValues[0], fieldValues[1]);\n            break;\n          case \"translation\":\n            transformData.translation.set(fieldValues[0], fieldValues[1]);\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      return transformData;\n    }\n    function buildGeometricNode(node) {\n      return node.fields[0].values;\n    }\n    function buildWorldInfoNode(node) {\n      const worldInfo = {};\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"title\":\n            worldInfo.title = fieldValues[0];\n            break;\n          case \"info\":\n            worldInfo.info = fieldValues;\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      return worldInfo;\n    }\n    function buildIndexedFaceSetNode(node) {\n      let color, coord, normal, texCoord;\n      let ccw = true,\n        solid = true,\n        creaseAngle = 0;\n      let colorIndex, coordIndex, normalIndex, texCoordIndex;\n      let colorPerVertex = true,\n        normalPerVertex = true;\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"color\":\n            const colorNode = fieldValues[0];\n            if (colorNode !== null) {\n              color = getNode(colorNode);\n            }\n            break;\n          case \"coord\":\n            const coordNode = fieldValues[0];\n            if (coordNode !== null) {\n              coord = getNode(coordNode);\n            }\n            break;\n          case \"normal\":\n            const normalNode = fieldValues[0];\n            if (normalNode !== null) {\n              normal = getNode(normalNode);\n            }\n            break;\n          case \"texCoord\":\n            const texCoordNode = fieldValues[0];\n            if (texCoordNode !== null) {\n              texCoord = getNode(texCoordNode);\n            }\n            break;\n          case \"ccw\":\n            ccw = fieldValues[0];\n            break;\n          case \"colorIndex\":\n            colorIndex = fieldValues;\n            break;\n          case \"colorPerVertex\":\n            colorPerVertex = fieldValues[0];\n            break;\n          case \"convex\":\n            break;\n          case \"coordIndex\":\n            coordIndex = fieldValues;\n            break;\n          case \"creaseAngle\":\n            creaseAngle = fieldValues[0];\n            break;\n          case \"normalIndex\":\n            normalIndex = fieldValues;\n            break;\n          case \"normalPerVertex\":\n            normalPerVertex = fieldValues[0];\n            break;\n          case \"solid\":\n            solid = fieldValues[0];\n            break;\n          case \"texCoordIndex\":\n            texCoordIndex = fieldValues;\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      if (coordIndex === void 0) {\n        console.warn(\"THREE.VRMLLoader: Missing coordIndex.\");\n        return new BufferGeometry();\n      }\n      const triangulatedCoordIndex = triangulateFaceIndex(coordIndex, ccw);\n      let colorAttribute;\n      let normalAttribute;\n      let uvAttribute;\n      if (color) {\n        if (colorPerVertex === true) {\n          if (colorIndex && colorIndex.length > 0) {\n            const triangulatedColorIndex = triangulateFaceIndex(colorIndex, ccw);\n            colorAttribute = computeAttributeFromIndexedData(triangulatedCoordIndex, triangulatedColorIndex, color, 3);\n          } else {\n            colorAttribute = toNonIndexedAttribute(triangulatedCoordIndex, new Float32BufferAttribute(color, 3));\n          }\n        } else {\n          if (colorIndex && colorIndex.length > 0) {\n            const flattenFaceColors = flattenData(color, colorIndex);\n            const triangulatedFaceColors = triangulateFaceData(flattenFaceColors, coordIndex);\n            colorAttribute = computeAttributeFromFaceData(triangulatedCoordIndex, triangulatedFaceColors);\n          } else {\n            const triangulatedFaceColors = triangulateFaceData(color, coordIndex);\n            colorAttribute = computeAttributeFromFaceData(triangulatedCoordIndex, triangulatedFaceColors);\n          }\n        }\n      }\n      if (normal) {\n        if (normalPerVertex === true) {\n          if (normalIndex && normalIndex.length > 0) {\n            const triangulatedNormalIndex = triangulateFaceIndex(normalIndex, ccw);\n            normalAttribute = computeAttributeFromIndexedData(triangulatedCoordIndex, triangulatedNormalIndex, normal, 3);\n          } else {\n            normalAttribute = toNonIndexedAttribute(triangulatedCoordIndex, new Float32BufferAttribute(normal, 3));\n          }\n        } else {\n          if (normalIndex && normalIndex.length > 0) {\n            const flattenFaceNormals = flattenData(normal, normalIndex);\n            const triangulatedFaceNormals = triangulateFaceData(flattenFaceNormals, coordIndex);\n            normalAttribute = computeAttributeFromFaceData(triangulatedCoordIndex, triangulatedFaceNormals);\n          } else {\n            const triangulatedFaceNormals = triangulateFaceData(normal, coordIndex);\n            normalAttribute = computeAttributeFromFaceData(triangulatedCoordIndex, triangulatedFaceNormals);\n          }\n        }\n      } else {\n        normalAttribute = computeNormalAttribute(triangulatedCoordIndex, coord, creaseAngle);\n      }\n      if (texCoord) {\n        if (texCoordIndex && texCoordIndex.length > 0) {\n          const triangulatedTexCoordIndex = triangulateFaceIndex(texCoordIndex, ccw);\n          uvAttribute = computeAttributeFromIndexedData(triangulatedCoordIndex, triangulatedTexCoordIndex, texCoord, 2);\n        } else {\n          uvAttribute = toNonIndexedAttribute(triangulatedCoordIndex, new Float32BufferAttribute(texCoord, 2));\n        }\n      }\n      const geometry = new BufferGeometry();\n      const positionAttribute = toNonIndexedAttribute(triangulatedCoordIndex, new Float32BufferAttribute(coord, 3));\n      geometry.setAttribute(\"position\", positionAttribute);\n      geometry.setAttribute(\"normal\", normalAttribute);\n      if (colorAttribute) geometry.setAttribute(\"color\", colorAttribute);\n      if (uvAttribute) geometry.setAttribute(\"uv\", uvAttribute);\n      geometry._solid = solid;\n      geometry._type = \"mesh\";\n      return geometry;\n    }\n    function buildIndexedLineSetNode(node) {\n      let color, coord;\n      let colorIndex, coordIndex;\n      let colorPerVertex = true;\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"color\":\n            const colorNode = fieldValues[0];\n            if (colorNode !== null) {\n              color = getNode(colorNode);\n            }\n            break;\n          case \"coord\":\n            const coordNode = fieldValues[0];\n            if (coordNode !== null) {\n              coord = getNode(coordNode);\n            }\n            break;\n          case \"colorIndex\":\n            colorIndex = fieldValues;\n            break;\n          case \"colorPerVertex\":\n            colorPerVertex = fieldValues[0];\n            break;\n          case \"coordIndex\":\n            coordIndex = fieldValues;\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      let colorAttribute;\n      const expandedLineIndex = expandLineIndex(coordIndex);\n      if (color) {\n        if (colorPerVertex === true) {\n          if (colorIndex.length > 0) {\n            const expandedColorIndex = expandLineIndex(colorIndex);\n            colorAttribute = computeAttributeFromIndexedData(expandedLineIndex, expandedColorIndex, color, 3);\n          } else {\n            colorAttribute = toNonIndexedAttribute(expandedLineIndex, new Float32BufferAttribute(color, 3));\n          }\n        } else {\n          if (colorIndex.length > 0) {\n            const flattenLineColors = flattenData(color, colorIndex);\n            const expandedLineColors = expandLineData(flattenLineColors, coordIndex);\n            colorAttribute = computeAttributeFromLineData(expandedLineIndex, expandedLineColors);\n          } else {\n            const expandedLineColors = expandLineData(color, coordIndex);\n            colorAttribute = computeAttributeFromLineData(expandedLineIndex, expandedLineColors);\n          }\n        }\n      }\n      const geometry = new BufferGeometry();\n      const positionAttribute = toNonIndexedAttribute(expandedLineIndex, new Float32BufferAttribute(coord, 3));\n      geometry.setAttribute(\"position\", positionAttribute);\n      if (colorAttribute) geometry.setAttribute(\"color\", colorAttribute);\n      geometry._type = \"line\";\n      return geometry;\n    }\n    function buildPointSetNode(node) {\n      let color, coord;\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"color\":\n            const colorNode = fieldValues[0];\n            if (colorNode !== null) {\n              color = getNode(colorNode);\n            }\n            break;\n          case \"coord\":\n            const coordNode = fieldValues[0];\n            if (coordNode !== null) {\n              coord = getNode(coordNode);\n            }\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      const geometry = new BufferGeometry();\n      geometry.setAttribute(\"position\", new Float32BufferAttribute(coord, 3));\n      if (color) geometry.setAttribute(\"color\", new Float32BufferAttribute(color, 3));\n      geometry._type = \"points\";\n      return geometry;\n    }\n    function buildBoxNode(node) {\n      const size = new Vector3(2, 2, 2);\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"size\":\n            size.x = fieldValues[0];\n            size.y = fieldValues[1];\n            size.z = fieldValues[2];\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      const geometry = new BoxGeometry(size.x, size.y, size.z);\n      return geometry;\n    }\n    function buildConeNode(node) {\n      let radius = 1,\n        height = 2,\n        openEnded = false;\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"bottom\":\n            openEnded = !fieldValues[0];\n            break;\n          case \"bottomRadius\":\n            radius = fieldValues[0];\n            break;\n          case \"height\":\n            height = fieldValues[0];\n            break;\n          case \"side\":\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      const geometry = new ConeGeometry(radius, height, 16, 1, openEnded);\n      return geometry;\n    }\n    function buildCylinderNode(node) {\n      let radius = 1,\n        height = 2;\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"bottom\":\n            break;\n          case \"radius\":\n            radius = fieldValues[0];\n            break;\n          case \"height\":\n            height = fieldValues[0];\n            break;\n          case \"side\":\n            break;\n          case \"top\":\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      const geometry = new CylinderGeometry(radius, radius, height, 16, 1);\n      return geometry;\n    }\n    function buildSphereNode(node) {\n      let radius = 1;\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"radius\":\n            radius = fieldValues[0];\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      const geometry = new SphereGeometry(radius, 16, 16);\n      return geometry;\n    }\n    function buildElevationGridNode(node) {\n      let color;\n      let normal;\n      let texCoord;\n      let height;\n      let colorPerVertex = true;\n      let normalPerVertex = true;\n      let solid = true;\n      let ccw = true;\n      let creaseAngle = 0;\n      let xDimension = 2;\n      let zDimension = 2;\n      let xSpacing = 1;\n      let zSpacing = 1;\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"color\":\n            const colorNode = fieldValues[0];\n            if (colorNode !== null) {\n              color = getNode(colorNode);\n            }\n            break;\n          case \"normal\":\n            const normalNode = fieldValues[0];\n            if (normalNode !== null) {\n              normal = getNode(normalNode);\n            }\n            break;\n          case \"texCoord\":\n            const texCoordNode = fieldValues[0];\n            if (texCoordNode !== null) {\n              texCoord = getNode(texCoordNode);\n            }\n            break;\n          case \"height\":\n            height = fieldValues;\n            break;\n          case \"ccw\":\n            ccw = fieldValues[0];\n            break;\n          case \"colorPerVertex\":\n            colorPerVertex = fieldValues[0];\n            break;\n          case \"creaseAngle\":\n            creaseAngle = fieldValues[0];\n            break;\n          case \"normalPerVertex\":\n            normalPerVertex = fieldValues[0];\n            break;\n          case \"solid\":\n            solid = fieldValues[0];\n            break;\n          case \"xDimension\":\n            xDimension = fieldValues[0];\n            break;\n          case \"xSpacing\":\n            xSpacing = fieldValues[0];\n            break;\n          case \"zDimension\":\n            zDimension = fieldValues[0];\n            break;\n          case \"zSpacing\":\n            zSpacing = fieldValues[0];\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      const vertices = [];\n      const normals = [];\n      const colors = [];\n      const uvs = [];\n      for (let i = 0; i < zDimension; i++) {\n        for (let j = 0; j < xDimension; j++) {\n          const index = i * xDimension + j;\n          const x = xSpacing * i;\n          const y = height[index];\n          const z = zSpacing * j;\n          vertices.push(x, y, z);\n          if (color && colorPerVertex === true) {\n            const r = color[index * 3 + 0];\n            const g = color[index * 3 + 1];\n            const b = color[index * 3 + 2];\n            colors.push(r, g, b);\n          }\n          if (normal && normalPerVertex === true) {\n            const xn = normal[index * 3 + 0];\n            const yn = normal[index * 3 + 1];\n            const zn = normal[index * 3 + 2];\n            normals.push(xn, yn, zn);\n          }\n          if (texCoord) {\n            const s = texCoord[index * 2 + 0];\n            const t = texCoord[index * 2 + 1];\n            uvs.push(s, t);\n          } else {\n            uvs.push(i / (xDimension - 1), j / (zDimension - 1));\n          }\n        }\n      }\n      const indices = [];\n      for (let i = 0; i < xDimension - 1; i++) {\n        for (let j = 0; j < zDimension - 1; j++) {\n          const a = i + j * xDimension;\n          const b = i + (j + 1) * xDimension;\n          const c = i + 1 + (j + 1) * xDimension;\n          const d = i + 1 + j * xDimension;\n          if (ccw === true) {\n            indices.push(a, c, b);\n            indices.push(c, a, d);\n          } else {\n            indices.push(a, b, c);\n            indices.push(c, d, a);\n          }\n        }\n      }\n      const positionAttribute = toNonIndexedAttribute(indices, new Float32BufferAttribute(vertices, 3));\n      const uvAttribute = toNonIndexedAttribute(indices, new Float32BufferAttribute(uvs, 2));\n      let colorAttribute;\n      let normalAttribute;\n      if (color) {\n        if (colorPerVertex === false) {\n          for (let i = 0; i < xDimension - 1; i++) {\n            for (let j = 0; j < zDimension - 1; j++) {\n              const index = i + j * (xDimension - 1);\n              const r = color[index * 3 + 0];\n              const g = color[index * 3 + 1];\n              const b = color[index * 3 + 2];\n              colors.push(r, g, b);\n              colors.push(r, g, b);\n              colors.push(r, g, b);\n              colors.push(r, g, b);\n              colors.push(r, g, b);\n              colors.push(r, g, b);\n            }\n          }\n          colorAttribute = new Float32BufferAttribute(colors, 3);\n        } else {\n          colorAttribute = toNonIndexedAttribute(indices, new Float32BufferAttribute(colors, 3));\n        }\n      }\n      if (normal) {\n        if (normalPerVertex === false) {\n          for (let i = 0; i < xDimension - 1; i++) {\n            for (let j = 0; j < zDimension - 1; j++) {\n              const index = i + j * (xDimension - 1);\n              const xn = normal[index * 3 + 0];\n              const yn = normal[index * 3 + 1];\n              const zn = normal[index * 3 + 2];\n              normals.push(xn, yn, zn);\n              normals.push(xn, yn, zn);\n              normals.push(xn, yn, zn);\n              normals.push(xn, yn, zn);\n              normals.push(xn, yn, zn);\n              normals.push(xn, yn, zn);\n            }\n          }\n          normalAttribute = new Float32BufferAttribute(normals, 3);\n        } else {\n          normalAttribute = toNonIndexedAttribute(indices, new Float32BufferAttribute(normals, 3));\n        }\n      } else {\n        normalAttribute = computeNormalAttribute(indices, vertices, creaseAngle);\n      }\n      const geometry = new BufferGeometry();\n      geometry.setAttribute(\"position\", positionAttribute);\n      geometry.setAttribute(\"normal\", normalAttribute);\n      geometry.setAttribute(\"uv\", uvAttribute);\n      if (colorAttribute) geometry.setAttribute(\"color\", colorAttribute);\n      geometry._solid = solid;\n      geometry._type = \"mesh\";\n      return geometry;\n    }\n    function buildExtrusionNode(node) {\n      let crossSection = [1, 1, 1, -1, -1, -1, -1, 1, 1, 1];\n      let spine = [0, 0, 0, 0, 1, 0];\n      let scale;\n      let orientation;\n      let beginCap = true;\n      let ccw = true;\n      let creaseAngle = 0;\n      let endCap = true;\n      let solid = true;\n      const fields = node.fields;\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i];\n        const fieldName = field.name;\n        const fieldValues = field.values;\n        switch (fieldName) {\n          case \"beginCap\":\n            beginCap = fieldValues[0];\n            break;\n          case \"ccw\":\n            ccw = fieldValues[0];\n            break;\n          case \"convex\":\n            break;\n          case \"creaseAngle\":\n            creaseAngle = fieldValues[0];\n            break;\n          case \"crossSection\":\n            crossSection = fieldValues;\n            break;\n          case \"endCap\":\n            endCap = fieldValues[0];\n            break;\n          case \"orientation\":\n            orientation = fieldValues;\n            break;\n          case \"scale\":\n            scale = fieldValues;\n            break;\n          case \"solid\":\n            solid = fieldValues[0];\n            break;\n          case \"spine\":\n            spine = fieldValues;\n            break;\n          default:\n            console.warn(\"THREE.VRMLLoader: Unknown field:\", fieldName);\n            break;\n        }\n      }\n      const crossSectionClosed = crossSection[0] === crossSection[crossSection.length - 2] && crossSection[1] === crossSection[crossSection.length - 1];\n      const vertices = [];\n      const spineVector = new Vector3();\n      const scaling = new Vector3();\n      const axis = new Vector3();\n      const vertex = new Vector3();\n      const quaternion = new Quaternion();\n      for (let i = 0, j = 0, o = 0, il = spine.length; i < il; i += 3, j += 2, o += 4) {\n        spineVector.fromArray(spine, i);\n        scaling.x = scale ? scale[j + 0] : 1;\n        scaling.y = 1;\n        scaling.z = scale ? scale[j + 1] : 1;\n        axis.x = orientation ? orientation[o + 0] : 0;\n        axis.y = orientation ? orientation[o + 1] : 0;\n        axis.z = orientation ? orientation[o + 2] : 1;\n        const angle = orientation ? orientation[o + 3] : 0;\n        for (let k = 0, kl = crossSection.length; k < kl; k += 2) {\n          vertex.x = crossSection[k + 0];\n          vertex.y = 0;\n          vertex.z = crossSection[k + 1];\n          vertex.multiply(scaling);\n          quaternion.setFromAxisAngle(axis, angle);\n          vertex.applyQuaternion(quaternion);\n          vertex.add(spineVector);\n          vertices.push(vertex.x, vertex.y, vertex.z);\n        }\n      }\n      const indices = [];\n      const spineCount = spine.length / 3;\n      const crossSectionCount = crossSection.length / 2;\n      for (let i = 0; i < spineCount - 1; i++) {\n        for (let j = 0; j < crossSectionCount - 1; j++) {\n          const a = j + i * crossSectionCount;\n          let b = j + 1 + i * crossSectionCount;\n          const c = j + (i + 1) * crossSectionCount;\n          let d = j + 1 + (i + 1) * crossSectionCount;\n          if (j === crossSectionCount - 2 && crossSectionClosed === true) {\n            b = i * crossSectionCount;\n            d = (i + 1) * crossSectionCount;\n          }\n          if (ccw === true) {\n            indices.push(a, b, c);\n            indices.push(c, b, d);\n          } else {\n            indices.push(a, c, b);\n            indices.push(c, d, b);\n          }\n        }\n      }\n      if (beginCap === true || endCap === true) {\n        const contour = [];\n        for (let i = 0, l = crossSection.length; i < l; i += 2) {\n          contour.push(new Vector2(crossSection[i], crossSection[i + 1]));\n        }\n        const faces = ShapeUtils.triangulateShape(contour, []);\n        const capIndices = [];\n        for (let i = 0, l = faces.length; i < l; i++) {\n          const face = faces[i];\n          capIndices.push(face[0], face[1], face[2]);\n        }\n        if (beginCap === true) {\n          for (let i = 0, l = capIndices.length; i < l; i += 3) {\n            if (ccw === true) {\n              indices.push(capIndices[i + 0], capIndices[i + 1], capIndices[i + 2]);\n            } else {\n              indices.push(capIndices[i + 0], capIndices[i + 2], capIndices[i + 1]);\n            }\n          }\n        }\n        if (endCap === true) {\n          const indexOffset = crossSectionCount * (spineCount - 1);\n          for (let i = 0, l = capIndices.length; i < l; i += 3) {\n            if (ccw === true) {\n              indices.push(indexOffset + capIndices[i + 0], indexOffset + capIndices[i + 2], indexOffset + capIndices[i + 1]);\n            } else {\n              indices.push(indexOffset + capIndices[i + 0], indexOffset + capIndices[i + 1], indexOffset + capIndices[i + 2]);\n            }\n          }\n        }\n      }\n      const positionAttribute = toNonIndexedAttribute(indices, new Float32BufferAttribute(vertices, 3));\n      const normalAttribute = computeNormalAttribute(indices, vertices, creaseAngle);\n      const geometry = new BufferGeometry();\n      geometry.setAttribute(\"position\", positionAttribute);\n      geometry.setAttribute(\"normal\", normalAttribute);\n      geometry._solid = solid;\n      geometry._type = \"mesh\";\n      return geometry;\n    }\n    function resolveUSE(identifier) {\n      const node = nodeMap[identifier];\n      const build = getNode(node);\n      return build.isObject3D || build.isMaterial ? build.clone() : build;\n    }\n    function parseFieldChildren(children, owner) {\n      for (let i = 0, l = children.length; i < l; i++) {\n        const object = getNode(children[i]);\n        if (object instanceof Object3D) owner.add(object);\n      }\n    }\n    function triangulateFaceIndex(index, ccw) {\n      const indices = [];\n      let start = 0;\n      for (let i = 0, l = index.length; i < l; i++) {\n        const i1 = index[start];\n        const i2 = index[i + (ccw ? 1 : 2)];\n        const i3 = index[i + (ccw ? 2 : 1)];\n        indices.push(i1, i2, i3);\n        if (index[i + 3] === -1 || i + 3 >= l) {\n          i += 3;\n          start = i + 1;\n        }\n      }\n      return indices;\n    }\n    function triangulateFaceData(data2, index) {\n      const triangulatedData = [];\n      let start = 0;\n      for (let i = 0, l = index.length; i < l; i++) {\n        const stride = start * 3;\n        const x = data2[stride];\n        const y = data2[stride + 1];\n        const z = data2[stride + 2];\n        triangulatedData.push(x, y, z);\n        if (index[i + 3] === -1 || i + 3 >= l) {\n          i += 3;\n          start++;\n        }\n      }\n      return triangulatedData;\n    }\n    function flattenData(data2, index) {\n      const flattenData2 = [];\n      for (let i = 0, l = index.length; i < l; i++) {\n        const i1 = index[i];\n        const stride = i1 * 3;\n        const x = data2[stride];\n        const y = data2[stride + 1];\n        const z = data2[stride + 2];\n        flattenData2.push(x, y, z);\n      }\n      return flattenData2;\n    }\n    function expandLineIndex(index) {\n      const indices = [];\n      for (let i = 0, l = index.length; i < l; i++) {\n        const i1 = index[i];\n        const i2 = index[i + 1];\n        indices.push(i1, i2);\n        if (index[i + 2] === -1 || i + 2 >= l) {\n          i += 2;\n        }\n      }\n      return indices;\n    }\n    function expandLineData(data2, index) {\n      const triangulatedData = [];\n      let start = 0;\n      for (let i = 0, l = index.length; i < l; i++) {\n        const stride = start * 3;\n        const x = data2[stride];\n        const y = data2[stride + 1];\n        const z = data2[stride + 2];\n        triangulatedData.push(x, y, z);\n        if (index[i + 2] === -1 || i + 2 >= l) {\n          i += 2;\n          start++;\n        }\n      }\n      return triangulatedData;\n    }\n    const vA = new Vector3();\n    const vB = new Vector3();\n    const vC = new Vector3();\n    const uvA = new Vector2();\n    const uvB = new Vector2();\n    const uvC = new Vector2();\n    function computeAttributeFromIndexedData(coordIndex, index, data2, itemSize) {\n      const array = [];\n      for (let i = 0, l = coordIndex.length; i < l; i += 3) {\n        const a = index[i];\n        const b = index[i + 1];\n        const c = index[i + 2];\n        if (itemSize === 2) {\n          uvA.fromArray(data2, a * itemSize);\n          uvB.fromArray(data2, b * itemSize);\n          uvC.fromArray(data2, c * itemSize);\n          array.push(uvA.x, uvA.y);\n          array.push(uvB.x, uvB.y);\n          array.push(uvC.x, uvC.y);\n        } else {\n          vA.fromArray(data2, a * itemSize);\n          vB.fromArray(data2, b * itemSize);\n          vC.fromArray(data2, c * itemSize);\n          array.push(vA.x, vA.y, vA.z);\n          array.push(vB.x, vB.y, vB.z);\n          array.push(vC.x, vC.y, vC.z);\n        }\n      }\n      return new Float32BufferAttribute(array, itemSize);\n    }\n    function computeAttributeFromFaceData(index, faceData) {\n      const array = [];\n      for (let i = 0, j = 0, l = index.length; i < l; i += 3, j++) {\n        vA.fromArray(faceData, j * 3);\n        array.push(vA.x, vA.y, vA.z);\n        array.push(vA.x, vA.y, vA.z);\n        array.push(vA.x, vA.y, vA.z);\n      }\n      return new Float32BufferAttribute(array, 3);\n    }\n    function computeAttributeFromLineData(index, lineData) {\n      const array = [];\n      for (let i = 0, j = 0, l = index.length; i < l; i += 2, j++) {\n        vA.fromArray(lineData, j * 3);\n        array.push(vA.x, vA.y, vA.z);\n        array.push(vA.x, vA.y, vA.z);\n      }\n      return new Float32BufferAttribute(array, 3);\n    }\n    function toNonIndexedAttribute(indices, attribute) {\n      const array = attribute.array;\n      const itemSize = attribute.itemSize;\n      const array2 = new array.constructor(indices.length * itemSize);\n      let index = 0,\n        index2 = 0;\n      for (let i = 0, l = indices.length; i < l; i++) {\n        index = indices[i] * itemSize;\n        for (let j = 0; j < itemSize; j++) {\n          array2[index2++] = array[index++];\n        }\n      }\n      return new Float32BufferAttribute(array2, itemSize);\n    }\n    const ab = new Vector3();\n    const cb = new Vector3();\n    function computeNormalAttribute(index, coord, creaseAngle) {\n      const faces = [];\n      const vertexNormals = {};\n      for (let i = 0, l = index.length; i < l; i += 3) {\n        const a = index[i];\n        const b = index[i + 1];\n        const c = index[i + 2];\n        const face = new Face(a, b, c);\n        vA.fromArray(coord, a * 3);\n        vB.fromArray(coord, b * 3);\n        vC.fromArray(coord, c * 3);\n        cb.subVectors(vC, vB);\n        ab.subVectors(vA, vB);\n        cb.cross(ab);\n        cb.normalize();\n        face.normal.copy(cb);\n        if (vertexNormals[a] === void 0) vertexNormals[a] = [];\n        if (vertexNormals[b] === void 0) vertexNormals[b] = [];\n        if (vertexNormals[c] === void 0) vertexNormals[c] = [];\n        vertexNormals[a].push(face.normal);\n        vertexNormals[b].push(face.normal);\n        vertexNormals[c].push(face.normal);\n        faces.push(face);\n      }\n      const normals = [];\n      for (let i = 0, l = faces.length; i < l; i++) {\n        const face = faces[i];\n        const nA = weightedNormal(vertexNormals[face.a], face.normal, creaseAngle);\n        const nB = weightedNormal(vertexNormals[face.b], face.normal, creaseAngle);\n        const nC = weightedNormal(vertexNormals[face.c], face.normal, creaseAngle);\n        vA.fromArray(coord, face.a * 3);\n        vB.fromArray(coord, face.b * 3);\n        vC.fromArray(coord, face.c * 3);\n        normals.push(nA.x, nA.y, nA.z);\n        normals.push(nB.x, nB.y, nB.z);\n        normals.push(nC.x, nC.y, nC.z);\n      }\n      return new Float32BufferAttribute(normals, 3);\n    }\n    function weightedNormal(normals, vector, creaseAngle) {\n      const normal = new Vector3();\n      if (creaseAngle === 0) {\n        normal.copy(vector);\n      } else {\n        for (let i = 0, l = normals.length; i < l; i++) {\n          if (normals[i].angleTo(vector) < creaseAngle) {\n            normal.add(normals[i]);\n          }\n        }\n      }\n      return normal.normalize();\n    }\n    function toColorArray(colors) {\n      const array = [];\n      for (let i = 0, l = colors.length; i < l; i += 3) {\n        array.push(new Color(colors[i], colors[i + 1], colors[i + 2]));\n      }\n      return array;\n    }\n    function paintFaces(geometry, radius, angles, colors, topDown) {\n      const thresholds = [];\n      const startAngle = topDown === true ? 0 : Math.PI;\n      for (let i = 0, l = colors.length; i < l; i++) {\n        let angle = i === 0 ? 0 : angles[i - 1];\n        angle = topDown === true ? angle : startAngle - angle;\n        const point = new Vector3();\n        point.setFromSphericalCoords(radius, angle, 0);\n        thresholds.push(point);\n      }\n      const indices = geometry.index;\n      const positionAttribute = geometry.attributes.position;\n      const colorAttribute = new BufferAttribute(new Float32Array(geometry.attributes.position.count * 3), 3);\n      const position = new Vector3();\n      const color = new Color();\n      for (let i = 0; i < indices.count; i++) {\n        const index = indices.getX(i);\n        position.fromBufferAttribute(positionAttribute, index);\n        let thresholdIndexA, thresholdIndexB;\n        let t = 1;\n        for (let j = 1; j < thresholds.length; j++) {\n          thresholdIndexA = j - 1;\n          thresholdIndexB = j;\n          const thresholdA = thresholds[thresholdIndexA];\n          const thresholdB = thresholds[thresholdIndexB];\n          if (topDown === true) {\n            if (position.y <= thresholdA.y && position.y > thresholdB.y) {\n              t = Math.abs(thresholdA.y - position.y) / Math.abs(thresholdA.y - thresholdB.y);\n              break;\n            }\n          } else {\n            if (position.y >= thresholdA.y && position.y < thresholdB.y) {\n              t = Math.abs(thresholdA.y - position.y) / Math.abs(thresholdA.y - thresholdB.y);\n              break;\n            }\n          }\n        }\n        const colorA = colors[thresholdIndexA];\n        const colorB = colors[thresholdIndexB];\n        color.copy(colorA).lerp(colorB, t);\n        colorAttribute.setXYZ(index, color.r, color.g, color.b);\n      }\n      geometry.setAttribute(\"color\", colorAttribute);\n    }\n    const textureLoader = new TextureLoader(this.manager);\n    textureLoader.setPath(this.resourcePath || path).setCrossOrigin(this.crossOrigin);\n    if (data.indexOf(\"#VRML V2.0\") === -1) {\n      throw Error(\"THREE.VRMLLexer: Version of VRML asset not supported.\");\n    }\n    const tree = generateVRMLTree(data);\n    const scene = parseTree(tree);\n    return scene;\n  }\n}\nclass VRMLLexer {\n  constructor(tokens) {\n    this.lexer = new Lexer(tokens);\n  }\n  lex(inputText) {\n    const lexingResult = this.lexer.tokenize(inputText);\n    if (lexingResult.errors.length > 0) {\n      console.error(lexingResult.errors);\n      throw Error(\"THREE.VRMLLexer: Lexing errors detected.\");\n    }\n    return lexingResult;\n  }\n}\nclass VRMLParser extends CstParser {\n  constructor(tokenVocabulary) {\n    super(tokenVocabulary);\n    const $ = this;\n    const Version = tokenVocabulary[\"Version\"];\n    const LCurly = tokenVocabulary[\"LCurly\"];\n    const RCurly = tokenVocabulary[\"RCurly\"];\n    const LSquare = tokenVocabulary[\"LSquare\"];\n    const RSquare = tokenVocabulary[\"RSquare\"];\n    const Identifier = tokenVocabulary[\"Identifier\"];\n    const RouteIdentifier = tokenVocabulary[\"RouteIdentifier\"];\n    const StringLiteral = tokenVocabulary[\"StringLiteral\"];\n    const HexLiteral = tokenVocabulary[\"HexLiteral\"];\n    const NumberLiteral = tokenVocabulary[\"NumberLiteral\"];\n    const TrueLiteral = tokenVocabulary[\"TrueLiteral\"];\n    const FalseLiteral = tokenVocabulary[\"FalseLiteral\"];\n    const NullLiteral = tokenVocabulary[\"NullLiteral\"];\n    const DEF = tokenVocabulary[\"DEF\"];\n    const USE = tokenVocabulary[\"USE\"];\n    const ROUTE = tokenVocabulary[\"ROUTE\"];\n    const TO = tokenVocabulary[\"TO\"];\n    const NodeName = tokenVocabulary[\"NodeName\"];\n    $.RULE(\"vrml\", function () {\n      $.SUBRULE($.version);\n      $.AT_LEAST_ONE(function () {\n        $.SUBRULE($.node);\n      });\n      $.MANY(function () {\n        $.SUBRULE($.route);\n      });\n    });\n    $.RULE(\"version\", function () {\n      $.CONSUME(Version);\n    });\n    $.RULE(\"node\", function () {\n      $.OPTION(function () {\n        $.SUBRULE($.def);\n      });\n      $.CONSUME(NodeName);\n      $.CONSUME(LCurly);\n      $.MANY(function () {\n        $.SUBRULE($.field);\n      });\n      $.CONSUME(RCurly);\n    });\n    $.RULE(\"field\", function () {\n      $.CONSUME(Identifier);\n      $.OR2([{\n        ALT: function () {\n          $.SUBRULE($.singleFieldValue);\n        }\n      }, {\n        ALT: function () {\n          $.SUBRULE($.multiFieldValue);\n        }\n      }]);\n    });\n    $.RULE(\"def\", function () {\n      $.CONSUME(DEF);\n      $.OR([{\n        ALT: function () {\n          $.CONSUME(Identifier);\n        }\n      }, {\n        ALT: function () {\n          $.CONSUME(NodeName);\n        }\n      }]);\n    });\n    $.RULE(\"use\", function () {\n      $.CONSUME(USE);\n      $.OR([{\n        ALT: function () {\n          $.CONSUME(Identifier);\n        }\n      }, {\n        ALT: function () {\n          $.CONSUME(NodeName);\n        }\n      }]);\n    });\n    $.RULE(\"singleFieldValue\", function () {\n      $.AT_LEAST_ONE(function () {\n        $.OR([{\n          ALT: function () {\n            $.SUBRULE($.node);\n          }\n        }, {\n          ALT: function () {\n            $.SUBRULE($.use);\n          }\n        }, {\n          ALT: function () {\n            $.CONSUME(StringLiteral);\n          }\n        }, {\n          ALT: function () {\n            $.CONSUME(HexLiteral);\n          }\n        }, {\n          ALT: function () {\n            $.CONSUME(NumberLiteral);\n          }\n        }, {\n          ALT: function () {\n            $.CONSUME(TrueLiteral);\n          }\n        }, {\n          ALT: function () {\n            $.CONSUME(FalseLiteral);\n          }\n        }, {\n          ALT: function () {\n            $.CONSUME(NullLiteral);\n          }\n        }]);\n      });\n    });\n    $.RULE(\"multiFieldValue\", function () {\n      $.CONSUME(LSquare);\n      $.MANY(function () {\n        $.OR([{\n          ALT: function () {\n            $.SUBRULE($.node);\n          }\n        }, {\n          ALT: function () {\n            $.SUBRULE($.use);\n          }\n        }, {\n          ALT: function () {\n            $.CONSUME(StringLiteral);\n          }\n        }, {\n          ALT: function () {\n            $.CONSUME(HexLiteral);\n          }\n        }, {\n          ALT: function () {\n            $.CONSUME(NumberLiteral);\n          }\n        }, {\n          ALT: function () {\n            $.CONSUME(NullLiteral);\n          }\n        }]);\n      });\n      $.CONSUME(RSquare);\n    });\n    $.RULE(\"route\", function () {\n      $.CONSUME(ROUTE);\n      $.CONSUME(RouteIdentifier);\n      $.CONSUME(TO);\n      $.CONSUME2(RouteIdentifier);\n    });\n    this.performSelfAnalysis();\n  }\n}\nclass Face {\n  constructor(a, b, c) {\n    this.a = a;\n    this.b = b;\n    this.c = c;\n    this.normal = new Vector3();\n  }\n}\nconst TEXTURE_TYPE = {\n  INTENSITY: 1,\n  INTENSITY_ALPHA: 2,\n  RGB: 3,\n  RGBA: 4\n};\nexport { VRMLLoader };", "map": {"version": 3, "names": ["VRMLLoader", "Loader", "constructor", "manager", "load", "url", "onLoad", "onProgress", "onError", "scope", "path", "LoaderUtils", "extractUrlBase", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "text", "parse", "e", "console", "error", "itemError", "data", "nodeMap", "generateVRMLTree", "data2", "tokenData", "createTokens", "lexer", "VRMLLexer", "tokens", "parser", "VRMLParser", "tokenVocabulary", "visitor", "createVisitor", "getBaseCstVisitorConstructor", "lexingResult", "lex", "input", "cstOutput", "vrml", "errors", "length", "Error", "ast", "visit", "RouteIdentifier", "createToken", "name", "pattern", "Identifier", "longer_alt", "nodeTypes", "Version", "NodeName", "RegExp", "join", "DEF", "USE", "ROUTE", "TO", "StringLiteral", "HexL<PERSON><PERSON>", "NumberLiteral", "TrueLiteral", "FalseLiteral", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LSquare", "RSquare", "L<PERSON><PERSON>", "RCurly", "Comment", "group", "<PERSON><PERSON>", "SKIPPED", "WhiteSpace", "i", "l", "token", "BaseVRMLVisitor", "VRMLToASTVisitor", "call", "validateVisitor", "prototype", "Object", "assign", "create", "ctx", "version", "nodes", "routes", "node", "push", "route", "image", "fields", "field", "def", "type", "values", "result", "singleFieldValue", "multiFieldValue", "use", "processField", "FROM", "stringLiteral", "replace", "numberLiteral", "parseFloat", "hexLiteral", "trueLiteral", "falseLiteral", "for<PERSON>ach", "parseTree", "tree2", "scene2", "Scene", "buildNodeMap", "object", "getNode", "Object3D", "add", "userData", "worldInfo", "field<PERSON><PERSON><PERSON>", "j", "jl", "resolveUSE", "build", "buildNode", "nodeName", "buildGroupingNode", "buildBackgroundNode", "buildShapeNode", "buildAppearanceNode", "buildMaterialNode", "buildImageTextureNode", "buildPixelTextureNode", "buildTextureTransformNode", "buildIndexedFaceSetNode", "buildIndexedLineSetNode", "buildPointSetNode", "buildBoxNode", "buildConeNode", "buildCylinderNode", "buildSphereNode", "buildElevationGridNode", "buildExtrusionNode", "buildGeometricNode", "buildWorldInfoNode", "warn", "hasOwnProperty", "Group", "fieldName", "parse<PERSON><PERSON><PERSON><PERSON><PERSON>n", "axis", "Vector3", "normalize", "angle", "quaternion", "setFromAxisAngle", "scale", "set", "position", "groundAngle", "groundColor", "skyAngle", "skyColor", "radius", "skyGeometry", "SphereGeometry", "skyMaterial", "MeshBasicMaterial", "fog", "side", "BackSide", "depthWrite", "depthTest", "paintFaces", "toColorArray", "vertexColors", "color", "setRGB", "sky", "<PERSON><PERSON>", "groundGeometry", "Math", "PI", "groundMaterial", "ground", "renderOrder", "Infinity", "material", "geometry", "attributes", "_type", "pointsMaterial", "PointsMaterial", "isMeshPhongMaterial", "copy", "emissive", "Points", "lineMaterial", "LineBasicMaterial", "LineSegments", "_solid", "FrontSide", "DoubleSide", "visible", "MeshPhongMaterial", "transformData", "materialData", "diffuseColor", "emissiveColor", "shininess", "specularColor", "specular", "transparency", "opacity", "transparent", "textureNode", "map", "__type", "TEXTURE_TYPE", "INTENSITY_ALPHA", "RGB", "RGBA", "center", "rotation", "repeat", "offset", "translation", "Color", "parseHexColor", "hex", "textureType", "value", "INTENSITY", "parseInt", "r", "g", "b", "a", "substring", "getTextureType", "num_components", "texture", "wrapS", "RepeatWrapping", "wrapT", "width", "height", "Uint8Array", "k", "stride", "DataTexture", "needsUpdate", "ClampToEdgeWrapping", "textureLoader", "Vector2", "title", "info", "coord", "normal", "texCoord", "ccw", "solid", "creaseAngle", "colorIndex", "coordIndex", "normalIndex", "texCoordIndex", "colorPerVertex", "normalPerVertex", "colorNode", "coordNode", "normalNode", "texCoordNode", "BufferGeometry", "triangulatedCoordIndex", "triangulateFaceIndex", "colorAttribute", "normalAttribute", "uvAttribute", "triangulatedColorIndex", "computeAttributeFromIndexedData", "toNonIndexedAttribute", "Float32BufferAttribute", "flattenFaceColors", "flattenData", "triangulatedFaceColors", "triangulateFaceData", "computeAttributeFromFaceData", "triangulatedNormalIndex", "flattenFaceNormals", "triangulatedFaceNormals", "computeNormalAttribute", "triangulatedTexCoordIndex", "positionAttribute", "setAttribute", "expandedLineIndex", "expandLineIndex", "expandedColorIndex", "flattenLineColors", "expandedLineColors", "expandLineData", "computeAttributeFromLineData", "size", "x", "y", "z", "BoxGeometry", "openEnded", "ConeGeometry", "CylinderGeometry", "xDimension", "zDimension", "xSpacing", "zSpacing", "vertices", "normals", "colors", "uvs", "index", "xn", "yn", "zn", "s", "t", "indices", "c", "d", "crossSection", "spine", "orientation", "beginCap", "endCap", "crossSectionClosed", "spineVector", "scaling", "vertex", "Quaternion", "o", "il", "fromArray", "kl", "multiply", "applyQuaternion", "spineCount", "crossSectionCount", "contour", "faces", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "triangulateShape", "capIndices", "face", "indexOffset", "identifier", "isObject3D", "isMaterial", "clone", "children", "owner", "start", "i1", "i2", "i3", "triangulatedData", "flattenData2", "vA", "vB", "vC", "uvA", "uvB", "uvC", "itemSize", "array", "faceData", "lineData", "attribute", "array2", "index2", "ab", "cb", "vertexNormals", "Face", "subVectors", "cross", "nA", "weightedNormal", "nB", "nC", "vector", "angleTo", "angles", "topDown", "thresholds", "startAngle", "point", "setFromSphericalCoords", "BufferAttribute", "Float32Array", "count", "getX", "fromBufferAttribute", "thresholdIndexA", "thresholdIndexB", "thresholdA", "thresholdB", "abs", "colorA", "colorB", "lerp", "setXYZ", "TextureLoader", "resourcePath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossOrigin", "indexOf", "tree", "scene", "inputText", "tokenize", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$", "RULE", "SUBRULE", "AT_LEAST_ONE", "MANY", "CONSUME", "OPTION", "OR2", "ALT", "OR", "CONSUME2", "performSelfAnalysis"], "sources": ["F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\node_modules\\src\\loaders\\VRMLLoader.js"], "sourcesContent": ["import {\n  BackSide,\n  BoxGeometry,\n  BufferAttribute,\n  BufferGeometry,\n  ClampToEdgeWrapping,\n  Color,\n  ConeGeometry,\n  CylinderGeometry,\n  DataTexture,\n  DoubleSide,\n  FileLoader,\n  Float32BufferAttribute,\n  FrontSide,\n  Group,\n  LineBasicMaterial,\n  LineSegments,\n  Loader,\n  LoaderUtils,\n  Mesh,\n  MeshBasicMaterial,\n  MeshPhongMaterial,\n  Object3D,\n  Points,\n  PointsMaterial,\n  Quaternion,\n  RepeatWrapping,\n  Scene,\n  ShapeUtils,\n  SphereGeometry,\n  TextureLoader,\n  Vector2,\n  Vector3,\n} from 'three'\nimport { Lexer, CstParser, createToken } from '../libs/chevrotain'\n\nclass VRMLLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const path = scope.path === '' ? LoaderUtils.extractUrlBase(url) : scope.path\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text, path))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(data, path) {\n    const nodeMap = {}\n\n    function generateVRMLTree(data) {\n      // create lexer, parser and visitor\n\n      const tokenData = createTokens()\n\n      const lexer = new VRMLLexer(tokenData.tokens)\n      const parser = new VRMLParser(tokenData.tokenVocabulary)\n      const visitor = createVisitor(parser.getBaseCstVisitorConstructor())\n\n      // lexing\n\n      const lexingResult = lexer.lex(data)\n      parser.input = lexingResult.tokens\n\n      // parsing\n\n      const cstOutput = parser.vrml()\n\n      if (parser.errors.length > 0) {\n        console.error(parser.errors)\n\n        throw Error('THREE.VRMLLoader: Parsing errors detected.')\n      }\n\n      // actions\n\n      const ast = visitor.visit(cstOutput)\n\n      return ast\n    }\n\n    function createTokens() {\n      // from http://gun.teipir.gr/VRML-amgem/spec/part1/concepts.html#SyntaxBasics\n\n      const RouteIdentifier = createToken({\n        name: 'RouteIdentifier',\n        pattern: /[^\\x30-\\x39\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d][^\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d]*[\\.][^\\x30-\\x39\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d][^\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d]*/,\n      })\n      const Identifier = createToken({\n        name: 'Identifier',\n        pattern: /[^\\x30-\\x39\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d][^\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d]*/,\n        longer_alt: RouteIdentifier,\n      })\n\n      // from http://gun.teipir.gr/VRML-amgem/spec/part1/nodesRef.html\n\n      const nodeTypes = [\n        'Anchor',\n        'Billboard',\n        'Collision',\n        'Group',\n        'Transform', // grouping nodes\n        'Inline',\n        'LOD',\n        'Switch', // special groups\n        'AudioClip',\n        'DirectionalLight',\n        'PointLight',\n        'Script',\n        'Shape',\n        'Sound',\n        'SpotLight',\n        'WorldInfo', // common nodes\n        'CylinderSensor',\n        'PlaneSensor',\n        'ProximitySensor',\n        'SphereSensor',\n        'TimeSensor',\n        'TouchSensor',\n        'VisibilitySensor', // sensors\n        'Box',\n        'Cone',\n        'Cylinder',\n        'ElevationGrid',\n        'Extrusion',\n        'IndexedFaceSet',\n        'IndexedLineSet',\n        'PointSet',\n        'Sphere', // geometries\n        'Color',\n        'Coordinate',\n        'Normal',\n        'TextureCoordinate', // geometric properties\n        'Appearance',\n        'FontStyle',\n        'ImageTexture',\n        'Material',\n        'MovieTexture',\n        'PixelTexture',\n        'TextureTransform', // appearance\n        'ColorInterpolator',\n        'CoordinateInterpolator',\n        'NormalInterpolator',\n        'OrientationInterpolator',\n        'PositionInterpolator',\n        'ScalarInterpolator', // interpolators\n        'Background',\n        'Fog',\n        'NavigationInfo',\n        'Viewpoint', // bindable nodes\n        'Text', // Text must be placed at the end of the regex so there are no matches for TextureTransform and TextureCoordinate\n      ]\n\n      //\n\n      const Version = createToken({\n        name: 'Version',\n        pattern: /#VRML.*/,\n        longer_alt: Identifier,\n      })\n\n      const NodeName = createToken({\n        name: 'NodeName',\n        pattern: new RegExp(nodeTypes.join('|')),\n        longer_alt: Identifier,\n      })\n\n      const DEF = createToken({\n        name: 'DEF',\n        pattern: /DEF/,\n        longer_alt: Identifier,\n      })\n\n      const USE = createToken({\n        name: 'USE',\n        pattern: /USE/,\n        longer_alt: Identifier,\n      })\n\n      const ROUTE = createToken({\n        name: 'ROUTE',\n        pattern: /ROUTE/,\n        longer_alt: Identifier,\n      })\n\n      const TO = createToken({\n        name: 'TO',\n        pattern: /TO/,\n        longer_alt: Identifier,\n      })\n\n      //\n\n      const StringLiteral = createToken({\n        name: 'StringLiteral',\n        pattern: /\"(?:[^\\\\\"\\n\\r]|\\\\[bfnrtv\"\\\\/]|\\\\u[0-9a-fA-F][0-9a-fA-F][0-9a-fA-F][0-9a-fA-F])*\"/,\n      })\n      const HexLiteral = createToken({ name: 'HexLiteral', pattern: /0[xX][0-9a-fA-F]+/ })\n      const NumberLiteral = createToken({ name: 'NumberLiteral', pattern: /[-+]?[0-9]*\\.?[0-9]+([eE][-+]?[0-9]+)?/ })\n      const TrueLiteral = createToken({ name: 'TrueLiteral', pattern: /TRUE/ })\n      const FalseLiteral = createToken({ name: 'FalseLiteral', pattern: /FALSE/ })\n      const NullLiteral = createToken({ name: 'NullLiteral', pattern: /NULL/ })\n      const LSquare = createToken({ name: 'LSquare', pattern: /\\[/ })\n      const RSquare = createToken({ name: 'RSquare', pattern: /]/ })\n      const LCurly = createToken({ name: 'LCurly', pattern: /{/ })\n      const RCurly = createToken({ name: 'RCurly', pattern: /}/ })\n      const Comment = createToken({\n        name: 'Comment',\n        pattern: /#.*/,\n        group: Lexer.SKIPPED,\n      })\n\n      // commas, blanks, tabs, newlines and carriage returns are whitespace characters wherever they appear outside of string fields\n\n      const WhiteSpace = createToken({\n        name: 'WhiteSpace',\n        pattern: /[ ,\\s]/,\n        group: Lexer.SKIPPED,\n      })\n\n      const tokens = [\n        WhiteSpace,\n        // keywords appear before the Identifier\n        NodeName,\n        DEF,\n        USE,\n        ROUTE,\n        TO,\n        TrueLiteral,\n        FalseLiteral,\n        NullLiteral,\n        // the Identifier must appear after the keywords because all keywords are valid identifiers\n        Version,\n        Identifier,\n        RouteIdentifier,\n        StringLiteral,\n        HexLiteral,\n        NumberLiteral,\n        LSquare,\n        RSquare,\n        LCurly,\n        RCurly,\n        Comment,\n      ]\n\n      const tokenVocabulary = {}\n\n      for (let i = 0, l = tokens.length; i < l; i++) {\n        const token = tokens[i]\n\n        tokenVocabulary[token.name] = token\n      }\n\n      return { tokens: tokens, tokenVocabulary: tokenVocabulary }\n    }\n\n    function createVisitor(BaseVRMLVisitor) {\n      // the visitor is created dynmaically based on the given base class\n\n      function VRMLToASTVisitor() {\n        BaseVRMLVisitor.call(this)\n\n        this.validateVisitor()\n      }\n\n      VRMLToASTVisitor.prototype = Object.assign(Object.create(BaseVRMLVisitor.prototype), {\n        constructor: VRMLToASTVisitor,\n\n        vrml: function (ctx) {\n          const data = {\n            version: this.visit(ctx.version),\n            nodes: [],\n            routes: [],\n          }\n\n          for (let i = 0, l = ctx.node.length; i < l; i++) {\n            const node = ctx.node[i]\n\n            data.nodes.push(this.visit(node))\n          }\n\n          if (ctx.route) {\n            for (let i = 0, l = ctx.route.length; i < l; i++) {\n              const route = ctx.route[i]\n\n              data.routes.push(this.visit(route))\n            }\n          }\n\n          return data\n        },\n\n        version: function (ctx) {\n          return ctx.Version[0].image\n        },\n\n        node: function (ctx) {\n          const data = {\n            name: ctx.NodeName[0].image,\n            fields: [],\n          }\n\n          if (ctx.field) {\n            for (let i = 0, l = ctx.field.length; i < l; i++) {\n              const field = ctx.field[i]\n\n              data.fields.push(this.visit(field))\n            }\n          }\n\n          // DEF\n\n          if (ctx.def) {\n            data.DEF = this.visit(ctx.def[0])\n          }\n\n          return data\n        },\n\n        field: function (ctx) {\n          const data = {\n            name: ctx.Identifier[0].image,\n            type: null,\n            values: null,\n          }\n\n          let result\n\n          // SFValue\n\n          if (ctx.singleFieldValue) {\n            result = this.visit(ctx.singleFieldValue[0])\n          }\n\n          // MFValue\n\n          if (ctx.multiFieldValue) {\n            result = this.visit(ctx.multiFieldValue[0])\n          }\n\n          data.type = result.type\n          data.values = result.values\n\n          return data\n        },\n\n        def: function (ctx) {\n          return (ctx.Identifier || ctx.NodeName)[0].image\n        },\n\n        use: function (ctx) {\n          return { USE: (ctx.Identifier || ctx.NodeName)[0].image }\n        },\n\n        singleFieldValue: function (ctx) {\n          return processField(this, ctx)\n        },\n\n        multiFieldValue: function (ctx) {\n          return processField(this, ctx)\n        },\n\n        route: function (ctx) {\n          const data = {\n            FROM: ctx.RouteIdentifier[0].image,\n            TO: ctx.RouteIdentifier[1].image,\n          }\n\n          return data\n        },\n      })\n\n      function processField(scope, ctx) {\n        const field = {\n          type: null,\n          values: [],\n        }\n\n        if (ctx.node) {\n          field.type = 'node'\n\n          for (let i = 0, l = ctx.node.length; i < l; i++) {\n            const node = ctx.node[i]\n\n            field.values.push(scope.visit(node))\n          }\n        }\n\n        if (ctx.use) {\n          field.type = 'use'\n\n          for (let i = 0, l = ctx.use.length; i < l; i++) {\n            const use = ctx.use[i]\n\n            field.values.push(scope.visit(use))\n          }\n        }\n\n        if (ctx.StringLiteral) {\n          field.type = 'string'\n\n          for (let i = 0, l = ctx.StringLiteral.length; i < l; i++) {\n            const stringLiteral = ctx.StringLiteral[i]\n\n            field.values.push(stringLiteral.image.replace(/'|\"/g, ''))\n          }\n        }\n\n        if (ctx.NumberLiteral) {\n          field.type = 'number'\n\n          for (let i = 0, l = ctx.NumberLiteral.length; i < l; i++) {\n            const numberLiteral = ctx.NumberLiteral[i]\n\n            field.values.push(parseFloat(numberLiteral.image))\n          }\n        }\n\n        if (ctx.HexLiteral) {\n          field.type = 'hex'\n\n          for (let i = 0, l = ctx.HexLiteral.length; i < l; i++) {\n            const hexLiteral = ctx.HexLiteral[i]\n\n            field.values.push(hexLiteral.image)\n          }\n        }\n\n        if (ctx.TrueLiteral) {\n          field.type = 'boolean'\n\n          for (let i = 0, l = ctx.TrueLiteral.length; i < l; i++) {\n            const trueLiteral = ctx.TrueLiteral[i]\n\n            if (trueLiteral.image === 'TRUE') field.values.push(true)\n          }\n        }\n\n        if (ctx.FalseLiteral) {\n          field.type = 'boolean'\n\n          for (let i = 0, l = ctx.FalseLiteral.length; i < l; i++) {\n            const falseLiteral = ctx.FalseLiteral[i]\n\n            if (falseLiteral.image === 'FALSE') field.values.push(false)\n          }\n        }\n\n        if (ctx.NullLiteral) {\n          field.type = 'null'\n\n          ctx.NullLiteral.forEach(function () {\n            field.values.push(null)\n          })\n        }\n\n        return field\n      }\n\n      return new VRMLToASTVisitor()\n    }\n\n    function parseTree(tree) {\n      // console.log( JSON.stringify( tree, null, 2 ) );\n\n      const nodes = tree.nodes\n      const scene = new Scene()\n\n      // first iteration: build nodemap based on DEF statements\n\n      for (let i = 0, l = nodes.length; i < l; i++) {\n        const node = nodes[i]\n\n        buildNodeMap(node)\n      }\n\n      // second iteration: build nodes\n\n      for (let i = 0, l = nodes.length; i < l; i++) {\n        const node = nodes[i]\n        const object = getNode(node)\n\n        if (object instanceof Object3D) scene.add(object)\n\n        if (node.name === 'WorldInfo') scene.userData.worldInfo = object\n      }\n\n      return scene\n    }\n\n    function buildNodeMap(node) {\n      if (node.DEF) {\n        nodeMap[node.DEF] = node\n      }\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n\n        if (field.type === 'node') {\n          const fieldValues = field.values\n\n          for (let j = 0, jl = fieldValues.length; j < jl; j++) {\n            buildNodeMap(fieldValues[j])\n          }\n        }\n      }\n    }\n\n    function getNode(node) {\n      // handle case where a node refers to a different one\n\n      if (node.USE) {\n        return resolveUSE(node.USE)\n      }\n\n      if (node.build !== undefined) return node.build\n\n      node.build = buildNode(node)\n\n      return node.build\n    }\n\n    // node builder\n\n    function buildNode(node) {\n      const nodeName = node.name\n      let build\n\n      switch (nodeName) {\n        case 'Group':\n        case 'Transform':\n        case 'Collision':\n          build = buildGroupingNode(node)\n          break\n\n        case 'Background':\n          build = buildBackgroundNode(node)\n          break\n\n        case 'Shape':\n          build = buildShapeNode(node)\n          break\n\n        case 'Appearance':\n          build = buildAppearanceNode(node)\n          break\n\n        case 'Material':\n          build = buildMaterialNode(node)\n          break\n\n        case 'ImageTexture':\n          build = buildImageTextureNode(node)\n          break\n\n        case 'PixelTexture':\n          build = buildPixelTextureNode(node)\n          break\n\n        case 'TextureTransform':\n          build = buildTextureTransformNode(node)\n          break\n\n        case 'IndexedFaceSet':\n          build = buildIndexedFaceSetNode(node)\n          break\n\n        case 'IndexedLineSet':\n          build = buildIndexedLineSetNode(node)\n          break\n\n        case 'PointSet':\n          build = buildPointSetNode(node)\n          break\n\n        case 'Box':\n          build = buildBoxNode(node)\n          break\n\n        case 'Cone':\n          build = buildConeNode(node)\n          break\n\n        case 'Cylinder':\n          build = buildCylinderNode(node)\n          break\n\n        case 'Sphere':\n          build = buildSphereNode(node)\n          break\n\n        case 'ElevationGrid':\n          build = buildElevationGridNode(node)\n          break\n\n        case 'Extrusion':\n          build = buildExtrusionNode(node)\n          break\n\n        case 'Color':\n        case 'Coordinate':\n        case 'Normal':\n        case 'TextureCoordinate':\n          build = buildGeometricNode(node)\n          break\n\n        case 'WorldInfo':\n          build = buildWorldInfoNode(node)\n          break\n\n        case 'Anchor':\n        case 'Billboard':\n\n        case 'Inline':\n        case 'LOD':\n        case 'Switch':\n\n        case 'AudioClip':\n        case 'DirectionalLight':\n        case 'PointLight':\n        case 'Script':\n        case 'Sound':\n        case 'SpotLight':\n\n        case 'CylinderSensor':\n        case 'PlaneSensor':\n        case 'ProximitySensor':\n        case 'SphereSensor':\n        case 'TimeSensor':\n        case 'TouchSensor':\n        case 'VisibilitySensor':\n\n        case 'Text':\n\n        case 'FontStyle':\n        case 'MovieTexture':\n\n        case 'ColorInterpolator':\n        case 'CoordinateInterpolator':\n        case 'NormalInterpolator':\n        case 'OrientationInterpolator':\n        case 'PositionInterpolator':\n        case 'ScalarInterpolator':\n\n        case 'Fog':\n        case 'NavigationInfo':\n        case 'Viewpoint':\n          // node not supported yet\n          break\n\n        default:\n          console.warn('THREE.VRMLLoader: Unknown node:', nodeName)\n          break\n      }\n\n      if (build !== undefined && node.DEF !== undefined && build.hasOwnProperty('name') === true) {\n        build.name = node.DEF\n      }\n\n      return build\n    }\n\n    function buildGroupingNode(node) {\n      const object = new Group()\n\n      //\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'bboxCenter':\n            // field not supported\n            break\n\n          case 'bboxSize':\n            // field not supported\n            break\n\n          case 'center':\n            // field not supported\n            break\n\n          case 'children':\n            parseFieldChildren(fieldValues, object)\n            break\n\n          case 'collide':\n            // field not supported\n            break\n\n          case 'rotation':\n            const axis = new Vector3(fieldValues[0], fieldValues[1], fieldValues[2]).normalize()\n            const angle = fieldValues[3]\n            object.quaternion.setFromAxisAngle(axis, angle)\n            break\n\n          case 'scale':\n            object.scale.set(fieldValues[0], fieldValues[1], fieldValues[2])\n            break\n\n          case 'scaleOrientation':\n            // field not supported\n            break\n\n          case 'translation':\n            object.position.set(fieldValues[0], fieldValues[1], fieldValues[2])\n            break\n\n          case 'proxy':\n            // field not supported\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      return object\n    }\n\n    function buildBackgroundNode(node) {\n      const group = new Group()\n\n      let groundAngle, groundColor\n      let skyAngle, skyColor\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'groundAngle':\n            groundAngle = fieldValues\n            break\n\n          case 'groundColor':\n            groundColor = fieldValues\n            break\n\n          case 'backUrl':\n            // field not supported\n            break\n\n          case 'bottomUrl':\n            // field not supported\n            break\n\n          case 'frontUrl':\n            // field not supported\n            break\n\n          case 'leftUrl':\n            // field not supported\n            break\n\n          case 'rightUrl':\n            // field not supported\n            break\n\n          case 'topUrl':\n            // field not supported\n            break\n\n          case 'skyAngle':\n            skyAngle = fieldValues\n            break\n\n          case 'skyColor':\n            skyColor = fieldValues\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      const radius = 10000\n\n      // sky\n\n      if (skyColor) {\n        const skyGeometry = new SphereGeometry(radius, 32, 16)\n        const skyMaterial = new MeshBasicMaterial({ fog: false, side: BackSide, depthWrite: false, depthTest: false })\n\n        if (skyColor.length > 3) {\n          paintFaces(skyGeometry, radius, skyAngle, toColorArray(skyColor), true)\n          skyMaterial.vertexColors = true\n        } else {\n          skyMaterial.color.setRGB(skyColor[0], skyColor[1], skyColor[2])\n        }\n\n        const sky = new Mesh(skyGeometry, skyMaterial)\n        group.add(sky)\n      }\n\n      // ground\n\n      if (groundColor) {\n        if (groundColor.length > 0) {\n          const groundGeometry = new SphereGeometry(radius, 32, 16, 0, 2 * Math.PI, 0.5 * Math.PI, 1.5 * Math.PI)\n          const groundMaterial = new MeshBasicMaterial({\n            fog: false,\n            side: BackSide,\n            vertexColors: true,\n            depthWrite: false,\n            depthTest: false,\n          })\n\n          paintFaces(groundGeometry, radius, groundAngle, toColorArray(groundColor), false)\n\n          const ground = new Mesh(groundGeometry, groundMaterial)\n          group.add(ground)\n        }\n      }\n\n      // render background group first\n\n      group.renderOrder = -Infinity\n\n      return group\n    }\n\n    function buildShapeNode(node) {\n      const fields = node.fields\n\n      // if the appearance field is NULL or unspecified, lighting is off and the unlit object color is (0, 0, 0)\n\n      let material = new MeshBasicMaterial({ color: 0x000000 })\n      let geometry\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'appearance':\n            if (fieldValues[0] !== null) {\n              material = getNode(fieldValues[0])\n            }\n\n            break\n\n          case 'geometry':\n            if (fieldValues[0] !== null) {\n              geometry = getNode(fieldValues[0])\n            }\n\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      // build 3D object\n\n      let object\n\n      if (geometry && geometry.attributes.position) {\n        const type = geometry._type\n\n        if (type === 'points') {\n          // points\n\n          const pointsMaterial = new PointsMaterial({ color: 0xffffff })\n\n          if (geometry.attributes.color !== undefined) {\n            pointsMaterial.vertexColors = true\n          } else {\n            // if the color field is NULL and there is a material defined for the appearance affecting this PointSet, then use the emissiveColor of the material to draw the points\n\n            if (material.isMeshPhongMaterial) {\n              pointsMaterial.color.copy(material.emissive)\n            }\n          }\n\n          object = new Points(geometry, pointsMaterial)\n        } else if (type === 'line') {\n          // lines\n\n          const lineMaterial = new LineBasicMaterial({ color: 0xffffff })\n\n          if (geometry.attributes.color !== undefined) {\n            lineMaterial.vertexColors = true\n          } else {\n            // if the color field is NULL and there is a material defined for the appearance affecting this IndexedLineSet, then use the emissiveColor of the material to draw the lines\n\n            if (material.isMeshPhongMaterial) {\n              lineMaterial.color.copy(material.emissive)\n            }\n          }\n\n          object = new LineSegments(geometry, lineMaterial)\n        } else {\n          // consider meshes\n\n          // check \"solid\" hint (it's placed in the geometry but affects the material)\n\n          if (geometry._solid !== undefined) {\n            material.side = geometry._solid ? FrontSide : DoubleSide\n          }\n\n          // check for vertex colors\n\n          if (geometry.attributes.color !== undefined) {\n            material.vertexColors = true\n          }\n\n          object = new Mesh(geometry, material)\n        }\n      } else {\n        object = new Object3D()\n\n        // if the geometry field is NULL or no vertices are defined the object is not drawn\n\n        object.visible = false\n      }\n\n      return object\n    }\n\n    function buildAppearanceNode(node) {\n      let material = new MeshPhongMaterial()\n      let transformData\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'material':\n            if (fieldValues[0] !== null) {\n              const materialData = getNode(fieldValues[0])\n\n              if (materialData.diffuseColor) material.color.copy(materialData.diffuseColor)\n              if (materialData.emissiveColor) material.emissive.copy(materialData.emissiveColor)\n              if (materialData.shininess) material.shininess = materialData.shininess\n              if (materialData.specularColor) material.specular.copy(materialData.specularColor)\n              if (materialData.transparency) material.opacity = 1 - materialData.transparency\n              if (materialData.transparency > 0) material.transparent = true\n            } else {\n              // if the material field is NULL or unspecified, lighting is off and the unlit object color is (0, 0, 0)\n\n              material = new MeshBasicMaterial({ color: 0x000000 })\n            }\n\n            break\n\n          case 'texture':\n            const textureNode = fieldValues[0]\n            if (textureNode !== null) {\n              if (textureNode.name === 'ImageTexture' || textureNode.name === 'PixelTexture') {\n                material.map = getNode(textureNode)\n              } else {\n                // MovieTexture not supported yet\n              }\n            }\n\n            break\n\n          case 'textureTransform':\n            if (fieldValues[0] !== null) {\n              transformData = getNode(fieldValues[0])\n            }\n\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      // only apply texture transform data if a texture was defined\n\n      if (material.map) {\n        // respect VRML lighting model\n\n        if (material.map.__type) {\n          switch (material.map.__type) {\n            case TEXTURE_TYPE.INTENSITY_ALPHA:\n              material.opacity = 1 // ignore transparency\n              break\n\n            case TEXTURE_TYPE.RGB:\n              material.color.set(0xffffff) // ignore material color\n              break\n\n            case TEXTURE_TYPE.RGBA:\n              material.color.set(0xffffff) // ignore material color\n              material.opacity = 1 // ignore transparency\n              break\n\n            default:\n          }\n\n          delete material.map.__type\n        }\n\n        // apply texture transform\n\n        if (transformData) {\n          material.map.center.copy(transformData.center)\n          material.map.rotation = transformData.rotation\n          material.map.repeat.copy(transformData.scale)\n          material.map.offset.copy(transformData.translation)\n        }\n      }\n\n      return material\n    }\n\n    function buildMaterialNode(node) {\n      const materialData = {}\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'ambientIntensity':\n            // field not supported\n            break\n\n          case 'diffuseColor':\n            materialData.diffuseColor = new Color(fieldValues[0], fieldValues[1], fieldValues[2])\n            break\n\n          case 'emissiveColor':\n            materialData.emissiveColor = new Color(fieldValues[0], fieldValues[1], fieldValues[2])\n            break\n\n          case 'shininess':\n            materialData.shininess = fieldValues[0]\n            break\n\n          case 'specularColor':\n            materialData.emissiveColor = new Color(fieldValues[0], fieldValues[1], fieldValues[2])\n            break\n\n          case 'transparency':\n            materialData.transparency = fieldValues[0]\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      return materialData\n    }\n\n    function parseHexColor(hex, textureType, color) {\n      let value\n\n      switch (textureType) {\n        case TEXTURE_TYPE.INTENSITY:\n          // Intensity texture: A one-component image specifies one-byte hexadecimal or integer values representing the intensity of the image\n          value = parseInt(hex)\n          color.r = value\n          color.g = value\n          color.b = value\n          color.a = 1\n          break\n\n        case TEXTURE_TYPE.INTENSITY_ALPHA:\n          // Intensity+Alpha texture: A two-component image specifies the intensity in the first (high) byte and the alpha opacity in the second (low) byte.\n          value = parseInt('0x' + hex.substring(2, 4))\n          color.r = value\n          color.g = value\n          color.b = value\n          color.a = parseInt('0x' + hex.substring(4, 6))\n          break\n\n        case TEXTURE_TYPE.RGB:\n          // RGB texture: Pixels in a three-component image specify the red component in the first (high) byte, followed by the green and blue components\n          color.r = parseInt('0x' + hex.substring(2, 4))\n          color.g = parseInt('0x' + hex.substring(4, 6))\n          color.b = parseInt('0x' + hex.substring(6, 8))\n          color.a = 1\n          break\n\n        case TEXTURE_TYPE.RGBA:\n          // RGBA texture: Four-component images specify the alpha opacity byte after red/green/blue\n          color.r = parseInt('0x' + hex.substring(2, 4))\n          color.g = parseInt('0x' + hex.substring(4, 6))\n          color.b = parseInt('0x' + hex.substring(6, 8))\n          color.a = parseInt('0x' + hex.substring(8, 10))\n          break\n\n        default:\n      }\n    }\n\n    function getTextureType(num_components) {\n      let type\n\n      switch (num_components) {\n        case 1:\n          type = TEXTURE_TYPE.INTENSITY\n          break\n\n        case 2:\n          type = TEXTURE_TYPE.INTENSITY_ALPHA\n          break\n\n        case 3:\n          type = TEXTURE_TYPE.RGB\n          break\n\n        case 4:\n          type = TEXTURE_TYPE.RGBA\n          break\n\n        default:\n      }\n\n      return type\n    }\n\n    function buildPixelTextureNode(node) {\n      let texture\n      let wrapS = RepeatWrapping\n      let wrapT = RepeatWrapping\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'image':\n            const width = fieldValues[0]\n            const height = fieldValues[1]\n            const num_components = fieldValues[2]\n\n            const textureType = getTextureType(num_components)\n\n            const data = new Uint8Array(4 * width * height)\n\n            const color = { r: 0, g: 0, b: 0, a: 0 }\n\n            for (let j = 3, k = 0, jl = fieldValues.length; j < jl; j++, k++) {\n              parseHexColor(fieldValues[j], textureType, color)\n\n              const stride = k * 4\n\n              data[stride + 0] = color.r\n              data[stride + 1] = color.g\n              data[stride + 2] = color.b\n              data[stride + 3] = color.a\n            }\n\n            texture = new DataTexture(data, width, height)\n            texture.needsUpdate = true\n            texture.__type = textureType // needed for material modifications\n            break\n\n          case 'repeatS':\n            if (fieldValues[0] === false) wrapS = ClampToEdgeWrapping\n            break\n\n          case 'repeatT':\n            if (fieldValues[0] === false) wrapT = ClampToEdgeWrapping\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      if (texture) {\n        texture.wrapS = wrapS\n        texture.wrapT = wrapT\n      }\n\n      return texture\n    }\n\n    function buildImageTextureNode(node) {\n      let texture\n      let wrapS = RepeatWrapping\n      let wrapT = RepeatWrapping\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'url':\n            const url = fieldValues[0]\n            if (url) texture = textureLoader.load(url)\n            break\n\n          case 'repeatS':\n            if (fieldValues[0] === false) wrapS = ClampToEdgeWrapping\n            break\n\n          case 'repeatT':\n            if (fieldValues[0] === false) wrapT = ClampToEdgeWrapping\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      if (texture) {\n        texture.wrapS = wrapS\n        texture.wrapT = wrapT\n      }\n\n      return texture\n    }\n\n    function buildTextureTransformNode(node) {\n      const transformData = {\n        center: new Vector2(),\n        rotation: new Vector2(),\n        scale: new Vector2(),\n        translation: new Vector2(),\n      }\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'center':\n            transformData.center.set(fieldValues[0], fieldValues[1])\n            break\n\n          case 'rotation':\n            transformData.rotation = fieldValues[0]\n            break\n\n          case 'scale':\n            transformData.scale.set(fieldValues[0], fieldValues[1])\n            break\n\n          case 'translation':\n            transformData.translation.set(fieldValues[0], fieldValues[1])\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      return transformData\n    }\n\n    function buildGeometricNode(node) {\n      return node.fields[0].values\n    }\n\n    function buildWorldInfoNode(node) {\n      const worldInfo = {}\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'title':\n            worldInfo.title = fieldValues[0]\n            break\n\n          case 'info':\n            worldInfo.info = fieldValues\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      return worldInfo\n    }\n\n    function buildIndexedFaceSetNode(node) {\n      let color, coord, normal, texCoord\n      let ccw = true,\n        solid = true,\n        creaseAngle = 0\n      let colorIndex, coordIndex, normalIndex, texCoordIndex\n      let colorPerVertex = true,\n        normalPerVertex = true\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'color':\n            const colorNode = fieldValues[0]\n\n            if (colorNode !== null) {\n              color = getNode(colorNode)\n            }\n\n            break\n\n          case 'coord':\n            const coordNode = fieldValues[0]\n\n            if (coordNode !== null) {\n              coord = getNode(coordNode)\n            }\n\n            break\n\n          case 'normal':\n            const normalNode = fieldValues[0]\n\n            if (normalNode !== null) {\n              normal = getNode(normalNode)\n            }\n\n            break\n\n          case 'texCoord':\n            const texCoordNode = fieldValues[0]\n\n            if (texCoordNode !== null) {\n              texCoord = getNode(texCoordNode)\n            }\n\n            break\n\n          case 'ccw':\n            ccw = fieldValues[0]\n            break\n\n          case 'colorIndex':\n            colorIndex = fieldValues\n            break\n\n          case 'colorPerVertex':\n            colorPerVertex = fieldValues[0]\n            break\n\n          case 'convex':\n            // field not supported\n            break\n\n          case 'coordIndex':\n            coordIndex = fieldValues\n            break\n\n          case 'creaseAngle':\n            creaseAngle = fieldValues[0]\n            break\n\n          case 'normalIndex':\n            normalIndex = fieldValues\n            break\n\n          case 'normalPerVertex':\n            normalPerVertex = fieldValues[0]\n            break\n\n          case 'solid':\n            solid = fieldValues[0]\n            break\n\n          case 'texCoordIndex':\n            texCoordIndex = fieldValues\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      if (coordIndex === undefined) {\n        console.warn('THREE.VRMLLoader: Missing coordIndex.')\n\n        return new BufferGeometry() // handle VRML files with incomplete geometry definition\n      }\n\n      const triangulatedCoordIndex = triangulateFaceIndex(coordIndex, ccw)\n\n      let colorAttribute\n      let normalAttribute\n      let uvAttribute\n\n      if (color) {\n        if (colorPerVertex === true) {\n          if (colorIndex && colorIndex.length > 0) {\n            // if the colorIndex field is not empty, then it is used to choose colors for each vertex of the IndexedFaceSet.\n\n            const triangulatedColorIndex = triangulateFaceIndex(colorIndex, ccw)\n            colorAttribute = computeAttributeFromIndexedData(triangulatedCoordIndex, triangulatedColorIndex, color, 3)\n          } else {\n            // if the colorIndex field is empty, then the coordIndex field is used to choose colors from the Color node\n\n            colorAttribute = toNonIndexedAttribute(triangulatedCoordIndex, new Float32BufferAttribute(color, 3))\n          }\n        } else {\n          if (colorIndex && colorIndex.length > 0) {\n            // if the colorIndex field is not empty, then they are used to choose one color for each face of the IndexedFaceSet\n\n            const flattenFaceColors = flattenData(color, colorIndex)\n            const triangulatedFaceColors = triangulateFaceData(flattenFaceColors, coordIndex)\n            colorAttribute = computeAttributeFromFaceData(triangulatedCoordIndex, triangulatedFaceColors)\n          } else {\n            // if the colorIndex field is empty, then the color are applied to each face of the IndexedFaceSet in order\n\n            const triangulatedFaceColors = triangulateFaceData(color, coordIndex)\n            colorAttribute = computeAttributeFromFaceData(triangulatedCoordIndex, triangulatedFaceColors)\n          }\n        }\n      }\n\n      if (normal) {\n        if (normalPerVertex === true) {\n          // consider vertex normals\n\n          if (normalIndex && normalIndex.length > 0) {\n            // if the normalIndex field is not empty, then it is used to choose normals for each vertex of the IndexedFaceSet.\n\n            const triangulatedNormalIndex = triangulateFaceIndex(normalIndex, ccw)\n            normalAttribute = computeAttributeFromIndexedData(\n              triangulatedCoordIndex,\n              triangulatedNormalIndex,\n              normal,\n              3,\n            )\n          } else {\n            // if the normalIndex field is empty, then the coordIndex field is used to choose normals from the Normal node\n\n            normalAttribute = toNonIndexedAttribute(triangulatedCoordIndex, new Float32BufferAttribute(normal, 3))\n          }\n        } else {\n          // consider face normals\n\n          if (normalIndex && normalIndex.length > 0) {\n            // if the normalIndex field is not empty, then they are used to choose one normal for each face of the IndexedFaceSet\n\n            const flattenFaceNormals = flattenData(normal, normalIndex)\n            const triangulatedFaceNormals = triangulateFaceData(flattenFaceNormals, coordIndex)\n            normalAttribute = computeAttributeFromFaceData(triangulatedCoordIndex, triangulatedFaceNormals)\n          } else {\n            // if the normalIndex field is empty, then the normals are applied to each face of the IndexedFaceSet in order\n\n            const triangulatedFaceNormals = triangulateFaceData(normal, coordIndex)\n            normalAttribute = computeAttributeFromFaceData(triangulatedCoordIndex, triangulatedFaceNormals)\n          }\n        }\n      } else {\n        // if the normal field is NULL, then the loader should automatically generate normals, using creaseAngle to determine if and how normals are smoothed across shared vertices\n\n        normalAttribute = computeNormalAttribute(triangulatedCoordIndex, coord, creaseAngle)\n      }\n\n      if (texCoord) {\n        // texture coordinates are always defined on vertex level\n\n        if (texCoordIndex && texCoordIndex.length > 0) {\n          // if the texCoordIndex field is not empty, then it is used to choose texture coordinates for each vertex of the IndexedFaceSet.\n\n          const triangulatedTexCoordIndex = triangulateFaceIndex(texCoordIndex, ccw)\n          uvAttribute = computeAttributeFromIndexedData(triangulatedCoordIndex, triangulatedTexCoordIndex, texCoord, 2)\n        } else {\n          // if the texCoordIndex field is empty, then the coordIndex array is used to choose texture coordinates from the TextureCoordinate node\n\n          uvAttribute = toNonIndexedAttribute(triangulatedCoordIndex, new Float32BufferAttribute(texCoord, 2))\n        }\n      }\n\n      const geometry = new BufferGeometry()\n      const positionAttribute = toNonIndexedAttribute(triangulatedCoordIndex, new Float32BufferAttribute(coord, 3))\n\n      geometry.setAttribute('position', positionAttribute)\n      geometry.setAttribute('normal', normalAttribute)\n\n      // optional attributes\n\n      if (colorAttribute) geometry.setAttribute('color', colorAttribute)\n      if (uvAttribute) geometry.setAttribute('uv', uvAttribute)\n\n      // \"solid\" influences the material so let's store it for later use\n\n      geometry._solid = solid\n      geometry._type = 'mesh'\n\n      return geometry\n    }\n\n    function buildIndexedLineSetNode(node) {\n      let color, coord\n      let colorIndex, coordIndex\n      let colorPerVertex = true\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'color':\n            const colorNode = fieldValues[0]\n\n            if (colorNode !== null) {\n              color = getNode(colorNode)\n            }\n\n            break\n\n          case 'coord':\n            const coordNode = fieldValues[0]\n\n            if (coordNode !== null) {\n              coord = getNode(coordNode)\n            }\n\n            break\n\n          case 'colorIndex':\n            colorIndex = fieldValues\n            break\n\n          case 'colorPerVertex':\n            colorPerVertex = fieldValues[0]\n            break\n\n          case 'coordIndex':\n            coordIndex = fieldValues\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      // build lines\n\n      let colorAttribute\n\n      const expandedLineIndex = expandLineIndex(coordIndex) // create an index for three.js's linesegment primitive\n\n      if (color) {\n        if (colorPerVertex === true) {\n          if (colorIndex.length > 0) {\n            // if the colorIndex field is not empty, then one color is used for each polyline of the IndexedLineSet.\n\n            const expandedColorIndex = expandLineIndex(colorIndex) // compute colors for each line segment (rendering primitve)\n            colorAttribute = computeAttributeFromIndexedData(expandedLineIndex, expandedColorIndex, color, 3) // compute data on vertex level\n          } else {\n            // if the colorIndex field is empty, then the colors are applied to each polyline of the IndexedLineSet in order.\n\n            colorAttribute = toNonIndexedAttribute(expandedLineIndex, new Float32BufferAttribute(color, 3))\n          }\n        } else {\n          if (colorIndex.length > 0) {\n            // if the colorIndex field is not empty, then colors are applied to each vertex of the IndexedLineSet\n\n            const flattenLineColors = flattenData(color, colorIndex) // compute colors for each VRML primitve\n            const expandedLineColors = expandLineData(flattenLineColors, coordIndex) // compute colors for each line segment (rendering primitve)\n            colorAttribute = computeAttributeFromLineData(expandedLineIndex, expandedLineColors) // compute data on vertex level\n          } else {\n            // if the colorIndex field is empty, then the coordIndex field is used to choose colors from the Color node\n\n            const expandedLineColors = expandLineData(color, coordIndex) // compute colors for each line segment (rendering primitve)\n            colorAttribute = computeAttributeFromLineData(expandedLineIndex, expandedLineColors) // compute data on vertex level\n          }\n        }\n      }\n\n      //\n\n      const geometry = new BufferGeometry()\n\n      const positionAttribute = toNonIndexedAttribute(expandedLineIndex, new Float32BufferAttribute(coord, 3))\n      geometry.setAttribute('position', positionAttribute)\n\n      if (colorAttribute) geometry.setAttribute('color', colorAttribute)\n\n      geometry._type = 'line'\n\n      return geometry\n    }\n\n    function buildPointSetNode(node) {\n      let color, coord\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'color':\n            const colorNode = fieldValues[0]\n\n            if (colorNode !== null) {\n              color = getNode(colorNode)\n            }\n\n            break\n\n          case 'coord':\n            const coordNode = fieldValues[0]\n\n            if (coordNode !== null) {\n              coord = getNode(coordNode)\n            }\n\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      const geometry = new BufferGeometry()\n\n      geometry.setAttribute('position', new Float32BufferAttribute(coord, 3))\n      if (color) geometry.setAttribute('color', new Float32BufferAttribute(color, 3))\n\n      geometry._type = 'points'\n\n      return geometry\n    }\n\n    function buildBoxNode(node) {\n      const size = new Vector3(2, 2, 2)\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'size':\n            size.x = fieldValues[0]\n            size.y = fieldValues[1]\n            size.z = fieldValues[2]\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      const geometry = new BoxGeometry(size.x, size.y, size.z)\n\n      return geometry\n    }\n\n    function buildConeNode(node) {\n      let radius = 1,\n        height = 2,\n        openEnded = false\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'bottom':\n            openEnded = !fieldValues[0]\n            break\n\n          case 'bottomRadius':\n            radius = fieldValues[0]\n            break\n\n          case 'height':\n            height = fieldValues[0]\n            break\n\n          case 'side':\n            // field not supported\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      const geometry = new ConeGeometry(radius, height, 16, 1, openEnded)\n\n      return geometry\n    }\n\n    function buildCylinderNode(node) {\n      let radius = 1,\n        height = 2\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'bottom':\n            // field not supported\n            break\n\n          case 'radius':\n            radius = fieldValues[0]\n            break\n\n          case 'height':\n            height = fieldValues[0]\n            break\n\n          case 'side':\n            // field not supported\n            break\n\n          case 'top':\n            // field not supported\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      const geometry = new CylinderGeometry(radius, radius, height, 16, 1)\n\n      return geometry\n    }\n\n    function buildSphereNode(node) {\n      let radius = 1\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'radius':\n            radius = fieldValues[0]\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      const geometry = new SphereGeometry(radius, 16, 16)\n\n      return geometry\n    }\n\n    function buildElevationGridNode(node) {\n      let color\n      let normal\n      let texCoord\n      let height\n\n      let colorPerVertex = true\n      let normalPerVertex = true\n      let solid = true\n      let ccw = true\n      let creaseAngle = 0\n      let xDimension = 2\n      let zDimension = 2\n      let xSpacing = 1\n      let zSpacing = 1\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'color':\n            const colorNode = fieldValues[0]\n\n            if (colorNode !== null) {\n              color = getNode(colorNode)\n            }\n\n            break\n\n          case 'normal':\n            const normalNode = fieldValues[0]\n\n            if (normalNode !== null) {\n              normal = getNode(normalNode)\n            }\n\n            break\n\n          case 'texCoord':\n            const texCoordNode = fieldValues[0]\n\n            if (texCoordNode !== null) {\n              texCoord = getNode(texCoordNode)\n            }\n\n            break\n\n          case 'height':\n            height = fieldValues\n            break\n\n          case 'ccw':\n            ccw = fieldValues[0]\n            break\n\n          case 'colorPerVertex':\n            colorPerVertex = fieldValues[0]\n            break\n\n          case 'creaseAngle':\n            creaseAngle = fieldValues[0]\n            break\n\n          case 'normalPerVertex':\n            normalPerVertex = fieldValues[0]\n            break\n\n          case 'solid':\n            solid = fieldValues[0]\n            break\n\n          case 'xDimension':\n            xDimension = fieldValues[0]\n            break\n\n          case 'xSpacing':\n            xSpacing = fieldValues[0]\n            break\n\n          case 'zDimension':\n            zDimension = fieldValues[0]\n            break\n\n          case 'zSpacing':\n            zSpacing = fieldValues[0]\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      // vertex data\n\n      const vertices = []\n      const normals = []\n      const colors = []\n      const uvs = []\n\n      for (let i = 0; i < zDimension; i++) {\n        for (let j = 0; j < xDimension; j++) {\n          // compute a row major index\n\n          const index = i * xDimension + j\n\n          // vertices\n\n          const x = xSpacing * i\n          const y = height[index]\n          const z = zSpacing * j\n\n          vertices.push(x, y, z)\n\n          // colors\n\n          if (color && colorPerVertex === true) {\n            const r = color[index * 3 + 0]\n            const g = color[index * 3 + 1]\n            const b = color[index * 3 + 2]\n\n            colors.push(r, g, b)\n          }\n\n          // normals\n\n          if (normal && normalPerVertex === true) {\n            const xn = normal[index * 3 + 0]\n            const yn = normal[index * 3 + 1]\n            const zn = normal[index * 3 + 2]\n\n            normals.push(xn, yn, zn)\n          }\n\n          // uvs\n\n          if (texCoord) {\n            const s = texCoord[index * 2 + 0]\n            const t = texCoord[index * 2 + 1]\n\n            uvs.push(s, t)\n          } else {\n            uvs.push(i / (xDimension - 1), j / (zDimension - 1))\n          }\n        }\n      }\n\n      // indices\n\n      const indices = []\n\n      for (let i = 0; i < xDimension - 1; i++) {\n        for (let j = 0; j < zDimension - 1; j++) {\n          // from https://tecfa.unige.ch/guides/vrml/vrml97/spec/part1/nodesRef.html#ElevationGrid\n\n          const a = i + j * xDimension\n          const b = i + (j + 1) * xDimension\n          const c = i + 1 + (j + 1) * xDimension\n          const d = i + 1 + j * xDimension\n\n          // faces\n\n          if (ccw === true) {\n            indices.push(a, c, b)\n            indices.push(c, a, d)\n          } else {\n            indices.push(a, b, c)\n            indices.push(c, d, a)\n          }\n        }\n      }\n\n      //\n\n      const positionAttribute = toNonIndexedAttribute(indices, new Float32BufferAttribute(vertices, 3))\n      const uvAttribute = toNonIndexedAttribute(indices, new Float32BufferAttribute(uvs, 2))\n      let colorAttribute\n      let normalAttribute\n\n      // color attribute\n\n      if (color) {\n        if (colorPerVertex === false) {\n          for (let i = 0; i < xDimension - 1; i++) {\n            for (let j = 0; j < zDimension - 1; j++) {\n              const index = i + j * (xDimension - 1)\n\n              const r = color[index * 3 + 0]\n              const g = color[index * 3 + 1]\n              const b = color[index * 3 + 2]\n\n              // one color per quad\n\n              colors.push(r, g, b)\n              colors.push(r, g, b)\n              colors.push(r, g, b)\n              colors.push(r, g, b)\n              colors.push(r, g, b)\n              colors.push(r, g, b)\n            }\n          }\n\n          colorAttribute = new Float32BufferAttribute(colors, 3)\n        } else {\n          colorAttribute = toNonIndexedAttribute(indices, new Float32BufferAttribute(colors, 3))\n        }\n      }\n\n      // normal attribute\n\n      if (normal) {\n        if (normalPerVertex === false) {\n          for (let i = 0; i < xDimension - 1; i++) {\n            for (let j = 0; j < zDimension - 1; j++) {\n              const index = i + j * (xDimension - 1)\n\n              const xn = normal[index * 3 + 0]\n              const yn = normal[index * 3 + 1]\n              const zn = normal[index * 3 + 2]\n\n              // one normal per quad\n\n              normals.push(xn, yn, zn)\n              normals.push(xn, yn, zn)\n              normals.push(xn, yn, zn)\n              normals.push(xn, yn, zn)\n              normals.push(xn, yn, zn)\n              normals.push(xn, yn, zn)\n            }\n          }\n\n          normalAttribute = new Float32BufferAttribute(normals, 3)\n        } else {\n          normalAttribute = toNonIndexedAttribute(indices, new Float32BufferAttribute(normals, 3))\n        }\n      } else {\n        normalAttribute = computeNormalAttribute(indices, vertices, creaseAngle)\n      }\n\n      // build geometry\n\n      const geometry = new BufferGeometry()\n      geometry.setAttribute('position', positionAttribute)\n      geometry.setAttribute('normal', normalAttribute)\n      geometry.setAttribute('uv', uvAttribute)\n\n      if (colorAttribute) geometry.setAttribute('color', colorAttribute)\n\n      // \"solid\" influences the material so let's store it for later use\n\n      geometry._solid = solid\n      geometry._type = 'mesh'\n\n      return geometry\n    }\n\n    function buildExtrusionNode(node) {\n      let crossSection = [1, 1, 1, -1, -1, -1, -1, 1, 1, 1]\n      let spine = [0, 0, 0, 0, 1, 0]\n      let scale\n      let orientation\n\n      let beginCap = true\n      let ccw = true\n      let creaseAngle = 0\n      let endCap = true\n      let solid = true\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'beginCap':\n            beginCap = fieldValues[0]\n            break\n\n          case 'ccw':\n            ccw = fieldValues[0]\n            break\n\n          case 'convex':\n            // field not supported\n            break\n\n          case 'creaseAngle':\n            creaseAngle = fieldValues[0]\n            break\n\n          case 'crossSection':\n            crossSection = fieldValues\n            break\n\n          case 'endCap':\n            endCap = fieldValues[0]\n            break\n\n          case 'orientation':\n            orientation = fieldValues\n            break\n\n          case 'scale':\n            scale = fieldValues\n            break\n\n          case 'solid':\n            solid = fieldValues[0]\n            break\n\n          case 'spine':\n            spine = fieldValues // only extrusion along the Y-axis are supported so far\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      const crossSectionClosed =\n        crossSection[0] === crossSection[crossSection.length - 2] &&\n        crossSection[1] === crossSection[crossSection.length - 1]\n\n      // vertices\n\n      const vertices = []\n      const spineVector = new Vector3()\n      const scaling = new Vector3()\n\n      const axis = new Vector3()\n      const vertex = new Vector3()\n      const quaternion = new Quaternion()\n\n      for (let i = 0, j = 0, o = 0, il = spine.length; i < il; i += 3, j += 2, o += 4) {\n        spineVector.fromArray(spine, i)\n\n        scaling.x = scale ? scale[j + 0] : 1\n        scaling.y = 1\n        scaling.z = scale ? scale[j + 1] : 1\n\n        axis.x = orientation ? orientation[o + 0] : 0\n        axis.y = orientation ? orientation[o + 1] : 0\n        axis.z = orientation ? orientation[o + 2] : 1\n        const angle = orientation ? orientation[o + 3] : 0\n\n        for (let k = 0, kl = crossSection.length; k < kl; k += 2) {\n          vertex.x = crossSection[k + 0]\n          vertex.y = 0\n          vertex.z = crossSection[k + 1]\n\n          // scale\n\n          vertex.multiply(scaling)\n\n          // rotate\n\n          quaternion.setFromAxisAngle(axis, angle)\n          vertex.applyQuaternion(quaternion)\n\n          // translate\n\n          vertex.add(spineVector)\n\n          vertices.push(vertex.x, vertex.y, vertex.z)\n        }\n      }\n\n      // indices\n\n      const indices = []\n\n      const spineCount = spine.length / 3\n      const crossSectionCount = crossSection.length / 2\n\n      for (let i = 0; i < spineCount - 1; i++) {\n        for (let j = 0; j < crossSectionCount - 1; j++) {\n          const a = j + i * crossSectionCount\n          let b = j + 1 + i * crossSectionCount\n          const c = j + (i + 1) * crossSectionCount\n          let d = j + 1 + (i + 1) * crossSectionCount\n\n          if (j === crossSectionCount - 2 && crossSectionClosed === true) {\n            b = i * crossSectionCount\n            d = (i + 1) * crossSectionCount\n          }\n\n          if (ccw === true) {\n            indices.push(a, b, c)\n            indices.push(c, b, d)\n          } else {\n            indices.push(a, c, b)\n            indices.push(c, d, b)\n          }\n        }\n      }\n\n      // triangulate cap\n\n      if (beginCap === true || endCap === true) {\n        const contour = []\n\n        for (let i = 0, l = crossSection.length; i < l; i += 2) {\n          contour.push(new Vector2(crossSection[i], crossSection[i + 1]))\n        }\n\n        const faces = ShapeUtils.triangulateShape(contour, [])\n        const capIndices = []\n\n        for (let i = 0, l = faces.length; i < l; i++) {\n          const face = faces[i]\n\n          capIndices.push(face[0], face[1], face[2])\n        }\n\n        // begin cap\n\n        if (beginCap === true) {\n          for (let i = 0, l = capIndices.length; i < l; i += 3) {\n            if (ccw === true) {\n              indices.push(capIndices[i + 0], capIndices[i + 1], capIndices[i + 2])\n            } else {\n              indices.push(capIndices[i + 0], capIndices[i + 2], capIndices[i + 1])\n            }\n          }\n        }\n\n        // end cap\n\n        if (endCap === true) {\n          const indexOffset = crossSectionCount * (spineCount - 1) // references to the first vertex of the last cross section\n\n          for (let i = 0, l = capIndices.length; i < l; i += 3) {\n            if (ccw === true) {\n              indices.push(\n                indexOffset + capIndices[i + 0],\n                indexOffset + capIndices[i + 2],\n                indexOffset + capIndices[i + 1],\n              )\n            } else {\n              indices.push(\n                indexOffset + capIndices[i + 0],\n                indexOffset + capIndices[i + 1],\n                indexOffset + capIndices[i + 2],\n              )\n            }\n          }\n        }\n      }\n\n      const positionAttribute = toNonIndexedAttribute(indices, new Float32BufferAttribute(vertices, 3))\n      const normalAttribute = computeNormalAttribute(indices, vertices, creaseAngle)\n\n      const geometry = new BufferGeometry()\n      geometry.setAttribute('position', positionAttribute)\n      geometry.setAttribute('normal', normalAttribute)\n      // no uvs yet\n\n      // \"solid\" influences the material so let's store it for later use\n\n      geometry._solid = solid\n      geometry._type = 'mesh'\n\n      return geometry\n    }\n\n    // helper functions\n\n    function resolveUSE(identifier) {\n      const node = nodeMap[identifier]\n      const build = getNode(node)\n\n      // because the same 3D objects can have different transformations, it's necessary to clone them.\n      // materials can be influenced by the geometry (e.g. vertex normals). cloning is necessary to avoid\n      // any side effects\n\n      return build.isObject3D || build.isMaterial ? build.clone() : build\n    }\n\n    function parseFieldChildren(children, owner) {\n      for (let i = 0, l = children.length; i < l; i++) {\n        const object = getNode(children[i])\n\n        if (object instanceof Object3D) owner.add(object)\n      }\n    }\n\n    function triangulateFaceIndex(index, ccw) {\n      const indices = []\n\n      // since face defintions can have more than three vertices, it's necessary to\n      // perform a simple triangulation\n\n      let start = 0\n\n      for (let i = 0, l = index.length; i < l; i++) {\n        const i1 = index[start]\n        const i2 = index[i + (ccw ? 1 : 2)]\n        const i3 = index[i + (ccw ? 2 : 1)]\n\n        indices.push(i1, i2, i3)\n\n        // an index of -1 indicates that the current face has ended and the next one begins\n\n        if (index[i + 3] === -1 || i + 3 >= l) {\n          i += 3\n          start = i + 1\n        }\n      }\n\n      return indices\n    }\n\n    function triangulateFaceData(data, index) {\n      const triangulatedData = []\n\n      let start = 0\n\n      for (let i = 0, l = index.length; i < l; i++) {\n        const stride = start * 3\n\n        const x = data[stride]\n        const y = data[stride + 1]\n        const z = data[stride + 2]\n\n        triangulatedData.push(x, y, z)\n\n        // an index of -1 indicates that the current face has ended and the next one begins\n\n        if (index[i + 3] === -1 || i + 3 >= l) {\n          i += 3\n          start++\n        }\n      }\n\n      return triangulatedData\n    }\n\n    function flattenData(data, index) {\n      const flattenData = []\n\n      for (let i = 0, l = index.length; i < l; i++) {\n        const i1 = index[i]\n\n        const stride = i1 * 3\n\n        const x = data[stride]\n        const y = data[stride + 1]\n        const z = data[stride + 2]\n\n        flattenData.push(x, y, z)\n      }\n\n      return flattenData\n    }\n\n    function expandLineIndex(index) {\n      const indices = []\n\n      for (let i = 0, l = index.length; i < l; i++) {\n        const i1 = index[i]\n        const i2 = index[i + 1]\n\n        indices.push(i1, i2)\n\n        // an index of -1 indicates that the current line has ended and the next one begins\n\n        if (index[i + 2] === -1 || i + 2 >= l) {\n          i += 2\n        }\n      }\n\n      return indices\n    }\n\n    function expandLineData(data, index) {\n      const triangulatedData = []\n\n      let start = 0\n\n      for (let i = 0, l = index.length; i < l; i++) {\n        const stride = start * 3\n\n        const x = data[stride]\n        const y = data[stride + 1]\n        const z = data[stride + 2]\n\n        triangulatedData.push(x, y, z)\n\n        // an index of -1 indicates that the current line has ended and the next one begins\n\n        if (index[i + 2] === -1 || i + 2 >= l) {\n          i += 2\n          start++\n        }\n      }\n\n      return triangulatedData\n    }\n\n    const vA = new Vector3()\n    const vB = new Vector3()\n    const vC = new Vector3()\n\n    const uvA = new Vector2()\n    const uvB = new Vector2()\n    const uvC = new Vector2()\n\n    function computeAttributeFromIndexedData(coordIndex, index, data, itemSize) {\n      const array = []\n\n      // we use the coordIndex.length as delimiter since normalIndex must contain at least as many indices\n\n      for (let i = 0, l = coordIndex.length; i < l; i += 3) {\n        const a = index[i]\n        const b = index[i + 1]\n        const c = index[i + 2]\n\n        if (itemSize === 2) {\n          uvA.fromArray(data, a * itemSize)\n          uvB.fromArray(data, b * itemSize)\n          uvC.fromArray(data, c * itemSize)\n\n          array.push(uvA.x, uvA.y)\n          array.push(uvB.x, uvB.y)\n          array.push(uvC.x, uvC.y)\n        } else {\n          vA.fromArray(data, a * itemSize)\n          vB.fromArray(data, b * itemSize)\n          vC.fromArray(data, c * itemSize)\n\n          array.push(vA.x, vA.y, vA.z)\n          array.push(vB.x, vB.y, vB.z)\n          array.push(vC.x, vC.y, vC.z)\n        }\n      }\n\n      return new Float32BufferAttribute(array, itemSize)\n    }\n\n    function computeAttributeFromFaceData(index, faceData) {\n      const array = []\n\n      for (let i = 0, j = 0, l = index.length; i < l; i += 3, j++) {\n        vA.fromArray(faceData, j * 3)\n\n        array.push(vA.x, vA.y, vA.z)\n        array.push(vA.x, vA.y, vA.z)\n        array.push(vA.x, vA.y, vA.z)\n      }\n\n      return new Float32BufferAttribute(array, 3)\n    }\n\n    function computeAttributeFromLineData(index, lineData) {\n      const array = []\n\n      for (let i = 0, j = 0, l = index.length; i < l; i += 2, j++) {\n        vA.fromArray(lineData, j * 3)\n\n        array.push(vA.x, vA.y, vA.z)\n        array.push(vA.x, vA.y, vA.z)\n      }\n\n      return new Float32BufferAttribute(array, 3)\n    }\n\n    function toNonIndexedAttribute(indices, attribute) {\n      const array = attribute.array\n      const itemSize = attribute.itemSize\n\n      const array2 = new array.constructor(indices.length * itemSize)\n\n      let index = 0,\n        index2 = 0\n\n      for (let i = 0, l = indices.length; i < l; i++) {\n        index = indices[i] * itemSize\n\n        for (let j = 0; j < itemSize; j++) {\n          array2[index2++] = array[index++]\n        }\n      }\n\n      return new Float32BufferAttribute(array2, itemSize)\n    }\n\n    const ab = new Vector3()\n    const cb = new Vector3()\n\n    function computeNormalAttribute(index, coord, creaseAngle) {\n      const faces = []\n      const vertexNormals = {}\n\n      // prepare face and raw vertex normals\n\n      for (let i = 0, l = index.length; i < l; i += 3) {\n        const a = index[i]\n        const b = index[i + 1]\n        const c = index[i + 2]\n\n        const face = new Face(a, b, c)\n\n        vA.fromArray(coord, a * 3)\n        vB.fromArray(coord, b * 3)\n        vC.fromArray(coord, c * 3)\n\n        cb.subVectors(vC, vB)\n        ab.subVectors(vA, vB)\n        cb.cross(ab)\n\n        cb.normalize()\n\n        face.normal.copy(cb)\n\n        if (vertexNormals[a] === undefined) vertexNormals[a] = []\n        if (vertexNormals[b] === undefined) vertexNormals[b] = []\n        if (vertexNormals[c] === undefined) vertexNormals[c] = []\n\n        vertexNormals[a].push(face.normal)\n        vertexNormals[b].push(face.normal)\n        vertexNormals[c].push(face.normal)\n\n        faces.push(face)\n      }\n\n      // compute vertex normals and build final geometry\n\n      const normals = []\n\n      for (let i = 0, l = faces.length; i < l; i++) {\n        const face = faces[i]\n\n        const nA = weightedNormal(vertexNormals[face.a], face.normal, creaseAngle)\n        const nB = weightedNormal(vertexNormals[face.b], face.normal, creaseAngle)\n        const nC = weightedNormal(vertexNormals[face.c], face.normal, creaseAngle)\n\n        vA.fromArray(coord, face.a * 3)\n        vB.fromArray(coord, face.b * 3)\n        vC.fromArray(coord, face.c * 3)\n\n        normals.push(nA.x, nA.y, nA.z)\n        normals.push(nB.x, nB.y, nB.z)\n        normals.push(nC.x, nC.y, nC.z)\n      }\n\n      return new Float32BufferAttribute(normals, 3)\n    }\n\n    function weightedNormal(normals, vector, creaseAngle) {\n      const normal = new Vector3()\n\n      if (creaseAngle === 0) {\n        normal.copy(vector)\n      } else {\n        for (let i = 0, l = normals.length; i < l; i++) {\n          if (normals[i].angleTo(vector) < creaseAngle) {\n            normal.add(normals[i])\n          }\n        }\n      }\n\n      return normal.normalize()\n    }\n\n    function toColorArray(colors) {\n      const array = []\n\n      for (let i = 0, l = colors.length; i < l; i += 3) {\n        array.push(new Color(colors[i], colors[i + 1], colors[i + 2]))\n      }\n\n      return array\n    }\n\n    /**\n     * Vertically paints the faces interpolating between the\n     * specified colors at the specified angels. This is used for the Background\n     * node, but could be applied to other nodes with multiple faces as well.\n     *\n     * When used with the Background node, default is directionIsDown is true if\n     * interpolating the skyColor down from the Zenith. When interpolationg up from\n     * the Nadir i.e. interpolating the groundColor, the directionIsDown is false.\n     *\n     * The first angle is never specified, it is the Zenith (0 rad). Angles are specified\n     * in radians. The geometry is thought a sphere, but could be anything. The color interpolation\n     * is linear along the Y axis in any case.\n     *\n     * You must specify one more color than you have angles at the beginning of the colors array.\n     * This is the color of the Zenith (the top of the shape).\n     *\n     * @param {BufferGeometry} geometry\n     * @param {number} radius\n     * @param {array} angles\n     * @param {array} colors\n     * @param {boolean} topDown - Whether to work top down or bottom up.\n     */\n    function paintFaces(geometry, radius, angles, colors, topDown) {\n      // compute threshold values\n\n      const thresholds = []\n      const startAngle = topDown === true ? 0 : Math.PI\n\n      for (let i = 0, l = colors.length; i < l; i++) {\n        let angle = i === 0 ? 0 : angles[i - 1]\n        angle = topDown === true ? angle : startAngle - angle\n\n        const point = new Vector3()\n        point.setFromSphericalCoords(radius, angle, 0)\n\n        thresholds.push(point)\n      }\n\n      // generate vertex colors\n\n      const indices = geometry.index\n      const positionAttribute = geometry.attributes.position\n      const colorAttribute = new BufferAttribute(new Float32Array(geometry.attributes.position.count * 3), 3)\n\n      const position = new Vector3()\n      const color = new Color()\n\n      for (let i = 0; i < indices.count; i++) {\n        const index = indices.getX(i)\n        position.fromBufferAttribute(positionAttribute, index)\n\n        let thresholdIndexA, thresholdIndexB\n        let t = 1\n\n        for (let j = 1; j < thresholds.length; j++) {\n          thresholdIndexA = j - 1\n          thresholdIndexB = j\n\n          const thresholdA = thresholds[thresholdIndexA]\n          const thresholdB = thresholds[thresholdIndexB]\n\n          if (topDown === true) {\n            // interpolation for sky color\n\n            if (position.y <= thresholdA.y && position.y > thresholdB.y) {\n              t = Math.abs(thresholdA.y - position.y) / Math.abs(thresholdA.y - thresholdB.y)\n\n              break\n            }\n          } else {\n            // interpolation for ground color\n\n            if (position.y >= thresholdA.y && position.y < thresholdB.y) {\n              t = Math.abs(thresholdA.y - position.y) / Math.abs(thresholdA.y - thresholdB.y)\n\n              break\n            }\n          }\n        }\n\n        const colorA = colors[thresholdIndexA]\n        const colorB = colors[thresholdIndexB]\n\n        color.copy(colorA).lerp(colorB, t)\n\n        colorAttribute.setXYZ(index, color.r, color.g, color.b)\n      }\n\n      geometry.setAttribute('color', colorAttribute)\n    }\n\n    //\n\n    const textureLoader = new TextureLoader(this.manager)\n    textureLoader.setPath(this.resourcePath || path).setCrossOrigin(this.crossOrigin)\n\n    // check version (only 2.0 is supported)\n\n    if (data.indexOf('#VRML V2.0') === -1) {\n      throw Error('THREE.VRMLLexer: Version of VRML asset not supported.')\n    }\n\n    // create JSON representing the tree structure of the VRML asset\n\n    const tree = generateVRMLTree(data)\n\n    // parse the tree structure to a three.js scene\n\n    const scene = parseTree(tree)\n\n    return scene\n  }\n}\n\nclass VRMLLexer {\n  constructor(tokens) {\n    this.lexer = new Lexer(tokens)\n  }\n\n  lex(inputText) {\n    const lexingResult = this.lexer.tokenize(inputText)\n\n    if (lexingResult.errors.length > 0) {\n      console.error(lexingResult.errors)\n\n      throw Error('THREE.VRMLLexer: Lexing errors detected.')\n    }\n\n    return lexingResult\n  }\n}\n\nclass VRMLParser extends CstParser {\n  constructor(tokenVocabulary) {\n    super(tokenVocabulary)\n\n    const $ = this\n\n    const Version = tokenVocabulary['Version']\n    const LCurly = tokenVocabulary['LCurly']\n    const RCurly = tokenVocabulary['RCurly']\n    const LSquare = tokenVocabulary['LSquare']\n    const RSquare = tokenVocabulary['RSquare']\n    const Identifier = tokenVocabulary['Identifier']\n    const RouteIdentifier = tokenVocabulary['RouteIdentifier']\n    const StringLiteral = tokenVocabulary['StringLiteral']\n    const HexLiteral = tokenVocabulary['HexLiteral']\n    const NumberLiteral = tokenVocabulary['NumberLiteral']\n    const TrueLiteral = tokenVocabulary['TrueLiteral']\n    const FalseLiteral = tokenVocabulary['FalseLiteral']\n    const NullLiteral = tokenVocabulary['NullLiteral']\n    const DEF = tokenVocabulary['DEF']\n    const USE = tokenVocabulary['USE']\n    const ROUTE = tokenVocabulary['ROUTE']\n    const TO = tokenVocabulary['TO']\n    const NodeName = tokenVocabulary['NodeName']\n\n    $.RULE('vrml', function () {\n      $.SUBRULE($.version)\n      $.AT_LEAST_ONE(function () {\n        $.SUBRULE($.node)\n      })\n      $.MANY(function () {\n        $.SUBRULE($.route)\n      })\n    })\n\n    $.RULE('version', function () {\n      $.CONSUME(Version)\n    })\n\n    $.RULE('node', function () {\n      $.OPTION(function () {\n        $.SUBRULE($.def)\n      })\n\n      $.CONSUME(NodeName)\n      $.CONSUME(LCurly)\n      $.MANY(function () {\n        $.SUBRULE($.field)\n      })\n      $.CONSUME(RCurly)\n    })\n\n    $.RULE('field', function () {\n      $.CONSUME(Identifier)\n\n      $.OR2([\n        {\n          ALT: function () {\n            $.SUBRULE($.singleFieldValue)\n          },\n        },\n        {\n          ALT: function () {\n            $.SUBRULE($.multiFieldValue)\n          },\n        },\n      ])\n    })\n\n    $.RULE('def', function () {\n      $.CONSUME(DEF)\n      $.OR([\n        {\n          ALT: function () {\n            $.CONSUME(Identifier)\n          },\n        },\n        {\n          ALT: function () {\n            $.CONSUME(NodeName)\n          },\n        },\n      ])\n    })\n\n    $.RULE('use', function () {\n      $.CONSUME(USE)\n      $.OR([\n        {\n          ALT: function () {\n            $.CONSUME(Identifier)\n          },\n        },\n        {\n          ALT: function () {\n            $.CONSUME(NodeName)\n          },\n        },\n      ])\n    })\n\n    $.RULE('singleFieldValue', function () {\n      $.AT_LEAST_ONE(function () {\n        $.OR([\n          {\n            ALT: function () {\n              $.SUBRULE($.node)\n            },\n          },\n          {\n            ALT: function () {\n              $.SUBRULE($.use)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(StringLiteral)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(HexLiteral)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(NumberLiteral)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(TrueLiteral)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(FalseLiteral)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(NullLiteral)\n            },\n          },\n        ])\n      })\n    })\n\n    $.RULE('multiFieldValue', function () {\n      $.CONSUME(LSquare)\n      $.MANY(function () {\n        $.OR([\n          {\n            ALT: function () {\n              $.SUBRULE($.node)\n            },\n          },\n          {\n            ALT: function () {\n              $.SUBRULE($.use)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(StringLiteral)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(HexLiteral)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(NumberLiteral)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(NullLiteral)\n            },\n          },\n        ])\n      })\n      $.CONSUME(RSquare)\n    })\n\n    $.RULE('route', function () {\n      $.CONSUME(ROUTE)\n      $.CONSUME(RouteIdentifier)\n      $.CONSUME(TO)\n      $.CONSUME2(RouteIdentifier)\n    })\n\n    this.performSelfAnalysis()\n  }\n}\n\nclass Face {\n  constructor(a, b, c) {\n    this.a = a\n    this.b = b\n    this.c = c\n    this.normal = new Vector3()\n  }\n}\n\nconst TEXTURE_TYPE = {\n  INTENSITY: 1,\n  INTENSITY_ALPHA: 2,\n  RGB: 3,\n  RGBA: 4,\n}\n\nexport { VRMLLoader }\n"], "mappings": ";;AAoCA,MAAMA,UAAA,SAAmBC,MAAA,CAAO;EAC9BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;EACd;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,IAAA,GAAOD,KAAA,CAAMC,IAAA,KAAS,KAAKC,WAAA,CAAYC,cAAA,CAAeP,GAAG,IAAII,KAAA,CAAMC,IAAA;IAEzE,MAAMG,MAAA,GAAS,IAAIC,UAAA,CAAWL,KAAA,CAAMN,OAAO;IAC3CU,MAAA,CAAOE,OAAA,CAAQN,KAAA,CAAMC,IAAI;IACzBG,MAAA,CAAOG,gBAAA,CAAiBP,KAAA,CAAMQ,aAAa;IAC3CJ,MAAA,CAAOK,kBAAA,CAAmBT,KAAA,CAAMU,eAAe;IAC/CN,MAAA,CAAOT,IAAA,CACLC,GAAA,EACA,UAAUe,IAAA,EAAM;MACd,IAAI;QACFd,MAAA,CAAOG,KAAA,CAAMY,KAAA,CAAMD,IAAA,EAAMV,IAAI,CAAC;MAC/B,SAAQY,CAAA,EAAP;QACA,IAAId,OAAA,EAAS;UACXA,OAAA,CAAQc,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDb,KAAA,CAAMN,OAAA,CAAQsB,SAAA,CAAUpB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDa,MAAMK,IAAA,EAAMhB,IAAA,EAAM;IAChB,MAAMiB,OAAA,GAAU,CAAE;IAElB,SAASC,iBAAiBC,KAAA,EAAM;MAG9B,MAAMC,SAAA,GAAYC,YAAA,CAAc;MAEhC,MAAMC,KAAA,GAAQ,IAAIC,SAAA,CAAUH,SAAA,CAAUI,MAAM;MAC5C,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAWN,SAAA,CAAUO,eAAe;MACvD,MAAMC,OAAA,GAAUC,aAAA,CAAcJ,MAAA,CAAOK,4BAAA,CAA4B,CAAE;MAInE,MAAMC,YAAA,GAAeT,KAAA,CAAMU,GAAA,CAAIb,KAAI;MACnCM,MAAA,CAAOQ,KAAA,GAAQF,YAAA,CAAaP,MAAA;MAI5B,MAAMU,SAAA,GAAYT,MAAA,CAAOU,IAAA,CAAM;MAE/B,IAAIV,MAAA,CAAOW,MAAA,CAAOC,MAAA,GAAS,GAAG;QAC5BxB,OAAA,CAAQC,KAAA,CAAMW,MAAA,CAAOW,MAAM;QAE3B,MAAME,KAAA,CAAM,4CAA4C;MACzD;MAID,MAAMC,GAAA,GAAMX,OAAA,CAAQY,KAAA,CAAMN,SAAS;MAEnC,OAAOK,GAAA;IACR;IAED,SAASlB,aAAA,EAAe;MAGtB,MAAMoB,eAAA,GAAkBC,WAAA,CAAY;QAClCC,IAAA,EAAM;QACNC,OAAA,EAAS;MACjB,CAAO;MACD,MAAMC,UAAA,GAAaH,WAAA,CAAY;QAC7BC,IAAA,EAAM;QACNC,OAAA,EAAS;QACTE,UAAA,EAAYL;MACpB,CAAO;MAID,MAAMM,SAAA,GAAY,CAChB,UACA,aACA,aACA,SACA;MAAA;MACA,UACA,OACA;MAAA;MACA,aACA,oBACA,cACA,UACA,SACA,SACA,aACA;MAAA;MACA,kBACA,eACA,mBACA,gBACA,cACA,eACA;MAAA;MACA,OACA,QACA,YACA,iBACA,aACA,kBACA,kBACA,YACA;MAAA;MACA,SACA,cACA,UACA;MAAA;MACA,cACA,aACA,gBACA,YACA,gBACA,gBACA;MAAA;MACA,qBACA,0BACA,sBACA,2BACA,wBACA;MAAA;MACA,cACA,OACA,kBACA;MAAA;MACA;MAAA;MAAA,CACD;MAID,MAAMC,OAAA,GAAUN,WAAA,CAAY;QAC1BC,IAAA,EAAM;QACNC,OAAA,EAAS;QACTE,UAAA,EAAYD;MACpB,CAAO;MAED,MAAMI,QAAA,GAAWP,WAAA,CAAY;QAC3BC,IAAA,EAAM;QACNC,OAAA,EAAS,IAAIM,MAAA,CAAOH,SAAA,CAAUI,IAAA,CAAK,GAAG,CAAC;QACvCL,UAAA,EAAYD;MACpB,CAAO;MAED,MAAMO,GAAA,GAAMV,WAAA,CAAY;QACtBC,IAAA,EAAM;QACNC,OAAA,EAAS;QACTE,UAAA,EAAYD;MACpB,CAAO;MAED,MAAMQ,GAAA,GAAMX,WAAA,CAAY;QACtBC,IAAA,EAAM;QACNC,OAAA,EAAS;QACTE,UAAA,EAAYD;MACpB,CAAO;MAED,MAAMS,KAAA,GAAQZ,WAAA,CAAY;QACxBC,IAAA,EAAM;QACNC,OAAA,EAAS;QACTE,UAAA,EAAYD;MACpB,CAAO;MAED,MAAMU,EAAA,GAAKb,WAAA,CAAY;QACrBC,IAAA,EAAM;QACNC,OAAA,EAAS;QACTE,UAAA,EAAYD;MACpB,CAAO;MAID,MAAMW,aAAA,GAAgBd,WAAA,CAAY;QAChCC,IAAA,EAAM;QACNC,OAAA,EAAS;MACjB,CAAO;MACD,MAAMa,UAAA,GAAaf,WAAA,CAAY;QAAEC,IAAA,EAAM;QAAcC,OAAA,EAAS;MAAA,CAAqB;MACnF,MAAMc,aAAA,GAAgBhB,WAAA,CAAY;QAAEC,IAAA,EAAM;QAAiBC,OAAA,EAAS;MAAA,CAA0C;MAC9G,MAAMe,WAAA,GAAcjB,WAAA,CAAY;QAAEC,IAAA,EAAM;QAAeC,OAAA,EAAS;MAAA,CAAQ;MACxE,MAAMgB,YAAA,GAAelB,WAAA,CAAY;QAAEC,IAAA,EAAM;QAAgBC,OAAA,EAAS;MAAA,CAAS;MAC3E,MAAMiB,WAAA,GAAcnB,WAAA,CAAY;QAAEC,IAAA,EAAM;QAAeC,OAAA,EAAS;MAAA,CAAQ;MACxE,MAAMkB,OAAA,GAAUpB,WAAA,CAAY;QAAEC,IAAA,EAAM;QAAWC,OAAA,EAAS;MAAA,CAAM;MAC9D,MAAMmB,OAAA,GAAUrB,WAAA,CAAY;QAAEC,IAAA,EAAM;QAAWC,OAAA,EAAS;MAAA,CAAK;MAC7D,MAAMoB,MAAA,GAAStB,WAAA,CAAY;QAAEC,IAAA,EAAM;QAAUC,OAAA,EAAS;MAAA,CAAK;MAC3D,MAAMqB,MAAA,GAASvB,WAAA,CAAY;QAAEC,IAAA,EAAM;QAAUC,OAAA,EAAS;MAAA,CAAK;MAC3D,MAAMsB,OAAA,GAAUxB,WAAA,CAAY;QAC1BC,IAAA,EAAM;QACNC,OAAA,EAAS;QACTuB,KAAA,EAAOC,KAAA,CAAMC;MACrB,CAAO;MAID,MAAMC,UAAA,GAAa5B,WAAA,CAAY;QAC7BC,IAAA,EAAM;QACNC,OAAA,EAAS;QACTuB,KAAA,EAAOC,KAAA,CAAMC;MACrB,CAAO;MAED,MAAM7C,MAAA,GAAS,CACb8C,UAAA;MAAA;MAEArB,QAAA,EACAG,GAAA,EACAC,GAAA,EACAC,KAAA,EACAC,EAAA,EACAI,WAAA,EACAC,YAAA,EACAC,WAAA;MAAA;MAEAb,OAAA,EACAH,UAAA,EACAJ,eAAA,EACAe,aAAA,EACAC,UAAA,EACAC,aAAA,EACAI,OAAA,EACAC,OAAA,EACAC,MAAA,EACAC,MAAA,EACAC,OAAA,CACD;MAED,MAAMvC,eAAA,GAAkB,CAAE;MAE1B,SAAS4C,CAAA,GAAI,GAAGC,CAAA,GAAIhD,MAAA,CAAOa,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAME,KAAA,GAAQjD,MAAA,CAAO+C,CAAC;QAEtB5C,eAAA,CAAgB8C,KAAA,CAAM9B,IAAI,IAAI8B,KAAA;MAC/B;MAED,OAAO;QAAEjD,MAAA;QAAgBG;MAAkC;IAC5D;IAED,SAASE,cAAc6C,eAAA,EAAiB;MAGtC,SAASC,iBAAA,EAAmB;QAC1BD,eAAA,CAAgBE,IAAA,CAAK,IAAI;QAEzB,KAAKC,eAAA,CAAiB;MACvB;MAEDF,gBAAA,CAAiBG,SAAA,GAAYC,MAAA,CAAOC,MAAA,CAAOD,MAAA,CAAOE,MAAA,CAAOP,eAAA,CAAgBI,SAAS,GAAG;QACnFtF,WAAA,EAAamF,gBAAA;QAEbxC,IAAA,EAAM,SAAAA,CAAU+C,GAAA,EAAK;UACnB,MAAM/D,KAAA,GAAO;YACXgE,OAAA,EAAS,KAAK3C,KAAA,CAAM0C,GAAA,CAAIC,OAAO;YAC/BC,KAAA,EAAO,EAAE;YACTC,MAAA,EAAQ;UACT;UAED,SAASd,CAAA,GAAI,GAAGC,CAAA,GAAIU,GAAA,CAAII,IAAA,CAAKjD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;YAC/C,MAAMe,IAAA,GAAOJ,GAAA,CAAII,IAAA,CAAKf,CAAC;YAEvBpD,KAAA,CAAKiE,KAAA,CAAMG,IAAA,CAAK,KAAK/C,KAAA,CAAM8C,IAAI,CAAC;UACjC;UAED,IAAIJ,GAAA,CAAIM,KAAA,EAAO;YACb,SAASjB,CAAA,GAAI,GAAGC,CAAA,GAAIU,GAAA,CAAIM,KAAA,CAAMnD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;cAChD,MAAMiB,KAAA,GAAQN,GAAA,CAAIM,KAAA,CAAMjB,CAAC;cAEzBpD,KAAA,CAAKkE,MAAA,CAAOE,IAAA,CAAK,KAAK/C,KAAA,CAAMgD,KAAK,CAAC;YACnC;UACF;UAED,OAAOrE,KAAA;QACR;QAEDgE,OAAA,EAAS,SAAAA,CAAUD,GAAA,EAAK;UACtB,OAAOA,GAAA,CAAIlC,OAAA,CAAQ,CAAC,EAAEyC,KAAA;QACvB;QAEDH,IAAA,EAAM,SAAAA,CAAUJ,GAAA,EAAK;UACnB,MAAM/D,KAAA,GAAO;YACXwB,IAAA,EAAMuC,GAAA,CAAIjC,QAAA,CAAS,CAAC,EAAEwC,KAAA;YACtBC,MAAA,EAAQ;UACT;UAED,IAAIR,GAAA,CAAIS,KAAA,EAAO;YACb,SAASpB,CAAA,GAAI,GAAGC,CAAA,GAAIU,GAAA,CAAIS,KAAA,CAAMtD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;cAChD,MAAMoB,KAAA,GAAQT,GAAA,CAAIS,KAAA,CAAMpB,CAAC;cAEzBpD,KAAA,CAAKuE,MAAA,CAAOH,IAAA,CAAK,KAAK/C,KAAA,CAAMmD,KAAK,CAAC;YACnC;UACF;UAID,IAAIT,GAAA,CAAIU,GAAA,EAAK;YACXzE,KAAA,CAAKiC,GAAA,GAAM,KAAKZ,KAAA,CAAM0C,GAAA,CAAIU,GAAA,CAAI,CAAC,CAAC;UACjC;UAED,OAAOzE,KAAA;QACR;QAEDwE,KAAA,EAAO,SAAAA,CAAUT,GAAA,EAAK;UACpB,MAAM/D,KAAA,GAAO;YACXwB,IAAA,EAAMuC,GAAA,CAAIrC,UAAA,CAAW,CAAC,EAAE4C,KAAA;YACxBI,IAAA,EAAM;YACNC,MAAA,EAAQ;UACT;UAED,IAAIC,MAAA;UAIJ,IAAIb,GAAA,CAAIc,gBAAA,EAAkB;YACxBD,MAAA,GAAS,KAAKvD,KAAA,CAAM0C,GAAA,CAAIc,gBAAA,CAAiB,CAAC,CAAC;UAC5C;UAID,IAAId,GAAA,CAAIe,eAAA,EAAiB;YACvBF,MAAA,GAAS,KAAKvD,KAAA,CAAM0C,GAAA,CAAIe,eAAA,CAAgB,CAAC,CAAC;UAC3C;UAED9E,KAAA,CAAK0E,IAAA,GAAOE,MAAA,CAAOF,IAAA;UACnB1E,KAAA,CAAK2E,MAAA,GAASC,MAAA,CAAOD,MAAA;UAErB,OAAO3E,KAAA;QACR;QAEDyE,GAAA,EAAK,SAAAA,CAAUV,GAAA,EAAK;UAClB,QAAQA,GAAA,CAAIrC,UAAA,IAAcqC,GAAA,CAAIjC,QAAA,EAAU,CAAC,EAAEwC,KAAA;QAC5C;QAEDS,GAAA,EAAK,SAAAA,CAAUhB,GAAA,EAAK;UAClB,OAAO;YAAE7B,GAAA,GAAM6B,GAAA,CAAIrC,UAAA,IAAcqC,GAAA,CAAIjC,QAAA,EAAU,CAAC,EAAEwC;UAAO;QAC1D;QAEDO,gBAAA,EAAkB,SAAAA,CAAUd,GAAA,EAAK;UAC/B,OAAOiB,YAAA,CAAa,MAAMjB,GAAG;QAC9B;QAEDe,eAAA,EAAiB,SAAAA,CAAUf,GAAA,EAAK;UAC9B,OAAOiB,YAAA,CAAa,MAAMjB,GAAG;QAC9B;QAEDM,KAAA,EAAO,SAAAA,CAAUN,GAAA,EAAK;UACpB,MAAM/D,KAAA,GAAO;YACXiF,IAAA,EAAMlB,GAAA,CAAIzC,eAAA,CAAgB,CAAC,EAAEgD,KAAA;YAC7BlC,EAAA,EAAI2B,GAAA,CAAIzC,eAAA,CAAgB,CAAC,EAAEgD;UAC5B;UAED,OAAOtE,KAAA;QACR;MACT,CAAO;MAED,SAASgF,aAAapG,KAAA,EAAOmF,GAAA,EAAK;QAChC,MAAMS,KAAA,GAAQ;UACZE,IAAA,EAAM;UACNC,MAAA,EAAQ;QACT;QAED,IAAIZ,GAAA,CAAII,IAAA,EAAM;UACZK,KAAA,CAAME,IAAA,GAAO;UAEb,SAAStB,CAAA,GAAI,GAAGC,CAAA,GAAIU,GAAA,CAAII,IAAA,CAAKjD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;YAC/C,MAAMe,IAAA,GAAOJ,GAAA,CAAII,IAAA,CAAKf,CAAC;YAEvBoB,KAAA,CAAMG,MAAA,CAAOP,IAAA,CAAKxF,KAAA,CAAMyC,KAAA,CAAM8C,IAAI,CAAC;UACpC;QACF;QAED,IAAIJ,GAAA,CAAIgB,GAAA,EAAK;UACXP,KAAA,CAAME,IAAA,GAAO;UAEb,SAAStB,CAAA,GAAI,GAAGC,CAAA,GAAIU,GAAA,CAAIgB,GAAA,CAAI7D,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;YAC9C,MAAM2B,GAAA,GAAMhB,GAAA,CAAIgB,GAAA,CAAI3B,CAAC;YAErBoB,KAAA,CAAMG,MAAA,CAAOP,IAAA,CAAKxF,KAAA,CAAMyC,KAAA,CAAM0D,GAAG,CAAC;UACnC;QACF;QAED,IAAIhB,GAAA,CAAI1B,aAAA,EAAe;UACrBmC,KAAA,CAAME,IAAA,GAAO;UAEb,SAAStB,CAAA,GAAI,GAAGC,CAAA,GAAIU,GAAA,CAAI1B,aAAA,CAAcnB,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;YACxD,MAAM8B,aAAA,GAAgBnB,GAAA,CAAI1B,aAAA,CAAce,CAAC;YAEzCoB,KAAA,CAAMG,MAAA,CAAOP,IAAA,CAAKc,aAAA,CAAcZ,KAAA,CAAMa,OAAA,CAAQ,QAAQ,EAAE,CAAC;UAC1D;QACF;QAED,IAAIpB,GAAA,CAAIxB,aAAA,EAAe;UACrBiC,KAAA,CAAME,IAAA,GAAO;UAEb,SAAStB,CAAA,GAAI,GAAGC,CAAA,GAAIU,GAAA,CAAIxB,aAAA,CAAcrB,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;YACxD,MAAMgC,aAAA,GAAgBrB,GAAA,CAAIxB,aAAA,CAAca,CAAC;YAEzCoB,KAAA,CAAMG,MAAA,CAAOP,IAAA,CAAKiB,UAAA,CAAWD,aAAA,CAAcd,KAAK,CAAC;UAClD;QACF;QAED,IAAIP,GAAA,CAAIzB,UAAA,EAAY;UAClBkC,KAAA,CAAME,IAAA,GAAO;UAEb,SAAStB,CAAA,GAAI,GAAGC,CAAA,GAAIU,GAAA,CAAIzB,UAAA,CAAWpB,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;YACrD,MAAMkC,UAAA,GAAavB,GAAA,CAAIzB,UAAA,CAAWc,CAAC;YAEnCoB,KAAA,CAAMG,MAAA,CAAOP,IAAA,CAAKkB,UAAA,CAAWhB,KAAK;UACnC;QACF;QAED,IAAIP,GAAA,CAAIvB,WAAA,EAAa;UACnBgC,KAAA,CAAME,IAAA,GAAO;UAEb,SAAStB,CAAA,GAAI,GAAGC,CAAA,GAAIU,GAAA,CAAIvB,WAAA,CAAYtB,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;YACtD,MAAMmC,WAAA,GAAcxB,GAAA,CAAIvB,WAAA,CAAYY,CAAC;YAErC,IAAImC,WAAA,CAAYjB,KAAA,KAAU,QAAQE,KAAA,CAAMG,MAAA,CAAOP,IAAA,CAAK,IAAI;UACzD;QACF;QAED,IAAIL,GAAA,CAAItB,YAAA,EAAc;UACpB+B,KAAA,CAAME,IAAA,GAAO;UAEb,SAAStB,CAAA,GAAI,GAAGC,CAAA,GAAIU,GAAA,CAAItB,YAAA,CAAavB,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;YACvD,MAAMoC,YAAA,GAAezB,GAAA,CAAItB,YAAA,CAAaW,CAAC;YAEvC,IAAIoC,YAAA,CAAalB,KAAA,KAAU,SAASE,KAAA,CAAMG,MAAA,CAAOP,IAAA,CAAK,KAAK;UAC5D;QACF;QAED,IAAIL,GAAA,CAAIrB,WAAA,EAAa;UACnB8B,KAAA,CAAME,IAAA,GAAO;UAEbX,GAAA,CAAIrB,WAAA,CAAY+C,OAAA,CAAQ,YAAY;YAClCjB,KAAA,CAAMG,MAAA,CAAOP,IAAA,CAAK,IAAI;UAClC,CAAW;QACF;QAED,OAAOI,KAAA;MACR;MAED,OAAO,IAAIhB,gBAAA,CAAkB;IAC9B;IAED,SAASkC,UAAUC,KAAA,EAAM;MAGvB,MAAM1B,KAAA,GAAQ0B,KAAA,CAAK1B,KAAA;MACnB,MAAM2B,MAAA,GAAQ,IAAIC,KAAA,CAAO;MAIzB,SAASzC,CAAA,GAAI,GAAGC,CAAA,GAAIY,KAAA,CAAM/C,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC5C,MAAMe,IAAA,GAAOF,KAAA,CAAMb,CAAC;QAEpB0C,YAAA,CAAa3B,IAAI;MAClB;MAID,SAASf,CAAA,GAAI,GAAGC,CAAA,GAAIY,KAAA,CAAM/C,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC5C,MAAMe,IAAA,GAAOF,KAAA,CAAMb,CAAC;QACpB,MAAM2C,MAAA,GAASC,OAAA,CAAQ7B,IAAI;QAE3B,IAAI4B,MAAA,YAAkBE,QAAA,EAAUL,MAAA,CAAMM,GAAA,CAAIH,MAAM;QAEhD,IAAI5B,IAAA,CAAK3C,IAAA,KAAS,aAAaoE,MAAA,CAAMO,QAAA,CAASC,SAAA,GAAYL,MAAA;MAC3D;MAED,OAAOH,MAAA;IACR;IAED,SAASE,aAAa3B,IAAA,EAAM;MAC1B,IAAIA,IAAA,CAAKlC,GAAA,EAAK;QACZnC,OAAA,CAAQqE,IAAA,CAAKlC,GAAG,IAAIkC,IAAA;MACrB;MAED,MAAMI,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QAEtB,IAAIoB,KAAA,CAAME,IAAA,KAAS,QAAQ;UACzB,MAAM2B,WAAA,GAAc7B,KAAA,CAAMG,MAAA;UAE1B,SAAS2B,CAAA,GAAI,GAAGC,EAAA,GAAKF,WAAA,CAAYnF,MAAA,EAAQoF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;YACpDR,YAAA,CAAaO,WAAA,CAAYC,CAAC,CAAC;UAC5B;QACF;MACF;IACF;IAED,SAASN,QAAQ7B,IAAA,EAAM;MAGrB,IAAIA,IAAA,CAAKjC,GAAA,EAAK;QACZ,OAAOsE,UAAA,CAAWrC,IAAA,CAAKjC,GAAG;MAC3B;MAED,IAAIiC,IAAA,CAAKsC,KAAA,KAAU,QAAW,OAAOtC,IAAA,CAAKsC,KAAA;MAE1CtC,IAAA,CAAKsC,KAAA,GAAQC,SAAA,CAAUvC,IAAI;MAE3B,OAAOA,IAAA,CAAKsC,KAAA;IACb;IAID,SAASC,UAAUvC,IAAA,EAAM;MACvB,MAAMwC,QAAA,GAAWxC,IAAA,CAAK3C,IAAA;MACtB,IAAIiF,KAAA;MAEJ,QAAQE,QAAA;QACN,KAAK;QACL,KAAK;QACL,KAAK;UACHF,KAAA,GAAQG,iBAAA,CAAkBzC,IAAI;UAC9B;QAEF,KAAK;UACHsC,KAAA,GAAQI,mBAAA,CAAoB1C,IAAI;UAChC;QAEF,KAAK;UACHsC,KAAA,GAAQK,cAAA,CAAe3C,IAAI;UAC3B;QAEF,KAAK;UACHsC,KAAA,GAAQM,mBAAA,CAAoB5C,IAAI;UAChC;QAEF,KAAK;UACHsC,KAAA,GAAQO,iBAAA,CAAkB7C,IAAI;UAC9B;QAEF,KAAK;UACHsC,KAAA,GAAQQ,qBAAA,CAAsB9C,IAAI;UAClC;QAEF,KAAK;UACHsC,KAAA,GAAQS,qBAAA,CAAsB/C,IAAI;UAClC;QAEF,KAAK;UACHsC,KAAA,GAAQU,yBAAA,CAA0BhD,IAAI;UACtC;QAEF,KAAK;UACHsC,KAAA,GAAQW,uBAAA,CAAwBjD,IAAI;UACpC;QAEF,KAAK;UACHsC,KAAA,GAAQY,uBAAA,CAAwBlD,IAAI;UACpC;QAEF,KAAK;UACHsC,KAAA,GAAQa,iBAAA,CAAkBnD,IAAI;UAC9B;QAEF,KAAK;UACHsC,KAAA,GAAQc,YAAA,CAAapD,IAAI;UACzB;QAEF,KAAK;UACHsC,KAAA,GAAQe,aAAA,CAAcrD,IAAI;UAC1B;QAEF,KAAK;UACHsC,KAAA,GAAQgB,iBAAA,CAAkBtD,IAAI;UAC9B;QAEF,KAAK;UACHsC,KAAA,GAAQiB,eAAA,CAAgBvD,IAAI;UAC5B;QAEF,KAAK;UACHsC,KAAA,GAAQkB,sBAAA,CAAuBxD,IAAI;UACnC;QAEF,KAAK;UACHsC,KAAA,GAAQmB,kBAAA,CAAmBzD,IAAI;UAC/B;QAEF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;UACHsC,KAAA,GAAQoB,kBAAA,CAAmB1D,IAAI;UAC/B;QAEF,KAAK;UACHsC,KAAA,GAAQqB,kBAAA,CAAmB3D,IAAI;UAC/B;QAEF,KAAK;QACL,KAAK;QAEL,KAAK;QACL,KAAK;QACL,KAAK;QAEL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QAEL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QAEL,KAAK;QAEL,KAAK;QACL,KAAK;QAEL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QAEL,KAAK;QACL,KAAK;QACL,KAAK;UAEH;QAEF;UACEzE,OAAA,CAAQqI,IAAA,CAAK,mCAAmCpB,QAAQ;UACxD;MACH;MAED,IAAIF,KAAA,KAAU,UAAatC,IAAA,CAAKlC,GAAA,KAAQ,UAAawE,KAAA,CAAMuB,cAAA,CAAe,MAAM,MAAM,MAAM;QAC1FvB,KAAA,CAAMjF,IAAA,GAAO2C,IAAA,CAAKlC,GAAA;MACnB;MAED,OAAOwE,KAAA;IACR;IAED,SAASG,kBAAkBzC,IAAA,EAAM;MAC/B,MAAM4B,MAAA,GAAS,IAAIkC,KAAA,CAAO;MAI1B,MAAM1D,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YAEH;UAEF,KAAK;YAEH;UAEF,KAAK;YAEH;UAEF,KAAK;YACHC,kBAAA,CAAmB9B,WAAA,EAAaN,MAAM;YACtC;UAEF,KAAK;YAEH;UAEF,KAAK;YACH,MAAMqC,IAAA,GAAO,IAAIC,OAAA,CAAQhC,WAAA,CAAY,CAAC,GAAGA,WAAA,CAAY,CAAC,GAAGA,WAAA,CAAY,CAAC,CAAC,EAAEiC,SAAA,CAAW;YACpF,MAAMC,KAAA,GAAQlC,WAAA,CAAY,CAAC;YAC3BN,MAAA,CAAOyC,UAAA,CAAWC,gBAAA,CAAiBL,IAAA,EAAMG,KAAK;YAC9C;UAEF,KAAK;YACHxC,MAAA,CAAO2C,KAAA,CAAMC,GAAA,CAAItC,WAAA,CAAY,CAAC,GAAGA,WAAA,CAAY,CAAC,GAAGA,WAAA,CAAY,CAAC,CAAC;YAC/D;UAEF,KAAK;YAEH;UAEF,KAAK;YACHN,MAAA,CAAO6C,QAAA,CAASD,GAAA,CAAItC,WAAA,CAAY,CAAC,GAAGA,WAAA,CAAY,CAAC,GAAGA,WAAA,CAAY,CAAC,CAAC;YAClE;UAEF,KAAK;YAEH;UAEF;YACE3G,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAED,OAAOnC,MAAA;IACR;IAED,SAASc,oBAAoB1C,IAAA,EAAM;MACjC,MAAMnB,KAAA,GAAQ,IAAIiF,KAAA,CAAO;MAEzB,IAAIY,WAAA,EAAaC,WAAA;MACjB,IAAIC,QAAA,EAAUC,QAAA;MAEd,MAAMzE,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YACHW,WAAA,GAAcxC,WAAA;YACd;UAEF,KAAK;YACHyC,WAAA,GAAczC,WAAA;YACd;UAEF,KAAK;YAEH;UAEF,KAAK;YAEH;UAEF,KAAK;YAEH;UAEF,KAAK;YAEH;UAEF,KAAK;YAEH;UAEF,KAAK;YAEH;UAEF,KAAK;YACH0C,QAAA,GAAW1C,WAAA;YACX;UAEF,KAAK;YACH2C,QAAA,GAAW3C,WAAA;YACX;UAEF;YACE3G,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAED,MAAMe,MAAA,GAAS;MAIf,IAAID,QAAA,EAAU;QACZ,MAAME,WAAA,GAAc,IAAIC,cAAA,CAAeF,MAAA,EAAQ,IAAI,EAAE;QACrD,MAAMG,WAAA,GAAc,IAAIC,iBAAA,CAAkB;UAAEC,GAAA,EAAK;UAAOC,IAAA,EAAMC,QAAA;UAAUC,UAAA,EAAY;UAAOC,SAAA,EAAW;QAAK,CAAE;QAE7G,IAAIV,QAAA,CAAS9H,MAAA,GAAS,GAAG;UACvByI,UAAA,CAAWT,WAAA,EAAaD,MAAA,EAAQF,QAAA,EAAUa,YAAA,CAAaZ,QAAQ,GAAG,IAAI;UACtEI,WAAA,CAAYS,YAAA,GAAe;QACrC,OAAe;UACLT,WAAA,CAAYU,KAAA,CAAMC,MAAA,CAAOf,QAAA,CAAS,CAAC,GAAGA,QAAA,CAAS,CAAC,GAAGA,QAAA,CAAS,CAAC,CAAC;QAC/D;QAED,MAAMgB,GAAA,GAAM,IAAIC,IAAA,CAAKf,WAAA,EAAaE,WAAW;QAC7CpG,KAAA,CAAMkD,GAAA,CAAI8D,GAAG;MACd;MAID,IAAIlB,WAAA,EAAa;QACf,IAAIA,WAAA,CAAY5H,MAAA,GAAS,GAAG;UAC1B,MAAMgJ,cAAA,GAAiB,IAAIf,cAAA,CAAeF,MAAA,EAAQ,IAAI,IAAI,GAAG,IAAIkB,IAAA,CAAKC,EAAA,EAAI,MAAMD,IAAA,CAAKC,EAAA,EAAI,MAAMD,IAAA,CAAKC,EAAE;UACtG,MAAMC,cAAA,GAAiB,IAAIhB,iBAAA,CAAkB;YAC3CC,GAAA,EAAK;YACLC,IAAA,EAAMC,QAAA;YACNK,YAAA,EAAc;YACdJ,UAAA,EAAY;YACZC,SAAA,EAAW;UACvB,CAAW;UAEDC,UAAA,CAAWO,cAAA,EAAgBjB,MAAA,EAAQJ,WAAA,EAAae,YAAA,CAAad,WAAW,GAAG,KAAK;UAEhF,MAAMwB,MAAA,GAAS,IAAIL,IAAA,CAAKC,cAAA,EAAgBG,cAAc;UACtDrH,KAAA,CAAMkD,GAAA,CAAIoE,MAAM;QACjB;MACF;MAIDtH,KAAA,CAAMuH,WAAA,GAAc,CAAAC,QAAA;MAEpB,OAAOxH,KAAA;IACR;IAED,SAAS8D,eAAe3C,IAAA,EAAM;MAC5B,MAAMI,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAIpB,IAAIkG,QAAA,GAAW,IAAIpB,iBAAA,CAAkB;QAAES,KAAA,EAAO;MAAQ,CAAE;MACxD,IAAIY,QAAA;MAEJ,SAAStH,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YACH,IAAI7B,WAAA,CAAY,CAAC,MAAM,MAAM;cAC3BoE,QAAA,GAAWzE,OAAA,CAAQK,WAAA,CAAY,CAAC,CAAC;YAClC;YAED;UAEF,KAAK;YACH,IAAIA,WAAA,CAAY,CAAC,MAAM,MAAM;cAC3BqE,QAAA,GAAW1E,OAAA,CAAQK,WAAA,CAAY,CAAC,CAAC;YAClC;YAED;UAEF;YACE3G,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAID,IAAInC,MAAA;MAEJ,IAAI2E,QAAA,IAAYA,QAAA,CAASC,UAAA,CAAW/B,QAAA,EAAU;QAC5C,MAAMlE,IAAA,GAAOgG,QAAA,CAASE,KAAA;QAEtB,IAAIlG,IAAA,KAAS,UAAU;UAGrB,MAAMmG,cAAA,GAAiB,IAAIC,cAAA,CAAe;YAAEhB,KAAA,EAAO;UAAQ,CAAE;UAE7D,IAAIY,QAAA,CAASC,UAAA,CAAWb,KAAA,KAAU,QAAW;YAC3Ce,cAAA,CAAehB,YAAA,GAAe;UAC1C,OAAiB;YAGL,IAAIY,QAAA,CAASM,mBAAA,EAAqB;cAChCF,cAAA,CAAef,KAAA,CAAMkB,IAAA,CAAKP,QAAA,CAASQ,QAAQ;YAC5C;UACF;UAEDlF,MAAA,GAAS,IAAImF,MAAA,CAAOR,QAAA,EAAUG,cAAc;QACtD,WAAmBnG,IAAA,KAAS,QAAQ;UAG1B,MAAMyG,YAAA,GAAe,IAAIC,iBAAA,CAAkB;YAAEtB,KAAA,EAAO;UAAQ,CAAE;UAE9D,IAAIY,QAAA,CAASC,UAAA,CAAWb,KAAA,KAAU,QAAW;YAC3CqB,YAAA,CAAatB,YAAA,GAAe;UACxC,OAAiB;YAGL,IAAIY,QAAA,CAASM,mBAAA,EAAqB;cAChCI,YAAA,CAAarB,KAAA,CAAMkB,IAAA,CAAKP,QAAA,CAASQ,QAAQ;YAC1C;UACF;UAEDlF,MAAA,GAAS,IAAIsF,YAAA,CAAaX,QAAA,EAAUS,YAAY;QAC1D,OAAe;UAKL,IAAIT,QAAA,CAASY,MAAA,KAAW,QAAW;YACjCb,QAAA,CAASlB,IAAA,GAAOmB,QAAA,CAASY,MAAA,GAASC,SAAA,GAAYC,UAAA;UAC/C;UAID,IAAId,QAAA,CAASC,UAAA,CAAWb,KAAA,KAAU,QAAW;YAC3CW,QAAA,CAASZ,YAAA,GAAe;UACzB;UAED9D,MAAA,GAAS,IAAIkE,IAAA,CAAKS,QAAA,EAAUD,QAAQ;QACrC;MACT,OAAa;QACL1E,MAAA,GAAS,IAAIE,QAAA,CAAU;QAIvBF,MAAA,CAAO0F,OAAA,GAAU;MAClB;MAED,OAAO1F,MAAA;IACR;IAED,SAASgB,oBAAoB5C,IAAA,EAAM;MACjC,IAAIsG,QAAA,GAAW,IAAIiB,iBAAA,CAAmB;MACtC,IAAIC,aAAA;MAEJ,MAAMpH,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YACH,IAAI7B,WAAA,CAAY,CAAC,MAAM,MAAM;cAC3B,MAAMuF,YAAA,GAAe5F,OAAA,CAAQK,WAAA,CAAY,CAAC,CAAC;cAE3C,IAAIuF,YAAA,CAAaC,YAAA,EAAcpB,QAAA,CAASX,KAAA,CAAMkB,IAAA,CAAKY,YAAA,CAAaC,YAAY;cAC5E,IAAID,YAAA,CAAaE,aAAA,EAAerB,QAAA,CAASQ,QAAA,CAASD,IAAA,CAAKY,YAAA,CAAaE,aAAa;cACjF,IAAIF,YAAA,CAAaG,SAAA,EAAWtB,QAAA,CAASsB,SAAA,GAAYH,YAAA,CAAaG,SAAA;cAC9D,IAAIH,YAAA,CAAaI,aAAA,EAAevB,QAAA,CAASwB,QAAA,CAASjB,IAAA,CAAKY,YAAA,CAAaI,aAAa;cACjF,IAAIJ,YAAA,CAAaM,YAAA,EAAczB,QAAA,CAAS0B,OAAA,GAAU,IAAIP,YAAA,CAAaM,YAAA;cACnE,IAAIN,YAAA,CAAaM,YAAA,GAAe,GAAGzB,QAAA,CAAS2B,WAAA,GAAc;YACxE,OAAmB;cAGL3B,QAAA,GAAW,IAAIpB,iBAAA,CAAkB;gBAAES,KAAA,EAAO;cAAQ,CAAE;YACrD;YAED;UAEF,KAAK;YACH,MAAMuC,WAAA,GAAchG,WAAA,CAAY,CAAC;YACjC,IAAIgG,WAAA,KAAgB,MAAM;cACxB,IAAIA,WAAA,CAAY7K,IAAA,KAAS,kBAAkB6K,WAAA,CAAY7K,IAAA,KAAS,gBAAgB;gBAC9EiJ,QAAA,CAAS6B,GAAA,GAAMtG,OAAA,CAAQqG,WAAW;cAGnC;YACF;YAED;UAEF,KAAK;YACH,IAAIhG,WAAA,CAAY,CAAC,MAAM,MAAM;cAC3BsF,aAAA,GAAgB3F,OAAA,CAAQK,WAAA,CAAY,CAAC,CAAC;YACvC;YAED;UAEF;YACE3G,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAID,IAAIuC,QAAA,CAAS6B,GAAA,EAAK;QAGhB,IAAI7B,QAAA,CAAS6B,GAAA,CAAIC,MAAA,EAAQ;UACvB,QAAQ9B,QAAA,CAAS6B,GAAA,CAAIC,MAAA;YACnB,KAAKC,YAAA,CAAaC,eAAA;cAChBhC,QAAA,CAAS0B,OAAA,GAAU;cACnB;YAEF,KAAKK,YAAA,CAAaE,GAAA;cAChBjC,QAAA,CAASX,KAAA,CAAMnB,GAAA,CAAI,QAAQ;cAC3B;YAEF,KAAK6D,YAAA,CAAaG,IAAA;cAChBlC,QAAA,CAASX,KAAA,CAAMnB,GAAA,CAAI,QAAQ;cAC3B8B,QAAA,CAAS0B,OAAA,GAAU;cACnB;UAGH;UAED,OAAO1B,QAAA,CAAS6B,GAAA,CAAIC,MAAA;QACrB;QAID,IAAIZ,aAAA,EAAe;UACjBlB,QAAA,CAAS6B,GAAA,CAAIM,MAAA,CAAO5B,IAAA,CAAKW,aAAA,CAAciB,MAAM;UAC7CnC,QAAA,CAAS6B,GAAA,CAAIO,QAAA,GAAWlB,aAAA,CAAckB,QAAA;UACtCpC,QAAA,CAAS6B,GAAA,CAAIQ,MAAA,CAAO9B,IAAA,CAAKW,aAAA,CAAcjD,KAAK;UAC5C+B,QAAA,CAAS6B,GAAA,CAAIS,MAAA,CAAO/B,IAAA,CAAKW,aAAA,CAAcqB,WAAW;QACnD;MACF;MAED,OAAOvC,QAAA;IACR;IAED,SAASzD,kBAAkB7C,IAAA,EAAM;MAC/B,MAAMyH,YAAA,GAAe,CAAE;MAEvB,MAAMrH,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YAEH;UAEF,KAAK;YACH0D,YAAA,CAAaC,YAAA,GAAe,IAAIoB,KAAA,CAAM5G,WAAA,CAAY,CAAC,GAAGA,WAAA,CAAY,CAAC,GAAGA,WAAA,CAAY,CAAC,CAAC;YACpF;UAEF,KAAK;YACHuF,YAAA,CAAaE,aAAA,GAAgB,IAAImB,KAAA,CAAM5G,WAAA,CAAY,CAAC,GAAGA,WAAA,CAAY,CAAC,GAAGA,WAAA,CAAY,CAAC,CAAC;YACrF;UAEF,KAAK;YACHuF,YAAA,CAAaG,SAAA,GAAY1F,WAAA,CAAY,CAAC;YACtC;UAEF,KAAK;YACHuF,YAAA,CAAaE,aAAA,GAAgB,IAAImB,KAAA,CAAM5G,WAAA,CAAY,CAAC,GAAGA,WAAA,CAAY,CAAC,GAAGA,WAAA,CAAY,CAAC,CAAC;YACrF;UAEF,KAAK;YACHuF,YAAA,CAAaM,YAAA,GAAe7F,WAAA,CAAY,CAAC;YACzC;UAEF;YACE3G,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAED,OAAO0D,YAAA;IACR;IAED,SAASsB,cAAcC,GAAA,EAAKC,WAAA,EAAatD,KAAA,EAAO;MAC9C,IAAIuD,KAAA;MAEJ,QAAQD,WAAA;QACN,KAAKZ,YAAA,CAAac,SAAA;UAEhBD,KAAA,GAAQE,QAAA,CAASJ,GAAG;UACpBrD,KAAA,CAAM0D,CAAA,GAAIH,KAAA;UACVvD,KAAA,CAAM2D,CAAA,GAAIJ,KAAA;UACVvD,KAAA,CAAM4D,CAAA,GAAIL,KAAA;UACVvD,KAAA,CAAM6D,CAAA,GAAI;UACV;QAEF,KAAKnB,YAAA,CAAaC,eAAA;UAEhBY,KAAA,GAAQE,QAAA,CAAS,OAAOJ,GAAA,CAAIS,SAAA,CAAU,GAAG,CAAC,CAAC;UAC3C9D,KAAA,CAAM0D,CAAA,GAAIH,KAAA;UACVvD,KAAA,CAAM2D,CAAA,GAAIJ,KAAA;UACVvD,KAAA,CAAM4D,CAAA,GAAIL,KAAA;UACVvD,KAAA,CAAM6D,CAAA,GAAIJ,QAAA,CAAS,OAAOJ,GAAA,CAAIS,SAAA,CAAU,GAAG,CAAC,CAAC;UAC7C;QAEF,KAAKpB,YAAA,CAAaE,GAAA;UAEhB5C,KAAA,CAAM0D,CAAA,GAAID,QAAA,CAAS,OAAOJ,GAAA,CAAIS,SAAA,CAAU,GAAG,CAAC,CAAC;UAC7C9D,KAAA,CAAM2D,CAAA,GAAIF,QAAA,CAAS,OAAOJ,GAAA,CAAIS,SAAA,CAAU,GAAG,CAAC,CAAC;UAC7C9D,KAAA,CAAM4D,CAAA,GAAIH,QAAA,CAAS,OAAOJ,GAAA,CAAIS,SAAA,CAAU,GAAG,CAAC,CAAC;UAC7C9D,KAAA,CAAM6D,CAAA,GAAI;UACV;QAEF,KAAKnB,YAAA,CAAaG,IAAA;UAEhB7C,KAAA,CAAM0D,CAAA,GAAID,QAAA,CAAS,OAAOJ,GAAA,CAAIS,SAAA,CAAU,GAAG,CAAC,CAAC;UAC7C9D,KAAA,CAAM2D,CAAA,GAAIF,QAAA,CAAS,OAAOJ,GAAA,CAAIS,SAAA,CAAU,GAAG,CAAC,CAAC;UAC7C9D,KAAA,CAAM4D,CAAA,GAAIH,QAAA,CAAS,OAAOJ,GAAA,CAAIS,SAAA,CAAU,GAAG,CAAC,CAAC;UAC7C9D,KAAA,CAAM6D,CAAA,GAAIJ,QAAA,CAAS,OAAOJ,GAAA,CAAIS,SAAA,CAAU,GAAG,EAAE,CAAC;UAC9C;MAGH;IACF;IAED,SAASC,eAAeC,cAAA,EAAgB;MACtC,IAAIpJ,IAAA;MAEJ,QAAQoJ,cAAA;QACN,KAAK;UACHpJ,IAAA,GAAO8H,YAAA,CAAac,SAAA;UACpB;QAEF,KAAK;UACH5I,IAAA,GAAO8H,YAAA,CAAaC,eAAA;UACpB;QAEF,KAAK;UACH/H,IAAA,GAAO8H,YAAA,CAAaE,GAAA;UACpB;QAEF,KAAK;UACHhI,IAAA,GAAO8H,YAAA,CAAaG,IAAA;UACpB;MAGH;MAED,OAAOjI,IAAA;IACR;IAED,SAASwC,sBAAsB/C,IAAA,EAAM;MACnC,IAAI4J,OAAA;MACJ,IAAIC,KAAA,GAAQC,cAAA;MACZ,IAAIC,KAAA,GAAQD,cAAA;MAEZ,MAAM1J,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YACH,MAAMiG,KAAA,GAAQ9H,WAAA,CAAY,CAAC;YAC3B,MAAM+H,MAAA,GAAS/H,WAAA,CAAY,CAAC;YAC5B,MAAMyH,cAAA,GAAiBzH,WAAA,CAAY,CAAC;YAEpC,MAAM+G,WAAA,GAAcS,cAAA,CAAeC,cAAc;YAEjD,MAAM9N,KAAA,GAAO,IAAIqO,UAAA,CAAW,IAAIF,KAAA,GAAQC,MAAM;YAE9C,MAAMtE,KAAA,GAAQ;cAAE0D,CAAA,EAAG;cAAGC,CAAA,EAAG;cAAGC,CAAA,EAAG;cAAGC,CAAA,EAAG;YAAG;YAExC,SAASrH,CAAA,GAAI,GAAGgI,CAAA,GAAI,GAAG/H,EAAA,GAAKF,WAAA,CAAYnF,MAAA,EAAQoF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAKgI,CAAA,IAAK;cAChEpB,aAAA,CAAc7G,WAAA,CAAYC,CAAC,GAAG8G,WAAA,EAAatD,KAAK;cAEhD,MAAMyE,MAAA,GAASD,CAAA,GAAI;cAEnBtO,KAAA,CAAKuO,MAAA,GAAS,CAAC,IAAIzE,KAAA,CAAM0D,CAAA;cACzBxN,KAAA,CAAKuO,MAAA,GAAS,CAAC,IAAIzE,KAAA,CAAM2D,CAAA;cACzBzN,KAAA,CAAKuO,MAAA,GAAS,CAAC,IAAIzE,KAAA,CAAM4D,CAAA;cACzB1N,KAAA,CAAKuO,MAAA,GAAS,CAAC,IAAIzE,KAAA,CAAM6D,CAAA;YAC1B;YAEDI,OAAA,GAAU,IAAIS,WAAA,CAAYxO,KAAA,EAAMmO,KAAA,EAAOC,MAAM;YAC7CL,OAAA,CAAQU,WAAA,GAAc;YACtBV,OAAA,CAAQxB,MAAA,GAASa,WAAA;YACjB;UAEF,KAAK;YACH,IAAI/G,WAAA,CAAY,CAAC,MAAM,OAAO2H,KAAA,GAAQU,mBAAA;YACtC;UAEF,KAAK;YACH,IAAIrI,WAAA,CAAY,CAAC,MAAM,OAAO6H,KAAA,GAAQQ,mBAAA;YACtC;UAEF;YACEhP,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAED,IAAI6F,OAAA,EAAS;QACXA,OAAA,CAAQC,KAAA,GAAQA,KAAA;QAChBD,OAAA,CAAQG,KAAA,GAAQA,KAAA;MACjB;MAED,OAAOH,OAAA;IACR;IAED,SAAS9G,sBAAsB9C,IAAA,EAAM;MACnC,IAAI4J,OAAA;MACJ,IAAIC,KAAA,GAAQC,cAAA;MACZ,IAAIC,KAAA,GAAQD,cAAA;MAEZ,MAAM1J,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YACH,MAAM1J,GAAA,GAAM6H,WAAA,CAAY,CAAC;YACzB,IAAI7H,GAAA,EAAKuP,OAAA,GAAUY,aAAA,CAAcpQ,IAAA,CAAKC,GAAG;YACzC;UAEF,KAAK;YACH,IAAI6H,WAAA,CAAY,CAAC,MAAM,OAAO2H,KAAA,GAAQU,mBAAA;YACtC;UAEF,KAAK;YACH,IAAIrI,WAAA,CAAY,CAAC,MAAM,OAAO6H,KAAA,GAAQQ,mBAAA;YACtC;UAEF;YACEhP,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAED,IAAI6F,OAAA,EAAS;QACXA,OAAA,CAAQC,KAAA,GAAQA,KAAA;QAChBD,OAAA,CAAQG,KAAA,GAAQA,KAAA;MACjB;MAED,OAAOH,OAAA;IACR;IAED,SAAS5G,0BAA0BhD,IAAA,EAAM;MACvC,MAAMwH,aAAA,GAAgB;QACpBiB,MAAA,EAAQ,IAAIgC,OAAA,CAAS;QACrB/B,QAAA,EAAU,IAAI+B,OAAA,CAAS;QACvBlG,KAAA,EAAO,IAAIkG,OAAA,CAAS;QACpB5B,WAAA,EAAa,IAAI4B,OAAA,CAAS;MAC3B;MAED,MAAMrK,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YACHyD,aAAA,CAAciB,MAAA,CAAOjE,GAAA,CAAItC,WAAA,CAAY,CAAC,GAAGA,WAAA,CAAY,CAAC,CAAC;YACvD;UAEF,KAAK;YACHsF,aAAA,CAAckB,QAAA,GAAWxG,WAAA,CAAY,CAAC;YACtC;UAEF,KAAK;YACHsF,aAAA,CAAcjD,KAAA,CAAMC,GAAA,CAAItC,WAAA,CAAY,CAAC,GAAGA,WAAA,CAAY,CAAC,CAAC;YACtD;UAEF,KAAK;YACHsF,aAAA,CAAcqB,WAAA,CAAYrE,GAAA,CAAItC,WAAA,CAAY,CAAC,GAAGA,WAAA,CAAY,CAAC,CAAC;YAC5D;UAEF;YACE3G,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAED,OAAOyD,aAAA;IACR;IAED,SAAS9D,mBAAmB1D,IAAA,EAAM;MAChC,OAAOA,IAAA,CAAKI,MAAA,CAAO,CAAC,EAAEI,MAAA;IACvB;IAED,SAASmD,mBAAmB3D,IAAA,EAAM;MAChC,MAAMiC,SAAA,GAAY,CAAE;MAEpB,MAAM7B,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YACH9B,SAAA,CAAUyI,KAAA,GAAQxI,WAAA,CAAY,CAAC;YAC/B;UAEF,KAAK;YACHD,SAAA,CAAU0I,IAAA,GAAOzI,WAAA;YACjB;UAEF;YACE3G,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAED,OAAO9B,SAAA;IACR;IAED,SAASgB,wBAAwBjD,IAAA,EAAM;MACrC,IAAI2F,KAAA,EAAOiF,KAAA,EAAOC,MAAA,EAAQC,QAAA;MAC1B,IAAIC,GAAA,GAAM;QACRC,KAAA,GAAQ;QACRC,WAAA,GAAc;MAChB,IAAIC,UAAA,EAAYC,UAAA,EAAYC,WAAA,EAAaC,aAAA;MACzC,IAAIC,cAAA,GAAiB;QACnBC,eAAA,GAAkB;MAEpB,MAAMnL,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YACH,MAAMyH,SAAA,GAAYtJ,WAAA,CAAY,CAAC;YAE/B,IAAIsJ,SAAA,KAAc,MAAM;cACtB7F,KAAA,GAAQ9D,OAAA,CAAQ2J,SAAS;YAC1B;YAED;UAEF,KAAK;YACH,MAAMC,SAAA,GAAYvJ,WAAA,CAAY,CAAC;YAE/B,IAAIuJ,SAAA,KAAc,MAAM;cACtBb,KAAA,GAAQ/I,OAAA,CAAQ4J,SAAS;YAC1B;YAED;UAEF,KAAK;YACH,MAAMC,UAAA,GAAaxJ,WAAA,CAAY,CAAC;YAEhC,IAAIwJ,UAAA,KAAe,MAAM;cACvBb,MAAA,GAAShJ,OAAA,CAAQ6J,UAAU;YAC5B;YAED;UAEF,KAAK;YACH,MAAMC,YAAA,GAAezJ,WAAA,CAAY,CAAC;YAElC,IAAIyJ,YAAA,KAAiB,MAAM;cACzBb,QAAA,GAAWjJ,OAAA,CAAQ8J,YAAY;YAChC;YAED;UAEF,KAAK;YACHZ,GAAA,GAAM7I,WAAA,CAAY,CAAC;YACnB;UAEF,KAAK;YACHgJ,UAAA,GAAahJ,WAAA;YACb;UAEF,KAAK;YACHoJ,cAAA,GAAiBpJ,WAAA,CAAY,CAAC;YAC9B;UAEF,KAAK;YAEH;UAEF,KAAK;YACHiJ,UAAA,GAAajJ,WAAA;YACb;UAEF,KAAK;YACH+I,WAAA,GAAc/I,WAAA,CAAY,CAAC;YAC3B;UAEF,KAAK;YACHkJ,WAAA,GAAclJ,WAAA;YACd;UAEF,KAAK;YACHqJ,eAAA,GAAkBrJ,WAAA,CAAY,CAAC;YAC/B;UAEF,KAAK;YACH8I,KAAA,GAAQ9I,WAAA,CAAY,CAAC;YACrB;UAEF,KAAK;YACHmJ,aAAA,GAAgBnJ,WAAA;YAChB;UAEF;YACE3G,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAED,IAAIoH,UAAA,KAAe,QAAW;QAC5B5P,OAAA,CAAQqI,IAAA,CAAK,uCAAuC;QAEpD,OAAO,IAAIgI,cAAA,CAAgB;MAC5B;MAED,MAAMC,sBAAA,GAAyBC,oBAAA,CAAqBX,UAAA,EAAYJ,GAAG;MAEnE,IAAIgB,cAAA;MACJ,IAAIC,eAAA;MACJ,IAAIC,WAAA;MAEJ,IAAItG,KAAA,EAAO;QACT,IAAI2F,cAAA,KAAmB,MAAM;UAC3B,IAAIJ,UAAA,IAAcA,UAAA,CAAWnO,MAAA,GAAS,GAAG;YAGvC,MAAMmP,sBAAA,GAAyBJ,oBAAA,CAAqBZ,UAAA,EAAYH,GAAG;YACnEgB,cAAA,GAAiBI,+BAAA,CAAgCN,sBAAA,EAAwBK,sBAAA,EAAwBvG,KAAA,EAAO,CAAC;UACrH,OAAiB;YAGLoG,cAAA,GAAiBK,qBAAA,CAAsBP,sBAAA,EAAwB,IAAIQ,sBAAA,CAAuB1G,KAAA,EAAO,CAAC,CAAC;UACpG;QACX,OAAe;UACL,IAAIuF,UAAA,IAAcA,UAAA,CAAWnO,MAAA,GAAS,GAAG;YAGvC,MAAMuP,iBAAA,GAAoBC,WAAA,CAAY5G,KAAA,EAAOuF,UAAU;YACvD,MAAMsB,sBAAA,GAAyBC,mBAAA,CAAoBH,iBAAA,EAAmBnB,UAAU;YAChFY,cAAA,GAAiBW,4BAAA,CAA6Bb,sBAAA,EAAwBW,sBAAsB;UACxG,OAAiB;YAGL,MAAMA,sBAAA,GAAyBC,mBAAA,CAAoB9G,KAAA,EAAOwF,UAAU;YACpEY,cAAA,GAAiBW,4BAAA,CAA6Bb,sBAAA,EAAwBW,sBAAsB;UAC7F;QACF;MACF;MAED,IAAI3B,MAAA,EAAQ;QACV,IAAIU,eAAA,KAAoB,MAAM;UAG5B,IAAIH,WAAA,IAAeA,WAAA,CAAYrO,MAAA,GAAS,GAAG;YAGzC,MAAM4P,uBAAA,GAA0Bb,oBAAA,CAAqBV,WAAA,EAAaL,GAAG;YACrEiB,eAAA,GAAkBG,+BAAA,CAChBN,sBAAA,EACAc,uBAAA,EACA9B,MAAA,EACA,CACD;UACb,OAAiB;YAGLmB,eAAA,GAAkBI,qBAAA,CAAsBP,sBAAA,EAAwB,IAAIQ,sBAAA,CAAuBxB,MAAA,EAAQ,CAAC,CAAC;UACtG;QACX,OAAe;UAGL,IAAIO,WAAA,IAAeA,WAAA,CAAYrO,MAAA,GAAS,GAAG;YAGzC,MAAM6P,kBAAA,GAAqBL,WAAA,CAAY1B,MAAA,EAAQO,WAAW;YAC1D,MAAMyB,uBAAA,GAA0BJ,mBAAA,CAAoBG,kBAAA,EAAoBzB,UAAU;YAClFa,eAAA,GAAkBU,4BAAA,CAA6Bb,sBAAA,EAAwBgB,uBAAuB;UAC1G,OAAiB;YAGL,MAAMA,uBAAA,GAA0BJ,mBAAA,CAAoB5B,MAAA,EAAQM,UAAU;YACtEa,eAAA,GAAkBU,4BAAA,CAA6Bb,sBAAA,EAAwBgB,uBAAuB;UAC/F;QACF;MACT,OAAa;QAGLb,eAAA,GAAkBc,sBAAA,CAAuBjB,sBAAA,EAAwBjB,KAAA,EAAOK,WAAW;MACpF;MAED,IAAIH,QAAA,EAAU;QAGZ,IAAIO,aAAA,IAAiBA,aAAA,CAActO,MAAA,GAAS,GAAG;UAG7C,MAAMgQ,yBAAA,GAA4BjB,oBAAA,CAAqBT,aAAA,EAAeN,GAAG;UACzEkB,WAAA,GAAcE,+BAAA,CAAgCN,sBAAA,EAAwBkB,yBAAA,EAA2BjC,QAAA,EAAU,CAAC;QACtH,OAAe;UAGLmB,WAAA,GAAcG,qBAAA,CAAsBP,sBAAA,EAAwB,IAAIQ,sBAAA,CAAuBvB,QAAA,EAAU,CAAC,CAAC;QACpG;MACF;MAED,MAAMvE,QAAA,GAAW,IAAIqF,cAAA,CAAgB;MACrC,MAAMoB,iBAAA,GAAoBZ,qBAAA,CAAsBP,sBAAA,EAAwB,IAAIQ,sBAAA,CAAuBzB,KAAA,EAAO,CAAC,CAAC;MAE5GrE,QAAA,CAAS0G,YAAA,CAAa,YAAYD,iBAAiB;MACnDzG,QAAA,CAAS0G,YAAA,CAAa,UAAUjB,eAAe;MAI/C,IAAID,cAAA,EAAgBxF,QAAA,CAAS0G,YAAA,CAAa,SAASlB,cAAc;MACjE,IAAIE,WAAA,EAAa1F,QAAA,CAAS0G,YAAA,CAAa,MAAMhB,WAAW;MAIxD1F,QAAA,CAASY,MAAA,GAAS6D,KAAA;MAClBzE,QAAA,CAASE,KAAA,GAAQ;MAEjB,OAAOF,QAAA;IACR;IAED,SAASrD,wBAAwBlD,IAAA,EAAM;MACrC,IAAI2F,KAAA,EAAOiF,KAAA;MACX,IAAIM,UAAA,EAAYC,UAAA;MAChB,IAAIG,cAAA,GAAiB;MAErB,MAAMlL,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YACH,MAAMyH,SAAA,GAAYtJ,WAAA,CAAY,CAAC;YAE/B,IAAIsJ,SAAA,KAAc,MAAM;cACtB7F,KAAA,GAAQ9D,OAAA,CAAQ2J,SAAS;YAC1B;YAED;UAEF,KAAK;YACH,MAAMC,SAAA,GAAYvJ,WAAA,CAAY,CAAC;YAE/B,IAAIuJ,SAAA,KAAc,MAAM;cACtBb,KAAA,GAAQ/I,OAAA,CAAQ4J,SAAS;YAC1B;YAED;UAEF,KAAK;YACHP,UAAA,GAAahJ,WAAA;YACb;UAEF,KAAK;YACHoJ,cAAA,GAAiBpJ,WAAA,CAAY,CAAC;YAC9B;UAEF,KAAK;YACHiJ,UAAA,GAAajJ,WAAA;YACb;UAEF;YACE3G,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAID,IAAIgI,cAAA;MAEJ,MAAMmB,iBAAA,GAAoBC,eAAA,CAAgBhC,UAAU;MAEpD,IAAIxF,KAAA,EAAO;QACT,IAAI2F,cAAA,KAAmB,MAAM;UAC3B,IAAIJ,UAAA,CAAWnO,MAAA,GAAS,GAAG;YAGzB,MAAMqQ,kBAAA,GAAqBD,eAAA,CAAgBjC,UAAU;YACrDa,cAAA,GAAiBI,+BAAA,CAAgCe,iBAAA,EAAmBE,kBAAA,EAAoBzH,KAAA,EAAO,CAAC;UAC5G,OAAiB;YAGLoG,cAAA,GAAiBK,qBAAA,CAAsBc,iBAAA,EAAmB,IAAIb,sBAAA,CAAuB1G,KAAA,EAAO,CAAC,CAAC;UAC/F;QACX,OAAe;UACL,IAAIuF,UAAA,CAAWnO,MAAA,GAAS,GAAG;YAGzB,MAAMsQ,iBAAA,GAAoBd,WAAA,CAAY5G,KAAA,EAAOuF,UAAU;YACvD,MAAMoC,kBAAA,GAAqBC,cAAA,CAAeF,iBAAA,EAAmBlC,UAAU;YACvEY,cAAA,GAAiByB,4BAAA,CAA6BN,iBAAA,EAAmBI,kBAAkB;UAC/F,OAAiB;YAGL,MAAMA,kBAAA,GAAqBC,cAAA,CAAe5H,KAAA,EAAOwF,UAAU;YAC3DY,cAAA,GAAiByB,4BAAA,CAA6BN,iBAAA,EAAmBI,kBAAkB;UACpF;QACF;MACF;MAID,MAAM/G,QAAA,GAAW,IAAIqF,cAAA,CAAgB;MAErC,MAAMoB,iBAAA,GAAoBZ,qBAAA,CAAsBc,iBAAA,EAAmB,IAAIb,sBAAA,CAAuBzB,KAAA,EAAO,CAAC,CAAC;MACvGrE,QAAA,CAAS0G,YAAA,CAAa,YAAYD,iBAAiB;MAEnD,IAAIjB,cAAA,EAAgBxF,QAAA,CAAS0G,YAAA,CAAa,SAASlB,cAAc;MAEjExF,QAAA,CAASE,KAAA,GAAQ;MAEjB,OAAOF,QAAA;IACR;IAED,SAASpD,kBAAkBnD,IAAA,EAAM;MAC/B,IAAI2F,KAAA,EAAOiF,KAAA;MAEX,MAAMxK,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YACH,MAAMyH,SAAA,GAAYtJ,WAAA,CAAY,CAAC;YAE/B,IAAIsJ,SAAA,KAAc,MAAM;cACtB7F,KAAA,GAAQ9D,OAAA,CAAQ2J,SAAS;YAC1B;YAED;UAEF,KAAK;YACH,MAAMC,SAAA,GAAYvJ,WAAA,CAAY,CAAC;YAE/B,IAAIuJ,SAAA,KAAc,MAAM;cACtBb,KAAA,GAAQ/I,OAAA,CAAQ4J,SAAS;YAC1B;YAED;UAEF;YACElQ,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAED,MAAMwC,QAAA,GAAW,IAAIqF,cAAA,CAAgB;MAErCrF,QAAA,CAAS0G,YAAA,CAAa,YAAY,IAAIZ,sBAAA,CAAuBzB,KAAA,EAAO,CAAC,CAAC;MACtE,IAAIjF,KAAA,EAAOY,QAAA,CAAS0G,YAAA,CAAa,SAAS,IAAIZ,sBAAA,CAAuB1G,KAAA,EAAO,CAAC,CAAC;MAE9EY,QAAA,CAASE,KAAA,GAAQ;MAEjB,OAAOF,QAAA;IACR;IAED,SAASnD,aAAapD,IAAA,EAAM;MAC1B,MAAMyN,IAAA,GAAO,IAAIvJ,OAAA,CAAQ,GAAG,GAAG,CAAC;MAEhC,MAAM9D,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YACH0J,IAAA,CAAKC,CAAA,GAAIxL,WAAA,CAAY,CAAC;YACtBuL,IAAA,CAAKE,CAAA,GAAIzL,WAAA,CAAY,CAAC;YACtBuL,IAAA,CAAKG,CAAA,GAAI1L,WAAA,CAAY,CAAC;YACtB;UAEF;YACE3G,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAED,MAAMwC,QAAA,GAAW,IAAIsH,WAAA,CAAYJ,IAAA,CAAKC,CAAA,EAAGD,IAAA,CAAKE,CAAA,EAAGF,IAAA,CAAKG,CAAC;MAEvD,OAAOrH,QAAA;IACR;IAED,SAASlD,cAAcrD,IAAA,EAAM;MAC3B,IAAI8E,MAAA,GAAS;QACXmF,MAAA,GAAS;QACT6D,SAAA,GAAY;MAEd,MAAM1N,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YACH+J,SAAA,GAAY,CAAC5L,WAAA,CAAY,CAAC;YAC1B;UAEF,KAAK;YACH4C,MAAA,GAAS5C,WAAA,CAAY,CAAC;YACtB;UAEF,KAAK;YACH+H,MAAA,GAAS/H,WAAA,CAAY,CAAC;YACtB;UAEF,KAAK;YAEH;UAEF;YACE3G,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAED,MAAMwC,QAAA,GAAW,IAAIwH,YAAA,CAAajJ,MAAA,EAAQmF,MAAA,EAAQ,IAAI,GAAG6D,SAAS;MAElE,OAAOvH,QAAA;IACR;IAED,SAASjD,kBAAkBtD,IAAA,EAAM;MAC/B,IAAI8E,MAAA,GAAS;QACXmF,MAAA,GAAS;MAEX,MAAM7J,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YAEH;UAEF,KAAK;YACHe,MAAA,GAAS5C,WAAA,CAAY,CAAC;YACtB;UAEF,KAAK;YACH+H,MAAA,GAAS/H,WAAA,CAAY,CAAC;YACtB;UAEF,KAAK;YAEH;UAEF,KAAK;YAEH;UAEF;YACE3G,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAED,MAAMwC,QAAA,GAAW,IAAIyH,gBAAA,CAAiBlJ,MAAA,EAAQA,MAAA,EAAQmF,MAAA,EAAQ,IAAI,CAAC;MAEnE,OAAO1D,QAAA;IACR;IAED,SAAShD,gBAAgBvD,IAAA,EAAM;MAC7B,IAAI8E,MAAA,GAAS;MAEb,MAAM1E,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YACHe,MAAA,GAAS5C,WAAA,CAAY,CAAC;YACtB;UAEF;YACE3G,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAED,MAAMwC,QAAA,GAAW,IAAIvB,cAAA,CAAeF,MAAA,EAAQ,IAAI,EAAE;MAElD,OAAOyB,QAAA;IACR;IAED,SAAS/C,uBAAuBxD,IAAA,EAAM;MACpC,IAAI2F,KAAA;MACJ,IAAIkF,MAAA;MACJ,IAAIC,QAAA;MACJ,IAAIb,MAAA;MAEJ,IAAIqB,cAAA,GAAiB;MACrB,IAAIC,eAAA,GAAkB;MACtB,IAAIP,KAAA,GAAQ;MACZ,IAAID,GAAA,GAAM;MACV,IAAIE,WAAA,GAAc;MAClB,IAAIgD,UAAA,GAAa;MACjB,IAAIC,UAAA,GAAa;MACjB,IAAIC,QAAA,GAAW;MACf,IAAIC,QAAA,GAAW;MAEf,MAAMhO,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YACH,MAAMyH,SAAA,GAAYtJ,WAAA,CAAY,CAAC;YAE/B,IAAIsJ,SAAA,KAAc,MAAM;cACtB7F,KAAA,GAAQ9D,OAAA,CAAQ2J,SAAS;YAC1B;YAED;UAEF,KAAK;YACH,MAAME,UAAA,GAAaxJ,WAAA,CAAY,CAAC;YAEhC,IAAIwJ,UAAA,KAAe,MAAM;cACvBb,MAAA,GAAShJ,OAAA,CAAQ6J,UAAU;YAC5B;YAED;UAEF,KAAK;YACH,MAAMC,YAAA,GAAezJ,WAAA,CAAY,CAAC;YAElC,IAAIyJ,YAAA,KAAiB,MAAM;cACzBb,QAAA,GAAWjJ,OAAA,CAAQ8J,YAAY;YAChC;YAED;UAEF,KAAK;YACH1B,MAAA,GAAS/H,WAAA;YACT;UAEF,KAAK;YACH6I,GAAA,GAAM7I,WAAA,CAAY,CAAC;YACnB;UAEF,KAAK;YACHoJ,cAAA,GAAiBpJ,WAAA,CAAY,CAAC;YAC9B;UAEF,KAAK;YACH+I,WAAA,GAAc/I,WAAA,CAAY,CAAC;YAC3B;UAEF,KAAK;YACHqJ,eAAA,GAAkBrJ,WAAA,CAAY,CAAC;YAC/B;UAEF,KAAK;YACH8I,KAAA,GAAQ9I,WAAA,CAAY,CAAC;YACrB;UAEF,KAAK;YACH+L,UAAA,GAAa/L,WAAA,CAAY,CAAC;YAC1B;UAEF,KAAK;YACHiM,QAAA,GAAWjM,WAAA,CAAY,CAAC;YACxB;UAEF,KAAK;YACHgM,UAAA,GAAahM,WAAA,CAAY,CAAC;YAC1B;UAEF,KAAK;YACHkM,QAAA,GAAWlM,WAAA,CAAY,CAAC;YACxB;UAEF;YACE3G,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAID,MAAMsK,QAAA,GAAW,EAAE;MACnB,MAAMC,OAAA,GAAU,EAAE;MAClB,MAAMC,MAAA,GAAS,EAAE;MACjB,MAAMC,GAAA,GAAM,EAAE;MAEd,SAASvP,CAAA,GAAI,GAAGA,CAAA,GAAIiP,UAAA,EAAYjP,CAAA,IAAK;QACnC,SAASkD,CAAA,GAAI,GAAGA,CAAA,GAAI8L,UAAA,EAAY9L,CAAA,IAAK;UAGnC,MAAMsM,KAAA,GAAQxP,CAAA,GAAIgP,UAAA,GAAa9L,CAAA;UAI/B,MAAMuL,CAAA,GAAIS,QAAA,GAAWlP,CAAA;UACrB,MAAM0O,CAAA,GAAI1D,MAAA,CAAOwE,KAAK;UACtB,MAAMb,CAAA,GAAIQ,QAAA,GAAWjM,CAAA;UAErBkM,QAAA,CAASpO,IAAA,CAAKyN,CAAA,EAAGC,CAAA,EAAGC,CAAC;UAIrB,IAAIjI,KAAA,IAAS2F,cAAA,KAAmB,MAAM;YACpC,MAAMjC,CAAA,GAAI1D,KAAA,CAAM8I,KAAA,GAAQ,IAAI,CAAC;YAC7B,MAAMnF,CAAA,GAAI3D,KAAA,CAAM8I,KAAA,GAAQ,IAAI,CAAC;YAC7B,MAAMlF,CAAA,GAAI5D,KAAA,CAAM8I,KAAA,GAAQ,IAAI,CAAC;YAE7BF,MAAA,CAAOtO,IAAA,CAAKoJ,CAAA,EAAGC,CAAA,EAAGC,CAAC;UACpB;UAID,IAAIsB,MAAA,IAAUU,eAAA,KAAoB,MAAM;YACtC,MAAMmD,EAAA,GAAK7D,MAAA,CAAO4D,KAAA,GAAQ,IAAI,CAAC;YAC/B,MAAME,EAAA,GAAK9D,MAAA,CAAO4D,KAAA,GAAQ,IAAI,CAAC;YAC/B,MAAMG,EAAA,GAAK/D,MAAA,CAAO4D,KAAA,GAAQ,IAAI,CAAC;YAE/BH,OAAA,CAAQrO,IAAA,CAAKyO,EAAA,EAAIC,EAAA,EAAIC,EAAE;UACxB;UAID,IAAI9D,QAAA,EAAU;YACZ,MAAM+D,CAAA,GAAI/D,QAAA,CAAS2D,KAAA,GAAQ,IAAI,CAAC;YAChC,MAAMK,CAAA,GAAIhE,QAAA,CAAS2D,KAAA,GAAQ,IAAI,CAAC;YAEhCD,GAAA,CAAIvO,IAAA,CAAK4O,CAAA,EAAGC,CAAC;UACzB,OAAiB;YACLN,GAAA,CAAIvO,IAAA,CAAKhB,CAAA,IAAKgP,UAAA,GAAa,IAAI9L,CAAA,IAAK+L,UAAA,GAAa,EAAE;UACpD;QACF;MACF;MAID,MAAMa,OAAA,GAAU,EAAE;MAElB,SAAS9P,CAAA,GAAI,GAAGA,CAAA,GAAIgP,UAAA,GAAa,GAAGhP,CAAA,IAAK;QACvC,SAASkD,CAAA,GAAI,GAAGA,CAAA,GAAI+L,UAAA,GAAa,GAAG/L,CAAA,IAAK;UAGvC,MAAMqH,CAAA,GAAIvK,CAAA,GAAIkD,CAAA,GAAI8L,UAAA;UAClB,MAAM1E,CAAA,GAAItK,CAAA,IAAKkD,CAAA,GAAI,KAAK8L,UAAA;UACxB,MAAMe,CAAA,GAAI/P,CAAA,GAAI,KAAKkD,CAAA,GAAI,KAAK8L,UAAA;UAC5B,MAAMgB,CAAA,GAAIhQ,CAAA,GAAI,IAAIkD,CAAA,GAAI8L,UAAA;UAItB,IAAIlD,GAAA,KAAQ,MAAM;YAChBgE,OAAA,CAAQ9O,IAAA,CAAKuJ,CAAA,EAAGwF,CAAA,EAAGzF,CAAC;YACpBwF,OAAA,CAAQ9O,IAAA,CAAK+O,CAAA,EAAGxF,CAAA,EAAGyF,CAAC;UAChC,OAAiB;YACLF,OAAA,CAAQ9O,IAAA,CAAKuJ,CAAA,EAAGD,CAAA,EAAGyF,CAAC;YACpBD,OAAA,CAAQ9O,IAAA,CAAK+O,CAAA,EAAGC,CAAA,EAAGzF,CAAC;UACrB;QACF;MACF;MAID,MAAMwD,iBAAA,GAAoBZ,qBAAA,CAAsB2C,OAAA,EAAS,IAAI1C,sBAAA,CAAuBgC,QAAA,EAAU,CAAC,CAAC;MAChG,MAAMpC,WAAA,GAAcG,qBAAA,CAAsB2C,OAAA,EAAS,IAAI1C,sBAAA,CAAuBmC,GAAA,EAAK,CAAC,CAAC;MACrF,IAAIzC,cAAA;MACJ,IAAIC,eAAA;MAIJ,IAAIrG,KAAA,EAAO;QACT,IAAI2F,cAAA,KAAmB,OAAO;UAC5B,SAASrM,CAAA,GAAI,GAAGA,CAAA,GAAIgP,UAAA,GAAa,GAAGhP,CAAA,IAAK;YACvC,SAASkD,CAAA,GAAI,GAAGA,CAAA,GAAI+L,UAAA,GAAa,GAAG/L,CAAA,IAAK;cACvC,MAAMsM,KAAA,GAAQxP,CAAA,GAAIkD,CAAA,IAAK8L,UAAA,GAAa;cAEpC,MAAM5E,CAAA,GAAI1D,KAAA,CAAM8I,KAAA,GAAQ,IAAI,CAAC;cAC7B,MAAMnF,CAAA,GAAI3D,KAAA,CAAM8I,KAAA,GAAQ,IAAI,CAAC;cAC7B,MAAMlF,CAAA,GAAI5D,KAAA,CAAM8I,KAAA,GAAQ,IAAI,CAAC;cAI7BF,MAAA,CAAOtO,IAAA,CAAKoJ,CAAA,EAAGC,CAAA,EAAGC,CAAC;cACnBgF,MAAA,CAAOtO,IAAA,CAAKoJ,CAAA,EAAGC,CAAA,EAAGC,CAAC;cACnBgF,MAAA,CAAOtO,IAAA,CAAKoJ,CAAA,EAAGC,CAAA,EAAGC,CAAC;cACnBgF,MAAA,CAAOtO,IAAA,CAAKoJ,CAAA,EAAGC,CAAA,EAAGC,CAAC;cACnBgF,MAAA,CAAOtO,IAAA,CAAKoJ,CAAA,EAAGC,CAAA,EAAGC,CAAC;cACnBgF,MAAA,CAAOtO,IAAA,CAAKoJ,CAAA,EAAGC,CAAA,EAAGC,CAAC;YACpB;UACF;UAEDwC,cAAA,GAAiB,IAAIM,sBAAA,CAAuBkC,MAAA,EAAQ,CAAC;QAC/D,OAAe;UACLxC,cAAA,GAAiBK,qBAAA,CAAsB2C,OAAA,EAAS,IAAI1C,sBAAA,CAAuBkC,MAAA,EAAQ,CAAC,CAAC;QACtF;MACF;MAID,IAAI1D,MAAA,EAAQ;QACV,IAAIU,eAAA,KAAoB,OAAO;UAC7B,SAAStM,CAAA,GAAI,GAAGA,CAAA,GAAIgP,UAAA,GAAa,GAAGhP,CAAA,IAAK;YACvC,SAASkD,CAAA,GAAI,GAAGA,CAAA,GAAI+L,UAAA,GAAa,GAAG/L,CAAA,IAAK;cACvC,MAAMsM,KAAA,GAAQxP,CAAA,GAAIkD,CAAA,IAAK8L,UAAA,GAAa;cAEpC,MAAMS,EAAA,GAAK7D,MAAA,CAAO4D,KAAA,GAAQ,IAAI,CAAC;cAC/B,MAAME,EAAA,GAAK9D,MAAA,CAAO4D,KAAA,GAAQ,IAAI,CAAC;cAC/B,MAAMG,EAAA,GAAK/D,MAAA,CAAO4D,KAAA,GAAQ,IAAI,CAAC;cAI/BH,OAAA,CAAQrO,IAAA,CAAKyO,EAAA,EAAIC,EAAA,EAAIC,EAAE;cACvBN,OAAA,CAAQrO,IAAA,CAAKyO,EAAA,EAAIC,EAAA,EAAIC,EAAE;cACvBN,OAAA,CAAQrO,IAAA,CAAKyO,EAAA,EAAIC,EAAA,EAAIC,EAAE;cACvBN,OAAA,CAAQrO,IAAA,CAAKyO,EAAA,EAAIC,EAAA,EAAIC,EAAE;cACvBN,OAAA,CAAQrO,IAAA,CAAKyO,EAAA,EAAIC,EAAA,EAAIC,EAAE;cACvBN,OAAA,CAAQrO,IAAA,CAAKyO,EAAA,EAAIC,EAAA,EAAIC,EAAE;YACxB;UACF;UAED5C,eAAA,GAAkB,IAAIK,sBAAA,CAAuBiC,OAAA,EAAS,CAAC;QACjE,OAAe;UACLtC,eAAA,GAAkBI,qBAAA,CAAsB2C,OAAA,EAAS,IAAI1C,sBAAA,CAAuBiC,OAAA,EAAS,CAAC,CAAC;QACxF;MACT,OAAa;QACLtC,eAAA,GAAkBc,sBAAA,CAAuBiC,OAAA,EAASV,QAAA,EAAUpD,WAAW;MACxE;MAID,MAAM1E,QAAA,GAAW,IAAIqF,cAAA,CAAgB;MACrCrF,QAAA,CAAS0G,YAAA,CAAa,YAAYD,iBAAiB;MACnDzG,QAAA,CAAS0G,YAAA,CAAa,UAAUjB,eAAe;MAC/CzF,QAAA,CAAS0G,YAAA,CAAa,MAAMhB,WAAW;MAEvC,IAAIF,cAAA,EAAgBxF,QAAA,CAAS0G,YAAA,CAAa,SAASlB,cAAc;MAIjExF,QAAA,CAASY,MAAA,GAAS6D,KAAA;MAClBzE,QAAA,CAASE,KAAA,GAAQ;MAEjB,OAAOF,QAAA;IACR;IAED,SAAS9C,mBAAmBzD,IAAA,EAAM;MAChC,IAAIkP,YAAA,GAAe,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC;MACpD,IAAIC,KAAA,GAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;MAC7B,IAAI5K,KAAA;MACJ,IAAI6K,WAAA;MAEJ,IAAIC,QAAA,GAAW;MACf,IAAItE,GAAA,GAAM;MACV,IAAIE,WAAA,GAAc;MAClB,IAAIqE,MAAA,GAAS;MACb,IAAItE,KAAA,GAAQ;MAEZ,MAAM5K,MAAA,GAASJ,IAAA,CAAKI,MAAA;MAEpB,SAASnB,CAAA,GAAI,GAAGC,CAAA,GAAIkB,MAAA,CAAOrD,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,MAAMoB,KAAA,GAAQD,MAAA,CAAOnB,CAAC;QACtB,MAAM8E,SAAA,GAAY1D,KAAA,CAAMhD,IAAA;QACxB,MAAM6E,WAAA,GAAc7B,KAAA,CAAMG,MAAA;QAE1B,QAAQuD,SAAA;UACN,KAAK;YACHsL,QAAA,GAAWnN,WAAA,CAAY,CAAC;YACxB;UAEF,KAAK;YACH6I,GAAA,GAAM7I,WAAA,CAAY,CAAC;YACnB;UAEF,KAAK;YAEH;UAEF,KAAK;YACH+I,WAAA,GAAc/I,WAAA,CAAY,CAAC;YAC3B;UAEF,KAAK;YACHgN,YAAA,GAAehN,WAAA;YACf;UAEF,KAAK;YACHoN,MAAA,GAASpN,WAAA,CAAY,CAAC;YACtB;UAEF,KAAK;YACHkN,WAAA,GAAclN,WAAA;YACd;UAEF,KAAK;YACHqC,KAAA,GAAQrC,WAAA;YACR;UAEF,KAAK;YACH8I,KAAA,GAAQ9I,WAAA,CAAY,CAAC;YACrB;UAEF,KAAK;YACHiN,KAAA,GAAQjN,WAAA;YACR;UAEF;YACE3G,OAAA,CAAQqI,IAAA,CAAK,oCAAoCG,SAAS;YAC1D;QACH;MACF;MAED,MAAMwL,kBAAA,GACJL,YAAA,CAAa,CAAC,MAAMA,YAAA,CAAaA,YAAA,CAAanS,MAAA,GAAS,CAAC,KACxDmS,YAAA,CAAa,CAAC,MAAMA,YAAA,CAAaA,YAAA,CAAanS,MAAA,GAAS,CAAC;MAI1D,MAAMsR,QAAA,GAAW,EAAE;MACnB,MAAMmB,WAAA,GAAc,IAAItL,OAAA,CAAS;MACjC,MAAMuL,OAAA,GAAU,IAAIvL,OAAA,CAAS;MAE7B,MAAMD,IAAA,GAAO,IAAIC,OAAA,CAAS;MAC1B,MAAMwL,MAAA,GAAS,IAAIxL,OAAA,CAAS;MAC5B,MAAMG,UAAA,GAAa,IAAIsL,UAAA,CAAY;MAEnC,SAAS1Q,CAAA,GAAI,GAAGkD,CAAA,GAAI,GAAGyN,CAAA,GAAI,GAAGC,EAAA,GAAKV,KAAA,CAAMpS,MAAA,EAAQkC,CAAA,GAAI4Q,EAAA,EAAI5Q,CAAA,IAAK,GAAGkD,CAAA,IAAK,GAAGyN,CAAA,IAAK,GAAG;QAC/EJ,WAAA,CAAYM,SAAA,CAAUX,KAAA,EAAOlQ,CAAC;QAE9BwQ,OAAA,CAAQ/B,CAAA,GAAInJ,KAAA,GAAQA,KAAA,CAAMpC,CAAA,GAAI,CAAC,IAAI;QACnCsN,OAAA,CAAQ9B,CAAA,GAAI;QACZ8B,OAAA,CAAQ7B,CAAA,GAAIrJ,KAAA,GAAQA,KAAA,CAAMpC,CAAA,GAAI,CAAC,IAAI;QAEnC8B,IAAA,CAAKyJ,CAAA,GAAI0B,WAAA,GAAcA,WAAA,CAAYQ,CAAA,GAAI,CAAC,IAAI;QAC5C3L,IAAA,CAAK0J,CAAA,GAAIyB,WAAA,GAAcA,WAAA,CAAYQ,CAAA,GAAI,CAAC,IAAI;QAC5C3L,IAAA,CAAK2J,CAAA,GAAIwB,WAAA,GAAcA,WAAA,CAAYQ,CAAA,GAAI,CAAC,IAAI;QAC5C,MAAMxL,KAAA,GAAQgL,WAAA,GAAcA,WAAA,CAAYQ,CAAA,GAAI,CAAC,IAAI;QAEjD,SAASzF,CAAA,GAAI,GAAG4F,EAAA,GAAKb,YAAA,CAAanS,MAAA,EAAQoN,CAAA,GAAI4F,EAAA,EAAI5F,CAAA,IAAK,GAAG;UACxDuF,MAAA,CAAOhC,CAAA,GAAIwB,YAAA,CAAa/E,CAAA,GAAI,CAAC;UAC7BuF,MAAA,CAAO/B,CAAA,GAAI;UACX+B,MAAA,CAAO9B,CAAA,GAAIsB,YAAA,CAAa/E,CAAA,GAAI,CAAC;UAI7BuF,MAAA,CAAOM,QAAA,CAASP,OAAO;UAIvBpL,UAAA,CAAWC,gBAAA,CAAiBL,IAAA,EAAMG,KAAK;UACvCsL,MAAA,CAAOO,eAAA,CAAgB5L,UAAU;UAIjCqL,MAAA,CAAO3N,GAAA,CAAIyN,WAAW;UAEtBnB,QAAA,CAASpO,IAAA,CAAKyP,MAAA,CAAOhC,CAAA,EAAGgC,MAAA,CAAO/B,CAAA,EAAG+B,MAAA,CAAO9B,CAAC;QAC3C;MACF;MAID,MAAMmB,OAAA,GAAU,EAAE;MAElB,MAAMmB,UAAA,GAAaf,KAAA,CAAMpS,MAAA,GAAS;MAClC,MAAMoT,iBAAA,GAAoBjB,YAAA,CAAanS,MAAA,GAAS;MAEhD,SAASkC,CAAA,GAAI,GAAGA,CAAA,GAAIiR,UAAA,GAAa,GAAGjR,CAAA,IAAK;QACvC,SAASkD,CAAA,GAAI,GAAGA,CAAA,GAAIgO,iBAAA,GAAoB,GAAGhO,CAAA,IAAK;UAC9C,MAAMqH,CAAA,GAAIrH,CAAA,GAAIlD,CAAA,GAAIkR,iBAAA;UAClB,IAAI5G,CAAA,GAAIpH,CAAA,GAAI,IAAIlD,CAAA,GAAIkR,iBAAA;UACpB,MAAMnB,CAAA,GAAI7M,CAAA,IAAKlD,CAAA,GAAI,KAAKkR,iBAAA;UACxB,IAAIlB,CAAA,GAAI9M,CAAA,GAAI,KAAKlD,CAAA,GAAI,KAAKkR,iBAAA;UAE1B,IAAIhO,CAAA,KAAMgO,iBAAA,GAAoB,KAAKZ,kBAAA,KAAuB,MAAM;YAC9DhG,CAAA,GAAItK,CAAA,GAAIkR,iBAAA;YACRlB,CAAA,IAAKhQ,CAAA,GAAI,KAAKkR,iBAAA;UACf;UAED,IAAIpF,GAAA,KAAQ,MAAM;YAChBgE,OAAA,CAAQ9O,IAAA,CAAKuJ,CAAA,EAAGD,CAAA,EAAGyF,CAAC;YACpBD,OAAA,CAAQ9O,IAAA,CAAK+O,CAAA,EAAGzF,CAAA,EAAG0F,CAAC;UAChC,OAAiB;YACLF,OAAA,CAAQ9O,IAAA,CAAKuJ,CAAA,EAAGwF,CAAA,EAAGzF,CAAC;YACpBwF,OAAA,CAAQ9O,IAAA,CAAK+O,CAAA,EAAGC,CAAA,EAAG1F,CAAC;UACrB;QACF;MACF;MAID,IAAI8F,QAAA,KAAa,QAAQC,MAAA,KAAW,MAAM;QACxC,MAAMc,OAAA,GAAU,EAAE;QAElB,SAASnR,CAAA,GAAI,GAAGC,CAAA,GAAIgQ,YAAA,CAAanS,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK,GAAG;UACtDmR,OAAA,CAAQnQ,IAAA,CAAK,IAAIwK,OAAA,CAAQyE,YAAA,CAAajQ,CAAC,GAAGiQ,YAAA,CAAajQ,CAAA,GAAI,CAAC,CAAC,CAAC;QAC/D;QAED,MAAMoR,KAAA,GAAQC,UAAA,CAAWC,gBAAA,CAAiBH,OAAA,EAAS,EAAE;QACrD,MAAMI,UAAA,GAAa,EAAE;QAErB,SAASvR,CAAA,GAAI,GAAGC,CAAA,GAAImR,KAAA,CAAMtT,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;UAC5C,MAAMwR,IAAA,GAAOJ,KAAA,CAAMpR,CAAC;UAEpBuR,UAAA,CAAWvQ,IAAA,CAAKwQ,IAAA,CAAK,CAAC,GAAGA,IAAA,CAAK,CAAC,GAAGA,IAAA,CAAK,CAAC,CAAC;QAC1C;QAID,IAAIpB,QAAA,KAAa,MAAM;UACrB,SAASpQ,CAAA,GAAI,GAAGC,CAAA,GAAIsR,UAAA,CAAWzT,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK,GAAG;YACpD,IAAI8L,GAAA,KAAQ,MAAM;cAChBgE,OAAA,CAAQ9O,IAAA,CAAKuQ,UAAA,CAAWvR,CAAA,GAAI,CAAC,GAAGuR,UAAA,CAAWvR,CAAA,GAAI,CAAC,GAAGuR,UAAA,CAAWvR,CAAA,GAAI,CAAC,CAAC;YAClF,OAAmB;cACL8P,OAAA,CAAQ9O,IAAA,CAAKuQ,UAAA,CAAWvR,CAAA,GAAI,CAAC,GAAGuR,UAAA,CAAWvR,CAAA,GAAI,CAAC,GAAGuR,UAAA,CAAWvR,CAAA,GAAI,CAAC,CAAC;YACrE;UACF;QACF;QAID,IAAIqQ,MAAA,KAAW,MAAM;UACnB,MAAMoB,WAAA,GAAcP,iBAAA,IAAqBD,UAAA,GAAa;UAEtD,SAASjR,CAAA,GAAI,GAAGC,CAAA,GAAIsR,UAAA,CAAWzT,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK,GAAG;YACpD,IAAI8L,GAAA,KAAQ,MAAM;cAChBgE,OAAA,CAAQ9O,IAAA,CACNyQ,WAAA,GAAcF,UAAA,CAAWvR,CAAA,GAAI,CAAC,GAC9ByR,WAAA,GAAcF,UAAA,CAAWvR,CAAA,GAAI,CAAC,GAC9ByR,WAAA,GAAcF,UAAA,CAAWvR,CAAA,GAAI,CAAC,CAC/B;YACf,OAAmB;cACL8P,OAAA,CAAQ9O,IAAA,CACNyQ,WAAA,GAAcF,UAAA,CAAWvR,CAAA,GAAI,CAAC,GAC9ByR,WAAA,GAAcF,UAAA,CAAWvR,CAAA,GAAI,CAAC,GAC9ByR,WAAA,GAAcF,UAAA,CAAWvR,CAAA,GAAI,CAAC,CAC/B;YACF;UACF;QACF;MACF;MAED,MAAM+N,iBAAA,GAAoBZ,qBAAA,CAAsB2C,OAAA,EAAS,IAAI1C,sBAAA,CAAuBgC,QAAA,EAAU,CAAC,CAAC;MAChG,MAAMrC,eAAA,GAAkBc,sBAAA,CAAuBiC,OAAA,EAASV,QAAA,EAAUpD,WAAW;MAE7E,MAAM1E,QAAA,GAAW,IAAIqF,cAAA,CAAgB;MACrCrF,QAAA,CAAS0G,YAAA,CAAa,YAAYD,iBAAiB;MACnDzG,QAAA,CAAS0G,YAAA,CAAa,UAAUjB,eAAe;MAK/CzF,QAAA,CAASY,MAAA,GAAS6D,KAAA;MAClBzE,QAAA,CAASE,KAAA,GAAQ;MAEjB,OAAOF,QAAA;IACR;IAID,SAASlE,WAAWsO,UAAA,EAAY;MAC9B,MAAM3Q,IAAA,GAAOrE,OAAA,CAAQgV,UAAU;MAC/B,MAAMrO,KAAA,GAAQT,OAAA,CAAQ7B,IAAI;MAM1B,OAAOsC,KAAA,CAAMsO,UAAA,IAActO,KAAA,CAAMuO,UAAA,GAAavO,KAAA,CAAMwO,KAAA,CAAK,IAAKxO,KAAA;IAC/D;IAED,SAAS0B,mBAAmB+M,QAAA,EAAUC,KAAA,EAAO;MAC3C,SAAS/R,CAAA,GAAI,GAAGC,CAAA,GAAI6R,QAAA,CAAShU,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC/C,MAAM2C,MAAA,GAASC,OAAA,CAAQkP,QAAA,CAAS9R,CAAC,CAAC;QAElC,IAAI2C,MAAA,YAAkBE,QAAA,EAAUkP,KAAA,CAAMjP,GAAA,CAAIH,MAAM;MACjD;IACF;IAED,SAASkK,qBAAqB2C,KAAA,EAAO1D,GAAA,EAAK;MACxC,MAAMgE,OAAA,GAAU,EAAE;MAKlB,IAAIkC,KAAA,GAAQ;MAEZ,SAAShS,CAAA,GAAI,GAAGC,CAAA,GAAIuP,KAAA,CAAM1R,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC5C,MAAMiS,EAAA,GAAKzC,KAAA,CAAMwC,KAAK;QACtB,MAAME,EAAA,GAAK1C,KAAA,CAAMxP,CAAA,IAAK8L,GAAA,GAAM,IAAI,EAAE;QAClC,MAAMqG,EAAA,GAAK3C,KAAA,CAAMxP,CAAA,IAAK8L,GAAA,GAAM,IAAI,EAAE;QAElCgE,OAAA,CAAQ9O,IAAA,CAAKiR,EAAA,EAAIC,EAAA,EAAIC,EAAE;QAIvB,IAAI3C,KAAA,CAAMxP,CAAA,GAAI,CAAC,MAAM,MAAMA,CAAA,GAAI,KAAKC,CAAA,EAAG;UACrCD,CAAA,IAAK;UACLgS,KAAA,GAAQhS,CAAA,GAAI;QACb;MACF;MAED,OAAO8P,OAAA;IACR;IAED,SAAStC,oBAAoB5Q,KAAA,EAAM4S,KAAA,EAAO;MACxC,MAAM4C,gBAAA,GAAmB,EAAE;MAE3B,IAAIJ,KAAA,GAAQ;MAEZ,SAAShS,CAAA,GAAI,GAAGC,CAAA,GAAIuP,KAAA,CAAM1R,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC5C,MAAMmL,MAAA,GAAS6G,KAAA,GAAQ;QAEvB,MAAMvD,CAAA,GAAI7R,KAAA,CAAKuO,MAAM;QACrB,MAAMuD,CAAA,GAAI9R,KAAA,CAAKuO,MAAA,GAAS,CAAC;QACzB,MAAMwD,CAAA,GAAI/R,KAAA,CAAKuO,MAAA,GAAS,CAAC;QAEzBiH,gBAAA,CAAiBpR,IAAA,CAAKyN,CAAA,EAAGC,CAAA,EAAGC,CAAC;QAI7B,IAAIa,KAAA,CAAMxP,CAAA,GAAI,CAAC,MAAM,MAAMA,CAAA,GAAI,KAAKC,CAAA,EAAG;UACrCD,CAAA,IAAK;UACLgS,KAAA;QACD;MACF;MAED,OAAOI,gBAAA;IACR;IAED,SAAS9E,YAAY1Q,KAAA,EAAM4S,KAAA,EAAO;MAChC,MAAM6C,YAAA,GAAc,EAAE;MAEtB,SAASrS,CAAA,GAAI,GAAGC,CAAA,GAAIuP,KAAA,CAAM1R,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC5C,MAAMiS,EAAA,GAAKzC,KAAA,CAAMxP,CAAC;QAElB,MAAMmL,MAAA,GAAS8G,EAAA,GAAK;QAEpB,MAAMxD,CAAA,GAAI7R,KAAA,CAAKuO,MAAM;QACrB,MAAMuD,CAAA,GAAI9R,KAAA,CAAKuO,MAAA,GAAS,CAAC;QACzB,MAAMwD,CAAA,GAAI/R,KAAA,CAAKuO,MAAA,GAAS,CAAC;QAEzBkH,YAAA,CAAYrR,IAAA,CAAKyN,CAAA,EAAGC,CAAA,EAAGC,CAAC;MACzB;MAED,OAAO0D,YAAA;IACR;IAED,SAASnE,gBAAgBsB,KAAA,EAAO;MAC9B,MAAMM,OAAA,GAAU,EAAE;MAElB,SAAS9P,CAAA,GAAI,GAAGC,CAAA,GAAIuP,KAAA,CAAM1R,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC5C,MAAMiS,EAAA,GAAKzC,KAAA,CAAMxP,CAAC;QAClB,MAAMkS,EAAA,GAAK1C,KAAA,CAAMxP,CAAA,GAAI,CAAC;QAEtB8P,OAAA,CAAQ9O,IAAA,CAAKiR,EAAA,EAAIC,EAAE;QAInB,IAAI1C,KAAA,CAAMxP,CAAA,GAAI,CAAC,MAAM,MAAMA,CAAA,GAAI,KAAKC,CAAA,EAAG;UACrCD,CAAA,IAAK;QACN;MACF;MAED,OAAO8P,OAAA;IACR;IAED,SAASxB,eAAe1R,KAAA,EAAM4S,KAAA,EAAO;MACnC,MAAM4C,gBAAA,GAAmB,EAAE;MAE3B,IAAIJ,KAAA,GAAQ;MAEZ,SAAShS,CAAA,GAAI,GAAGC,CAAA,GAAIuP,KAAA,CAAM1R,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC5C,MAAMmL,MAAA,GAAS6G,KAAA,GAAQ;QAEvB,MAAMvD,CAAA,GAAI7R,KAAA,CAAKuO,MAAM;QACrB,MAAMuD,CAAA,GAAI9R,KAAA,CAAKuO,MAAA,GAAS,CAAC;QACzB,MAAMwD,CAAA,GAAI/R,KAAA,CAAKuO,MAAA,GAAS,CAAC;QAEzBiH,gBAAA,CAAiBpR,IAAA,CAAKyN,CAAA,EAAGC,CAAA,EAAGC,CAAC;QAI7B,IAAIa,KAAA,CAAMxP,CAAA,GAAI,CAAC,MAAM,MAAMA,CAAA,GAAI,KAAKC,CAAA,EAAG;UACrCD,CAAA,IAAK;UACLgS,KAAA;QACD;MACF;MAED,OAAOI,gBAAA;IACR;IAED,MAAME,EAAA,GAAK,IAAIrN,OAAA,CAAS;IACxB,MAAMsN,EAAA,GAAK,IAAItN,OAAA,CAAS;IACxB,MAAMuN,EAAA,GAAK,IAAIvN,OAAA,CAAS;IAExB,MAAMwN,GAAA,GAAM,IAAIjH,OAAA,CAAS;IACzB,MAAMkH,GAAA,GAAM,IAAIlH,OAAA,CAAS;IACzB,MAAMmH,GAAA,GAAM,IAAInH,OAAA,CAAS;IAEzB,SAAS0B,gCAAgChB,UAAA,EAAYsD,KAAA,EAAO5S,KAAA,EAAMgW,QAAA,EAAU;MAC1E,MAAMC,KAAA,GAAQ,EAAE;MAIhB,SAAS7S,CAAA,GAAI,GAAGC,CAAA,GAAIiM,UAAA,CAAWpO,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK,GAAG;QACpD,MAAMuK,CAAA,GAAIiF,KAAA,CAAMxP,CAAC;QACjB,MAAMsK,CAAA,GAAIkF,KAAA,CAAMxP,CAAA,GAAI,CAAC;QACrB,MAAM+P,CAAA,GAAIP,KAAA,CAAMxP,CAAA,GAAI,CAAC;QAErB,IAAI4S,QAAA,KAAa,GAAG;UAClBH,GAAA,CAAI5B,SAAA,CAAUjU,KAAA,EAAM2N,CAAA,GAAIqI,QAAQ;UAChCF,GAAA,CAAI7B,SAAA,CAAUjU,KAAA,EAAM0N,CAAA,GAAIsI,QAAQ;UAChCD,GAAA,CAAI9B,SAAA,CAAUjU,KAAA,EAAMmT,CAAA,GAAI6C,QAAQ;UAEhCC,KAAA,CAAM7R,IAAA,CAAKyR,GAAA,CAAIhE,CAAA,EAAGgE,GAAA,CAAI/D,CAAC;UACvBmE,KAAA,CAAM7R,IAAA,CAAK0R,GAAA,CAAIjE,CAAA,EAAGiE,GAAA,CAAIhE,CAAC;UACvBmE,KAAA,CAAM7R,IAAA,CAAK2R,GAAA,CAAIlE,CAAA,EAAGkE,GAAA,CAAIjE,CAAC;QACjC,OAAe;UACL4D,EAAA,CAAGzB,SAAA,CAAUjU,KAAA,EAAM2N,CAAA,GAAIqI,QAAQ;UAC/BL,EAAA,CAAG1B,SAAA,CAAUjU,KAAA,EAAM0N,CAAA,GAAIsI,QAAQ;UAC/BJ,EAAA,CAAG3B,SAAA,CAAUjU,KAAA,EAAMmT,CAAA,GAAI6C,QAAQ;UAE/BC,KAAA,CAAM7R,IAAA,CAAKsR,EAAA,CAAG7D,CAAA,EAAG6D,EAAA,CAAG5D,CAAA,EAAG4D,EAAA,CAAG3D,CAAC;UAC3BkE,KAAA,CAAM7R,IAAA,CAAKuR,EAAA,CAAG9D,CAAA,EAAG8D,EAAA,CAAG7D,CAAA,EAAG6D,EAAA,CAAG5D,CAAC;UAC3BkE,KAAA,CAAM7R,IAAA,CAAKwR,EAAA,CAAG/D,CAAA,EAAG+D,EAAA,CAAG9D,CAAA,EAAG8D,EAAA,CAAG7D,CAAC;QAC5B;MACF;MAED,OAAO,IAAIvB,sBAAA,CAAuByF,KAAA,EAAOD,QAAQ;IAClD;IAED,SAASnF,6BAA6B+B,KAAA,EAAOsD,QAAA,EAAU;MACrD,MAAMD,KAAA,GAAQ,EAAE;MAEhB,SAAS7S,CAAA,GAAI,GAAGkD,CAAA,GAAI,GAAGjD,CAAA,GAAIuP,KAAA,CAAM1R,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK,GAAGkD,CAAA,IAAK;QAC3DoP,EAAA,CAAGzB,SAAA,CAAUiC,QAAA,EAAU5P,CAAA,GAAI,CAAC;QAE5B2P,KAAA,CAAM7R,IAAA,CAAKsR,EAAA,CAAG7D,CAAA,EAAG6D,EAAA,CAAG5D,CAAA,EAAG4D,EAAA,CAAG3D,CAAC;QAC3BkE,KAAA,CAAM7R,IAAA,CAAKsR,EAAA,CAAG7D,CAAA,EAAG6D,EAAA,CAAG5D,CAAA,EAAG4D,EAAA,CAAG3D,CAAC;QAC3BkE,KAAA,CAAM7R,IAAA,CAAKsR,EAAA,CAAG7D,CAAA,EAAG6D,EAAA,CAAG5D,CAAA,EAAG4D,EAAA,CAAG3D,CAAC;MAC5B;MAED,OAAO,IAAIvB,sBAAA,CAAuByF,KAAA,EAAO,CAAC;IAC3C;IAED,SAAStE,6BAA6BiB,KAAA,EAAOuD,QAAA,EAAU;MACrD,MAAMF,KAAA,GAAQ,EAAE;MAEhB,SAAS7S,CAAA,GAAI,GAAGkD,CAAA,GAAI,GAAGjD,CAAA,GAAIuP,KAAA,CAAM1R,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK,GAAGkD,CAAA,IAAK;QAC3DoP,EAAA,CAAGzB,SAAA,CAAUkC,QAAA,EAAU7P,CAAA,GAAI,CAAC;QAE5B2P,KAAA,CAAM7R,IAAA,CAAKsR,EAAA,CAAG7D,CAAA,EAAG6D,EAAA,CAAG5D,CAAA,EAAG4D,EAAA,CAAG3D,CAAC;QAC3BkE,KAAA,CAAM7R,IAAA,CAAKsR,EAAA,CAAG7D,CAAA,EAAG6D,EAAA,CAAG5D,CAAA,EAAG4D,EAAA,CAAG3D,CAAC;MAC5B;MAED,OAAO,IAAIvB,sBAAA,CAAuByF,KAAA,EAAO,CAAC;IAC3C;IAED,SAAS1F,sBAAsB2C,OAAA,EAASkD,SAAA,EAAW;MACjD,MAAMH,KAAA,GAAQG,SAAA,CAAUH,KAAA;MACxB,MAAMD,QAAA,GAAWI,SAAA,CAAUJ,QAAA;MAE3B,MAAMK,MAAA,GAAS,IAAIJ,KAAA,CAAM5X,WAAA,CAAY6U,OAAA,CAAQhS,MAAA,GAAS8U,QAAQ;MAE9D,IAAIpD,KAAA,GAAQ;QACV0D,MAAA,GAAS;MAEX,SAASlT,CAAA,GAAI,GAAGC,CAAA,GAAI6P,OAAA,CAAQhS,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC9CwP,KAAA,GAAQM,OAAA,CAAQ9P,CAAC,IAAI4S,QAAA;QAErB,SAAS1P,CAAA,GAAI,GAAGA,CAAA,GAAI0P,QAAA,EAAU1P,CAAA,IAAK;UACjC+P,MAAA,CAAOC,MAAA,EAAQ,IAAIL,KAAA,CAAMrD,KAAA,EAAO;QACjC;MACF;MAED,OAAO,IAAIpC,sBAAA,CAAuB6F,MAAA,EAAQL,QAAQ;IACnD;IAED,MAAMO,EAAA,GAAK,IAAIlO,OAAA,CAAS;IACxB,MAAMmO,EAAA,GAAK,IAAInO,OAAA,CAAS;IAExB,SAAS4I,uBAAuB2B,KAAA,EAAO7D,KAAA,EAAOK,WAAA,EAAa;MACzD,MAAMoF,KAAA,GAAQ,EAAE;MAChB,MAAMiC,aAAA,GAAgB,CAAE;MAIxB,SAASrT,CAAA,GAAI,GAAGC,CAAA,GAAIuP,KAAA,CAAM1R,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK,GAAG;QAC/C,MAAMuK,CAAA,GAAIiF,KAAA,CAAMxP,CAAC;QACjB,MAAMsK,CAAA,GAAIkF,KAAA,CAAMxP,CAAA,GAAI,CAAC;QACrB,MAAM+P,CAAA,GAAIP,KAAA,CAAMxP,CAAA,GAAI,CAAC;QAErB,MAAMwR,IAAA,GAAO,IAAI8B,IAAA,CAAK/I,CAAA,EAAGD,CAAA,EAAGyF,CAAC;QAE7BuC,EAAA,CAAGzB,SAAA,CAAUlF,KAAA,EAAOpB,CAAA,GAAI,CAAC;QACzBgI,EAAA,CAAG1B,SAAA,CAAUlF,KAAA,EAAOrB,CAAA,GAAI,CAAC;QACzBkI,EAAA,CAAG3B,SAAA,CAAUlF,KAAA,EAAOoE,CAAA,GAAI,CAAC;QAEzBqD,EAAA,CAAGG,UAAA,CAAWf,EAAA,EAAID,EAAE;QACpBY,EAAA,CAAGI,UAAA,CAAWjB,EAAA,EAAIC,EAAE;QACpBa,EAAA,CAAGI,KAAA,CAAML,EAAE;QAEXC,EAAA,CAAGlO,SAAA,CAAW;QAEdsM,IAAA,CAAK5F,MAAA,CAAOhE,IAAA,CAAKwL,EAAE;QAEnB,IAAIC,aAAA,CAAc9I,CAAC,MAAM,QAAW8I,aAAA,CAAc9I,CAAC,IAAI,EAAE;QACzD,IAAI8I,aAAA,CAAc/I,CAAC,MAAM,QAAW+I,aAAA,CAAc/I,CAAC,IAAI,EAAE;QACzD,IAAI+I,aAAA,CAActD,CAAC,MAAM,QAAWsD,aAAA,CAActD,CAAC,IAAI,EAAE;QAEzDsD,aAAA,CAAc9I,CAAC,EAAEvJ,IAAA,CAAKwQ,IAAA,CAAK5F,MAAM;QACjCyH,aAAA,CAAc/I,CAAC,EAAEtJ,IAAA,CAAKwQ,IAAA,CAAK5F,MAAM;QACjCyH,aAAA,CAActD,CAAC,EAAE/O,IAAA,CAAKwQ,IAAA,CAAK5F,MAAM;QAEjCwF,KAAA,CAAMpQ,IAAA,CAAKwQ,IAAI;MAChB;MAID,MAAMnC,OAAA,GAAU,EAAE;MAElB,SAASrP,CAAA,GAAI,GAAGC,CAAA,GAAImR,KAAA,CAAMtT,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC5C,MAAMwR,IAAA,GAAOJ,KAAA,CAAMpR,CAAC;QAEpB,MAAMyT,EAAA,GAAKC,cAAA,CAAeL,aAAA,CAAc7B,IAAA,CAAKjH,CAAC,GAAGiH,IAAA,CAAK5F,MAAA,EAAQI,WAAW;QACzE,MAAM2H,EAAA,GAAKD,cAAA,CAAeL,aAAA,CAAc7B,IAAA,CAAKlH,CAAC,GAAGkH,IAAA,CAAK5F,MAAA,EAAQI,WAAW;QACzE,MAAM4H,EAAA,GAAKF,cAAA,CAAeL,aAAA,CAAc7B,IAAA,CAAKzB,CAAC,GAAGyB,IAAA,CAAK5F,MAAA,EAAQI,WAAW;QAEzEsG,EAAA,CAAGzB,SAAA,CAAUlF,KAAA,EAAO6F,IAAA,CAAKjH,CAAA,GAAI,CAAC;QAC9BgI,EAAA,CAAG1B,SAAA,CAAUlF,KAAA,EAAO6F,IAAA,CAAKlH,CAAA,GAAI,CAAC;QAC9BkI,EAAA,CAAG3B,SAAA,CAAUlF,KAAA,EAAO6F,IAAA,CAAKzB,CAAA,GAAI,CAAC;QAE9BV,OAAA,CAAQrO,IAAA,CAAKyS,EAAA,CAAGhF,CAAA,EAAGgF,EAAA,CAAG/E,CAAA,EAAG+E,EAAA,CAAG9E,CAAC;QAC7BU,OAAA,CAAQrO,IAAA,CAAK2S,EAAA,CAAGlF,CAAA,EAAGkF,EAAA,CAAGjF,CAAA,EAAGiF,EAAA,CAAGhF,CAAC;QAC7BU,OAAA,CAAQrO,IAAA,CAAK4S,EAAA,CAAGnF,CAAA,EAAGmF,EAAA,CAAGlF,CAAA,EAAGkF,EAAA,CAAGjF,CAAC;MAC9B;MAED,OAAO,IAAIvB,sBAAA,CAAuBiC,OAAA,EAAS,CAAC;IAC7C;IAED,SAASqE,eAAerE,OAAA,EAASwE,MAAA,EAAQ7H,WAAA,EAAa;MACpD,MAAMJ,MAAA,GAAS,IAAI3G,OAAA,CAAS;MAE5B,IAAI+G,WAAA,KAAgB,GAAG;QACrBJ,MAAA,CAAOhE,IAAA,CAAKiM,MAAM;MAC1B,OAAa;QACL,SAAS7T,CAAA,GAAI,GAAGC,CAAA,GAAIoP,OAAA,CAAQvR,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;UAC9C,IAAIqP,OAAA,CAAQrP,CAAC,EAAE8T,OAAA,CAAQD,MAAM,IAAI7H,WAAA,EAAa;YAC5CJ,MAAA,CAAO9I,GAAA,CAAIuM,OAAA,CAAQrP,CAAC,CAAC;UACtB;QACF;MACF;MAED,OAAO4L,MAAA,CAAO1G,SAAA,CAAW;IAC1B;IAED,SAASsB,aAAa8I,MAAA,EAAQ;MAC5B,MAAMuD,KAAA,GAAQ,EAAE;MAEhB,SAAS7S,CAAA,GAAI,GAAGC,CAAA,GAAIqP,MAAA,CAAOxR,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK,GAAG;QAChD6S,KAAA,CAAM7R,IAAA,CAAK,IAAI6I,KAAA,CAAMyF,MAAA,CAAOtP,CAAC,GAAGsP,MAAA,CAAOtP,CAAA,GAAI,CAAC,GAAGsP,MAAA,CAAOtP,CAAA,GAAI,CAAC,CAAC,CAAC;MAC9D;MAED,OAAO6S,KAAA;IACR;IAwBD,SAAStM,WAAWe,QAAA,EAAUzB,MAAA,EAAQkO,MAAA,EAAQzE,MAAA,EAAQ0E,OAAA,EAAS;MAG7D,MAAMC,UAAA,GAAa,EAAE;MACrB,MAAMC,UAAA,GAAaF,OAAA,KAAY,OAAO,IAAIjN,IAAA,CAAKC,EAAA;MAE/C,SAAShH,CAAA,GAAI,GAAGC,CAAA,GAAIqP,MAAA,CAAOxR,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC7C,IAAImF,KAAA,GAAQnF,CAAA,KAAM,IAAI,IAAI+T,MAAA,CAAO/T,CAAA,GAAI,CAAC;QACtCmF,KAAA,GAAQ6O,OAAA,KAAY,OAAO7O,KAAA,GAAQ+O,UAAA,GAAa/O,KAAA;QAEhD,MAAMgP,KAAA,GAAQ,IAAIlP,OAAA,CAAS;QAC3BkP,KAAA,CAAMC,sBAAA,CAAuBvO,MAAA,EAAQV,KAAA,EAAO,CAAC;QAE7C8O,UAAA,CAAWjT,IAAA,CAAKmT,KAAK;MACtB;MAID,MAAMrE,OAAA,GAAUxI,QAAA,CAASkI,KAAA;MACzB,MAAMzB,iBAAA,GAAoBzG,QAAA,CAASC,UAAA,CAAW/B,QAAA;MAC9C,MAAMsH,cAAA,GAAiB,IAAIuH,eAAA,CAAgB,IAAIC,YAAA,CAAahN,QAAA,CAASC,UAAA,CAAW/B,QAAA,CAAS+O,KAAA,GAAQ,CAAC,GAAG,CAAC;MAEtG,MAAM/O,QAAA,GAAW,IAAIP,OAAA,CAAS;MAC9B,MAAMyB,KAAA,GAAQ,IAAImD,KAAA,CAAO;MAEzB,SAAS7J,CAAA,GAAI,GAAGA,CAAA,GAAI8P,OAAA,CAAQyE,KAAA,EAAOvU,CAAA,IAAK;QACtC,MAAMwP,KAAA,GAAQM,OAAA,CAAQ0E,IAAA,CAAKxU,CAAC;QAC5BwF,QAAA,CAASiP,mBAAA,CAAoB1G,iBAAA,EAAmByB,KAAK;QAErD,IAAIkF,eAAA,EAAiBC,eAAA;QACrB,IAAI9E,CAAA,GAAI;QAER,SAAS3M,CAAA,GAAI,GAAGA,CAAA,GAAI+Q,UAAA,CAAWnW,MAAA,EAAQoF,CAAA,IAAK;UAC1CwR,eAAA,GAAkBxR,CAAA,GAAI;UACtByR,eAAA,GAAkBzR,CAAA;UAElB,MAAM0R,UAAA,GAAaX,UAAA,CAAWS,eAAe;UAC7C,MAAMG,UAAA,GAAaZ,UAAA,CAAWU,eAAe;UAE7C,IAAIX,OAAA,KAAY,MAAM;YAGpB,IAAIxO,QAAA,CAASkJ,CAAA,IAAKkG,UAAA,CAAWlG,CAAA,IAAKlJ,QAAA,CAASkJ,CAAA,GAAImG,UAAA,CAAWnG,CAAA,EAAG;cAC3DmB,CAAA,GAAI9I,IAAA,CAAK+N,GAAA,CAAIF,UAAA,CAAWlG,CAAA,GAAIlJ,QAAA,CAASkJ,CAAC,IAAI3H,IAAA,CAAK+N,GAAA,CAAIF,UAAA,CAAWlG,CAAA,GAAImG,UAAA,CAAWnG,CAAC;cAE9E;YACD;UACb,OAAiB;YAGL,IAAIlJ,QAAA,CAASkJ,CAAA,IAAKkG,UAAA,CAAWlG,CAAA,IAAKlJ,QAAA,CAASkJ,CAAA,GAAImG,UAAA,CAAWnG,CAAA,EAAG;cAC3DmB,CAAA,GAAI9I,IAAA,CAAK+N,GAAA,CAAIF,UAAA,CAAWlG,CAAA,GAAIlJ,QAAA,CAASkJ,CAAC,IAAI3H,IAAA,CAAK+N,GAAA,CAAIF,UAAA,CAAWlG,CAAA,GAAImG,UAAA,CAAWnG,CAAC;cAE9E;YACD;UACF;QACF;QAED,MAAMqG,MAAA,GAASzF,MAAA,CAAOoF,eAAe;QACrC,MAAMM,MAAA,GAAS1F,MAAA,CAAOqF,eAAe;QAErCjO,KAAA,CAAMkB,IAAA,CAAKmN,MAAM,EAAEE,IAAA,CAAKD,MAAA,EAAQnF,CAAC;QAEjC/C,cAAA,CAAeoI,MAAA,CAAO1F,KAAA,EAAO9I,KAAA,CAAM0D,CAAA,EAAG1D,KAAA,CAAM2D,CAAA,EAAG3D,KAAA,CAAM4D,CAAC;MACvD;MAEDhD,QAAA,CAAS0G,YAAA,CAAa,SAASlB,cAAc;IAC9C;IAID,MAAMvB,aAAA,GAAgB,IAAI4J,aAAA,CAAc,KAAKja,OAAO;IACpDqQ,aAAA,CAAczP,OAAA,CAAQ,KAAKsZ,YAAA,IAAgB3Z,IAAI,EAAE4Z,cAAA,CAAe,KAAKC,WAAW;IAIhF,IAAI7Y,IAAA,CAAK8Y,OAAA,CAAQ,YAAY,MAAM,IAAI;MACrC,MAAMxX,KAAA,CAAM,uDAAuD;IACpE;IAID,MAAMyX,IAAA,GAAO7Y,gBAAA,CAAiBF,IAAI;IAIlC,MAAMgZ,KAAA,GAAQnT,SAAA,CAAUkT,IAAI;IAE5B,OAAOC,KAAA;EACR;AACH;AAEA,MAAMzY,SAAA,CAAU;EACd/B,YAAYgC,MAAA,EAAQ;IAClB,KAAKF,KAAA,GAAQ,IAAI8C,KAAA,CAAM5C,MAAM;EAC9B;EAEDQ,IAAIiY,SAAA,EAAW;IACb,MAAMlY,YAAA,GAAe,KAAKT,KAAA,CAAM4Y,QAAA,CAASD,SAAS;IAElD,IAAIlY,YAAA,CAAaK,MAAA,CAAOC,MAAA,GAAS,GAAG;MAClCxB,OAAA,CAAQC,KAAA,CAAMiB,YAAA,CAAaK,MAAM;MAEjC,MAAME,KAAA,CAAM,0CAA0C;IACvD;IAED,OAAOP,YAAA;EACR;AACH;AAEA,MAAML,UAAA,SAAmByY,SAAA,CAAU;EACjC3a,YAAYmC,eAAA,EAAiB;IAC3B,MAAMA,eAAe;IAErB,MAAMyY,CAAA,GAAI;IAEV,MAAMpX,OAAA,GAAUrB,eAAA,CAAgB,SAAS;IACzC,MAAMqC,MAAA,GAASrC,eAAA,CAAgB,QAAQ;IACvC,MAAMsC,MAAA,GAAStC,eAAA,CAAgB,QAAQ;IACvC,MAAMmC,OAAA,GAAUnC,eAAA,CAAgB,SAAS;IACzC,MAAMoC,OAAA,GAAUpC,eAAA,CAAgB,SAAS;IACzC,MAAMkB,UAAA,GAAalB,eAAA,CAAgB,YAAY;IAC/C,MAAMc,eAAA,GAAkBd,eAAA,CAAgB,iBAAiB;IACzD,MAAM6B,aAAA,GAAgB7B,eAAA,CAAgB,eAAe;IACrD,MAAM8B,UAAA,GAAa9B,eAAA,CAAgB,YAAY;IAC/C,MAAM+B,aAAA,GAAgB/B,eAAA,CAAgB,eAAe;IACrD,MAAMgC,WAAA,GAAchC,eAAA,CAAgB,aAAa;IACjD,MAAMiC,YAAA,GAAejC,eAAA,CAAgB,cAAc;IACnD,MAAMkC,WAAA,GAAclC,eAAA,CAAgB,aAAa;IACjD,MAAMyB,GAAA,GAAMzB,eAAA,CAAgB,KAAK;IACjC,MAAM0B,GAAA,GAAM1B,eAAA,CAAgB,KAAK;IACjC,MAAM2B,KAAA,GAAQ3B,eAAA,CAAgB,OAAO;IACrC,MAAM4B,EAAA,GAAK5B,eAAA,CAAgB,IAAI;IAC/B,MAAMsB,QAAA,GAAWtB,eAAA,CAAgB,UAAU;IAE3CyY,CAAA,CAAEC,IAAA,CAAK,QAAQ,YAAY;MACzBD,CAAA,CAAEE,OAAA,CAAQF,CAAA,CAAEjV,OAAO;MACnBiV,CAAA,CAAEG,YAAA,CAAa,YAAY;QACzBH,CAAA,CAAEE,OAAA,CAAQF,CAAA,CAAE9U,IAAI;MACxB,CAAO;MACD8U,CAAA,CAAEI,IAAA,CAAK,YAAY;QACjBJ,CAAA,CAAEE,OAAA,CAAQF,CAAA,CAAE5U,KAAK;MACzB,CAAO;IACP,CAAK;IAED4U,CAAA,CAAEC,IAAA,CAAK,WAAW,YAAY;MAC5BD,CAAA,CAAEK,OAAA,CAAQzX,OAAO;IACvB,CAAK;IAEDoX,CAAA,CAAEC,IAAA,CAAK,QAAQ,YAAY;MACzBD,CAAA,CAAEM,MAAA,CAAO,YAAY;QACnBN,CAAA,CAAEE,OAAA,CAAQF,CAAA,CAAExU,GAAG;MACvB,CAAO;MAEDwU,CAAA,CAAEK,OAAA,CAAQxX,QAAQ;MAClBmX,CAAA,CAAEK,OAAA,CAAQzW,MAAM;MAChBoW,CAAA,CAAEI,IAAA,CAAK,YAAY;QACjBJ,CAAA,CAAEE,OAAA,CAAQF,CAAA,CAAEzU,KAAK;MACzB,CAAO;MACDyU,CAAA,CAAEK,OAAA,CAAQxW,MAAM;IACtB,CAAK;IAEDmW,CAAA,CAAEC,IAAA,CAAK,SAAS,YAAY;MAC1BD,CAAA,CAAEK,OAAA,CAAQ5X,UAAU;MAEpBuX,CAAA,CAAEO,GAAA,CAAI,CACJ;QACEC,GAAA,EAAK,SAAAA,CAAA,EAAY;UACfR,CAAA,CAAEE,OAAA,CAAQF,CAAA,CAAEpU,gBAAgB;QAC7B;MACF,GACD;QACE4U,GAAA,EAAK,SAAAA,CAAA,EAAY;UACfR,CAAA,CAAEE,OAAA,CAAQF,CAAA,CAAEnU,eAAe;QAC5B;MACF,EACF;IACP,CAAK;IAEDmU,CAAA,CAAEC,IAAA,CAAK,OAAO,YAAY;MACxBD,CAAA,CAAEK,OAAA,CAAQrX,GAAG;MACbgX,CAAA,CAAES,EAAA,CAAG,CACH;QACED,GAAA,EAAK,SAAAA,CAAA,EAAY;UACfR,CAAA,CAAEK,OAAA,CAAQ5X,UAAU;QACrB;MACF,GACD;QACE+X,GAAA,EAAK,SAAAA,CAAA,EAAY;UACfR,CAAA,CAAEK,OAAA,CAAQxX,QAAQ;QACnB;MACF,EACF;IACP,CAAK;IAEDmX,CAAA,CAAEC,IAAA,CAAK,OAAO,YAAY;MACxBD,CAAA,CAAEK,OAAA,CAAQpX,GAAG;MACb+W,CAAA,CAAES,EAAA,CAAG,CACH;QACED,GAAA,EAAK,SAAAA,CAAA,EAAY;UACfR,CAAA,CAAEK,OAAA,CAAQ5X,UAAU;QACrB;MACF,GACD;QACE+X,GAAA,EAAK,SAAAA,CAAA,EAAY;UACfR,CAAA,CAAEK,OAAA,CAAQxX,QAAQ;QACnB;MACF,EACF;IACP,CAAK;IAEDmX,CAAA,CAAEC,IAAA,CAAK,oBAAoB,YAAY;MACrCD,CAAA,CAAEG,YAAA,CAAa,YAAY;QACzBH,CAAA,CAAES,EAAA,CAAG,CACH;UACED,GAAA,EAAK,SAAAA,CAAA,EAAY;YACfR,CAAA,CAAEE,OAAA,CAAQF,CAAA,CAAE9U,IAAI;UACjB;QACF,GACD;UACEsV,GAAA,EAAK,SAAAA,CAAA,EAAY;YACfR,CAAA,CAAEE,OAAA,CAAQF,CAAA,CAAElU,GAAG;UAChB;QACF,GACD;UACE0U,GAAA,EAAK,SAAAA,CAAA,EAAY;YACfR,CAAA,CAAEK,OAAA,CAAQjX,aAAa;UACxB;QACF,GACD;UACEoX,GAAA,EAAK,SAAAA,CAAA,EAAY;YACfR,CAAA,CAAEK,OAAA,CAAQhX,UAAU;UACrB;QACF,GACD;UACEmX,GAAA,EAAK,SAAAA,CAAA,EAAY;YACfR,CAAA,CAAEK,OAAA,CAAQ/W,aAAa;UACxB;QACF,GACD;UACEkX,GAAA,EAAK,SAAAA,CAAA,EAAY;YACfR,CAAA,CAAEK,OAAA,CAAQ9W,WAAW;UACtB;QACF,GACD;UACEiX,GAAA,EAAK,SAAAA,CAAA,EAAY;YACfR,CAAA,CAAEK,OAAA,CAAQ7W,YAAY;UACvB;QACF,GACD;UACEgX,GAAA,EAAK,SAAAA,CAAA,EAAY;YACfR,CAAA,CAAEK,OAAA,CAAQ5W,WAAW;UACtB;QACF,EACF;MACT,CAAO;IACP,CAAK;IAEDuW,CAAA,CAAEC,IAAA,CAAK,mBAAmB,YAAY;MACpCD,CAAA,CAAEK,OAAA,CAAQ3W,OAAO;MACjBsW,CAAA,CAAEI,IAAA,CAAK,YAAY;QACjBJ,CAAA,CAAES,EAAA,CAAG,CACH;UACED,GAAA,EAAK,SAAAA,CAAA,EAAY;YACfR,CAAA,CAAEE,OAAA,CAAQF,CAAA,CAAE9U,IAAI;UACjB;QACF,GACD;UACEsV,GAAA,EAAK,SAAAA,CAAA,EAAY;YACfR,CAAA,CAAEE,OAAA,CAAQF,CAAA,CAAElU,GAAG;UAChB;QACF,GACD;UACE0U,GAAA,EAAK,SAAAA,CAAA,EAAY;YACfR,CAAA,CAAEK,OAAA,CAAQjX,aAAa;UACxB;QACF,GACD;UACEoX,GAAA,EAAK,SAAAA,CAAA,EAAY;YACfR,CAAA,CAAEK,OAAA,CAAQhX,UAAU;UACrB;QACF,GACD;UACEmX,GAAA,EAAK,SAAAA,CAAA,EAAY;YACfR,CAAA,CAAEK,OAAA,CAAQ/W,aAAa;UACxB;QACF,GACD;UACEkX,GAAA,EAAK,SAAAA,CAAA,EAAY;YACfR,CAAA,CAAEK,OAAA,CAAQ5W,WAAW;UACtB;QACF,EACF;MACT,CAAO;MACDuW,CAAA,CAAEK,OAAA,CAAQ1W,OAAO;IACvB,CAAK;IAEDqW,CAAA,CAAEC,IAAA,CAAK,SAAS,YAAY;MAC1BD,CAAA,CAAEK,OAAA,CAAQnX,KAAK;MACf8W,CAAA,CAAEK,OAAA,CAAQhY,eAAe;MACzB2X,CAAA,CAAEK,OAAA,CAAQlX,EAAE;MACZ6W,CAAA,CAAEU,QAAA,CAASrY,eAAe;IAChC,CAAK;IAED,KAAKsY,mBAAA,CAAqB;EAC3B;AACH;AAEA,MAAMlD,IAAA,CAAK;EACTrY,YAAYsP,CAAA,EAAGD,CAAA,EAAGyF,CAAA,EAAG;IACnB,KAAKxF,CAAA,GAAIA,CAAA;IACT,KAAKD,CAAA,GAAIA,CAAA;IACT,KAAKyF,CAAA,GAAIA,CAAA;IACT,KAAKnE,MAAA,GAAS,IAAI3G,OAAA,CAAS;EAC5B;AACH;AAEA,MAAMmE,YAAA,GAAe;EACnBc,SAAA,EAAW;EACXb,eAAA,EAAiB;EACjBC,GAAA,EAAK;EACLC,IAAA,EAAM;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}