{"ast": null, "code": "/*\n  Progress within given range\n\n  Given a lower limit and an upper limit, we return the progress\n  (expressed as a number 0-1) represented by the given value, and\n  limit that progress to within 0-1.\n\n  @param [number]: Lower limit\n  @param [number]: Upper limit\n  @param [number]: Value to find progress within given range\n  @return [number]: Progress of value within range as expressed 0-1\n*/\n/*#__NO_SIDE_EFFECTS__*/\nconst progress = (from, to, value) => {\n  const toFromDifference = to - from;\n  return toFromDifference === 0 ? 1 : (value - from) / toFromDifference;\n};\nexport { progress };", "map": {"version": 3, "names": ["progress", "from", "to", "value", "toFromDifference"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/motion-utils/dist/es/progress.mjs"], "sourcesContent": ["/*\n  Progress within given range\n\n  Given a lower limit and an upper limit, we return the progress\n  (expressed as a number 0-1) represented by the given value, and\n  limit that progress to within 0-1.\n\n  @param [number]: Lower limit\n  @param [number]: Upper limit\n  @param [number]: Value to find progress within given range\n  @return [number]: Progress of value within range as expressed 0-1\n*/\n/*#__NO_SIDE_EFFECTS__*/\nconst progress = (from, to, value) => {\n    const toFromDifference = to - from;\n    return toFromDifference === 0 ? 1 : (value - from) / toFromDifference;\n};\n\nexport { progress };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,QAAQ,GAAGA,CAACC,IAAI,EAAEC,EAAE,EAAEC,KAAK,KAAK;EAClC,MAAMC,gBAAgB,GAAGF,EAAE,GAAGD,IAAI;EAClC,OAAOG,gBAAgB,KAAK,CAAC,GAAG,CAAC,GAAG,CAACD,KAAK,GAAGF,IAAI,IAAIG,gBAAgB;AACzE,CAAC;AAED,SAASJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}