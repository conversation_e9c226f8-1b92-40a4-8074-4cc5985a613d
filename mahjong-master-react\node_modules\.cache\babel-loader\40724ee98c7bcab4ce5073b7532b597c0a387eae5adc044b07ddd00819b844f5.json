{"ast": null, "code": "import { LatheGeometry, Path } from \"three\";\nconst CapsuleGeometry = /* @__PURE__ */(() => {\n  class CapsuleGeometry2 extends LatheGeometry {\n    constructor(radius = 1, length = 1, capSegments = 4, radialSegments = 8) {\n      const path = new Path();\n      path.absarc(0, -length / 2, radius, Math.PI * 1.5, 0);\n      path.absarc(0, length / 2, radius, 0, Math.PI * 0.5);\n      super(path.getPoints(capSegments), radialSegments);\n      this.type = \"CapsuleGeometry\";\n      this.parameters = {\n        radius,\n        height: length,\n        capSegments,\n        radialSegments\n      };\n    }\n    static fromJSON(data) {\n      return new CapsuleGeometry2(data.radius, data.length, data.capSegments, data.radialSegments);\n    }\n  }\n  return CapsuleGeometry2;\n})();\nexport { CapsuleGeometry };", "map": {"version": 3, "names": ["CapsuleGeometry", "CapsuleGeometry2", "LatheGeometry", "constructor", "radius", "length", "capSegments", "radialSegments", "path", "Path", "absarc", "Math", "PI", "getPoints", "type", "parameters", "height", "fromJSON", "data"], "sources": ["F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\node_modules\\src\\_polyfill\\CapsuleGeometry.js"], "sourcesContent": ["import { Path, LatheGeometry } from 'three'\n\nconst CapsuleGeometry = /* @__PURE__ */ (() => {\n  class CapsuleGeometry extends LatheGeometry {\n    constructor(radius = 1, length = 1, capSegments = 4, radialSegments = 8) {\n      const path = new Path()\n      path.absarc(0, -length / 2, radius, Math.PI * 1.5, 0)\n      path.absarc(0, length / 2, radius, 0, Math.PI * 0.5)\n\n      super(path.getPoints(capSegments), radialSegments)\n\n      this.type = 'CapsuleGeometry'\n\n      this.parameters = {\n        radius: radius,\n        height: length,\n        capSegments: capSegments,\n        radialSegments: radialSegments,\n      }\n    }\n\n    static fromJSON(data) {\n      return new CapsuleGeometry(data.radius, data.length, data.capSegments, data.radialSegments)\n    }\n  }\n\n  return CapsuleGeometry\n})()\n\nexport { CapsuleGeometry }\n"], "mappings": ";AAEK,MAACA,eAAA,GAAmC,sBAAM;EAC7C,MAAMC,gBAAA,SAAwBC,aAAA,CAAc;IAC1CC,YAAYC,MAAA,GAAS,GAAGC,MAAA,GAAS,GAAGC,WAAA,GAAc,GAAGC,cAAA,GAAiB,GAAG;MACvE,MAAMC,IAAA,GAAO,IAAIC,IAAA,CAAM;MACvBD,IAAA,CAAKE,MAAA,CAAO,GAAG,CAACL,MAAA,GAAS,GAAGD,MAAA,EAAQO,IAAA,CAAKC,EAAA,GAAK,KAAK,CAAC;MACpDJ,IAAA,CAAKE,MAAA,CAAO,GAAGL,MAAA,GAAS,GAAGD,MAAA,EAAQ,GAAGO,IAAA,CAAKC,EAAA,GAAK,GAAG;MAEnD,MAAMJ,IAAA,CAAKK,SAAA,CAAUP,WAAW,GAAGC,cAAc;MAEjD,KAAKO,IAAA,GAAO;MAEZ,KAAKC,UAAA,GAAa;QAChBX,MAAA;QACAY,MAAA,EAAQX,MAAA;QACRC,WAAA;QACAC;MACD;IACF;IAED,OAAOU,SAASC,IAAA,EAAM;MACpB,OAAO,IAAIjB,gBAAA,CAAgBiB,IAAA,CAAKd,MAAA,EAAQc,IAAA,CAAKb,MAAA,EAAQa,IAAA,CAAKZ,WAAA,EAAaY,IAAA,CAAKX,cAAc;IAC3F;EACF;EAED,OAAON,gBAAA;AACT,GAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}