{"ast": null, "code": "// Accepts an easing function and returns a new one that outputs reversed values.\n// Turns easeIn into easeOut.\nconst reverseEasing = easing => p => 1 - easing(1 - p);\nexport { reverseEasing };", "map": {"version": 3, "names": ["reverseEasing", "easing", "p"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/motion-utils/dist/es/easing/modifiers/reverse.mjs"], "sourcesContent": ["// Accepts an easing function and returns a new one that outputs reversed values.\n// Turns easeIn into easeOut.\nconst reverseEasing = (easing) => (p) => 1 - easing(1 - p);\n\nexport { reverseEasing };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,aAAa,GAAIC,MAAM,IAAMC,CAAC,IAAK,CAAC,GAAGD,MAAM,CAAC,CAAC,GAAGC,CAAC,CAAC;AAE1D,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}