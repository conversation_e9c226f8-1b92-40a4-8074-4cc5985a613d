{"ast": null, "code": "const singleColorRegex = /^(?:#[0-9a-f]{3,8}|(?:rgb|h[s\\u017F]l)a?\\((?:-?[\\.0-9]+%?[\\t-\\r ,\\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]+){2}-?[\\.0-9]+%?[\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*(?:[,\\/][\\t-\\r \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000\\uFEFF]*)?(?:\\b[0-9]+(?:\\.[0-9]+)?|\\.[0-9]+)?%?\\))$/i;\nexport { singleColorRegex };", "map": {"version": 3, "names": ["singleColorRegex"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/motion-dom/dist/es/value/types/utils/single-color-regex.mjs"], "sourcesContent": ["const singleColorRegex = /^(?:#[\\da-f]{3,8}|(?:rgb|hsl)a?\\((?:-?[\\d.]+%?[,\\s]+){2}-?[\\d.]+%?\\s*(?:[,/]\\s*)?(?:\\b\\d+(?:\\.\\d+)?|\\.\\d+)?%?\\))$/iu;\n\nexport { singleColorRegex };\n"], "mappings": "AAAA,MAAMA,gBAAgB,GAAG,4UAAqH;AAE9I,SAASA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}