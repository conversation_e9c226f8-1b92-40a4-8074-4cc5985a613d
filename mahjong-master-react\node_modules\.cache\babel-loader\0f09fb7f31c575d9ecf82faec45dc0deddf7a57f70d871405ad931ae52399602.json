{"ast": null, "code": "import { BYTES_PER_NODE, IS_LEAFNODE_FLAG } from '../Constants.js';\nimport { IS_LEAF } from '../utils/nodeBufferUtils.js';\nlet float32Array, uint32Array, uint16Array, uint8Array;\nconst MAX_POINTER = Math.pow(2, 32);\nexport function countNodes(node) {\n  if ('count' in node) {\n    return 1;\n  } else {\n    return 1 + countNodes(node.left) + countNodes(node.right);\n  }\n}\nexport function populateBuffer(byteOffset, node, buffer) {\n  float32Array = new Float32Array(buffer);\n  uint32Array = new Uint32Array(buffer);\n  uint16Array = new Uint16Array(buffer);\n  uint8Array = new Uint8Array(buffer);\n  return _populateBuffer(byteOffset, node);\n}\n\n// pack structure\n// boundingData  \t\t\t\t: 6 float32\n// right / offset \t\t\t\t: 1 uint32\n// splitAxis / isLeaf + count \t: 1 uint32 / 2 uint16\nfunction _populateBuffer(byteOffset, node) {\n  const stride4Offset = byteOffset / 4;\n  const stride2Offset = byteOffset / 2;\n  const isLeaf = 'count' in node;\n  const boundingData = node.boundingData;\n  for (let i = 0; i < 6; i++) {\n    float32Array[stride4Offset + i] = boundingData[i];\n  }\n  if (isLeaf) {\n    if (node.buffer) {\n      const buffer = node.buffer;\n      uint8Array.set(new Uint8Array(buffer), byteOffset);\n      for (let offset = byteOffset, l = byteOffset + buffer.byteLength; offset < l; offset += BYTES_PER_NODE) {\n        const offset2 = offset / 2;\n        if (!IS_LEAF(offset2, uint16Array)) {\n          uint32Array[offset / 4 + 6] += stride4Offset;\n        }\n      }\n      return byteOffset + buffer.byteLength;\n    } else {\n      const offset = node.offset;\n      const count = node.count;\n      uint32Array[stride4Offset + 6] = offset;\n      uint16Array[stride2Offset + 14] = count;\n      uint16Array[stride2Offset + 15] = IS_LEAFNODE_FLAG;\n      return byteOffset + BYTES_PER_NODE;\n    }\n  } else {\n    const left = node.left;\n    const right = node.right;\n    const splitAxis = node.splitAxis;\n    let nextUnusedPointer;\n    nextUnusedPointer = _populateBuffer(byteOffset + BYTES_PER_NODE, left);\n    if (nextUnusedPointer / 4 > MAX_POINTER) {\n      throw new Error('MeshBVH: Cannot store child pointer greater than 32 bits.');\n    }\n    uint32Array[stride4Offset + 6] = nextUnusedPointer / 4;\n    nextUnusedPointer = _populateBuffer(nextUnusedPointer, right);\n    uint32Array[stride4Offset + 7] = splitAxis;\n    return nextUnusedPointer;\n  }\n}", "map": {"version": 3, "names": ["BYTES_PER_NODE", "IS_LEAFNODE_FLAG", "IS_LEAF", "float32Array", "uint32Array", "uint16Array", "uint8Array", "MAX_POINTER", "Math", "pow", "countNodes", "node", "left", "right", "populate<PERSON><PERSON>er", "byteOffset", "buffer", "Float32Array", "Uint32Array", "Uint16Array", "Uint8Array", "_populateBuffer", "stride4Offset", "stride2Offset", "<PERSON><PERSON><PERSON><PERSON>", "boundingData", "i", "set", "offset", "l", "byteLength", "offset2", "count", "splitAxis", "nextUnusedPointer", "Error"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/three-mesh-bvh/src/core/build/buildUtils.js"], "sourcesContent": ["import { BYTES_PER_NODE, IS_LEAFNODE_FLAG } from '../Constants.js';\nimport { IS_LEAF } from '../utils/nodeBufferUtils.js';\n\nlet float32Array, uint32Array, uint16Array, uint8Array;\nconst MAX_POINTER = Math.pow( 2, 32 );\n\nexport function countNodes( node ) {\n\n\tif ( 'count' in node ) {\n\n\t\treturn 1;\n\n\t} else {\n\n\t\treturn 1 + countNodes( node.left ) + countNodes( node.right );\n\n\t}\n\n}\n\nexport function populateBuffer( byteOffset, node, buffer ) {\n\n\tfloat32Array = new Float32Array( buffer );\n\tuint32Array = new Uint32Array( buffer );\n\tuint16Array = new Uint16Array( buffer );\n\tuint8Array = new Uint8Array( buffer );\n\n\treturn _populateBuffer( byteOffset, node );\n\n}\n\n// pack structure\n// boundingData  \t\t\t\t: 6 float32\n// right / offset \t\t\t\t: 1 uint32\n// splitAxis / isLeaf + count \t: 1 uint32 / 2 uint16\nfunction _populateBuffer( byteOffset, node ) {\n\n\tconst stride4Offset = byteOffset / 4;\n\tconst stride2Offset = byteOffset / 2;\n\tconst isLeaf = 'count' in node;\n\tconst boundingData = node.boundingData;\n\tfor ( let i = 0; i < 6; i ++ ) {\n\n\t\tfloat32Array[ stride4Offset + i ] = boundingData[ i ];\n\n\t}\n\n\tif ( isLeaf ) {\n\n\t\tif ( node.buffer ) {\n\n\t\t\tconst buffer = node.buffer;\n\t\t\tuint8Array.set( new Uint8Array( buffer ), byteOffset );\n\n\t\t\tfor ( let offset = byteOffset, l = byteOffset + buffer.byteLength; offset < l; offset += BYTES_PER_NODE ) {\n\n\t\t\t\tconst offset2 = offset / 2;\n\t\t\t\tif ( ! IS_LEAF( offset2, uint16Array ) ) {\n\n\t\t\t\t\tuint32Array[ ( offset / 4 ) + 6 ] += stride4Offset;\n\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn byteOffset + buffer.byteLength;\n\n\t\t} else {\n\n\t\t\tconst offset = node.offset;\n\t\t\tconst count = node.count;\n\t\t\tuint32Array[ stride4Offset + 6 ] = offset;\n\t\t\tuint16Array[ stride2Offset + 14 ] = count;\n\t\t\tuint16Array[ stride2Offset + 15 ] = IS_LEAFNODE_FLAG;\n\t\t\treturn byteOffset + BYTES_PER_NODE;\n\n\t\t}\n\n\t} else {\n\n\t\tconst left = node.left;\n\t\tconst right = node.right;\n\t\tconst splitAxis = node.splitAxis;\n\n\t\tlet nextUnusedPointer;\n\t\tnextUnusedPointer = _populateBuffer( byteOffset + BYTES_PER_NODE, left );\n\n\t\tif ( ( nextUnusedPointer / 4 ) > MAX_POINTER ) {\n\n\t\t\tthrow new Error( 'MeshBVH: Cannot store child pointer greater than 32 bits.' );\n\n\t\t}\n\n\t\tuint32Array[ stride4Offset + 6 ] = nextUnusedPointer / 4;\n\t\tnextUnusedPointer = _populateBuffer( nextUnusedPointer, right );\n\n\t\tuint32Array[ stride4Offset + 7 ] = splitAxis;\n\t\treturn nextUnusedPointer;\n\n\t}\n\n}\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,gBAAgB,QAAQ,iBAAiB;AAClE,SAASC,OAAO,QAAQ,6BAA6B;AAErD,IAAIC,YAAY,EAAEC,WAAW,EAAEC,WAAW,EAAEC,UAAU;AACtD,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAE,CAAC,EAAE,EAAG,CAAC;AAErC,OAAO,SAASC,UAAUA,CAAEC,IAAI,EAAG;EAElC,IAAK,OAAO,IAAIA,IAAI,EAAG;IAEtB,OAAO,CAAC;EAET,CAAC,MAAM;IAEN,OAAO,CAAC,GAAGD,UAAU,CAAEC,IAAI,CAACC,IAAK,CAAC,GAAGF,UAAU,CAAEC,IAAI,CAACE,KAAM,CAAC;EAE9D;AAED;AAEA,OAAO,SAASC,cAAcA,CAAEC,UAAU,EAAEJ,IAAI,EAAEK,MAAM,EAAG;EAE1Db,YAAY,GAAG,IAAIc,YAAY,CAAED,MAAO,CAAC;EACzCZ,WAAW,GAAG,IAAIc,WAAW,CAAEF,MAAO,CAAC;EACvCX,WAAW,GAAG,IAAIc,WAAW,CAAEH,MAAO,CAAC;EACvCV,UAAU,GAAG,IAAIc,UAAU,CAAEJ,MAAO,CAAC;EAErC,OAAOK,eAAe,CAAEN,UAAU,EAAEJ,IAAK,CAAC;AAE3C;;AAEA;AACA;AACA;AACA;AACA,SAASU,eAAeA,CAAEN,UAAU,EAAEJ,IAAI,EAAG;EAE5C,MAAMW,aAAa,GAAGP,UAAU,GAAG,CAAC;EACpC,MAAMQ,aAAa,GAAGR,UAAU,GAAG,CAAC;EACpC,MAAMS,MAAM,GAAG,OAAO,IAAIb,IAAI;EAC9B,MAAMc,YAAY,GAAGd,IAAI,CAACc,YAAY;EACtC,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;IAE9BvB,YAAY,CAAEmB,aAAa,GAAGI,CAAC,CAAE,GAAGD,YAAY,CAAEC,CAAC,CAAE;EAEtD;EAEA,IAAKF,MAAM,EAAG;IAEb,IAAKb,IAAI,CAACK,MAAM,EAAG;MAElB,MAAMA,MAAM,GAAGL,IAAI,CAACK,MAAM;MAC1BV,UAAU,CAACqB,GAAG,CAAE,IAAIP,UAAU,CAAEJ,MAAO,CAAC,EAAED,UAAW,CAAC;MAEtD,KAAM,IAAIa,MAAM,GAAGb,UAAU,EAAEc,CAAC,GAAGd,UAAU,GAAGC,MAAM,CAACc,UAAU,EAAEF,MAAM,GAAGC,CAAC,EAAED,MAAM,IAAI5B,cAAc,EAAG;QAEzG,MAAM+B,OAAO,GAAGH,MAAM,GAAG,CAAC;QAC1B,IAAK,CAAE1B,OAAO,CAAE6B,OAAO,EAAE1B,WAAY,CAAC,EAAG;UAExCD,WAAW,CAAIwB,MAAM,GAAG,CAAC,GAAK,CAAC,CAAE,IAAIN,aAAa;QAGnD;MAED;MAEA,OAAOP,UAAU,GAAGC,MAAM,CAACc,UAAU;IAEtC,CAAC,MAAM;MAEN,MAAMF,MAAM,GAAGjB,IAAI,CAACiB,MAAM;MAC1B,MAAMI,KAAK,GAAGrB,IAAI,CAACqB,KAAK;MACxB5B,WAAW,CAAEkB,aAAa,GAAG,CAAC,CAAE,GAAGM,MAAM;MACzCvB,WAAW,CAAEkB,aAAa,GAAG,EAAE,CAAE,GAAGS,KAAK;MACzC3B,WAAW,CAAEkB,aAAa,GAAG,EAAE,CAAE,GAAGtB,gBAAgB;MACpD,OAAOc,UAAU,GAAGf,cAAc;IAEnC;EAED,CAAC,MAAM;IAEN,MAAMY,IAAI,GAAGD,IAAI,CAACC,IAAI;IACtB,MAAMC,KAAK,GAAGF,IAAI,CAACE,KAAK;IACxB,MAAMoB,SAAS,GAAGtB,IAAI,CAACsB,SAAS;IAEhC,IAAIC,iBAAiB;IACrBA,iBAAiB,GAAGb,eAAe,CAAEN,UAAU,GAAGf,cAAc,EAAEY,IAAK,CAAC;IAExE,IAAOsB,iBAAiB,GAAG,CAAC,GAAK3B,WAAW,EAAG;MAE9C,MAAM,IAAI4B,KAAK,CAAE,2DAA4D,CAAC;IAE/E;IAEA/B,WAAW,CAAEkB,aAAa,GAAG,CAAC,CAAE,GAAGY,iBAAiB,GAAG,CAAC;IACxDA,iBAAiB,GAAGb,eAAe,CAAEa,iBAAiB,EAAErB,KAAM,CAAC;IAE/DT,WAAW,CAAEkB,aAAa,GAAG,CAAC,CAAE,GAAGW,SAAS;IAC5C,OAAOC,iBAAiB;EAEzB;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}