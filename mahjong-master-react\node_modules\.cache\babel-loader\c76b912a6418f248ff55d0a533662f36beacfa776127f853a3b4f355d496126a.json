{"ast": null, "code": "const createStoreImpl = createState => {\n  let state;\n  const listeners = /* @__PURE__ */new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach(listener => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = listener => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const destroy = () => {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n      console.warn(\"[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected.\");\n    }\n    listeners.clear();\n  };\n  const api = {\n    setState,\n    getState,\n    getInitialState,\n    subscribe,\n    destroy\n  };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = createState => createState ? createStoreImpl(createState) : createStoreImpl;\nvar vanilla = createState => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\"[DEPRECATED] Default export is deprecated. Instead use import { createStore } from 'zustand/vanilla'.\");\n  }\n  return createStore(createState);\n};\nexport { createStore, vanilla as default };", "map": {"version": 3, "names": ["createStoreImpl", "createState", "state", "listeners", "Set", "setState", "partial", "replace", "nextState", "Object", "is", "previousState", "assign", "for<PERSON>ach", "listener", "getState", "getInitialState", "initialState", "subscribe", "add", "delete", "destroy", "import", "meta", "env", "MODE", "console", "warn", "clear", "api", "createStore", "vanilla", "default"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/tunnel-rat/node_modules/zustand/esm/vanilla.mjs"], "sourcesContent": ["const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const destroy = () => {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n      console.warn(\n        \"[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected.\"\n      );\n    }\n    listeners.clear();\n  };\n  const api = { setState, getState, getInitialState, subscribe, destroy };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\nvar vanilla = (createState) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use import { createStore } from 'zustand/vanilla'.\"\n    );\n  }\n  return createStore(createState);\n};\n\nexport { createStore, vanilla as default };\n"], "mappings": "AAAA,MAAMA,eAAe,GAAIC,WAAW,IAAK;EACvC,IAAIC,KAAK;EACT,MAAMC,SAAS,GAAG,eAAgB,IAAIC,GAAG,CAAC,CAAC;EAC3C,MAAMC,QAAQ,GAAGA,CAACC,OAAO,EAAEC,OAAO,KAAK;IACrC,MAAMC,SAAS,GAAG,OAAOF,OAAO,KAAK,UAAU,GAAGA,OAAO,CAACJ,KAAK,CAAC,GAAGI,OAAO;IAC1E,IAAI,CAACG,MAAM,CAACC,EAAE,CAACF,SAAS,EAAEN,KAAK,CAAC,EAAE;MAChC,MAAMS,aAAa,GAAGT,KAAK;MAC3BA,KAAK,GAAG,CAACK,OAAO,IAAI,IAAI,GAAGA,OAAO,GAAG,OAAOC,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,IAAI,IAAIA,SAAS,GAAGC,MAAM,CAACG,MAAM,CAAC,CAAC,CAAC,EAAEV,KAAK,EAAEM,SAAS,CAAC;MAC3IL,SAAS,CAACU,OAAO,CAAEC,QAAQ,IAAKA,QAAQ,CAACZ,KAAK,EAAES,aAAa,CAAC,CAAC;IACjE;EACF,CAAC;EACD,MAAMI,QAAQ,GAAGA,CAAA,KAAMb,KAAK;EAC5B,MAAMc,eAAe,GAAGA,CAAA,KAAMC,YAAY;EAC1C,MAAMC,SAAS,GAAIJ,QAAQ,IAAK;IAC9BX,SAAS,CAACgB,GAAG,CAACL,QAAQ,CAAC;IACvB,OAAO,MAAMX,SAAS,CAACiB,MAAM,CAACN,QAAQ,CAAC;EACzC,CAAC;EACD,MAAMO,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAI,CAACC,MAAM,CAACC,IAAI,CAACC,GAAG,GAAGF,MAAM,CAACC,IAAI,CAACC,GAAG,CAACC,IAAI,GAAG,KAAK,CAAC,MAAM,YAAY,EAAE;MACtEC,OAAO,CAACC,IAAI,CACV,wMACF,CAAC;IACH;IACAxB,SAAS,CAACyB,KAAK,CAAC,CAAC;EACnB,CAAC;EACD,MAAMC,GAAG,GAAG;IAAExB,QAAQ;IAAEU,QAAQ;IAAEC,eAAe;IAAEE,SAAS;IAAEG;EAAQ,CAAC;EACvE,MAAMJ,YAAY,GAAGf,KAAK,GAAGD,WAAW,CAACI,QAAQ,EAAEU,QAAQ,EAAEc,GAAG,CAAC;EACjE,OAAOA,GAAG;AACZ,CAAC;AACD,MAAMC,WAAW,GAAI7B,WAAW,IAAKA,WAAW,GAAGD,eAAe,CAACC,WAAW,CAAC,GAAGD,eAAe;AACjG,IAAI+B,OAAO,GAAI9B,WAAW,IAAK;EAC7B,IAAI,CAACqB,MAAM,CAACC,IAAI,CAACC,GAAG,GAAGF,MAAM,CAACC,IAAI,CAACC,GAAG,CAACC,IAAI,GAAG,KAAK,CAAC,MAAM,YAAY,EAAE;IACtEC,OAAO,CAACC,IAAI,CACV,uGACF,CAAC;EACH;EACA,OAAOG,WAAW,CAAC7B,WAAW,CAAC;AACjC,CAAC;AAED,SAAS6B,WAAW,EAAEC,OAAO,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}