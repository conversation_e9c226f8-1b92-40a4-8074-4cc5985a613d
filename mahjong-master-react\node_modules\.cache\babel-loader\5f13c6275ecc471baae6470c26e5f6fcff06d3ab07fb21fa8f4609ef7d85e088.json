{"ast": null, "code": "import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ector2, <PERSON>ector3, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Path, BufferGeometry, Float32BufferAttribute, ShapePath } from \"three\";\nconst COLOR_SPACE_SVG = \"srgb\";\nconst SVGLoader = /* @__PURE__ */(() => {\n  class SVGLoader2 extends Loader {\n    constructor(manager) {\n      super(manager);\n      this.defaultDPI = 90;\n      this.defaultUnit = \"px\";\n    }\n    load(url, onLoad, onProgress, onError) {\n      const scope = this;\n      const loader = new FileLoader(scope.manager);\n      loader.setPath(scope.path);\n      loader.setRequestHeader(scope.requestHeader);\n      loader.setWithCredentials(scope.withCredentials);\n      loader.load(url, function (text) {\n        try {\n          onLoad(scope.parse(text));\n        } catch (e) {\n          if (onError) {\n            onError(e);\n          } else {\n            console.error(e);\n          }\n          scope.manager.itemError(url);\n        }\n      }, on<PERSON>rog<PERSON>, onError);\n    }\n    parse(text) {\n      const scope = this;\n      function parseNode(node, style) {\n        if (node.nodeType !== 1) return;\n        const transform = getNodeTransform(node);\n        let isDefsNode = false;\n        let path = null;\n        switch (node.nodeName) {\n          case \"svg\":\n            style = parseStyle(node, style);\n            break;\n          case \"style\":\n            parseCSSStylesheet(node);\n            break;\n          case \"g\":\n            style = parseStyle(node, style);\n            break;\n          case \"path\":\n            style = parseStyle(node, style);\n            if (node.hasAttribute(\"d\")) path = parsePathNode(node);\n            break;\n          case \"rect\":\n            style = parseStyle(node, style);\n            path = parseRectNode(node);\n            break;\n          case \"polygon\":\n            style = parseStyle(node, style);\n            path = parsePolygonNode(node);\n            break;\n          case \"polyline\":\n            style = parseStyle(node, style);\n            path = parsePolylineNode(node);\n            break;\n          case \"circle\":\n            style = parseStyle(node, style);\n            path = parseCircleNode(node);\n            break;\n          case \"ellipse\":\n            style = parseStyle(node, style);\n            path = parseEllipseNode(node);\n            break;\n          case \"line\":\n            style = parseStyle(node, style);\n            path = parseLineNode(node);\n            break;\n          case \"defs\":\n            isDefsNode = true;\n            break;\n          case \"use\":\n            style = parseStyle(node, style);\n            const href = node.getAttributeNS(\"http://www.w3.org/1999/xlink\", \"href\") || \"\";\n            const usedNodeId = href.substring(1);\n            const usedNode = node.viewportElement.getElementById(usedNodeId);\n            if (usedNode) {\n              parseNode(usedNode, style);\n            } else {\n              console.warn(\"SVGLoader: 'use node' references non-existent node id: \" + usedNodeId);\n            }\n            break;\n        }\n        if (path) {\n          if (style.fill !== void 0 && style.fill !== \"none\") {\n            path.color.setStyle(style.fill, COLOR_SPACE_SVG);\n          }\n          transformPath(path, currentTransform);\n          paths.push(path);\n          path.userData = {\n            node,\n            style\n          };\n        }\n        const childNodes = node.childNodes;\n        for (let i = 0; i < childNodes.length; i++) {\n          const node2 = childNodes[i];\n          if (isDefsNode && node2.nodeName !== \"style\" && node2.nodeName !== \"defs\") {\n            continue;\n          }\n          parseNode(node2, style);\n        }\n        if (transform) {\n          transformStack.pop();\n          if (transformStack.length > 0) {\n            currentTransform.copy(transformStack[transformStack.length - 1]);\n          } else {\n            currentTransform.identity();\n          }\n        }\n      }\n      function parsePathNode(node) {\n        const path = new ShapePath();\n        const point = new Vector2();\n        const control = new Vector2();\n        const firstPoint = new Vector2();\n        let isFirstPoint = true;\n        let doSetFirstPoint = false;\n        const d = node.getAttribute(\"d\");\n        if (d === \"\" || d === \"none\") return null;\n        const commands = d.match(/[a-df-z][^a-df-z]*/gi);\n        for (let i = 0, l = commands.length; i < l; i++) {\n          const command = commands[i];\n          const type = command.charAt(0);\n          const data2 = command.slice(1).trim();\n          if (isFirstPoint === true) {\n            doSetFirstPoint = true;\n            isFirstPoint = false;\n          }\n          let numbers;\n          switch (type) {\n            case \"M\":\n              numbers = parseFloats(data2);\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                point.x = numbers[j + 0];\n                point.y = numbers[j + 1];\n                control.x = point.x;\n                control.y = point.y;\n                if (j === 0) {\n                  path.moveTo(point.x, point.y);\n                } else {\n                  path.lineTo(point.x, point.y);\n                }\n                if (j === 0) firstPoint.copy(point);\n              }\n              break;\n            case \"H\":\n              numbers = parseFloats(data2);\n              for (let j = 0, jl = numbers.length; j < jl; j++) {\n                point.x = numbers[j];\n                control.x = point.x;\n                control.y = point.y;\n                path.lineTo(point.x, point.y);\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point);\n              }\n              break;\n            case \"V\":\n              numbers = parseFloats(data2);\n              for (let j = 0, jl = numbers.length; j < jl; j++) {\n                point.y = numbers[j];\n                control.x = point.x;\n                control.y = point.y;\n                path.lineTo(point.x, point.y);\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point);\n              }\n              break;\n            case \"L\":\n              numbers = parseFloats(data2);\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                point.x = numbers[j + 0];\n                point.y = numbers[j + 1];\n                control.x = point.x;\n                control.y = point.y;\n                path.lineTo(point.x, point.y);\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point);\n              }\n              break;\n            case \"C\":\n              numbers = parseFloats(data2);\n              for (let j = 0, jl = numbers.length; j < jl; j += 6) {\n                path.bezierCurveTo(numbers[j + 0], numbers[j + 1], numbers[j + 2], numbers[j + 3], numbers[j + 4], numbers[j + 5]);\n                control.x = numbers[j + 2];\n                control.y = numbers[j + 3];\n                point.x = numbers[j + 4];\n                point.y = numbers[j + 5];\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point);\n              }\n              break;\n            case \"S\":\n              numbers = parseFloats(data2);\n              for (let j = 0, jl = numbers.length; j < jl; j += 4) {\n                path.bezierCurveTo(getReflection(point.x, control.x), getReflection(point.y, control.y), numbers[j + 0], numbers[j + 1], numbers[j + 2], numbers[j + 3]);\n                control.x = numbers[j + 0];\n                control.y = numbers[j + 1];\n                point.x = numbers[j + 2];\n                point.y = numbers[j + 3];\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point);\n              }\n              break;\n            case \"Q\":\n              numbers = parseFloats(data2);\n              for (let j = 0, jl = numbers.length; j < jl; j += 4) {\n                path.quadraticCurveTo(numbers[j + 0], numbers[j + 1], numbers[j + 2], numbers[j + 3]);\n                control.x = numbers[j + 0];\n                control.y = numbers[j + 1];\n                point.x = numbers[j + 2];\n                point.y = numbers[j + 3];\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point);\n              }\n              break;\n            case \"T\":\n              numbers = parseFloats(data2);\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                const rx = getReflection(point.x, control.x);\n                const ry = getReflection(point.y, control.y);\n                path.quadraticCurveTo(rx, ry, numbers[j + 0], numbers[j + 1]);\n                control.x = rx;\n                control.y = ry;\n                point.x = numbers[j + 0];\n                point.y = numbers[j + 1];\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point);\n              }\n              break;\n            case \"A\":\n              numbers = parseFloats(data2, [3, 4], 7);\n              for (let j = 0, jl = numbers.length; j < jl; j += 7) {\n                if (numbers[j + 5] == point.x && numbers[j + 6] == point.y) continue;\n                const start = point.clone();\n                point.x = numbers[j + 5];\n                point.y = numbers[j + 6];\n                control.x = point.x;\n                control.y = point.y;\n                parseArcCommand(path, numbers[j], numbers[j + 1], numbers[j + 2], numbers[j + 3], numbers[j + 4], start, point);\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point);\n              }\n              break;\n            case \"m\":\n              numbers = parseFloats(data2);\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                point.x += numbers[j + 0];\n                point.y += numbers[j + 1];\n                control.x = point.x;\n                control.y = point.y;\n                if (j === 0) {\n                  path.moveTo(point.x, point.y);\n                } else {\n                  path.lineTo(point.x, point.y);\n                }\n                if (j === 0) firstPoint.copy(point);\n              }\n              break;\n            case \"h\":\n              numbers = parseFloats(data2);\n              for (let j = 0, jl = numbers.length; j < jl; j++) {\n                point.x += numbers[j];\n                control.x = point.x;\n                control.y = point.y;\n                path.lineTo(point.x, point.y);\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point);\n              }\n              break;\n            case \"v\":\n              numbers = parseFloats(data2);\n              for (let j = 0, jl = numbers.length; j < jl; j++) {\n                point.y += numbers[j];\n                control.x = point.x;\n                control.y = point.y;\n                path.lineTo(point.x, point.y);\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point);\n              }\n              break;\n            case \"l\":\n              numbers = parseFloats(data2);\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                point.x += numbers[j + 0];\n                point.y += numbers[j + 1];\n                control.x = point.x;\n                control.y = point.y;\n                path.lineTo(point.x, point.y);\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point);\n              }\n              break;\n            case \"c\":\n              numbers = parseFloats(data2);\n              for (let j = 0, jl = numbers.length; j < jl; j += 6) {\n                path.bezierCurveTo(point.x + numbers[j + 0], point.y + numbers[j + 1], point.x + numbers[j + 2], point.y + numbers[j + 3], point.x + numbers[j + 4], point.y + numbers[j + 5]);\n                control.x = point.x + numbers[j + 2];\n                control.y = point.y + numbers[j + 3];\n                point.x += numbers[j + 4];\n                point.y += numbers[j + 5];\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point);\n              }\n              break;\n            case \"s\":\n              numbers = parseFloats(data2);\n              for (let j = 0, jl = numbers.length; j < jl; j += 4) {\n                path.bezierCurveTo(getReflection(point.x, control.x), getReflection(point.y, control.y), point.x + numbers[j + 0], point.y + numbers[j + 1], point.x + numbers[j + 2], point.y + numbers[j + 3]);\n                control.x = point.x + numbers[j + 0];\n                control.y = point.y + numbers[j + 1];\n                point.x += numbers[j + 2];\n                point.y += numbers[j + 3];\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point);\n              }\n              break;\n            case \"q\":\n              numbers = parseFloats(data2);\n              for (let j = 0, jl = numbers.length; j < jl; j += 4) {\n                path.quadraticCurveTo(point.x + numbers[j + 0], point.y + numbers[j + 1], point.x + numbers[j + 2], point.y + numbers[j + 3]);\n                control.x = point.x + numbers[j + 0];\n                control.y = point.y + numbers[j + 1];\n                point.x += numbers[j + 2];\n                point.y += numbers[j + 3];\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point);\n              }\n              break;\n            case \"t\":\n              numbers = parseFloats(data2);\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                const rx = getReflection(point.x, control.x);\n                const ry = getReflection(point.y, control.y);\n                path.quadraticCurveTo(rx, ry, point.x + numbers[j + 0], point.y + numbers[j + 1]);\n                control.x = rx;\n                control.y = ry;\n                point.x = point.x + numbers[j + 0];\n                point.y = point.y + numbers[j + 1];\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point);\n              }\n              break;\n            case \"a\":\n              numbers = parseFloats(data2, [3, 4], 7);\n              for (let j = 0, jl = numbers.length; j < jl; j += 7) {\n                if (numbers[j + 5] == 0 && numbers[j + 6] == 0) continue;\n                const start = point.clone();\n                point.x += numbers[j + 5];\n                point.y += numbers[j + 6];\n                control.x = point.x;\n                control.y = point.y;\n                parseArcCommand(path, numbers[j], numbers[j + 1], numbers[j + 2], numbers[j + 3], numbers[j + 4], start, point);\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point);\n              }\n              break;\n            case \"Z\":\n            case \"z\":\n              path.currentPath.autoClose = true;\n              if (path.currentPath.curves.length > 0) {\n                point.copy(firstPoint);\n                path.currentPath.currentPoint.copy(point);\n                isFirstPoint = true;\n              }\n              break;\n            default:\n              console.warn(command);\n          }\n          doSetFirstPoint = false;\n        }\n        return path;\n      }\n      function parseCSSStylesheet(node) {\n        if (!node.sheet || !node.sheet.cssRules || !node.sheet.cssRules.length) return;\n        for (let i = 0; i < node.sheet.cssRules.length; i++) {\n          const stylesheet = node.sheet.cssRules[i];\n          if (stylesheet.type !== 1) continue;\n          const selectorList = stylesheet.selectorText.split(/,/gm).filter(Boolean).map(i2 => i2.trim());\n          for (let j = 0; j < selectorList.length; j++) {\n            const definitions = Object.fromEntries(Object.entries(stylesheet.style).filter(([, v]) => v !== \"\"));\n            stylesheets[selectorList[j]] = Object.assign(stylesheets[selectorList[j]] || {}, definitions);\n          }\n        }\n      }\n      function parseArcCommand(path, rx, ry, x_axis_rotation, large_arc_flag, sweep_flag, start, end) {\n        if (rx == 0 || ry == 0) {\n          path.lineTo(end.x, end.y);\n          return;\n        }\n        x_axis_rotation = x_axis_rotation * Math.PI / 180;\n        rx = Math.abs(rx);\n        ry = Math.abs(ry);\n        const dx2 = (start.x - end.x) / 2;\n        const dy2 = (start.y - end.y) / 2;\n        const x1p = Math.cos(x_axis_rotation) * dx2 + Math.sin(x_axis_rotation) * dy2;\n        const y1p = -Math.sin(x_axis_rotation) * dx2 + Math.cos(x_axis_rotation) * dy2;\n        let rxs = rx * rx;\n        let rys = ry * ry;\n        const x1ps = x1p * x1p;\n        const y1ps = y1p * y1p;\n        const cr = x1ps / rxs + y1ps / rys;\n        if (cr > 1) {\n          const s = Math.sqrt(cr);\n          rx = s * rx;\n          ry = s * ry;\n          rxs = rx * rx;\n          rys = ry * ry;\n        }\n        const dq = rxs * y1ps + rys * x1ps;\n        const pq = (rxs * rys - dq) / dq;\n        let q = Math.sqrt(Math.max(0, pq));\n        if (large_arc_flag === sweep_flag) q = -q;\n        const cxp = q * rx * y1p / ry;\n        const cyp = -q * ry * x1p / rx;\n        const cx = Math.cos(x_axis_rotation) * cxp - Math.sin(x_axis_rotation) * cyp + (start.x + end.x) / 2;\n        const cy = Math.sin(x_axis_rotation) * cxp + Math.cos(x_axis_rotation) * cyp + (start.y + end.y) / 2;\n        const theta = svgAngle(1, 0, (x1p - cxp) / rx, (y1p - cyp) / ry);\n        const delta = svgAngle((x1p - cxp) / rx, (y1p - cyp) / ry, (-x1p - cxp) / rx, (-y1p - cyp) / ry) % (Math.PI * 2);\n        path.currentPath.absellipse(cx, cy, rx, ry, theta, theta + delta, sweep_flag === 0, x_axis_rotation);\n      }\n      function svgAngle(ux, uy, vx, vy) {\n        const dot = ux * vx + uy * vy;\n        const len = Math.sqrt(ux * ux + uy * uy) * Math.sqrt(vx * vx + vy * vy);\n        let ang = Math.acos(Math.max(-1, Math.min(1, dot / len)));\n        if (ux * vy - uy * vx < 0) ang = -ang;\n        return ang;\n      }\n      function parseRectNode(node) {\n        const x = parseFloatWithUnits(node.getAttribute(\"x\") || 0);\n        const y = parseFloatWithUnits(node.getAttribute(\"y\") || 0);\n        const rx = parseFloatWithUnits(node.getAttribute(\"rx\") || node.getAttribute(\"ry\") || 0);\n        const ry = parseFloatWithUnits(node.getAttribute(\"ry\") || node.getAttribute(\"rx\") || 0);\n        const w = parseFloatWithUnits(node.getAttribute(\"width\"));\n        const h = parseFloatWithUnits(node.getAttribute(\"height\"));\n        const bci = 1 - 0.551915024494;\n        const path = new ShapePath();\n        path.moveTo(x + rx, y);\n        path.lineTo(x + w - rx, y);\n        if (rx !== 0 || ry !== 0) {\n          path.bezierCurveTo(x + w - rx * bci, y, x + w, y + ry * bci, x + w, y + ry);\n        }\n        path.lineTo(x + w, y + h - ry);\n        if (rx !== 0 || ry !== 0) {\n          path.bezierCurveTo(x + w, y + h - ry * bci, x + w - rx * bci, y + h, x + w - rx, y + h);\n        }\n        path.lineTo(x + rx, y + h);\n        if (rx !== 0 || ry !== 0) {\n          path.bezierCurveTo(x + rx * bci, y + h, x, y + h - ry * bci, x, y + h - ry);\n        }\n        path.lineTo(x, y + ry);\n        if (rx !== 0 || ry !== 0) {\n          path.bezierCurveTo(x, y + ry * bci, x + rx * bci, y, x + rx, y);\n        }\n        return path;\n      }\n      function parsePolygonNode(node) {\n        function iterator(match, a, b) {\n          const x = parseFloatWithUnits(a);\n          const y = parseFloatWithUnits(b);\n          if (index === 0) {\n            path.moveTo(x, y);\n          } else {\n            path.lineTo(x, y);\n          }\n          index++;\n        }\n        const regex = /([+-]?\\d*\\.?\\d+(?:e[+-]?\\d+)?)(?:,|\\s)([+-]?\\d*\\.?\\d+(?:e[+-]?\\d+)?)/g;\n        const path = new ShapePath();\n        let index = 0;\n        node.getAttribute(\"points\").replace(regex, iterator);\n        path.currentPath.autoClose = true;\n        return path;\n      }\n      function parsePolylineNode(node) {\n        function iterator(match, a, b) {\n          const x = parseFloatWithUnits(a);\n          const y = parseFloatWithUnits(b);\n          if (index === 0) {\n            path.moveTo(x, y);\n          } else {\n            path.lineTo(x, y);\n          }\n          index++;\n        }\n        const regex = /([+-]?\\d*\\.?\\d+(?:e[+-]?\\d+)?)(?:,|\\s)([+-]?\\d*\\.?\\d+(?:e[+-]?\\d+)?)/g;\n        const path = new ShapePath();\n        let index = 0;\n        node.getAttribute(\"points\").replace(regex, iterator);\n        path.currentPath.autoClose = false;\n        return path;\n      }\n      function parseCircleNode(node) {\n        const x = parseFloatWithUnits(node.getAttribute(\"cx\") || 0);\n        const y = parseFloatWithUnits(node.getAttribute(\"cy\") || 0);\n        const r = parseFloatWithUnits(node.getAttribute(\"r\") || 0);\n        const subpath = new Path();\n        subpath.absarc(x, y, r, 0, Math.PI * 2);\n        const path = new ShapePath();\n        path.subPaths.push(subpath);\n        return path;\n      }\n      function parseEllipseNode(node) {\n        const x = parseFloatWithUnits(node.getAttribute(\"cx\") || 0);\n        const y = parseFloatWithUnits(node.getAttribute(\"cy\") || 0);\n        const rx = parseFloatWithUnits(node.getAttribute(\"rx\") || 0);\n        const ry = parseFloatWithUnits(node.getAttribute(\"ry\") || 0);\n        const subpath = new Path();\n        subpath.absellipse(x, y, rx, ry, 0, Math.PI * 2);\n        const path = new ShapePath();\n        path.subPaths.push(subpath);\n        return path;\n      }\n      function parseLineNode(node) {\n        const x1 = parseFloatWithUnits(node.getAttribute(\"x1\") || 0);\n        const y1 = parseFloatWithUnits(node.getAttribute(\"y1\") || 0);\n        const x2 = parseFloatWithUnits(node.getAttribute(\"x2\") || 0);\n        const y2 = parseFloatWithUnits(node.getAttribute(\"y2\") || 0);\n        const path = new ShapePath();\n        path.moveTo(x1, y1);\n        path.lineTo(x2, y2);\n        path.currentPath.autoClose = false;\n        return path;\n      }\n      function parseStyle(node, style) {\n        style = Object.assign({}, style);\n        let stylesheetStyles = {};\n        if (node.hasAttribute(\"class\")) {\n          const classSelectors = node.getAttribute(\"class\").split(/\\s/).filter(Boolean).map(i => i.trim());\n          for (let i = 0; i < classSelectors.length; i++) {\n            stylesheetStyles = Object.assign(stylesheetStyles, stylesheets[\".\" + classSelectors[i]]);\n          }\n        }\n        if (node.hasAttribute(\"id\")) {\n          stylesheetStyles = Object.assign(stylesheetStyles, stylesheets[\"#\" + node.getAttribute(\"id\")]);\n        }\n        function addStyle(svgName, jsName, adjustFunction) {\n          if (adjustFunction === void 0) adjustFunction = function copy(v) {\n            if (v.startsWith(\"url\")) console.warn(\"SVGLoader: url access in attributes is not implemented.\");\n            return v;\n          };\n          if (node.hasAttribute(svgName)) style[jsName] = adjustFunction(node.getAttribute(svgName));\n          if (stylesheetStyles[svgName]) style[jsName] = adjustFunction(stylesheetStyles[svgName]);\n          if (node.style && node.style[svgName] !== \"\") style[jsName] = adjustFunction(node.style[svgName]);\n        }\n        function clamp(v) {\n          return Math.max(0, Math.min(1, parseFloatWithUnits(v)));\n        }\n        function positive(v) {\n          return Math.max(0, parseFloatWithUnits(v));\n        }\n        addStyle(\"fill\", \"fill\");\n        addStyle(\"fill-opacity\", \"fillOpacity\", clamp);\n        addStyle(\"fill-rule\", \"fillRule\");\n        addStyle(\"opacity\", \"opacity\", clamp);\n        addStyle(\"stroke\", \"stroke\");\n        addStyle(\"stroke-opacity\", \"strokeOpacity\", clamp);\n        addStyle(\"stroke-width\", \"strokeWidth\", positive);\n        addStyle(\"stroke-linejoin\", \"strokeLineJoin\");\n        addStyle(\"stroke-linecap\", \"strokeLineCap\");\n        addStyle(\"stroke-miterlimit\", \"strokeMiterLimit\", positive);\n        addStyle(\"visibility\", \"visibility\");\n        return style;\n      }\n      function getReflection(a, b) {\n        return a - (b - a);\n      }\n      function parseFloats(input, flags, stride) {\n        if (typeof input !== \"string\") {\n          throw new TypeError(\"Invalid input: \" + typeof input);\n        }\n        const RE = {\n          SEPARATOR: /[ \\t\\r\\n\\,.\\-+]/,\n          WHITESPACE: /[ \\t\\r\\n]/,\n          DIGIT: /[\\d]/,\n          SIGN: /[-+]/,\n          POINT: /\\./,\n          COMMA: /,/,\n          EXP: /e/i,\n          FLAGS: /[01]/\n        };\n        const SEP = 0;\n        const INT = 1;\n        const FLOAT = 2;\n        const EXP = 3;\n        let state = SEP;\n        let seenComma = true;\n        let number = \"\",\n          exponent = \"\";\n        const result = [];\n        function throwSyntaxError(current2, i, partial) {\n          const error = new SyntaxError('Unexpected character \"' + current2 + '\" at index ' + i + \".\");\n          error.partial = partial;\n          throw error;\n        }\n        function newNumber() {\n          if (number !== \"\") {\n            if (exponent === \"\") result.push(Number(number));else result.push(Number(number) * Math.pow(10, Number(exponent)));\n          }\n          number = \"\";\n          exponent = \"\";\n        }\n        let current;\n        const length = input.length;\n        for (let i = 0; i < length; i++) {\n          current = input[i];\n          if (Array.isArray(flags) && flags.includes(result.length % stride) && RE.FLAGS.test(current)) {\n            state = INT;\n            number = current;\n            newNumber();\n            continue;\n          }\n          if (state === SEP) {\n            if (RE.WHITESPACE.test(current)) {\n              continue;\n            }\n            if (RE.DIGIT.test(current) || RE.SIGN.test(current)) {\n              state = INT;\n              number = current;\n              continue;\n            }\n            if (RE.POINT.test(current)) {\n              state = FLOAT;\n              number = current;\n              continue;\n            }\n            if (RE.COMMA.test(current)) {\n              if (seenComma) {\n                throwSyntaxError(current, i, result);\n              }\n              seenComma = true;\n            }\n          }\n          if (state === INT) {\n            if (RE.DIGIT.test(current)) {\n              number += current;\n              continue;\n            }\n            if (RE.POINT.test(current)) {\n              number += current;\n              state = FLOAT;\n              continue;\n            }\n            if (RE.EXP.test(current)) {\n              state = EXP;\n              continue;\n            }\n            if (RE.SIGN.test(current) && number.length === 1 && RE.SIGN.test(number[0])) {\n              throwSyntaxError(current, i, result);\n            }\n          }\n          if (state === FLOAT) {\n            if (RE.DIGIT.test(current)) {\n              number += current;\n              continue;\n            }\n            if (RE.EXP.test(current)) {\n              state = EXP;\n              continue;\n            }\n            if (RE.POINT.test(current) && number[number.length - 1] === \".\") {\n              throwSyntaxError(current, i, result);\n            }\n          }\n          if (state === EXP) {\n            if (RE.DIGIT.test(current)) {\n              exponent += current;\n              continue;\n            }\n            if (RE.SIGN.test(current)) {\n              if (exponent === \"\") {\n                exponent += current;\n                continue;\n              }\n              if (exponent.length === 1 && RE.SIGN.test(exponent)) {\n                throwSyntaxError(current, i, result);\n              }\n            }\n          }\n          if (RE.WHITESPACE.test(current)) {\n            newNumber();\n            state = SEP;\n            seenComma = false;\n          } else if (RE.COMMA.test(current)) {\n            newNumber();\n            state = SEP;\n            seenComma = true;\n          } else if (RE.SIGN.test(current)) {\n            newNumber();\n            state = INT;\n            number = current;\n          } else if (RE.POINT.test(current)) {\n            newNumber();\n            state = FLOAT;\n            number = current;\n          } else {\n            throwSyntaxError(current, i, result);\n          }\n        }\n        newNumber();\n        return result;\n      }\n      const units = [\"mm\", \"cm\", \"in\", \"pt\", \"pc\", \"px\"];\n      const unitConversion = {\n        mm: {\n          mm: 1,\n          cm: 0.1,\n          in: 1 / 25.4,\n          pt: 72 / 25.4,\n          pc: 6 / 25.4,\n          px: -1\n        },\n        cm: {\n          mm: 10,\n          cm: 1,\n          in: 1 / 2.54,\n          pt: 72 / 2.54,\n          pc: 6 / 2.54,\n          px: -1\n        },\n        in: {\n          mm: 25.4,\n          cm: 2.54,\n          in: 1,\n          pt: 72,\n          pc: 6,\n          px: -1\n        },\n        pt: {\n          mm: 25.4 / 72,\n          cm: 2.54 / 72,\n          in: 1 / 72,\n          pt: 1,\n          pc: 6 / 72,\n          px: -1\n        },\n        pc: {\n          mm: 25.4 / 6,\n          cm: 2.54 / 6,\n          in: 1 / 6,\n          pt: 72 / 6,\n          pc: 1,\n          px: -1\n        },\n        px: {\n          px: 1\n        }\n      };\n      function parseFloatWithUnits(string) {\n        let theUnit = \"px\";\n        if (typeof string === \"string\" || string instanceof String) {\n          for (let i = 0, n = units.length; i < n; i++) {\n            const u = units[i];\n            if (string.endsWith(u)) {\n              theUnit = u;\n              string = string.substring(0, string.length - u.length);\n              break;\n            }\n          }\n        }\n        let scale = void 0;\n        if (theUnit === \"px\" && scope.defaultUnit !== \"px\") {\n          scale = unitConversion[\"in\"][scope.defaultUnit] / scope.defaultDPI;\n        } else {\n          scale = unitConversion[theUnit][scope.defaultUnit];\n          if (scale < 0) {\n            scale = unitConversion[theUnit][\"in\"] * scope.defaultDPI;\n          }\n        }\n        return scale * parseFloat(string);\n      }\n      function getNodeTransform(node) {\n        if (!(node.hasAttribute(\"transform\") || node.nodeName === \"use\" && (node.hasAttribute(\"x\") || node.hasAttribute(\"y\")))) {\n          return null;\n        }\n        const transform = parseNodeTransform(node);\n        if (transformStack.length > 0) {\n          transform.premultiply(transformStack[transformStack.length - 1]);\n        }\n        currentTransform.copy(transform);\n        transformStack.push(transform);\n        return transform;\n      }\n      function parseNodeTransform(node) {\n        const transform = new Matrix3();\n        const currentTransform2 = tempTransform0;\n        if (node.nodeName === \"use\" && (node.hasAttribute(\"x\") || node.hasAttribute(\"y\"))) {\n          const tx = parseFloatWithUnits(node.getAttribute(\"x\"));\n          const ty = parseFloatWithUnits(node.getAttribute(\"y\"));\n          transform.translate(tx, ty);\n        }\n        if (node.hasAttribute(\"transform\")) {\n          const transformsTexts = node.getAttribute(\"transform\").split(\")\");\n          for (let tIndex = transformsTexts.length - 1; tIndex >= 0; tIndex--) {\n            const transformText = transformsTexts[tIndex].trim();\n            if (transformText === \"\") continue;\n            const openParPos = transformText.indexOf(\"(\");\n            const closeParPos = transformText.length;\n            if (openParPos > 0 && openParPos < closeParPos) {\n              const transformType = transformText.slice(0, openParPos);\n              const array = parseFloats(transformText.slice(openParPos + 1));\n              currentTransform2.identity();\n              switch (transformType) {\n                case \"translate\":\n                  if (array.length >= 1) {\n                    const tx = array[0];\n                    let ty = 0;\n                    if (array.length >= 2) {\n                      ty = array[1];\n                    }\n                    currentTransform2.translate(tx, ty);\n                  }\n                  break;\n                case \"rotate\":\n                  if (array.length >= 1) {\n                    let angle = 0;\n                    let cx = 0;\n                    let cy = 0;\n                    angle = array[0] * Math.PI / 180;\n                    if (array.length >= 3) {\n                      cx = array[1];\n                      cy = array[2];\n                    }\n                    tempTransform1.makeTranslation(-cx, -cy);\n                    tempTransform2.makeRotation(angle);\n                    tempTransform3.multiplyMatrices(tempTransform2, tempTransform1);\n                    tempTransform1.makeTranslation(cx, cy);\n                    currentTransform2.multiplyMatrices(tempTransform1, tempTransform3);\n                  }\n                  break;\n                case \"scale\":\n                  if (array.length >= 1) {\n                    const scaleX = array[0];\n                    let scaleY = scaleX;\n                    if (array.length >= 2) {\n                      scaleY = array[1];\n                    }\n                    currentTransform2.scale(scaleX, scaleY);\n                  }\n                  break;\n                case \"skewX\":\n                  if (array.length === 1) {\n                    currentTransform2.set(1, Math.tan(array[0] * Math.PI / 180), 0, 0, 1, 0, 0, 0, 1);\n                  }\n                  break;\n                case \"skewY\":\n                  if (array.length === 1) {\n                    currentTransform2.set(1, 0, 0, Math.tan(array[0] * Math.PI / 180), 1, 0, 0, 0, 1);\n                  }\n                  break;\n                case \"matrix\":\n                  if (array.length === 6) {\n                    currentTransform2.set(array[0], array[2], array[4], array[1], array[3], array[5], 0, 0, 1);\n                  }\n                  break;\n              }\n            }\n            transform.premultiply(currentTransform2);\n          }\n        }\n        return transform;\n      }\n      function transformPath(path, m) {\n        function transfVec2(v2) {\n          tempV3.set(v2.x, v2.y, 1).applyMatrix3(m);\n          v2.set(tempV3.x, tempV3.y);\n        }\n        function transfEllipseGeneric(curve) {\n          const a = curve.xRadius;\n          const b = curve.yRadius;\n          const cosTheta = Math.cos(curve.aRotation);\n          const sinTheta = Math.sin(curve.aRotation);\n          const v1 = new Vector3(a * cosTheta, a * sinTheta, 0);\n          const v2 = new Vector3(-b * sinTheta, b * cosTheta, 0);\n          const f1 = v1.applyMatrix3(m);\n          const f2 = v2.applyMatrix3(m);\n          const mF = tempTransform0.set(f1.x, f2.x, 0, f1.y, f2.y, 0, 0, 0, 1);\n          const mFInv = tempTransform1.copy(mF).invert();\n          const mFInvT = tempTransform2.copy(mFInv).transpose();\n          const mQ = mFInvT.multiply(mFInv);\n          const mQe = mQ.elements;\n          const ed = eigenDecomposition(mQe[0], mQe[1], mQe[4]);\n          const rt1sqrt = Math.sqrt(ed.rt1);\n          const rt2sqrt = Math.sqrt(ed.rt2);\n          curve.xRadius = 1 / rt1sqrt;\n          curve.yRadius = 1 / rt2sqrt;\n          curve.aRotation = Math.atan2(ed.sn, ed.cs);\n          const isFullEllipse = (curve.aEndAngle - curve.aStartAngle) % (2 * Math.PI) < Number.EPSILON;\n          if (!isFullEllipse) {\n            const mDsqrt = tempTransform1.set(rt1sqrt, 0, 0, 0, rt2sqrt, 0, 0, 0, 1);\n            const mRT = tempTransform2.set(ed.cs, ed.sn, 0, -ed.sn, ed.cs, 0, 0, 0, 1);\n            const mDRF = mDsqrt.multiply(mRT).multiply(mF);\n            const transformAngle = phi => {\n              const {\n                x: cosR,\n                y: sinR\n              } = new Vector3(Math.cos(phi), Math.sin(phi), 0).applyMatrix3(mDRF);\n              return Math.atan2(sinR, cosR);\n            };\n            curve.aStartAngle = transformAngle(curve.aStartAngle);\n            curve.aEndAngle = transformAngle(curve.aEndAngle);\n            if (isTransformFlipped(m)) {\n              curve.aClockwise = !curve.aClockwise;\n            }\n          }\n        }\n        function transfEllipseNoSkew(curve) {\n          const sx = getTransformScaleX(m);\n          const sy = getTransformScaleY(m);\n          curve.xRadius *= sx;\n          curve.yRadius *= sy;\n          const theta = sx > Number.EPSILON ? Math.atan2(m.elements[1], m.elements[0]) : Math.atan2(-m.elements[3], m.elements[4]);\n          curve.aRotation += theta;\n          if (isTransformFlipped(m)) {\n            curve.aStartAngle *= -1;\n            curve.aEndAngle *= -1;\n            curve.aClockwise = !curve.aClockwise;\n          }\n        }\n        const subPaths = path.subPaths;\n        for (let i = 0, n = subPaths.length; i < n; i++) {\n          const subPath = subPaths[i];\n          const curves = subPath.curves;\n          for (let j = 0; j < curves.length; j++) {\n            const curve = curves[j];\n            if (curve.isLineCurve) {\n              transfVec2(curve.v1);\n              transfVec2(curve.v2);\n            } else if (curve.isCubicBezierCurve) {\n              transfVec2(curve.v0);\n              transfVec2(curve.v1);\n              transfVec2(curve.v2);\n              transfVec2(curve.v3);\n            } else if (curve.isQuadraticBezierCurve) {\n              transfVec2(curve.v0);\n              transfVec2(curve.v1);\n              transfVec2(curve.v2);\n            } else if (curve.isEllipseCurve) {\n              tempV2.set(curve.aX, curve.aY);\n              transfVec2(tempV2);\n              curve.aX = tempV2.x;\n              curve.aY = tempV2.y;\n              if (isTransformSkewed(m)) {\n                transfEllipseGeneric(curve);\n              } else {\n                transfEllipseNoSkew(curve);\n              }\n            }\n          }\n        }\n      }\n      function isTransformFlipped(m) {\n        const te = m.elements;\n        return te[0] * te[4] - te[1] * te[3] < 0;\n      }\n      function isTransformSkewed(m) {\n        const te = m.elements;\n        const basisDot = te[0] * te[3] + te[1] * te[4];\n        if (basisDot === 0) return false;\n        const sx = getTransformScaleX(m);\n        const sy = getTransformScaleY(m);\n        return Math.abs(basisDot / (sx * sy)) > Number.EPSILON;\n      }\n      function getTransformScaleX(m) {\n        const te = m.elements;\n        return Math.sqrt(te[0] * te[0] + te[1] * te[1]);\n      }\n      function getTransformScaleY(m) {\n        const te = m.elements;\n        return Math.sqrt(te[3] * te[3] + te[4] * te[4]);\n      }\n      function eigenDecomposition(A, B, C) {\n        let rt1, rt2, cs, sn, t;\n        const sm = A + C;\n        const df = A - C;\n        const rt = Math.sqrt(df * df + 4 * B * B);\n        if (sm > 0) {\n          rt1 = 0.5 * (sm + rt);\n          t = 1 / rt1;\n          rt2 = A * t * C - B * t * B;\n        } else if (sm < 0) {\n          rt2 = 0.5 * (sm - rt);\n        } else {\n          rt1 = 0.5 * rt;\n          rt2 = -0.5 * rt;\n        }\n        if (df > 0) {\n          cs = df + rt;\n        } else {\n          cs = df - rt;\n        }\n        if (Math.abs(cs) > 2 * Math.abs(B)) {\n          t = -2 * B / cs;\n          sn = 1 / Math.sqrt(1 + t * t);\n          cs = t * sn;\n        } else if (Math.abs(B) === 0) {\n          cs = 1;\n          sn = 0;\n        } else {\n          t = -0.5 * cs / B;\n          cs = 1 / Math.sqrt(1 + t * t);\n          sn = t * cs;\n        }\n        if (df > 0) {\n          t = cs;\n          cs = -sn;\n          sn = t;\n        }\n        return {\n          rt1,\n          rt2,\n          cs,\n          sn\n        };\n      }\n      const paths = [];\n      const stylesheets = {};\n      const transformStack = [];\n      const tempTransform0 = new Matrix3();\n      const tempTransform1 = new Matrix3();\n      const tempTransform2 = new Matrix3();\n      const tempTransform3 = new Matrix3();\n      const tempV2 = new Vector2();\n      const tempV3 = new Vector3();\n      const currentTransform = new Matrix3();\n      const xml = new DOMParser().parseFromString(text, \"image/svg+xml\");\n      parseNode(xml.documentElement, {\n        fill: \"#000\",\n        fillOpacity: 1,\n        strokeOpacity: 1,\n        strokeWidth: 1,\n        strokeLineJoin: \"miter\",\n        strokeLineCap: \"butt\",\n        strokeMiterLimit: 4\n      });\n      const data = {\n        paths,\n        xml: xml.documentElement\n      };\n      return data;\n    }\n    static createShapes(shapePath) {\n      const BIGNUMBER = 999999999;\n      const IntersectionLocationType = {\n        ORIGIN: 0,\n        DESTINATION: 1,\n        BETWEEN: 2,\n        LEFT: 3,\n        RIGHT: 4,\n        BEHIND: 5,\n        BEYOND: 6\n      };\n      const classifyResult = {\n        loc: IntersectionLocationType.ORIGIN,\n        t: 0\n      };\n      function findEdgeIntersection(a0, a1, b0, b1) {\n        const x1 = a0.x;\n        const x2 = a1.x;\n        const x3 = b0.x;\n        const x4 = b1.x;\n        const y1 = a0.y;\n        const y2 = a1.y;\n        const y3 = b0.y;\n        const y4 = b1.y;\n        const nom1 = (x4 - x3) * (y1 - y3) - (y4 - y3) * (x1 - x3);\n        const nom2 = (x2 - x1) * (y1 - y3) - (y2 - y1) * (x1 - x3);\n        const denom = (y4 - y3) * (x2 - x1) - (x4 - x3) * (y2 - y1);\n        const t1 = nom1 / denom;\n        const t2 = nom2 / denom;\n        if (denom === 0 && nom1 !== 0 || t1 <= 0 || t1 >= 1 || t2 < 0 || t2 > 1) {\n          return null;\n        } else if (nom1 === 0 && denom === 0) {\n          for (let i = 0; i < 2; i++) {\n            classifyPoint(i === 0 ? b0 : b1, a0, a1);\n            if (classifyResult.loc == IntersectionLocationType.ORIGIN) {\n              const point = i === 0 ? b0 : b1;\n              return {\n                x: point.x,\n                y: point.y,\n                t: classifyResult.t\n              };\n            } else if (classifyResult.loc == IntersectionLocationType.BETWEEN) {\n              const x = +(x1 + classifyResult.t * (x2 - x1)).toPrecision(10);\n              const y = +(y1 + classifyResult.t * (y2 - y1)).toPrecision(10);\n              return {\n                x,\n                y,\n                t: classifyResult.t\n              };\n            }\n          }\n          return null;\n        } else {\n          for (let i = 0; i < 2; i++) {\n            classifyPoint(i === 0 ? b0 : b1, a0, a1);\n            if (classifyResult.loc == IntersectionLocationType.ORIGIN) {\n              const point = i === 0 ? b0 : b1;\n              return {\n                x: point.x,\n                y: point.y,\n                t: classifyResult.t\n              };\n            }\n          }\n          const x = +(x1 + t1 * (x2 - x1)).toPrecision(10);\n          const y = +(y1 + t1 * (y2 - y1)).toPrecision(10);\n          return {\n            x,\n            y,\n            t: t1\n          };\n        }\n      }\n      function classifyPoint(p, edgeStart, edgeEnd) {\n        const ax = edgeEnd.x - edgeStart.x;\n        const ay = edgeEnd.y - edgeStart.y;\n        const bx = p.x - edgeStart.x;\n        const by = p.y - edgeStart.y;\n        const sa = ax * by - bx * ay;\n        if (p.x === edgeStart.x && p.y === edgeStart.y) {\n          classifyResult.loc = IntersectionLocationType.ORIGIN;\n          classifyResult.t = 0;\n          return;\n        }\n        if (p.x === edgeEnd.x && p.y === edgeEnd.y) {\n          classifyResult.loc = IntersectionLocationType.DESTINATION;\n          classifyResult.t = 1;\n          return;\n        }\n        if (sa < -Number.EPSILON) {\n          classifyResult.loc = IntersectionLocationType.LEFT;\n          return;\n        }\n        if (sa > Number.EPSILON) {\n          classifyResult.loc = IntersectionLocationType.RIGHT;\n          return;\n        }\n        if (ax * bx < 0 || ay * by < 0) {\n          classifyResult.loc = IntersectionLocationType.BEHIND;\n          return;\n        }\n        if (Math.sqrt(ax * ax + ay * ay) < Math.sqrt(bx * bx + by * by)) {\n          classifyResult.loc = IntersectionLocationType.BEYOND;\n          return;\n        }\n        let t;\n        if (ax !== 0) {\n          t = bx / ax;\n        } else {\n          t = by / ay;\n        }\n        classifyResult.loc = IntersectionLocationType.BETWEEN;\n        classifyResult.t = t;\n      }\n      function getIntersections(path1, path2) {\n        const intersectionsRaw = [];\n        const intersections = [];\n        for (let index = 1; index < path1.length; index++) {\n          const path1EdgeStart = path1[index - 1];\n          const path1EdgeEnd = path1[index];\n          for (let index2 = 1; index2 < path2.length; index2++) {\n            const path2EdgeStart = path2[index2 - 1];\n            const path2EdgeEnd = path2[index2];\n            const intersection = findEdgeIntersection(path1EdgeStart, path1EdgeEnd, path2EdgeStart, path2EdgeEnd);\n            if (intersection !== null && intersectionsRaw.find(i => i.t <= intersection.t + Number.EPSILON && i.t >= intersection.t - Number.EPSILON) === void 0) {\n              intersectionsRaw.push(intersection);\n              intersections.push(new Vector2(intersection.x, intersection.y));\n            }\n          }\n        }\n        return intersections;\n      }\n      function getScanlineIntersections(scanline, boundingBox, paths) {\n        const center = new Vector2();\n        boundingBox.getCenter(center);\n        const allIntersections = [];\n        paths.forEach(path => {\n          if (path.boundingBox.containsPoint(center)) {\n            const intersections = getIntersections(scanline, path.points);\n            intersections.forEach(p => {\n              allIntersections.push({\n                identifier: path.identifier,\n                isCW: path.isCW,\n                point: p\n              });\n            });\n          }\n        });\n        allIntersections.sort((i1, i2) => {\n          return i1.point.x - i2.point.x;\n        });\n        return allIntersections;\n      }\n      function isHoleTo(simplePath, allPaths, scanlineMinX2, scanlineMaxX2, _fillRule) {\n        if (_fillRule === null || _fillRule === void 0 || _fillRule === \"\") {\n          _fillRule = \"nonzero\";\n        }\n        const centerBoundingBox = new Vector2();\n        simplePath.boundingBox.getCenter(centerBoundingBox);\n        const scanline = [new Vector2(scanlineMinX2, centerBoundingBox.y), new Vector2(scanlineMaxX2, centerBoundingBox.y)];\n        const scanlineIntersections = getScanlineIntersections(scanline, simplePath.boundingBox, allPaths);\n        scanlineIntersections.sort((i1, i2) => {\n          return i1.point.x - i2.point.x;\n        });\n        const baseIntersections = [];\n        const otherIntersections = [];\n        scanlineIntersections.forEach(i2 => {\n          if (i2.identifier === simplePath.identifier) {\n            baseIntersections.push(i2);\n          } else {\n            otherIntersections.push(i2);\n          }\n        });\n        const firstXOfPath = baseIntersections[0].point.x;\n        const stack = [];\n        let i = 0;\n        while (i < otherIntersections.length && otherIntersections[i].point.x < firstXOfPath) {\n          if (stack.length > 0 && stack[stack.length - 1] === otherIntersections[i].identifier) {\n            stack.pop();\n          } else {\n            stack.push(otherIntersections[i].identifier);\n          }\n          i++;\n        }\n        stack.push(simplePath.identifier);\n        if (_fillRule === \"evenodd\") {\n          const isHole = stack.length % 2 === 0 ? true : false;\n          const isHoleFor = stack[stack.length - 2];\n          return {\n            identifier: simplePath.identifier,\n            isHole,\n            for: isHoleFor\n          };\n        } else if (_fillRule === \"nonzero\") {\n          let isHole = true;\n          let isHoleFor = null;\n          let lastCWValue = null;\n          for (let i2 = 0; i2 < stack.length; i2++) {\n            const identifier = stack[i2];\n            if (isHole) {\n              lastCWValue = allPaths[identifier].isCW;\n              isHole = false;\n              isHoleFor = identifier;\n            } else if (lastCWValue !== allPaths[identifier].isCW) {\n              lastCWValue = allPaths[identifier].isCW;\n              isHole = true;\n            }\n          }\n          return {\n            identifier: simplePath.identifier,\n            isHole,\n            for: isHoleFor\n          };\n        } else {\n          console.warn('fill-rule: \"' + _fillRule + '\" is currently not implemented.');\n        }\n      }\n      let scanlineMinX = BIGNUMBER;\n      let scanlineMaxX = -BIGNUMBER;\n      let simplePaths = shapePath.subPaths.map(p => {\n        const points = p.getPoints();\n        let maxY = -BIGNUMBER;\n        let minY = BIGNUMBER;\n        let maxX = -BIGNUMBER;\n        let minX = BIGNUMBER;\n        for (let i = 0; i < points.length; i++) {\n          const p2 = points[i];\n          if (p2.y > maxY) {\n            maxY = p2.y;\n          }\n          if (p2.y < minY) {\n            minY = p2.y;\n          }\n          if (p2.x > maxX) {\n            maxX = p2.x;\n          }\n          if (p2.x < minX) {\n            minX = p2.x;\n          }\n        }\n        if (scanlineMaxX <= maxX) {\n          scanlineMaxX = maxX + 1;\n        }\n        if (scanlineMinX >= minX) {\n          scanlineMinX = minX - 1;\n        }\n        return {\n          curves: p.curves,\n          points,\n          isCW: ShapeUtils.isClockWise(points),\n          identifier: -1,\n          boundingBox: new Box2(new Vector2(minX, minY), new Vector2(maxX, maxY))\n        };\n      });\n      simplePaths = simplePaths.filter(sp => sp.points.length > 1);\n      for (let identifier = 0; identifier < simplePaths.length; identifier++) {\n        simplePaths[identifier].identifier = identifier;\n      }\n      const isAHole = simplePaths.map(p => isHoleTo(p, simplePaths, scanlineMinX, scanlineMaxX, shapePath.userData ? shapePath.userData.style.fillRule : void 0));\n      const shapesToReturn = [];\n      simplePaths.forEach(p => {\n        const amIAHole = isAHole[p.identifier];\n        if (!amIAHole.isHole) {\n          const shape = new Shape();\n          shape.curves = p.curves;\n          const holes = isAHole.filter(h => h.isHole && h.for === p.identifier);\n          holes.forEach(h => {\n            const hole = simplePaths[h.identifier];\n            const path = new Path();\n            path.curves = hole.curves;\n            shape.holes.push(path);\n          });\n          shapesToReturn.push(shape);\n        }\n      });\n      return shapesToReturn;\n    }\n    static getStrokeStyle(width, color, lineJoin, lineCap, miterLimit) {\n      width = width !== void 0 ? width : 1;\n      color = color !== void 0 ? color : \"#000\";\n      lineJoin = lineJoin !== void 0 ? lineJoin : \"miter\";\n      lineCap = lineCap !== void 0 ? lineCap : \"butt\";\n      miterLimit = miterLimit !== void 0 ? miterLimit : 4;\n      return {\n        strokeColor: color,\n        strokeWidth: width,\n        strokeLineJoin: lineJoin,\n        strokeLineCap: lineCap,\n        strokeMiterLimit: miterLimit\n      };\n    }\n    static pointsToStroke(points, style, arcDivisions, minDistance) {\n      const vertices = [];\n      const normals = [];\n      const uvs = [];\n      if (SVGLoader2.pointsToStrokeWithBuffers(points, style, arcDivisions, minDistance, vertices, normals, uvs) === 0) {\n        return null;\n      }\n      const geometry = new BufferGeometry();\n      geometry.setAttribute(\"position\", new Float32BufferAttribute(vertices, 3));\n      geometry.setAttribute(\"normal\", new Float32BufferAttribute(normals, 3));\n      geometry.setAttribute(\"uv\", new Float32BufferAttribute(uvs, 2));\n      return geometry;\n    }\n    static pointsToStrokeWithBuffers(points, style, arcDivisions, minDistance, vertices, normals, uvs, vertexOffset) {\n      const tempV2_1 = new Vector2();\n      const tempV2_2 = new Vector2();\n      const tempV2_3 = new Vector2();\n      const tempV2_4 = new Vector2();\n      const tempV2_5 = new Vector2();\n      const tempV2_6 = new Vector2();\n      const tempV2_7 = new Vector2();\n      const lastPointL = new Vector2();\n      const lastPointR = new Vector2();\n      const point0L = new Vector2();\n      const point0R = new Vector2();\n      const currentPointL = new Vector2();\n      const currentPointR = new Vector2();\n      const nextPointL = new Vector2();\n      const nextPointR = new Vector2();\n      const innerPoint = new Vector2();\n      const outerPoint = new Vector2();\n      arcDivisions = arcDivisions !== void 0 ? arcDivisions : 12;\n      minDistance = minDistance !== void 0 ? minDistance : 1e-3;\n      vertexOffset = vertexOffset !== void 0 ? vertexOffset : 0;\n      points = removeDuplicatedPoints(points);\n      const numPoints = points.length;\n      if (numPoints < 2) return 0;\n      const isClosed = points[0].equals(points[numPoints - 1]);\n      let currentPoint;\n      let previousPoint = points[0];\n      let nextPoint;\n      const strokeWidth2 = style.strokeWidth / 2;\n      const deltaU = 1 / (numPoints - 1);\n      let u0 = 0,\n        u1;\n      let innerSideModified;\n      let joinIsOnLeftSide;\n      let isMiter;\n      let initialJoinIsOnLeftSide = false;\n      let numVertices = 0;\n      let currentCoordinate = vertexOffset * 3;\n      let currentCoordinateUV = vertexOffset * 2;\n      getNormal(points[0], points[1], tempV2_1).multiplyScalar(strokeWidth2);\n      lastPointL.copy(points[0]).sub(tempV2_1);\n      lastPointR.copy(points[0]).add(tempV2_1);\n      point0L.copy(lastPointL);\n      point0R.copy(lastPointR);\n      for (let iPoint = 1; iPoint < numPoints; iPoint++) {\n        currentPoint = points[iPoint];\n        if (iPoint === numPoints - 1) {\n          if (isClosed) {\n            nextPoint = points[1];\n          } else nextPoint = void 0;\n        } else {\n          nextPoint = points[iPoint + 1];\n        }\n        const normal1 = tempV2_1;\n        getNormal(previousPoint, currentPoint, normal1);\n        tempV2_3.copy(normal1).multiplyScalar(strokeWidth2);\n        currentPointL.copy(currentPoint).sub(tempV2_3);\n        currentPointR.copy(currentPoint).add(tempV2_3);\n        u1 = u0 + deltaU;\n        innerSideModified = false;\n        if (nextPoint !== void 0) {\n          getNormal(currentPoint, nextPoint, tempV2_2);\n          tempV2_3.copy(tempV2_2).multiplyScalar(strokeWidth2);\n          nextPointL.copy(currentPoint).sub(tempV2_3);\n          nextPointR.copy(currentPoint).add(tempV2_3);\n          joinIsOnLeftSide = true;\n          tempV2_3.subVectors(nextPoint, previousPoint);\n          if (normal1.dot(tempV2_3) < 0) {\n            joinIsOnLeftSide = false;\n          }\n          if (iPoint === 1) initialJoinIsOnLeftSide = joinIsOnLeftSide;\n          tempV2_3.subVectors(nextPoint, currentPoint);\n          tempV2_3.normalize();\n          const dot = Math.abs(normal1.dot(tempV2_3));\n          if (dot > Number.EPSILON) {\n            const miterSide = strokeWidth2 / dot;\n            tempV2_3.multiplyScalar(-miterSide);\n            tempV2_4.subVectors(currentPoint, previousPoint);\n            tempV2_5.copy(tempV2_4).setLength(miterSide).add(tempV2_3);\n            innerPoint.copy(tempV2_5).negate();\n            const miterLength2 = tempV2_5.length();\n            const segmentLengthPrev = tempV2_4.length();\n            tempV2_4.divideScalar(segmentLengthPrev);\n            tempV2_6.subVectors(nextPoint, currentPoint);\n            const segmentLengthNext = tempV2_6.length();\n            tempV2_6.divideScalar(segmentLengthNext);\n            if (tempV2_4.dot(innerPoint) < segmentLengthPrev && tempV2_6.dot(innerPoint) < segmentLengthNext) {\n              innerSideModified = true;\n            }\n            outerPoint.copy(tempV2_5).add(currentPoint);\n            innerPoint.add(currentPoint);\n            isMiter = false;\n            if (innerSideModified) {\n              if (joinIsOnLeftSide) {\n                nextPointR.copy(innerPoint);\n                currentPointR.copy(innerPoint);\n              } else {\n                nextPointL.copy(innerPoint);\n                currentPointL.copy(innerPoint);\n              }\n            } else {\n              makeSegmentTriangles();\n            }\n            switch (style.strokeLineJoin) {\n              case \"bevel\":\n                makeSegmentWithBevelJoin(joinIsOnLeftSide, innerSideModified, u1);\n                break;\n              case \"round\":\n                createSegmentTrianglesWithMiddleSection(joinIsOnLeftSide, innerSideModified);\n                if (joinIsOnLeftSide) {\n                  makeCircularSector(currentPoint, currentPointL, nextPointL, u1, 0);\n                } else {\n                  makeCircularSector(currentPoint, nextPointR, currentPointR, u1, 1);\n                }\n                break;\n              case \"miter\":\n              case \"miter-clip\":\n              default:\n                const miterFraction = strokeWidth2 * style.strokeMiterLimit / miterLength2;\n                if (miterFraction < 1) {\n                  if (style.strokeLineJoin !== \"miter-clip\") {\n                    makeSegmentWithBevelJoin(joinIsOnLeftSide, innerSideModified, u1);\n                    break;\n                  } else {\n                    createSegmentTrianglesWithMiddleSection(joinIsOnLeftSide, innerSideModified);\n                    if (joinIsOnLeftSide) {\n                      tempV2_6.subVectors(outerPoint, currentPointL).multiplyScalar(miterFraction).add(currentPointL);\n                      tempV2_7.subVectors(outerPoint, nextPointL).multiplyScalar(miterFraction).add(nextPointL);\n                      addVertex(currentPointL, u1, 0);\n                      addVertex(tempV2_6, u1, 0);\n                      addVertex(currentPoint, u1, 0.5);\n                      addVertex(currentPoint, u1, 0.5);\n                      addVertex(tempV2_6, u1, 0);\n                      addVertex(tempV2_7, u1, 0);\n                      addVertex(currentPoint, u1, 0.5);\n                      addVertex(tempV2_7, u1, 0);\n                      addVertex(nextPointL, u1, 0);\n                    } else {\n                      tempV2_6.subVectors(outerPoint, currentPointR).multiplyScalar(miterFraction).add(currentPointR);\n                      tempV2_7.subVectors(outerPoint, nextPointR).multiplyScalar(miterFraction).add(nextPointR);\n                      addVertex(currentPointR, u1, 1);\n                      addVertex(tempV2_6, u1, 1);\n                      addVertex(currentPoint, u1, 0.5);\n                      addVertex(currentPoint, u1, 0.5);\n                      addVertex(tempV2_6, u1, 1);\n                      addVertex(tempV2_7, u1, 1);\n                      addVertex(currentPoint, u1, 0.5);\n                      addVertex(tempV2_7, u1, 1);\n                      addVertex(nextPointR, u1, 1);\n                    }\n                  }\n                } else {\n                  if (innerSideModified) {\n                    if (joinIsOnLeftSide) {\n                      addVertex(lastPointR, u0, 1);\n                      addVertex(lastPointL, u0, 0);\n                      addVertex(outerPoint, u1, 0);\n                      addVertex(lastPointR, u0, 1);\n                      addVertex(outerPoint, u1, 0);\n                      addVertex(innerPoint, u1, 1);\n                    } else {\n                      addVertex(lastPointR, u0, 1);\n                      addVertex(lastPointL, u0, 0);\n                      addVertex(outerPoint, u1, 1);\n                      addVertex(lastPointL, u0, 0);\n                      addVertex(innerPoint, u1, 0);\n                      addVertex(outerPoint, u1, 1);\n                    }\n                    if (joinIsOnLeftSide) {\n                      nextPointL.copy(outerPoint);\n                    } else {\n                      nextPointR.copy(outerPoint);\n                    }\n                  } else {\n                    if (joinIsOnLeftSide) {\n                      addVertex(currentPointL, u1, 0);\n                      addVertex(outerPoint, u1, 0);\n                      addVertex(currentPoint, u1, 0.5);\n                      addVertex(currentPoint, u1, 0.5);\n                      addVertex(outerPoint, u1, 0);\n                      addVertex(nextPointL, u1, 0);\n                    } else {\n                      addVertex(currentPointR, u1, 1);\n                      addVertex(outerPoint, u1, 1);\n                      addVertex(currentPoint, u1, 0.5);\n                      addVertex(currentPoint, u1, 0.5);\n                      addVertex(outerPoint, u1, 1);\n                      addVertex(nextPointR, u1, 1);\n                    }\n                  }\n                  isMiter = true;\n                }\n                break;\n            }\n          } else {\n            makeSegmentTriangles();\n          }\n        } else {\n          makeSegmentTriangles();\n        }\n        if (!isClosed && iPoint === numPoints - 1) {\n          addCapGeometry(points[0], point0L, point0R, joinIsOnLeftSide, true, u0);\n        }\n        u0 = u1;\n        previousPoint = currentPoint;\n        lastPointL.copy(nextPointL);\n        lastPointR.copy(nextPointR);\n      }\n      if (!isClosed) {\n        addCapGeometry(currentPoint, currentPointL, currentPointR, joinIsOnLeftSide, false, u1);\n      } else if (innerSideModified && vertices) {\n        let lastOuter = outerPoint;\n        let lastInner = innerPoint;\n        if (initialJoinIsOnLeftSide !== joinIsOnLeftSide) {\n          lastOuter = innerPoint;\n          lastInner = outerPoint;\n        }\n        if (joinIsOnLeftSide) {\n          if (isMiter || initialJoinIsOnLeftSide) {\n            lastInner.toArray(vertices, 0 * 3);\n            lastInner.toArray(vertices, 3 * 3);\n            if (isMiter) {\n              lastOuter.toArray(vertices, 1 * 3);\n            }\n          }\n        } else {\n          if (isMiter || !initialJoinIsOnLeftSide) {\n            lastInner.toArray(vertices, 1 * 3);\n            lastInner.toArray(vertices, 3 * 3);\n            if (isMiter) {\n              lastOuter.toArray(vertices, 0 * 3);\n            }\n          }\n        }\n      }\n      return numVertices;\n      function getNormal(p1, p2, result) {\n        result.subVectors(p2, p1);\n        return result.set(-result.y, result.x).normalize();\n      }\n      function addVertex(position, u, v) {\n        if (vertices) {\n          vertices[currentCoordinate] = position.x;\n          vertices[currentCoordinate + 1] = position.y;\n          vertices[currentCoordinate + 2] = 0;\n          if (normals) {\n            normals[currentCoordinate] = 0;\n            normals[currentCoordinate + 1] = 0;\n            normals[currentCoordinate + 2] = 1;\n          }\n          currentCoordinate += 3;\n          if (uvs) {\n            uvs[currentCoordinateUV] = u;\n            uvs[currentCoordinateUV + 1] = v;\n            currentCoordinateUV += 2;\n          }\n        }\n        numVertices += 3;\n      }\n      function makeCircularSector(center, p1, p2, u, v) {\n        tempV2_1.copy(p1).sub(center).normalize();\n        tempV2_2.copy(p2).sub(center).normalize();\n        let angle = Math.PI;\n        const dot = tempV2_1.dot(tempV2_2);\n        if (Math.abs(dot) < 1) angle = Math.abs(Math.acos(dot));\n        angle /= arcDivisions;\n        tempV2_3.copy(p1);\n        for (let i = 0, il = arcDivisions - 1; i < il; i++) {\n          tempV2_4.copy(tempV2_3).rotateAround(center, angle);\n          addVertex(tempV2_3, u, v);\n          addVertex(tempV2_4, u, v);\n          addVertex(center, u, 0.5);\n          tempV2_3.copy(tempV2_4);\n        }\n        addVertex(tempV2_4, u, v);\n        addVertex(p2, u, v);\n        addVertex(center, u, 0.5);\n      }\n      function makeSegmentTriangles() {\n        addVertex(lastPointR, u0, 1);\n        addVertex(lastPointL, u0, 0);\n        addVertex(currentPointL, u1, 0);\n        addVertex(lastPointR, u0, 1);\n        addVertex(currentPointL, u1, 0);\n        addVertex(currentPointR, u1, 1);\n      }\n      function makeSegmentWithBevelJoin(joinIsOnLeftSide2, innerSideModified2, u) {\n        if (innerSideModified2) {\n          if (joinIsOnLeftSide2) {\n            addVertex(lastPointR, u0, 1);\n            addVertex(lastPointL, u0, 0);\n            addVertex(currentPointL, u1, 0);\n            addVertex(lastPointR, u0, 1);\n            addVertex(currentPointL, u1, 0);\n            addVertex(innerPoint, u1, 1);\n            addVertex(currentPointL, u, 0);\n            addVertex(nextPointL, u, 0);\n            addVertex(innerPoint, u, 0.5);\n          } else {\n            addVertex(lastPointR, u0, 1);\n            addVertex(lastPointL, u0, 0);\n            addVertex(currentPointR, u1, 1);\n            addVertex(lastPointL, u0, 0);\n            addVertex(innerPoint, u1, 0);\n            addVertex(currentPointR, u1, 1);\n            addVertex(currentPointR, u, 1);\n            addVertex(innerPoint, u, 0);\n            addVertex(nextPointR, u, 1);\n          }\n        } else {\n          if (joinIsOnLeftSide2) {\n            addVertex(currentPointL, u, 0);\n            addVertex(nextPointL, u, 0);\n            addVertex(currentPoint, u, 0.5);\n          } else {\n            addVertex(currentPointR, u, 1);\n            addVertex(nextPointR, u, 0);\n            addVertex(currentPoint, u, 0.5);\n          }\n        }\n      }\n      function createSegmentTrianglesWithMiddleSection(joinIsOnLeftSide2, innerSideModified2) {\n        if (innerSideModified2) {\n          if (joinIsOnLeftSide2) {\n            addVertex(lastPointR, u0, 1);\n            addVertex(lastPointL, u0, 0);\n            addVertex(currentPointL, u1, 0);\n            addVertex(lastPointR, u0, 1);\n            addVertex(currentPointL, u1, 0);\n            addVertex(innerPoint, u1, 1);\n            addVertex(currentPointL, u0, 0);\n            addVertex(currentPoint, u1, 0.5);\n            addVertex(innerPoint, u1, 1);\n            addVertex(currentPoint, u1, 0.5);\n            addVertex(nextPointL, u0, 0);\n            addVertex(innerPoint, u1, 1);\n          } else {\n            addVertex(lastPointR, u0, 1);\n            addVertex(lastPointL, u0, 0);\n            addVertex(currentPointR, u1, 1);\n            addVertex(lastPointL, u0, 0);\n            addVertex(innerPoint, u1, 0);\n            addVertex(currentPointR, u1, 1);\n            addVertex(currentPointR, u0, 1);\n            addVertex(innerPoint, u1, 0);\n            addVertex(currentPoint, u1, 0.5);\n            addVertex(currentPoint, u1, 0.5);\n            addVertex(innerPoint, u1, 0);\n            addVertex(nextPointR, u0, 1);\n          }\n        }\n      }\n      function addCapGeometry(center, p1, p2, joinIsOnLeftSide2, start, u) {\n        switch (style.strokeLineCap) {\n          case \"round\":\n            if (start) {\n              makeCircularSector(center, p2, p1, u, 0.5);\n            } else {\n              makeCircularSector(center, p1, p2, u, 0.5);\n            }\n            break;\n          case \"square\":\n            if (start) {\n              tempV2_1.subVectors(p1, center);\n              tempV2_2.set(tempV2_1.y, -tempV2_1.x);\n              tempV2_3.addVectors(tempV2_1, tempV2_2).add(center);\n              tempV2_4.subVectors(tempV2_2, tempV2_1).add(center);\n              if (joinIsOnLeftSide2) {\n                tempV2_3.toArray(vertices, 1 * 3);\n                tempV2_4.toArray(vertices, 0 * 3);\n                tempV2_4.toArray(vertices, 3 * 3);\n              } else {\n                tempV2_3.toArray(vertices, 1 * 3);\n                uvs[3 * 2 + 1] === 1 ? tempV2_4.toArray(vertices, 3 * 3) : tempV2_3.toArray(vertices, 3 * 3);\n                tempV2_4.toArray(vertices, 0 * 3);\n              }\n            } else {\n              tempV2_1.subVectors(p2, center);\n              tempV2_2.set(tempV2_1.y, -tempV2_1.x);\n              tempV2_3.addVectors(tempV2_1, tempV2_2).add(center);\n              tempV2_4.subVectors(tempV2_2, tempV2_1).add(center);\n              const vl = vertices.length;\n              if (joinIsOnLeftSide2) {\n                tempV2_3.toArray(vertices, vl - 1 * 3);\n                tempV2_4.toArray(vertices, vl - 2 * 3);\n                tempV2_4.toArray(vertices, vl - 4 * 3);\n              } else {\n                tempV2_4.toArray(vertices, vl - 2 * 3);\n                tempV2_3.toArray(vertices, vl - 1 * 3);\n                tempV2_4.toArray(vertices, vl - 4 * 3);\n              }\n            }\n            break;\n        }\n      }\n      function removeDuplicatedPoints(points2) {\n        let dupPoints = false;\n        for (let i = 1, n = points2.length - 1; i < n; i++) {\n          if (points2[i].distanceTo(points2[i + 1]) < minDistance) {\n            dupPoints = true;\n            break;\n          }\n        }\n        if (!dupPoints) return points2;\n        const newPoints = [];\n        newPoints.push(points2[0]);\n        for (let i = 1, n = points2.length - 1; i < n; i++) {\n          if (points2[i].distanceTo(points2[i + 1]) >= minDistance) {\n            newPoints.push(points2[i]);\n          }\n        }\n        newPoints.push(points2[points2.length - 1]);\n        return newPoints;\n      }\n    }\n  }\n  return SVGLoader2;\n})();\nexport { SVGLoader };", "map": {"version": 3, "names": ["COLOR_SPACE_SVG", "SVGLoader", "SVGLoader2", "Loader", "constructor", "manager", "defaultDPI", "defaultUnit", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "text", "parse", "e", "console", "error", "itemError", "parseNode", "node", "style", "nodeType", "transform", "getNodeTransform", "isDefsNode", "nodeName", "parseStyle", "parseCSSStylesheet", "hasAttribute", "parsePathNode", "parseRectNode", "parsePolygonNode", "parsePolylineNode", "parseCircleNode", "parseEllipseNode", "parseLineNode", "href", "getAttributeNS", "usedNodeId", "substring", "usedNode", "viewportElement", "getElementById", "warn", "fill", "color", "setStyle", "transformPath", "currentTransform", "paths", "push", "userData", "childNodes", "i", "length", "node2", "transformStack", "pop", "copy", "identity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "point", "Vector2", "control", "firstPoint", "isFirstPoint", "doSetFirstPoint", "d", "getAttribute", "commands", "match", "l", "command", "type", "char<PERSON>t", "data2", "slice", "trim", "numbers", "parseFloats", "j", "jl", "x", "y", "moveTo", "lineTo", "bezierCurveTo", "getReflection", "quadraticCurveTo", "rx", "ry", "start", "clone", "parseArcCommand", "currentPath", "autoClose", "curves", "currentPoint", "sheet", "cssRules", "stylesheet", "selectorList", "selectorText", "split", "filter", "Boolean", "map", "i2", "definitions", "Object", "fromEntries", "entries", "v", "stylesheets", "assign", "x_axis_rotation", "large_arc_flag", "sweep_flag", "end", "Math", "PI", "abs", "dx2", "dy2", "x1p", "cos", "sin", "y1p", "rxs", "rys", "x1ps", "y1ps", "cr", "s", "sqrt", "dq", "pq", "q", "max", "cxp", "cyp", "cx", "cy", "theta", "svgAngle", "delta", "absellipse", "ux", "uy", "vx", "vy", "dot", "len", "ang", "acos", "min", "parseFloatWithUnits", "w", "h", "bci", "iterator", "a", "b", "index", "regex", "replace", "r", "subpath", "Path", "absarc", "subPaths", "x1", "y1", "x2", "y2", "stylesheetStyles", "classSelectors", "addStyle", "svgName", "jsName", "adjustFunction", "startsWith", "clamp", "positive", "input", "flags", "stride", "TypeError", "RE", "SEPARATOR", "WHITESPACE", "DIGIT", "SIGN", "POINT", "COMMA", "EXP", "FLAGS", "SEP", "INT", "FLOAT", "state", "<PERSON><PERSON><PERSON><PERSON>", "number", "exponent", "result", "throwSyntaxError", "current2", "partial", "SyntaxError", "newNumber", "Number", "pow", "current", "Array", "isArray", "includes", "test", "units", "unitConversion", "mm", "cm", "in", "pt", "pc", "px", "string", "theUnit", "String", "n", "u", "endsWith", "scale", "parseFloat", "parseNodeTransform", "premultiply", "Matrix3", "currentTransform2", "tempTransform0", "tx", "ty", "translate", "transformsTexts", "tIndex", "transformText", "openParPos", "indexOf", "closeParPos", "transformType", "array", "angle", "tempTransform1", "makeTranslation", "tempTransform2", "makeRotation", "tempTransform3", "multiplyMatrices", "scaleX", "scaleY", "set", "tan", "m", "transfVec2", "v2", "tempV3", "applyMatrix3", "transfEllipseGeneric", "curve", "xRadius", "yRadius", "cosTheta", "aRotation", "sinTheta", "v1", "Vector3", "f1", "f2", "mF", "mFInv", "invert", "mFInvT", "transpose", "mQ", "multiply", "mQe", "elements", "ed", "eigenDecomposition", "rt1sqrt", "rt1", "rt2sqrt", "rt2", "atan2", "sn", "cs", "is<PERSON>ull<PERSON>llipse", "aEndAngle", "aStartAngle", "EPSILON", "mDsqrt", "mRT", "mDRF", "transformAngle", "phi", "cosR", "sinR", "isTransformFlipped", "aClockwise", "transfEllipseNoSkew", "sx", "getTransformScaleX", "sy", "getTransformScaleY", "subPath", "isLineCurve", "isCubicBezierCurve", "v0", "v3", "isQuadraticBezierCurve", "isEllipseCurve", "tempV2", "aX", "aY", "isTransformSkewed", "te", "basisDot", "A", "B", "C", "t", "sm", "df", "rt", "xml", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "documentElement", "fillOpacity", "strokeOpacity", "strokeWidth", "stroke<PERSON><PERSON><PERSON><PERSON>n", "strokeLineCap", "strokeMiterLimit", "data", "createShapes", "shapePath", "BIGNUMBER", "IntersectionLocationType", "ORIGIN", "DESTINATION", "BETWEEN", "LEFT", "RIGHT", "BEHIND", "BEYOND", "classifyResult", "loc", "findEdgeIntersection", "a0", "a1", "b0", "b1", "x3", "x4", "y3", "y4", "nom1", "nom2", "denom", "t1", "t2", "classifyPoint", "toPrecision", "p", "edgeStart", "edgeEnd", "ax", "ay", "bx", "by", "sa", "getIntersections", "path1", "path2", "intersectionsRaw", "intersections", "path1EdgeStart", "path1EdgeEnd", "index2", "path2EdgeStart", "path2EdgeEnd", "intersection", "find", "getScanlineIntersections", "scanline", "boundingBox", "center", "getCenter", "allIntersections", "for<PERSON>ach", "containsPoint", "points", "identifier", "isCW", "sort", "i1", "isHoleTo", "simplePath", "allPaths", "scanlineMinX2", "scanlineMaxX2", "_fillRule", "centerBoundingBox", "scanlineIntersections", "baseIntersections", "otherIntersections", "firstXOfPath", "stack", "isHole", "isHoleFor", "for", "lastCWValue", "scanlineMinX", "scanlineMaxX", "simplePaths", "getPoints", "maxY", "minY", "maxX", "minX", "p2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isClockWise", "Box2", "sp", "isAHole", "fillRule", "shapesToReturn", "amIAHole", "shape", "<PERSON><PERSON><PERSON>", "holes", "hole", "getStrokeStyle", "width", "lineJoin", "lineCap", "miterLimit", "strokeColor", "pointsToStroke", "arcDivisions", "minDistance", "vertices", "normals", "uvs", "pointsToStrokeWithBuffers", "geometry", "BufferGeometry", "setAttribute", "Float32BufferAttribute", "vertexOffset", "tempV2_1", "tempV2_2", "tempV2_3", "tempV2_4", "tempV2_5", "tempV2_6", "tempV2_7", "lastPointL", "lastPointR", "point0L", "point0R", "currentPointL", "currentPointR", "nextPointL", "nextPointR", "innerPoint", "outerPoint", "removeDuplicatedPoints", "numPoints", "isClosed", "equals", "previousPoint", "nextPoint", "strokeWidth2", "deltaU", "u0", "u1", "innerSideModified", "joinIsOnLeftSide", "isMiter", "initialJoinIsOnLeftSide", "numVertices", "currentCoordinate", "currentCoordinateUV", "getNormal", "multiplyScalar", "sub", "add", "iPoint", "normal1", "subVectors", "normalize", "miterSide", "<PERSON><PERSON><PERSON><PERSON>", "negate", "miterLength2", "segmentLengthPrev", "divideScalar", "segmentLengthNext", "makeSegmentTriangles", "makeSegmentWithBevelJoin", "createSegmentTrianglesWithMiddleSection", "makeCircularSector", "miterFraction", "addVertex", "addCapGeometry", "lastOuter", "lastInner", "toArray", "p1", "position", "il", "rotateAround", "joinIsOnLeftSide2", "innerSideModified2", "addVectors", "vl", "points2", "dupPoints", "distanceTo", "newPoints"], "sources": ["F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\node_modules\\src\\loaders\\SVGLoader.js"], "sourcesContent": ["import {\n  Box2,\n  <PERSON>ufferG<PERSON><PERSON>,\n  FileLoader,\n  Float32BufferAttribute,\n  Loader,\n  Matrix3,\n  Path,\n  Shape,\n  <PERSON>hapePath,\n  <PERSON>hape<PERSON><PERSON>s,\n  Vector2,\n  Vector3,\n} from 'three'\n\nconst COLOR_SPACE_SVG = 'srgb'\n\nconst SVGLoader = /* @__PURE__ */ (() => {\n  class SVGLoader extends Loader {\n    constructor(manager) {\n      super(manager)\n\n      // Default dots per inch\n      this.defaultDPI = 90\n\n      // Accepted units: 'mm', 'cm', 'in', 'pt', 'pc', 'px'\n      this.defaultUnit = 'px'\n    }\n\n    load(url, onLoad, onProgress, onError) {\n      const scope = this\n\n      const loader = new FileLoader(scope.manager)\n      loader.setPath(scope.path)\n      loader.setRequestHeader(scope.requestHeader)\n      loader.setWithCredentials(scope.withCredentials)\n      loader.load(\n        url,\n        function (text) {\n          try {\n            onLoad(scope.parse(text))\n          } catch (e) {\n            if (onError) {\n              onError(e)\n            } else {\n              console.error(e)\n            }\n\n            scope.manager.itemError(url)\n          }\n        },\n        onProgress,\n        onError,\n      )\n    }\n\n    parse(text) {\n      const scope = this\n\n      function parseNode(node, style) {\n        if (node.nodeType !== 1) return\n\n        const transform = getNodeTransform(node)\n\n        let isDefsNode = false\n\n        let path = null\n\n        switch (node.nodeName) {\n          case 'svg':\n            style = parseStyle(node, style)\n            break\n\n          case 'style':\n            parseCSSStylesheet(node)\n            break\n\n          case 'g':\n            style = parseStyle(node, style)\n            break\n\n          case 'path':\n            style = parseStyle(node, style)\n            if (node.hasAttribute('d')) path = parsePathNode(node)\n            break\n\n          case 'rect':\n            style = parseStyle(node, style)\n            path = parseRectNode(node)\n            break\n\n          case 'polygon':\n            style = parseStyle(node, style)\n            path = parsePolygonNode(node)\n            break\n\n          case 'polyline':\n            style = parseStyle(node, style)\n            path = parsePolylineNode(node)\n            break\n\n          case 'circle':\n            style = parseStyle(node, style)\n            path = parseCircleNode(node)\n            break\n\n          case 'ellipse':\n            style = parseStyle(node, style)\n            path = parseEllipseNode(node)\n            break\n\n          case 'line':\n            style = parseStyle(node, style)\n            path = parseLineNode(node)\n            break\n\n          case 'defs':\n            isDefsNode = true\n            break\n\n          case 'use':\n            style = parseStyle(node, style)\n\n            const href = node.getAttributeNS('http://www.w3.org/1999/xlink', 'href') || ''\n            const usedNodeId = href.substring(1)\n            const usedNode = node.viewportElement.getElementById(usedNodeId)\n            if (usedNode) {\n              parseNode(usedNode, style)\n            } else {\n              console.warn(\"SVGLoader: 'use node' references non-existent node id: \" + usedNodeId)\n            }\n\n            break\n\n          default:\n          // console.log( node );\n        }\n\n        if (path) {\n          if (style.fill !== undefined && style.fill !== 'none') {\n            path.color.setStyle(style.fill, COLOR_SPACE_SVG)\n          }\n\n          transformPath(path, currentTransform)\n\n          paths.push(path)\n\n          path.userData = { node: node, style: style }\n        }\n\n        const childNodes = node.childNodes\n\n        for (let i = 0; i < childNodes.length; i++) {\n          const node = childNodes[i]\n\n          if (isDefsNode && node.nodeName !== 'style' && node.nodeName !== 'defs') {\n            // Ignore everything in defs except CSS style definitions\n            // and nested defs, because it is OK by the standard to have\n            // <style/> there.\n            continue\n          }\n\n          parseNode(node, style)\n        }\n\n        if (transform) {\n          transformStack.pop()\n\n          if (transformStack.length > 0) {\n            currentTransform.copy(transformStack[transformStack.length - 1])\n          } else {\n            currentTransform.identity()\n          }\n        }\n      }\n\n      function parsePathNode(node) {\n        const path = new ShapePath()\n\n        const point = new Vector2()\n        const control = new Vector2()\n\n        const firstPoint = new Vector2()\n        let isFirstPoint = true\n        let doSetFirstPoint = false\n\n        const d = node.getAttribute('d')\n\n        if (d === '' || d === 'none') return null\n\n        // console.log( d );\n\n        const commands = d.match(/[a-df-z][^a-df-z]*/gi)\n\n        for (let i = 0, l = commands.length; i < l; i++) {\n          const command = commands[i]\n\n          const type = command.charAt(0)\n          const data = command.slice(1).trim()\n\n          if (isFirstPoint === true) {\n            doSetFirstPoint = true\n            isFirstPoint = false\n          }\n\n          let numbers\n\n          switch (type) {\n            case 'M':\n              numbers = parseFloats(data)\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                point.x = numbers[j + 0]\n                point.y = numbers[j + 1]\n                control.x = point.x\n                control.y = point.y\n\n                if (j === 0) {\n                  path.moveTo(point.x, point.y)\n                } else {\n                  path.lineTo(point.x, point.y)\n                }\n\n                if (j === 0) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'H':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j++) {\n                point.x = numbers[j]\n                control.x = point.x\n                control.y = point.y\n                path.lineTo(point.x, point.y)\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'V':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j++) {\n                point.y = numbers[j]\n                control.x = point.x\n                control.y = point.y\n                path.lineTo(point.x, point.y)\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'L':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                point.x = numbers[j + 0]\n                point.y = numbers[j + 1]\n                control.x = point.x\n                control.y = point.y\n                path.lineTo(point.x, point.y)\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'C':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 6) {\n                path.bezierCurveTo(\n                  numbers[j + 0],\n                  numbers[j + 1],\n                  numbers[j + 2],\n                  numbers[j + 3],\n                  numbers[j + 4],\n                  numbers[j + 5],\n                )\n                control.x = numbers[j + 2]\n                control.y = numbers[j + 3]\n                point.x = numbers[j + 4]\n                point.y = numbers[j + 5]\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'S':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 4) {\n                path.bezierCurveTo(\n                  getReflection(point.x, control.x),\n                  getReflection(point.y, control.y),\n                  numbers[j + 0],\n                  numbers[j + 1],\n                  numbers[j + 2],\n                  numbers[j + 3],\n                )\n                control.x = numbers[j + 0]\n                control.y = numbers[j + 1]\n                point.x = numbers[j + 2]\n                point.y = numbers[j + 3]\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'Q':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 4) {\n                path.quadraticCurveTo(numbers[j + 0], numbers[j + 1], numbers[j + 2], numbers[j + 3])\n                control.x = numbers[j + 0]\n                control.y = numbers[j + 1]\n                point.x = numbers[j + 2]\n                point.y = numbers[j + 3]\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'T':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                const rx = getReflection(point.x, control.x)\n                const ry = getReflection(point.y, control.y)\n                path.quadraticCurveTo(rx, ry, numbers[j + 0], numbers[j + 1])\n                control.x = rx\n                control.y = ry\n                point.x = numbers[j + 0]\n                point.y = numbers[j + 1]\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'A':\n              numbers = parseFloats(data, [3, 4], 7)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 7) {\n                // skip command if start point == end point\n                if (numbers[j + 5] == point.x && numbers[j + 6] == point.y) continue\n\n                const start = point.clone()\n                point.x = numbers[j + 5]\n                point.y = numbers[j + 6]\n                control.x = point.x\n                control.y = point.y\n                parseArcCommand(\n                  path,\n                  numbers[j],\n                  numbers[j + 1],\n                  numbers[j + 2],\n                  numbers[j + 3],\n                  numbers[j + 4],\n                  start,\n                  point,\n                )\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'm':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                point.x += numbers[j + 0]\n                point.y += numbers[j + 1]\n                control.x = point.x\n                control.y = point.y\n\n                if (j === 0) {\n                  path.moveTo(point.x, point.y)\n                } else {\n                  path.lineTo(point.x, point.y)\n                }\n\n                if (j === 0) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'h':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j++) {\n                point.x += numbers[j]\n                control.x = point.x\n                control.y = point.y\n                path.lineTo(point.x, point.y)\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'v':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j++) {\n                point.y += numbers[j]\n                control.x = point.x\n                control.y = point.y\n                path.lineTo(point.x, point.y)\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'l':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                point.x += numbers[j + 0]\n                point.y += numbers[j + 1]\n                control.x = point.x\n                control.y = point.y\n                path.lineTo(point.x, point.y)\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'c':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 6) {\n                path.bezierCurveTo(\n                  point.x + numbers[j + 0],\n                  point.y + numbers[j + 1],\n                  point.x + numbers[j + 2],\n                  point.y + numbers[j + 3],\n                  point.x + numbers[j + 4],\n                  point.y + numbers[j + 5],\n                )\n                control.x = point.x + numbers[j + 2]\n                control.y = point.y + numbers[j + 3]\n                point.x += numbers[j + 4]\n                point.y += numbers[j + 5]\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 's':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 4) {\n                path.bezierCurveTo(\n                  getReflection(point.x, control.x),\n                  getReflection(point.y, control.y),\n                  point.x + numbers[j + 0],\n                  point.y + numbers[j + 1],\n                  point.x + numbers[j + 2],\n                  point.y + numbers[j + 3],\n                )\n                control.x = point.x + numbers[j + 0]\n                control.y = point.y + numbers[j + 1]\n                point.x += numbers[j + 2]\n                point.y += numbers[j + 3]\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'q':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 4) {\n                path.quadraticCurveTo(\n                  point.x + numbers[j + 0],\n                  point.y + numbers[j + 1],\n                  point.x + numbers[j + 2],\n                  point.y + numbers[j + 3],\n                )\n                control.x = point.x + numbers[j + 0]\n                control.y = point.y + numbers[j + 1]\n                point.x += numbers[j + 2]\n                point.y += numbers[j + 3]\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 't':\n              numbers = parseFloats(data)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 2) {\n                const rx = getReflection(point.x, control.x)\n                const ry = getReflection(point.y, control.y)\n                path.quadraticCurveTo(rx, ry, point.x + numbers[j + 0], point.y + numbers[j + 1])\n                control.x = rx\n                control.y = ry\n                point.x = point.x + numbers[j + 0]\n                point.y = point.y + numbers[j + 1]\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'a':\n              numbers = parseFloats(data, [3, 4], 7)\n\n              for (let j = 0, jl = numbers.length; j < jl; j += 7) {\n                // skip command if no displacement\n                if (numbers[j + 5] == 0 && numbers[j + 6] == 0) continue\n\n                const start = point.clone()\n                point.x += numbers[j + 5]\n                point.y += numbers[j + 6]\n                control.x = point.x\n                control.y = point.y\n                parseArcCommand(\n                  path,\n                  numbers[j],\n                  numbers[j + 1],\n                  numbers[j + 2],\n                  numbers[j + 3],\n                  numbers[j + 4],\n                  start,\n                  point,\n                )\n\n                if (j === 0 && doSetFirstPoint === true) firstPoint.copy(point)\n              }\n\n              break\n\n            case 'Z':\n            case 'z':\n              path.currentPath.autoClose = true\n\n              if (path.currentPath.curves.length > 0) {\n                // Reset point to beginning of Path\n                point.copy(firstPoint)\n                path.currentPath.currentPoint.copy(point)\n                isFirstPoint = true\n              }\n\n              break\n\n            default:\n              console.warn(command)\n          }\n\n          // console.log( type, parseFloats( data ), parseFloats( data ).length  )\n\n          doSetFirstPoint = false\n        }\n\n        return path\n      }\n\n      function parseCSSStylesheet(node) {\n        if (!node.sheet || !node.sheet.cssRules || !node.sheet.cssRules.length) return\n\n        for (let i = 0; i < node.sheet.cssRules.length; i++) {\n          const stylesheet = node.sheet.cssRules[i]\n\n          if (stylesheet.type !== 1) continue\n\n          const selectorList = stylesheet.selectorText\n            .split(/,/gm)\n            .filter(Boolean)\n            .map((i) => i.trim())\n\n          for (let j = 0; j < selectorList.length; j++) {\n            // Remove empty rules\n            const definitions = Object.fromEntries(Object.entries(stylesheet.style).filter(([, v]) => v !== ''))\n\n            stylesheets[selectorList[j]] = Object.assign(stylesheets[selectorList[j]] || {}, definitions)\n          }\n        }\n      }\n\n      /**\n       * https://www.w3.org/TR/SVG/implnote.html#ArcImplementationNotes\n       * https://mortoray.com/2017/02/16/rendering-an-svg-elliptical-arc-as-bezier-curves/ Appendix: Endpoint to center arc conversion\n       * From\n       * rx ry x-axis-rotation large-arc-flag sweep-flag x y\n       * To\n       * aX, aY, xRadius, yRadius, aStartAngle, aEndAngle, aClockwise, aRotation\n       */\n\n      function parseArcCommand(path, rx, ry, x_axis_rotation, large_arc_flag, sweep_flag, start, end) {\n        if (rx == 0 || ry == 0) {\n          // draw a line if either of the radii == 0\n          path.lineTo(end.x, end.y)\n          return\n        }\n\n        x_axis_rotation = (x_axis_rotation * Math.PI) / 180\n\n        // Ensure radii are positive\n        rx = Math.abs(rx)\n        ry = Math.abs(ry)\n\n        // Compute (x1', y1')\n        const dx2 = (start.x - end.x) / 2.0\n        const dy2 = (start.y - end.y) / 2.0\n        const x1p = Math.cos(x_axis_rotation) * dx2 + Math.sin(x_axis_rotation) * dy2\n        const y1p = -Math.sin(x_axis_rotation) * dx2 + Math.cos(x_axis_rotation) * dy2\n\n        // Compute (cx', cy')\n        let rxs = rx * rx\n        let rys = ry * ry\n        const x1ps = x1p * x1p\n        const y1ps = y1p * y1p\n\n        // Ensure radii are large enough\n        const cr = x1ps / rxs + y1ps / rys\n\n        if (cr > 1) {\n          // scale up rx,ry equally so cr == 1\n          const s = Math.sqrt(cr)\n          rx = s * rx\n          ry = s * ry\n          rxs = rx * rx\n          rys = ry * ry\n        }\n\n        const dq = rxs * y1ps + rys * x1ps\n        const pq = (rxs * rys - dq) / dq\n        let q = Math.sqrt(Math.max(0, pq))\n        if (large_arc_flag === sweep_flag) q = -q\n        const cxp = (q * rx * y1p) / ry\n        const cyp = (-q * ry * x1p) / rx\n\n        // Step 3: Compute (cx, cy) from (cx', cy')\n        const cx = Math.cos(x_axis_rotation) * cxp - Math.sin(x_axis_rotation) * cyp + (start.x + end.x) / 2\n        const cy = Math.sin(x_axis_rotation) * cxp + Math.cos(x_axis_rotation) * cyp + (start.y + end.y) / 2\n\n        // Step 4: Compute θ1 and Δθ\n        const theta = svgAngle(1, 0, (x1p - cxp) / rx, (y1p - cyp) / ry)\n        const delta = svgAngle((x1p - cxp) / rx, (y1p - cyp) / ry, (-x1p - cxp) / rx, (-y1p - cyp) / ry) % (Math.PI * 2)\n\n        path.currentPath.absellipse(cx, cy, rx, ry, theta, theta + delta, sweep_flag === 0, x_axis_rotation)\n      }\n\n      function svgAngle(ux, uy, vx, vy) {\n        const dot = ux * vx + uy * vy\n        const len = Math.sqrt(ux * ux + uy * uy) * Math.sqrt(vx * vx + vy * vy)\n        let ang = Math.acos(Math.max(-1, Math.min(1, dot / len))) // floating point precision, slightly over values appear\n        if (ux * vy - uy * vx < 0) ang = -ang\n        return ang\n      }\n\n      /*\n       * According to https://www.w3.org/TR/SVG/shapes.html#RectElementRXAttribute\n       * rounded corner should be rendered to elliptical arc, but bezier curve does the job well enough\n       */\n      function parseRectNode(node) {\n        const x = parseFloatWithUnits(node.getAttribute('x') || 0)\n        const y = parseFloatWithUnits(node.getAttribute('y') || 0)\n        const rx = parseFloatWithUnits(node.getAttribute('rx') || node.getAttribute('ry') || 0)\n        const ry = parseFloatWithUnits(node.getAttribute('ry') || node.getAttribute('rx') || 0)\n        const w = parseFloatWithUnits(node.getAttribute('width'))\n        const h = parseFloatWithUnits(node.getAttribute('height'))\n\n        // Ellipse arc to Bezier approximation Coefficient (Inversed). See:\n        // https://spencermortensen.com/articles/bezier-circle/\n        const bci = 1 - 0.551915024494\n\n        const path = new ShapePath()\n\n        // top left\n        path.moveTo(x + rx, y)\n\n        // top right\n        path.lineTo(x + w - rx, y)\n        if (rx !== 0 || ry !== 0) {\n          path.bezierCurveTo(x + w - rx * bci, y, x + w, y + ry * bci, x + w, y + ry)\n        }\n\n        // bottom right\n        path.lineTo(x + w, y + h - ry)\n        if (rx !== 0 || ry !== 0) {\n          path.bezierCurveTo(x + w, y + h - ry * bci, x + w - rx * bci, y + h, x + w - rx, y + h)\n        }\n\n        // bottom left\n        path.lineTo(x + rx, y + h)\n        if (rx !== 0 || ry !== 0) {\n          path.bezierCurveTo(x + rx * bci, y + h, x, y + h - ry * bci, x, y + h - ry)\n        }\n\n        // back to top left\n        path.lineTo(x, y + ry)\n        if (rx !== 0 || ry !== 0) {\n          path.bezierCurveTo(x, y + ry * bci, x + rx * bci, y, x + rx, y)\n        }\n\n        return path\n      }\n\n      function parsePolygonNode(node) {\n        function iterator(match, a, b) {\n          const x = parseFloatWithUnits(a)\n          const y = parseFloatWithUnits(b)\n\n          if (index === 0) {\n            path.moveTo(x, y)\n          } else {\n            path.lineTo(x, y)\n          }\n\n          index++\n        }\n\n        const regex = /([+-]?\\d*\\.?\\d+(?:e[+-]?\\d+)?)(?:,|\\s)([+-]?\\d*\\.?\\d+(?:e[+-]?\\d+)?)/g\n\n        const path = new ShapePath()\n\n        let index = 0\n\n        node.getAttribute('points').replace(regex, iterator)\n\n        path.currentPath.autoClose = true\n\n        return path\n      }\n\n      function parsePolylineNode(node) {\n        function iterator(match, a, b) {\n          const x = parseFloatWithUnits(a)\n          const y = parseFloatWithUnits(b)\n\n          if (index === 0) {\n            path.moveTo(x, y)\n          } else {\n            path.lineTo(x, y)\n          }\n\n          index++\n        }\n\n        const regex = /([+-]?\\d*\\.?\\d+(?:e[+-]?\\d+)?)(?:,|\\s)([+-]?\\d*\\.?\\d+(?:e[+-]?\\d+)?)/g\n\n        const path = new ShapePath()\n\n        let index = 0\n\n        node.getAttribute('points').replace(regex, iterator)\n\n        path.currentPath.autoClose = false\n\n        return path\n      }\n\n      function parseCircleNode(node) {\n        const x = parseFloatWithUnits(node.getAttribute('cx') || 0)\n        const y = parseFloatWithUnits(node.getAttribute('cy') || 0)\n        const r = parseFloatWithUnits(node.getAttribute('r') || 0)\n\n        const subpath = new Path()\n        subpath.absarc(x, y, r, 0, Math.PI * 2)\n\n        const path = new ShapePath()\n        path.subPaths.push(subpath)\n\n        return path\n      }\n\n      function parseEllipseNode(node) {\n        const x = parseFloatWithUnits(node.getAttribute('cx') || 0)\n        const y = parseFloatWithUnits(node.getAttribute('cy') || 0)\n        const rx = parseFloatWithUnits(node.getAttribute('rx') || 0)\n        const ry = parseFloatWithUnits(node.getAttribute('ry') || 0)\n\n        const subpath = new Path()\n        subpath.absellipse(x, y, rx, ry, 0, Math.PI * 2)\n\n        const path = new ShapePath()\n        path.subPaths.push(subpath)\n\n        return path\n      }\n\n      function parseLineNode(node) {\n        const x1 = parseFloatWithUnits(node.getAttribute('x1') || 0)\n        const y1 = parseFloatWithUnits(node.getAttribute('y1') || 0)\n        const x2 = parseFloatWithUnits(node.getAttribute('x2') || 0)\n        const y2 = parseFloatWithUnits(node.getAttribute('y2') || 0)\n\n        const path = new ShapePath()\n        path.moveTo(x1, y1)\n        path.lineTo(x2, y2)\n        path.currentPath.autoClose = false\n\n        return path\n      }\n\n      //\n\n      function parseStyle(node, style) {\n        style = Object.assign({}, style) // clone style\n\n        let stylesheetStyles = {}\n\n        if (node.hasAttribute('class')) {\n          const classSelectors = node\n            .getAttribute('class')\n            .split(/\\s/)\n            .filter(Boolean)\n            .map((i) => i.trim())\n\n          for (let i = 0; i < classSelectors.length; i++) {\n            stylesheetStyles = Object.assign(stylesheetStyles, stylesheets['.' + classSelectors[i]])\n          }\n        }\n\n        if (node.hasAttribute('id')) {\n          stylesheetStyles = Object.assign(stylesheetStyles, stylesheets['#' + node.getAttribute('id')])\n        }\n\n        function addStyle(svgName, jsName, adjustFunction) {\n          if (adjustFunction === undefined)\n            adjustFunction = function copy(v) {\n              if (v.startsWith('url')) console.warn('SVGLoader: url access in attributes is not implemented.')\n\n              return v\n            }\n\n          if (node.hasAttribute(svgName)) style[jsName] = adjustFunction(node.getAttribute(svgName))\n          if (stylesheetStyles[svgName]) style[jsName] = adjustFunction(stylesheetStyles[svgName])\n          if (node.style && node.style[svgName] !== '') style[jsName] = adjustFunction(node.style[svgName])\n        }\n\n        function clamp(v) {\n          return Math.max(0, Math.min(1, parseFloatWithUnits(v)))\n        }\n\n        function positive(v) {\n          return Math.max(0, parseFloatWithUnits(v))\n        }\n\n        addStyle('fill', 'fill')\n        addStyle('fill-opacity', 'fillOpacity', clamp)\n        addStyle('fill-rule', 'fillRule')\n        addStyle('opacity', 'opacity', clamp)\n        addStyle('stroke', 'stroke')\n        addStyle('stroke-opacity', 'strokeOpacity', clamp)\n        addStyle('stroke-width', 'strokeWidth', positive)\n        addStyle('stroke-linejoin', 'strokeLineJoin')\n        addStyle('stroke-linecap', 'strokeLineCap')\n        addStyle('stroke-miterlimit', 'strokeMiterLimit', positive)\n        addStyle('visibility', 'visibility')\n\n        return style\n      }\n\n      // http://www.w3.org/TR/SVG11/implnote.html#PathElementImplementationNotes\n\n      function getReflection(a, b) {\n        return a - (b - a)\n      }\n\n      // from https://github.com/ppvg/svg-numbers (MIT License)\n\n      function parseFloats(input, flags, stride) {\n        if (typeof input !== 'string') {\n          throw new TypeError('Invalid input: ' + typeof input)\n        }\n\n        // Character groups\n        const RE = {\n          SEPARATOR: /[ \\t\\r\\n\\,.\\-+]/,\n          WHITESPACE: /[ \\t\\r\\n]/,\n          DIGIT: /[\\d]/,\n          SIGN: /[-+]/,\n          POINT: /\\./,\n          COMMA: /,/,\n          EXP: /e/i,\n          FLAGS: /[01]/,\n        }\n\n        // States\n        const SEP = 0\n        const INT = 1\n        const FLOAT = 2\n        const EXP = 3\n\n        let state = SEP\n        let seenComma = true\n        let number = '',\n          exponent = ''\n        const result = []\n\n        function throwSyntaxError(current, i, partial) {\n          const error = new SyntaxError('Unexpected character \"' + current + '\" at index ' + i + '.')\n          error.partial = partial\n          throw error\n        }\n\n        function newNumber() {\n          if (number !== '') {\n            if (exponent === '') result.push(Number(number))\n            else result.push(Number(number) * Math.pow(10, Number(exponent)))\n          }\n\n          number = ''\n          exponent = ''\n        }\n\n        let current\n        const length = input.length\n\n        for (let i = 0; i < length; i++) {\n          current = input[i]\n\n          // check for flags\n          if (Array.isArray(flags) && flags.includes(result.length % stride) && RE.FLAGS.test(current)) {\n            state = INT\n            number = current\n            newNumber()\n            continue\n          }\n\n          // parse until next number\n          if (state === SEP) {\n            // eat whitespace\n            if (RE.WHITESPACE.test(current)) {\n              continue\n            }\n\n            // start new number\n            if (RE.DIGIT.test(current) || RE.SIGN.test(current)) {\n              state = INT\n              number = current\n              continue\n            }\n\n            if (RE.POINT.test(current)) {\n              state = FLOAT\n              number = current\n              continue\n            }\n\n            // throw on double commas (e.g. \"1, , 2\")\n            if (RE.COMMA.test(current)) {\n              if (seenComma) {\n                throwSyntaxError(current, i, result)\n              }\n\n              seenComma = true\n            }\n          }\n\n          // parse integer part\n          if (state === INT) {\n            if (RE.DIGIT.test(current)) {\n              number += current\n              continue\n            }\n\n            if (RE.POINT.test(current)) {\n              number += current\n              state = FLOAT\n              continue\n            }\n\n            if (RE.EXP.test(current)) {\n              state = EXP\n              continue\n            }\n\n            // throw on double signs (\"-+1\"), but not on sign as separator (\"-1-2\")\n            if (RE.SIGN.test(current) && number.length === 1 && RE.SIGN.test(number[0])) {\n              throwSyntaxError(current, i, result)\n            }\n          }\n\n          // parse decimal part\n          if (state === FLOAT) {\n            if (RE.DIGIT.test(current)) {\n              number += current\n              continue\n            }\n\n            if (RE.EXP.test(current)) {\n              state = EXP\n              continue\n            }\n\n            // throw on double decimal points (e.g. \"1..2\")\n            if (RE.POINT.test(current) && number[number.length - 1] === '.') {\n              throwSyntaxError(current, i, result)\n            }\n          }\n\n          // parse exponent part\n          if (state === EXP) {\n            if (RE.DIGIT.test(current)) {\n              exponent += current\n              continue\n            }\n\n            if (RE.SIGN.test(current)) {\n              if (exponent === '') {\n                exponent += current\n                continue\n              }\n\n              if (exponent.length === 1 && RE.SIGN.test(exponent)) {\n                throwSyntaxError(current, i, result)\n              }\n            }\n          }\n\n          // end of number\n          if (RE.WHITESPACE.test(current)) {\n            newNumber()\n            state = SEP\n            seenComma = false\n          } else if (RE.COMMA.test(current)) {\n            newNumber()\n            state = SEP\n            seenComma = true\n          } else if (RE.SIGN.test(current)) {\n            newNumber()\n            state = INT\n            number = current\n          } else if (RE.POINT.test(current)) {\n            newNumber()\n            state = FLOAT\n            number = current\n          } else {\n            throwSyntaxError(current, i, result)\n          }\n        }\n\n        // add the last number found (if any)\n        newNumber()\n\n        return result\n      }\n\n      // Units\n\n      const units = ['mm', 'cm', 'in', 'pt', 'pc', 'px']\n\n      // Conversion: [ fromUnit ][ toUnit ] (-1 means dpi dependent)\n      const unitConversion = {\n        mm: {\n          mm: 1,\n          cm: 0.1,\n          in: 1 / 25.4,\n          pt: 72 / 25.4,\n          pc: 6 / 25.4,\n          px: -1,\n        },\n        cm: {\n          mm: 10,\n          cm: 1,\n          in: 1 / 2.54,\n          pt: 72 / 2.54,\n          pc: 6 / 2.54,\n          px: -1,\n        },\n        in: {\n          mm: 25.4,\n          cm: 2.54,\n          in: 1,\n          pt: 72,\n          pc: 6,\n          px: -1,\n        },\n        pt: {\n          mm: 25.4 / 72,\n          cm: 2.54 / 72,\n          in: 1 / 72,\n          pt: 1,\n          pc: 6 / 72,\n          px: -1,\n        },\n        pc: {\n          mm: 25.4 / 6,\n          cm: 2.54 / 6,\n          in: 1 / 6,\n          pt: 72 / 6,\n          pc: 1,\n          px: -1,\n        },\n        px: {\n          px: 1,\n        },\n      }\n\n      function parseFloatWithUnits(string) {\n        let theUnit = 'px'\n\n        if (typeof string === 'string' || string instanceof String) {\n          for (let i = 0, n = units.length; i < n; i++) {\n            const u = units[i]\n\n            if (string.endsWith(u)) {\n              theUnit = u\n              string = string.substring(0, string.length - u.length)\n              break\n            }\n          }\n        }\n\n        let scale = undefined\n\n        if (theUnit === 'px' && scope.defaultUnit !== 'px') {\n          // Conversion scale from  pixels to inches, then to default units\n\n          scale = unitConversion['in'][scope.defaultUnit] / scope.defaultDPI\n        } else {\n          scale = unitConversion[theUnit][scope.defaultUnit]\n\n          if (scale < 0) {\n            // Conversion scale to pixels\n\n            scale = unitConversion[theUnit]['in'] * scope.defaultDPI\n          }\n        }\n\n        return scale * parseFloat(string)\n      }\n\n      // Transforms\n\n      function getNodeTransform(node) {\n        if (\n          !(\n            node.hasAttribute('transform') ||\n            (node.nodeName === 'use' && (node.hasAttribute('x') || node.hasAttribute('y')))\n          )\n        ) {\n          return null\n        }\n\n        const transform = parseNodeTransform(node)\n\n        if (transformStack.length > 0) {\n          transform.premultiply(transformStack[transformStack.length - 1])\n        }\n\n        currentTransform.copy(transform)\n        transformStack.push(transform)\n\n        return transform\n      }\n\n      function parseNodeTransform(node) {\n        const transform = new Matrix3()\n        const currentTransform = tempTransform0\n\n        if (node.nodeName === 'use' && (node.hasAttribute('x') || node.hasAttribute('y'))) {\n          const tx = parseFloatWithUnits(node.getAttribute('x'))\n          const ty = parseFloatWithUnits(node.getAttribute('y'))\n\n          transform.translate(tx, ty)\n        }\n\n        if (node.hasAttribute('transform')) {\n          const transformsTexts = node.getAttribute('transform').split(')')\n\n          for (let tIndex = transformsTexts.length - 1; tIndex >= 0; tIndex--) {\n            const transformText = transformsTexts[tIndex].trim()\n\n            if (transformText === '') continue\n\n            const openParPos = transformText.indexOf('(')\n            const closeParPos = transformText.length\n\n            if (openParPos > 0 && openParPos < closeParPos) {\n              const transformType = transformText.slice(0, openParPos)\n\n              const array = parseFloats(transformText.slice(openParPos + 1))\n\n              currentTransform.identity()\n\n              switch (transformType) {\n                case 'translate':\n                  if (array.length >= 1) {\n                    const tx = array[0]\n                    let ty = 0\n\n                    if (array.length >= 2) {\n                      ty = array[1]\n                    }\n\n                    currentTransform.translate(tx, ty)\n                  }\n\n                  break\n\n                case 'rotate':\n                  if (array.length >= 1) {\n                    let angle = 0\n                    let cx = 0\n                    let cy = 0\n\n                    // Angle\n                    angle = (array[0] * Math.PI) / 180\n\n                    if (array.length >= 3) {\n                      // Center x, y\n                      cx = array[1]\n                      cy = array[2]\n                    }\n\n                    // Rotate around center (cx, cy)\n                    tempTransform1.makeTranslation(-cx, -cy)\n                    tempTransform2.makeRotation(angle)\n                    tempTransform3.multiplyMatrices(tempTransform2, tempTransform1)\n                    tempTransform1.makeTranslation(cx, cy)\n                    currentTransform.multiplyMatrices(tempTransform1, tempTransform3)\n                  }\n\n                  break\n\n                case 'scale':\n                  if (array.length >= 1) {\n                    const scaleX = array[0]\n                    let scaleY = scaleX\n\n                    if (array.length >= 2) {\n                      scaleY = array[1]\n                    }\n\n                    currentTransform.scale(scaleX, scaleY)\n                  }\n\n                  break\n\n                case 'skewX':\n                  if (array.length === 1) {\n                    currentTransform.set(1, Math.tan((array[0] * Math.PI) / 180), 0, 0, 1, 0, 0, 0, 1)\n                  }\n\n                  break\n\n                case 'skewY':\n                  if (array.length === 1) {\n                    currentTransform.set(1, 0, 0, Math.tan((array[0] * Math.PI) / 180), 1, 0, 0, 0, 1)\n                  }\n\n                  break\n\n                case 'matrix':\n                  if (array.length === 6) {\n                    currentTransform.set(array[0], array[2], array[4], array[1], array[3], array[5], 0, 0, 1)\n                  }\n\n                  break\n              }\n            }\n\n            transform.premultiply(currentTransform)\n          }\n        }\n\n        return transform\n      }\n\n      function transformPath(path, m) {\n        function transfVec2(v2) {\n          tempV3.set(v2.x, v2.y, 1).applyMatrix3(m)\n\n          v2.set(tempV3.x, tempV3.y)\n        }\n\n        function transfEllipseGeneric(curve) {\n          // For math description see:\n          // https://math.stackexchange.com/questions/4544164\n\n          const a = curve.xRadius\n          const b = curve.yRadius\n\n          const cosTheta = Math.cos(curve.aRotation)\n          const sinTheta = Math.sin(curve.aRotation)\n\n          const v1 = new Vector3(a * cosTheta, a * sinTheta, 0)\n          const v2 = new Vector3(-b * sinTheta, b * cosTheta, 0)\n\n          const f1 = v1.applyMatrix3(m)\n          const f2 = v2.applyMatrix3(m)\n\n          const mF = tempTransform0.set(f1.x, f2.x, 0, f1.y, f2.y, 0, 0, 0, 1)\n\n          const mFInv = tempTransform1.copy(mF).invert()\n          const mFInvT = tempTransform2.copy(mFInv).transpose()\n          const mQ = mFInvT.multiply(mFInv)\n          const mQe = mQ.elements\n\n          const ed = eigenDecomposition(mQe[0], mQe[1], mQe[4])\n          const rt1sqrt = Math.sqrt(ed.rt1)\n          const rt2sqrt = Math.sqrt(ed.rt2)\n\n          curve.xRadius = 1 / rt1sqrt\n          curve.yRadius = 1 / rt2sqrt\n          curve.aRotation = Math.atan2(ed.sn, ed.cs)\n\n          const isFullEllipse = (curve.aEndAngle - curve.aStartAngle) % (2 * Math.PI) < Number.EPSILON\n\n          // Do not touch angles of a full ellipse because after transformation they\n          // would converge to a sinle value effectively removing the whole curve\n\n          if (!isFullEllipse) {\n            const mDsqrt = tempTransform1.set(rt1sqrt, 0, 0, 0, rt2sqrt, 0, 0, 0, 1)\n\n            const mRT = tempTransform2.set(ed.cs, ed.sn, 0, -ed.sn, ed.cs, 0, 0, 0, 1)\n\n            const mDRF = mDsqrt.multiply(mRT).multiply(mF)\n\n            const transformAngle = (phi) => {\n              const { x: cosR, y: sinR } = new Vector3(Math.cos(phi), Math.sin(phi), 0).applyMatrix3(mDRF)\n\n              return Math.atan2(sinR, cosR)\n            }\n\n            curve.aStartAngle = transformAngle(curve.aStartAngle)\n            curve.aEndAngle = transformAngle(curve.aEndAngle)\n\n            if (isTransformFlipped(m)) {\n              curve.aClockwise = !curve.aClockwise\n            }\n          }\n        }\n\n        function transfEllipseNoSkew(curve) {\n          // Faster shortcut if no skew is applied\n          // (e.g, a euclidean transform of a group containing the ellipse)\n\n          const sx = getTransformScaleX(m)\n          const sy = getTransformScaleY(m)\n\n          curve.xRadius *= sx\n          curve.yRadius *= sy\n\n          // Extract rotation angle from the matrix of form:\n          //\n          //  | cosθ sx   -sinθ sy |\n          //  | sinθ sx    cosθ sy |\n          //\n          // Remembering that tanθ = sinθ / cosθ; and that\n          // `sx`, `sy`, or both might be zero.\n          const theta =\n            sx > Number.EPSILON ? Math.atan2(m.elements[1], m.elements[0]) : Math.atan2(-m.elements[3], m.elements[4])\n\n          curve.aRotation += theta\n\n          if (isTransformFlipped(m)) {\n            curve.aStartAngle *= -1\n            curve.aEndAngle *= -1\n            curve.aClockwise = !curve.aClockwise\n          }\n        }\n\n        const subPaths = path.subPaths\n\n        for (let i = 0, n = subPaths.length; i < n; i++) {\n          const subPath = subPaths[i]\n          const curves = subPath.curves\n\n          for (let j = 0; j < curves.length; j++) {\n            const curve = curves[j]\n\n            if (curve.isLineCurve) {\n              transfVec2(curve.v1)\n              transfVec2(curve.v2)\n            } else if (curve.isCubicBezierCurve) {\n              transfVec2(curve.v0)\n              transfVec2(curve.v1)\n              transfVec2(curve.v2)\n              transfVec2(curve.v3)\n            } else if (curve.isQuadraticBezierCurve) {\n              transfVec2(curve.v0)\n              transfVec2(curve.v1)\n              transfVec2(curve.v2)\n            } else if (curve.isEllipseCurve) {\n              // Transform ellipse center point\n\n              tempV2.set(curve.aX, curve.aY)\n              transfVec2(tempV2)\n              curve.aX = tempV2.x\n              curve.aY = tempV2.y\n\n              // Transform ellipse shape parameters\n\n              if (isTransformSkewed(m)) {\n                transfEllipseGeneric(curve)\n              } else {\n                transfEllipseNoSkew(curve)\n              }\n            }\n          }\n        }\n      }\n\n      function isTransformFlipped(m) {\n        const te = m.elements\n        return te[0] * te[4] - te[1] * te[3] < 0\n      }\n\n      function isTransformSkewed(m) {\n        const te = m.elements\n        const basisDot = te[0] * te[3] + te[1] * te[4]\n\n        // Shortcut for trivial rotations and transformations\n        if (basisDot === 0) return false\n\n        const sx = getTransformScaleX(m)\n        const sy = getTransformScaleY(m)\n\n        return Math.abs(basisDot / (sx * sy)) > Number.EPSILON\n      }\n\n      function getTransformScaleX(m) {\n        const te = m.elements\n        return Math.sqrt(te[0] * te[0] + te[1] * te[1])\n      }\n\n      function getTransformScaleY(m) {\n        const te = m.elements\n        return Math.sqrt(te[3] * te[3] + te[4] * te[4])\n      }\n\n      // Calculates the eigensystem of a real symmetric 2x2 matrix\n      //    [ A  B ]\n      //    [ B  C ]\n      // in the form\n      //    [ A  B ]  =  [ cs  -sn ] [ rt1   0  ] [  cs  sn ]\n      //    [ B  C ]     [ sn   cs ] [  0   rt2 ] [ -sn  cs ]\n      // where rt1 >= rt2.\n      //\n      // Adapted from: https://www.mpi-hd.mpg.de/personalhomes/globes/3x3/index.html\n      // -> Algorithms for real symmetric matrices -> Analytical (2x2 symmetric)\n      function eigenDecomposition(A, B, C) {\n        let rt1, rt2, cs, sn, t\n        const sm = A + C\n        const df = A - C\n        const rt = Math.sqrt(df * df + 4 * B * B)\n\n        if (sm > 0) {\n          rt1 = 0.5 * (sm + rt)\n          t = 1 / rt1\n          rt2 = A * t * C - B * t * B\n        } else if (sm < 0) {\n          rt2 = 0.5 * (sm - rt)\n        } else {\n          // This case needs to be treated separately to avoid div by 0\n\n          rt1 = 0.5 * rt\n          rt2 = -0.5 * rt\n        }\n\n        // Calculate eigenvectors\n\n        if (df > 0) {\n          cs = df + rt\n        } else {\n          cs = df - rt\n        }\n\n        if (Math.abs(cs) > 2 * Math.abs(B)) {\n          t = (-2 * B) / cs\n          sn = 1 / Math.sqrt(1 + t * t)\n          cs = t * sn\n        } else if (Math.abs(B) === 0) {\n          cs = 1\n          sn = 0\n        } else {\n          t = (-0.5 * cs) / B\n          cs = 1 / Math.sqrt(1 + t * t)\n          sn = t * cs\n        }\n\n        if (df > 0) {\n          t = cs\n          cs = -sn\n          sn = t\n        }\n\n        return { rt1, rt2, cs, sn }\n      }\n\n      //\n\n      const paths = []\n      const stylesheets = {}\n\n      const transformStack = []\n\n      const tempTransform0 = new Matrix3()\n      const tempTransform1 = new Matrix3()\n      const tempTransform2 = new Matrix3()\n      const tempTransform3 = new Matrix3()\n      const tempV2 = new Vector2()\n      const tempV3 = new Vector3()\n\n      const currentTransform = new Matrix3()\n\n      const xml = new DOMParser().parseFromString(text, 'image/svg+xml') // application/xml\n\n      parseNode(xml.documentElement, {\n        fill: '#000',\n        fillOpacity: 1,\n        strokeOpacity: 1,\n        strokeWidth: 1,\n        strokeLineJoin: 'miter',\n        strokeLineCap: 'butt',\n        strokeMiterLimit: 4,\n      })\n\n      const data = { paths: paths, xml: xml.documentElement }\n\n      // console.log( paths );\n      return data\n    }\n\n    static createShapes(shapePath) {\n      // Param shapePath: a shapepath as returned by the parse function of this class\n      // Returns Shape object\n\n      const BIGNUMBER = 999999999\n\n      const IntersectionLocationType = {\n        ORIGIN: 0,\n        DESTINATION: 1,\n        BETWEEN: 2,\n        LEFT: 3,\n        RIGHT: 4,\n        BEHIND: 5,\n        BEYOND: 6,\n      }\n\n      const classifyResult = {\n        loc: IntersectionLocationType.ORIGIN,\n        t: 0,\n      }\n\n      function findEdgeIntersection(a0, a1, b0, b1) {\n        const x1 = a0.x\n        const x2 = a1.x\n        const x3 = b0.x\n        const x4 = b1.x\n        const y1 = a0.y\n        const y2 = a1.y\n        const y3 = b0.y\n        const y4 = b1.y\n        const nom1 = (x4 - x3) * (y1 - y3) - (y4 - y3) * (x1 - x3)\n        const nom2 = (x2 - x1) * (y1 - y3) - (y2 - y1) * (x1 - x3)\n        const denom = (y4 - y3) * (x2 - x1) - (x4 - x3) * (y2 - y1)\n        const t1 = nom1 / denom\n        const t2 = nom2 / denom\n\n        if ((denom === 0 && nom1 !== 0) || t1 <= 0 || t1 >= 1 || t2 < 0 || t2 > 1) {\n          //1. lines are parallel or edges don't intersect\n\n          return null\n        } else if (nom1 === 0 && denom === 0) {\n          //2. lines are colinear\n\n          //check if endpoints of edge2 (b0-b1) lies on edge1 (a0-a1)\n          for (let i = 0; i < 2; i++) {\n            classifyPoint(i === 0 ? b0 : b1, a0, a1)\n            //find position of this endpoints relatively to edge1\n            if (classifyResult.loc == IntersectionLocationType.ORIGIN) {\n              const point = i === 0 ? b0 : b1\n              return { x: point.x, y: point.y, t: classifyResult.t }\n            } else if (classifyResult.loc == IntersectionLocationType.BETWEEN) {\n              const x = +(x1 + classifyResult.t * (x2 - x1)).toPrecision(10)\n              const y = +(y1 + classifyResult.t * (y2 - y1)).toPrecision(10)\n              return { x: x, y: y, t: classifyResult.t }\n            }\n          }\n\n          return null\n        } else {\n          //3. edges intersect\n\n          for (let i = 0; i < 2; i++) {\n            classifyPoint(i === 0 ? b0 : b1, a0, a1)\n\n            if (classifyResult.loc == IntersectionLocationType.ORIGIN) {\n              const point = i === 0 ? b0 : b1\n              return { x: point.x, y: point.y, t: classifyResult.t }\n            }\n          }\n\n          const x = +(x1 + t1 * (x2 - x1)).toPrecision(10)\n          const y = +(y1 + t1 * (y2 - y1)).toPrecision(10)\n          return { x: x, y: y, t: t1 }\n        }\n      }\n\n      function classifyPoint(p, edgeStart, edgeEnd) {\n        const ax = edgeEnd.x - edgeStart.x\n        const ay = edgeEnd.y - edgeStart.y\n        const bx = p.x - edgeStart.x\n        const by = p.y - edgeStart.y\n        const sa = ax * by - bx * ay\n\n        if (p.x === edgeStart.x && p.y === edgeStart.y) {\n          classifyResult.loc = IntersectionLocationType.ORIGIN\n          classifyResult.t = 0\n          return\n        }\n\n        if (p.x === edgeEnd.x && p.y === edgeEnd.y) {\n          classifyResult.loc = IntersectionLocationType.DESTINATION\n          classifyResult.t = 1\n          return\n        }\n\n        if (sa < -Number.EPSILON) {\n          classifyResult.loc = IntersectionLocationType.LEFT\n          return\n        }\n\n        if (sa > Number.EPSILON) {\n          classifyResult.loc = IntersectionLocationType.RIGHT\n          return\n        }\n\n        if (ax * bx < 0 || ay * by < 0) {\n          classifyResult.loc = IntersectionLocationType.BEHIND\n          return\n        }\n\n        if (Math.sqrt(ax * ax + ay * ay) < Math.sqrt(bx * bx + by * by)) {\n          classifyResult.loc = IntersectionLocationType.BEYOND\n          return\n        }\n\n        let t\n\n        if (ax !== 0) {\n          t = bx / ax\n        } else {\n          t = by / ay\n        }\n\n        classifyResult.loc = IntersectionLocationType.BETWEEN\n        classifyResult.t = t\n      }\n\n      function getIntersections(path1, path2) {\n        const intersectionsRaw = []\n        const intersections = []\n\n        for (let index = 1; index < path1.length; index++) {\n          const path1EdgeStart = path1[index - 1]\n          const path1EdgeEnd = path1[index]\n\n          for (let index2 = 1; index2 < path2.length; index2++) {\n            const path2EdgeStart = path2[index2 - 1]\n            const path2EdgeEnd = path2[index2]\n\n            const intersection = findEdgeIntersection(path1EdgeStart, path1EdgeEnd, path2EdgeStart, path2EdgeEnd)\n\n            if (\n              intersection !== null &&\n              intersectionsRaw.find(\n                (i) => i.t <= intersection.t + Number.EPSILON && i.t >= intersection.t - Number.EPSILON,\n              ) === undefined\n            ) {\n              intersectionsRaw.push(intersection)\n              intersections.push(new Vector2(intersection.x, intersection.y))\n            }\n          }\n        }\n\n        return intersections\n      }\n\n      function getScanlineIntersections(scanline, boundingBox, paths) {\n        const center = new Vector2()\n        boundingBox.getCenter(center)\n\n        const allIntersections = []\n\n        paths.forEach((path) => {\n          // check if the center of the bounding box is in the bounding box of the paths.\n          // this is a pruning method to limit the search of intersections in paths that can't envelop of the current path.\n          // if a path envelops another path. The center of that oter path, has to be inside the bounding box of the enveloping path.\n          if (path.boundingBox.containsPoint(center)) {\n            const intersections = getIntersections(scanline, path.points)\n\n            intersections.forEach((p) => {\n              allIntersections.push({ identifier: path.identifier, isCW: path.isCW, point: p })\n            })\n          }\n        })\n\n        allIntersections.sort((i1, i2) => {\n          return i1.point.x - i2.point.x\n        })\n\n        return allIntersections\n      }\n\n      function isHoleTo(simplePath, allPaths, scanlineMinX, scanlineMaxX, _fillRule) {\n        if (_fillRule === null || _fillRule === undefined || _fillRule === '') {\n          _fillRule = 'nonzero'\n        }\n\n        const centerBoundingBox = new Vector2()\n        simplePath.boundingBox.getCenter(centerBoundingBox)\n\n        const scanline = [\n          new Vector2(scanlineMinX, centerBoundingBox.y),\n          new Vector2(scanlineMaxX, centerBoundingBox.y),\n        ]\n\n        const scanlineIntersections = getScanlineIntersections(scanline, simplePath.boundingBox, allPaths)\n\n        scanlineIntersections.sort((i1, i2) => {\n          return i1.point.x - i2.point.x\n        })\n\n        const baseIntersections = []\n        const otherIntersections = []\n\n        scanlineIntersections.forEach((i) => {\n          if (i.identifier === simplePath.identifier) {\n            baseIntersections.push(i)\n          } else {\n            otherIntersections.push(i)\n          }\n        })\n\n        const firstXOfPath = baseIntersections[0].point.x\n\n        // build up the path hierarchy\n        const stack = []\n        let i = 0\n\n        while (i < otherIntersections.length && otherIntersections[i].point.x < firstXOfPath) {\n          if (stack.length > 0 && stack[stack.length - 1] === otherIntersections[i].identifier) {\n            stack.pop()\n          } else {\n            stack.push(otherIntersections[i].identifier)\n          }\n\n          i++\n        }\n\n        stack.push(simplePath.identifier)\n\n        if (_fillRule === 'evenodd') {\n          const isHole = stack.length % 2 === 0 ? true : false\n          const isHoleFor = stack[stack.length - 2]\n\n          return { identifier: simplePath.identifier, isHole: isHole, for: isHoleFor }\n        } else if (_fillRule === 'nonzero') {\n          // check if path is a hole by counting the amount of paths with alternating rotations it has to cross.\n          let isHole = true\n          let isHoleFor = null\n          let lastCWValue = null\n\n          for (let i = 0; i < stack.length; i++) {\n            const identifier = stack[i]\n            if (isHole) {\n              lastCWValue = allPaths[identifier].isCW\n              isHole = false\n              isHoleFor = identifier\n            } else if (lastCWValue !== allPaths[identifier].isCW) {\n              lastCWValue = allPaths[identifier].isCW\n              isHole = true\n            }\n          }\n\n          return { identifier: simplePath.identifier, isHole: isHole, for: isHoleFor }\n        } else {\n          console.warn('fill-rule: \"' + _fillRule + '\" is currently not implemented.')\n        }\n      }\n\n      // check for self intersecting paths\n      // TODO\n\n      // check intersecting paths\n      // TODO\n\n      // prepare paths for hole detection\n      let scanlineMinX = BIGNUMBER\n      let scanlineMaxX = -BIGNUMBER\n\n      let simplePaths = shapePath.subPaths.map((p) => {\n        const points = p.getPoints()\n        let maxY = -BIGNUMBER\n        let minY = BIGNUMBER\n        let maxX = -BIGNUMBER\n        let minX = BIGNUMBER\n\n        //points.forEach(p => p.y *= -1);\n\n        for (let i = 0; i < points.length; i++) {\n          const p = points[i]\n\n          if (p.y > maxY) {\n            maxY = p.y\n          }\n\n          if (p.y < minY) {\n            minY = p.y\n          }\n\n          if (p.x > maxX) {\n            maxX = p.x\n          }\n\n          if (p.x < minX) {\n            minX = p.x\n          }\n        }\n\n        //\n        if (scanlineMaxX <= maxX) {\n          scanlineMaxX = maxX + 1\n        }\n\n        if (scanlineMinX >= minX) {\n          scanlineMinX = minX - 1\n        }\n\n        return {\n          curves: p.curves,\n          points: points,\n          isCW: ShapeUtils.isClockWise(points),\n          identifier: -1,\n          boundingBox: new Box2(new Vector2(minX, minY), new Vector2(maxX, maxY)),\n        }\n      })\n\n      simplePaths = simplePaths.filter((sp) => sp.points.length > 1)\n\n      for (let identifier = 0; identifier < simplePaths.length; identifier++) {\n        simplePaths[identifier].identifier = identifier\n      }\n\n      // check if path is solid or a hole\n      const isAHole = simplePaths.map((p) =>\n        isHoleTo(\n          p,\n          simplePaths,\n          scanlineMinX,\n          scanlineMaxX,\n          shapePath.userData ? shapePath.userData.style.fillRule : undefined,\n        ),\n      )\n\n      const shapesToReturn = []\n      simplePaths.forEach((p) => {\n        const amIAHole = isAHole[p.identifier]\n\n        if (!amIAHole.isHole) {\n          const shape = new Shape()\n          shape.curves = p.curves\n          const holes = isAHole.filter((h) => h.isHole && h.for === p.identifier)\n          holes.forEach((h) => {\n            const hole = simplePaths[h.identifier]\n            const path = new Path()\n            path.curves = hole.curves\n            shape.holes.push(path)\n          })\n          shapesToReturn.push(shape)\n        }\n      })\n\n      return shapesToReturn\n    }\n\n    static getStrokeStyle(width, color, lineJoin, lineCap, miterLimit) {\n      // Param width: Stroke width\n      // Param color: As returned by THREE.Color.getStyle()\n      // Param lineJoin: One of \"round\", \"bevel\", \"miter\" or \"miter-limit\"\n      // Param lineCap: One of \"round\", \"square\" or \"butt\"\n      // Param miterLimit: Maximum join length, in multiples of the \"width\" parameter (join is truncated if it exceeds that distance)\n      // Returns style object\n\n      width = width !== undefined ? width : 1\n      color = color !== undefined ? color : '#000'\n      lineJoin = lineJoin !== undefined ? lineJoin : 'miter'\n      lineCap = lineCap !== undefined ? lineCap : 'butt'\n      miterLimit = miterLimit !== undefined ? miterLimit : 4\n\n      return {\n        strokeColor: color,\n        strokeWidth: width,\n        strokeLineJoin: lineJoin,\n        strokeLineCap: lineCap,\n        strokeMiterLimit: miterLimit,\n      }\n    }\n\n    static pointsToStroke(points, style, arcDivisions, minDistance) {\n      // Generates a stroke with some width around the given path.\n      // The path can be open or closed (last point equals to first point)\n      // Param points: Array of Vector2D (the path). Minimum 2 points.\n      // Param style: Object with SVG properties as returned by SVGLoader.getStrokeStyle(), or SVGLoader.parse() in the path.userData.style object\n      // Params arcDivisions: Arc divisions for round joins and endcaps. (Optional)\n      // Param minDistance: Points closer to this distance will be merged. (Optional)\n      // Returns BufferGeometry with stroke triangles (In plane z = 0). UV coordinates are generated ('u' along path. 'v' across it, from left to right)\n\n      const vertices = []\n      const normals = []\n      const uvs = []\n\n      if (SVGLoader.pointsToStrokeWithBuffers(points, style, arcDivisions, minDistance, vertices, normals, uvs) === 0) {\n        return null\n      }\n\n      const geometry = new BufferGeometry()\n      geometry.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n      geometry.setAttribute('normal', new Float32BufferAttribute(normals, 3))\n      geometry.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n\n      return geometry\n    }\n\n    static pointsToStrokeWithBuffers(points, style, arcDivisions, minDistance, vertices, normals, uvs, vertexOffset) {\n      // This function can be called to update existing arrays or buffers.\n      // Accepts same parameters as pointsToStroke, plus the buffers and optional offset.\n      // Param vertexOffset: Offset vertices to start writing in the buffers (3 elements/vertex for vertices and normals, and 2 elements/vertex for uvs)\n      // Returns number of written vertices / normals / uvs pairs\n      // if 'vertices' parameter is undefined no triangles will be generated, but the returned vertices count will still be valid (useful to preallocate the buffers)\n      // 'normals' and 'uvs' buffers are optional\n\n      const tempV2_1 = new Vector2()\n      const tempV2_2 = new Vector2()\n      const tempV2_3 = new Vector2()\n      const tempV2_4 = new Vector2()\n      const tempV2_5 = new Vector2()\n      const tempV2_6 = new Vector2()\n      const tempV2_7 = new Vector2()\n      const lastPointL = new Vector2()\n      const lastPointR = new Vector2()\n      const point0L = new Vector2()\n      const point0R = new Vector2()\n      const currentPointL = new Vector2()\n      const currentPointR = new Vector2()\n      const nextPointL = new Vector2()\n      const nextPointR = new Vector2()\n      const innerPoint = new Vector2()\n      const outerPoint = new Vector2()\n\n      arcDivisions = arcDivisions !== undefined ? arcDivisions : 12\n      minDistance = minDistance !== undefined ? minDistance : 0.001\n      vertexOffset = vertexOffset !== undefined ? vertexOffset : 0\n\n      // First ensure there are no duplicated points\n      points = removeDuplicatedPoints(points)\n\n      const numPoints = points.length\n\n      if (numPoints < 2) return 0\n\n      const isClosed = points[0].equals(points[numPoints - 1])\n\n      let currentPoint\n      let previousPoint = points[0]\n      let nextPoint\n\n      const strokeWidth2 = style.strokeWidth / 2\n\n      const deltaU = 1 / (numPoints - 1)\n      let u0 = 0,\n        u1\n\n      let innerSideModified\n      let joinIsOnLeftSide\n      let isMiter\n      let initialJoinIsOnLeftSide = false\n\n      let numVertices = 0\n      let currentCoordinate = vertexOffset * 3\n      let currentCoordinateUV = vertexOffset * 2\n\n      // Get initial left and right stroke points\n      getNormal(points[0], points[1], tempV2_1).multiplyScalar(strokeWidth2)\n      lastPointL.copy(points[0]).sub(tempV2_1)\n      lastPointR.copy(points[0]).add(tempV2_1)\n      point0L.copy(lastPointL)\n      point0R.copy(lastPointR)\n\n      for (let iPoint = 1; iPoint < numPoints; iPoint++) {\n        currentPoint = points[iPoint]\n\n        // Get next point\n        if (iPoint === numPoints - 1) {\n          if (isClosed) {\n            // Skip duplicated initial point\n            nextPoint = points[1]\n          } else nextPoint = undefined\n        } else {\n          nextPoint = points[iPoint + 1]\n        }\n\n        // Normal of previous segment in tempV2_1\n        const normal1 = tempV2_1\n        getNormal(previousPoint, currentPoint, normal1)\n\n        tempV2_3.copy(normal1).multiplyScalar(strokeWidth2)\n        currentPointL.copy(currentPoint).sub(tempV2_3)\n        currentPointR.copy(currentPoint).add(tempV2_3)\n\n        u1 = u0 + deltaU\n\n        innerSideModified = false\n\n        if (nextPoint !== undefined) {\n          // Normal of next segment in tempV2_2\n          getNormal(currentPoint, nextPoint, tempV2_2)\n\n          tempV2_3.copy(tempV2_2).multiplyScalar(strokeWidth2)\n          nextPointL.copy(currentPoint).sub(tempV2_3)\n          nextPointR.copy(currentPoint).add(tempV2_3)\n\n          joinIsOnLeftSide = true\n          tempV2_3.subVectors(nextPoint, previousPoint)\n          if (normal1.dot(tempV2_3) < 0) {\n            joinIsOnLeftSide = false\n          }\n\n          if (iPoint === 1) initialJoinIsOnLeftSide = joinIsOnLeftSide\n\n          tempV2_3.subVectors(nextPoint, currentPoint)\n          tempV2_3.normalize()\n          const dot = Math.abs(normal1.dot(tempV2_3))\n\n          // If path is straight, don't create join\n          if (dot > Number.EPSILON) {\n            // Compute inner and outer segment intersections\n            const miterSide = strokeWidth2 / dot\n            tempV2_3.multiplyScalar(-miterSide)\n            tempV2_4.subVectors(currentPoint, previousPoint)\n            tempV2_5.copy(tempV2_4).setLength(miterSide).add(tempV2_3)\n            innerPoint.copy(tempV2_5).negate()\n            const miterLength2 = tempV2_5.length()\n            const segmentLengthPrev = tempV2_4.length()\n            tempV2_4.divideScalar(segmentLengthPrev)\n            tempV2_6.subVectors(nextPoint, currentPoint)\n            const segmentLengthNext = tempV2_6.length()\n            tempV2_6.divideScalar(segmentLengthNext)\n            // Check that previous and next segments doesn't overlap with the innerPoint of intersection\n            if (tempV2_4.dot(innerPoint) < segmentLengthPrev && tempV2_6.dot(innerPoint) < segmentLengthNext) {\n              innerSideModified = true\n            }\n\n            outerPoint.copy(tempV2_5).add(currentPoint)\n            innerPoint.add(currentPoint)\n\n            isMiter = false\n\n            if (innerSideModified) {\n              if (joinIsOnLeftSide) {\n                nextPointR.copy(innerPoint)\n                currentPointR.copy(innerPoint)\n              } else {\n                nextPointL.copy(innerPoint)\n                currentPointL.copy(innerPoint)\n              }\n            } else {\n              // The segment triangles are generated here if there was overlapping\n\n              makeSegmentTriangles()\n            }\n\n            switch (style.strokeLineJoin) {\n              case 'bevel':\n                makeSegmentWithBevelJoin(joinIsOnLeftSide, innerSideModified, u1)\n\n                break\n\n              case 'round':\n                // Segment triangles\n\n                createSegmentTrianglesWithMiddleSection(joinIsOnLeftSide, innerSideModified)\n\n                // Join triangles\n\n                if (joinIsOnLeftSide) {\n                  makeCircularSector(currentPoint, currentPointL, nextPointL, u1, 0)\n                } else {\n                  makeCircularSector(currentPoint, nextPointR, currentPointR, u1, 1)\n                }\n\n                break\n\n              case 'miter':\n              case 'miter-clip':\n              default:\n                const miterFraction = (strokeWidth2 * style.strokeMiterLimit) / miterLength2\n\n                if (miterFraction < 1) {\n                  // The join miter length exceeds the miter limit\n\n                  if (style.strokeLineJoin !== 'miter-clip') {\n                    makeSegmentWithBevelJoin(joinIsOnLeftSide, innerSideModified, u1)\n                    break\n                  } else {\n                    // Segment triangles\n\n                    createSegmentTrianglesWithMiddleSection(joinIsOnLeftSide, innerSideModified)\n\n                    // Miter-clip join triangles\n\n                    if (joinIsOnLeftSide) {\n                      tempV2_6.subVectors(outerPoint, currentPointL).multiplyScalar(miterFraction).add(currentPointL)\n                      tempV2_7.subVectors(outerPoint, nextPointL).multiplyScalar(miterFraction).add(nextPointL)\n\n                      addVertex(currentPointL, u1, 0)\n                      addVertex(tempV2_6, u1, 0)\n                      addVertex(currentPoint, u1, 0.5)\n\n                      addVertex(currentPoint, u1, 0.5)\n                      addVertex(tempV2_6, u1, 0)\n                      addVertex(tempV2_7, u1, 0)\n\n                      addVertex(currentPoint, u1, 0.5)\n                      addVertex(tempV2_7, u1, 0)\n                      addVertex(nextPointL, u1, 0)\n                    } else {\n                      tempV2_6.subVectors(outerPoint, currentPointR).multiplyScalar(miterFraction).add(currentPointR)\n                      tempV2_7.subVectors(outerPoint, nextPointR).multiplyScalar(miterFraction).add(nextPointR)\n\n                      addVertex(currentPointR, u1, 1)\n                      addVertex(tempV2_6, u1, 1)\n                      addVertex(currentPoint, u1, 0.5)\n\n                      addVertex(currentPoint, u1, 0.5)\n                      addVertex(tempV2_6, u1, 1)\n                      addVertex(tempV2_7, u1, 1)\n\n                      addVertex(currentPoint, u1, 0.5)\n                      addVertex(tempV2_7, u1, 1)\n                      addVertex(nextPointR, u1, 1)\n                    }\n                  }\n                } else {\n                  // Miter join segment triangles\n\n                  if (innerSideModified) {\n                    // Optimized segment + join triangles\n\n                    if (joinIsOnLeftSide) {\n                      addVertex(lastPointR, u0, 1)\n                      addVertex(lastPointL, u0, 0)\n                      addVertex(outerPoint, u1, 0)\n\n                      addVertex(lastPointR, u0, 1)\n                      addVertex(outerPoint, u1, 0)\n                      addVertex(innerPoint, u1, 1)\n                    } else {\n                      addVertex(lastPointR, u0, 1)\n                      addVertex(lastPointL, u0, 0)\n                      addVertex(outerPoint, u1, 1)\n\n                      addVertex(lastPointL, u0, 0)\n                      addVertex(innerPoint, u1, 0)\n                      addVertex(outerPoint, u1, 1)\n                    }\n\n                    if (joinIsOnLeftSide) {\n                      nextPointL.copy(outerPoint)\n                    } else {\n                      nextPointR.copy(outerPoint)\n                    }\n                  } else {\n                    // Add extra miter join triangles\n\n                    if (joinIsOnLeftSide) {\n                      addVertex(currentPointL, u1, 0)\n                      addVertex(outerPoint, u1, 0)\n                      addVertex(currentPoint, u1, 0.5)\n\n                      addVertex(currentPoint, u1, 0.5)\n                      addVertex(outerPoint, u1, 0)\n                      addVertex(nextPointL, u1, 0)\n                    } else {\n                      addVertex(currentPointR, u1, 1)\n                      addVertex(outerPoint, u1, 1)\n                      addVertex(currentPoint, u1, 0.5)\n\n                      addVertex(currentPoint, u1, 0.5)\n                      addVertex(outerPoint, u1, 1)\n                      addVertex(nextPointR, u1, 1)\n                    }\n                  }\n\n                  isMiter = true\n                }\n\n                break\n            }\n          } else {\n            // The segment triangles are generated here when two consecutive points are collinear\n\n            makeSegmentTriangles()\n          }\n        } else {\n          // The segment triangles are generated here if it is the ending segment\n\n          makeSegmentTriangles()\n        }\n\n        if (!isClosed && iPoint === numPoints - 1) {\n          // Start line endcap\n          addCapGeometry(points[0], point0L, point0R, joinIsOnLeftSide, true, u0)\n        }\n\n        // Increment loop variables\n\n        u0 = u1\n\n        previousPoint = currentPoint\n\n        lastPointL.copy(nextPointL)\n        lastPointR.copy(nextPointR)\n      }\n\n      if (!isClosed) {\n        // Ending line endcap\n        addCapGeometry(currentPoint, currentPointL, currentPointR, joinIsOnLeftSide, false, u1)\n      } else if (innerSideModified && vertices) {\n        // Modify path first segment vertices to adjust to the segments inner and outer intersections\n\n        let lastOuter = outerPoint\n        let lastInner = innerPoint\n\n        if (initialJoinIsOnLeftSide !== joinIsOnLeftSide) {\n          lastOuter = innerPoint\n          lastInner = outerPoint\n        }\n\n        if (joinIsOnLeftSide) {\n          if (isMiter || initialJoinIsOnLeftSide) {\n            lastInner.toArray(vertices, 0 * 3)\n            lastInner.toArray(vertices, 3 * 3)\n\n            if (isMiter) {\n              lastOuter.toArray(vertices, 1 * 3)\n            }\n          }\n        } else {\n          if (isMiter || !initialJoinIsOnLeftSide) {\n            lastInner.toArray(vertices, 1 * 3)\n            lastInner.toArray(vertices, 3 * 3)\n\n            if (isMiter) {\n              lastOuter.toArray(vertices, 0 * 3)\n            }\n          }\n        }\n      }\n\n      return numVertices\n\n      // -- End of algorithm\n\n      // -- Functions\n\n      function getNormal(p1, p2, result) {\n        result.subVectors(p2, p1)\n        return result.set(-result.y, result.x).normalize()\n      }\n\n      function addVertex(position, u, v) {\n        if (vertices) {\n          vertices[currentCoordinate] = position.x\n          vertices[currentCoordinate + 1] = position.y\n          vertices[currentCoordinate + 2] = 0\n\n          if (normals) {\n            normals[currentCoordinate] = 0\n            normals[currentCoordinate + 1] = 0\n            normals[currentCoordinate + 2] = 1\n          }\n\n          currentCoordinate += 3\n\n          if (uvs) {\n            uvs[currentCoordinateUV] = u\n            uvs[currentCoordinateUV + 1] = v\n\n            currentCoordinateUV += 2\n          }\n        }\n\n        numVertices += 3\n      }\n\n      function makeCircularSector(center, p1, p2, u, v) {\n        // param p1, p2: Points in the circle arc.\n        // p1 and p2 are in clockwise direction.\n\n        tempV2_1.copy(p1).sub(center).normalize()\n        tempV2_2.copy(p2).sub(center).normalize()\n\n        let angle = Math.PI\n        const dot = tempV2_1.dot(tempV2_2)\n        if (Math.abs(dot) < 1) angle = Math.abs(Math.acos(dot))\n\n        angle /= arcDivisions\n\n        tempV2_3.copy(p1)\n\n        for (let i = 0, il = arcDivisions - 1; i < il; i++) {\n          tempV2_4.copy(tempV2_3).rotateAround(center, angle)\n\n          addVertex(tempV2_3, u, v)\n          addVertex(tempV2_4, u, v)\n          addVertex(center, u, 0.5)\n\n          tempV2_3.copy(tempV2_4)\n        }\n\n        addVertex(tempV2_4, u, v)\n        addVertex(p2, u, v)\n        addVertex(center, u, 0.5)\n      }\n\n      function makeSegmentTriangles() {\n        addVertex(lastPointR, u0, 1)\n        addVertex(lastPointL, u0, 0)\n        addVertex(currentPointL, u1, 0)\n\n        addVertex(lastPointR, u0, 1)\n        addVertex(currentPointL, u1, 0)\n        addVertex(currentPointR, u1, 1)\n      }\n\n      function makeSegmentWithBevelJoin(joinIsOnLeftSide, innerSideModified, u) {\n        if (innerSideModified) {\n          // Optimized segment + bevel triangles\n\n          if (joinIsOnLeftSide) {\n            // Path segments triangles\n\n            addVertex(lastPointR, u0, 1)\n            addVertex(lastPointL, u0, 0)\n            addVertex(currentPointL, u1, 0)\n\n            addVertex(lastPointR, u0, 1)\n            addVertex(currentPointL, u1, 0)\n            addVertex(innerPoint, u1, 1)\n\n            // Bevel join triangle\n\n            addVertex(currentPointL, u, 0)\n            addVertex(nextPointL, u, 0)\n            addVertex(innerPoint, u, 0.5)\n          } else {\n            // Path segments triangles\n\n            addVertex(lastPointR, u0, 1)\n            addVertex(lastPointL, u0, 0)\n            addVertex(currentPointR, u1, 1)\n\n            addVertex(lastPointL, u0, 0)\n            addVertex(innerPoint, u1, 0)\n            addVertex(currentPointR, u1, 1)\n\n            // Bevel join triangle\n\n            addVertex(currentPointR, u, 1)\n            addVertex(innerPoint, u, 0)\n            addVertex(nextPointR, u, 1)\n          }\n        } else {\n          // Bevel join triangle. The segment triangles are done in the main loop\n\n          if (joinIsOnLeftSide) {\n            addVertex(currentPointL, u, 0)\n            addVertex(nextPointL, u, 0)\n            addVertex(currentPoint, u, 0.5)\n          } else {\n            addVertex(currentPointR, u, 1)\n            addVertex(nextPointR, u, 0)\n            addVertex(currentPoint, u, 0.5)\n          }\n        }\n      }\n\n      function createSegmentTrianglesWithMiddleSection(joinIsOnLeftSide, innerSideModified) {\n        if (innerSideModified) {\n          if (joinIsOnLeftSide) {\n            addVertex(lastPointR, u0, 1)\n            addVertex(lastPointL, u0, 0)\n            addVertex(currentPointL, u1, 0)\n\n            addVertex(lastPointR, u0, 1)\n            addVertex(currentPointL, u1, 0)\n            addVertex(innerPoint, u1, 1)\n\n            addVertex(currentPointL, u0, 0)\n            addVertex(currentPoint, u1, 0.5)\n            addVertex(innerPoint, u1, 1)\n\n            addVertex(currentPoint, u1, 0.5)\n            addVertex(nextPointL, u0, 0)\n            addVertex(innerPoint, u1, 1)\n          } else {\n            addVertex(lastPointR, u0, 1)\n            addVertex(lastPointL, u0, 0)\n            addVertex(currentPointR, u1, 1)\n\n            addVertex(lastPointL, u0, 0)\n            addVertex(innerPoint, u1, 0)\n            addVertex(currentPointR, u1, 1)\n\n            addVertex(currentPointR, u0, 1)\n            addVertex(innerPoint, u1, 0)\n            addVertex(currentPoint, u1, 0.5)\n\n            addVertex(currentPoint, u1, 0.5)\n            addVertex(innerPoint, u1, 0)\n            addVertex(nextPointR, u0, 1)\n          }\n        }\n      }\n\n      function addCapGeometry(center, p1, p2, joinIsOnLeftSide, start, u) {\n        // param center: End point of the path\n        // param p1, p2: Left and right cap points\n\n        switch (style.strokeLineCap) {\n          case 'round':\n            if (start) {\n              makeCircularSector(center, p2, p1, u, 0.5)\n            } else {\n              makeCircularSector(center, p1, p2, u, 0.5)\n            }\n\n            break\n\n          case 'square':\n            if (start) {\n              tempV2_1.subVectors(p1, center)\n              tempV2_2.set(tempV2_1.y, -tempV2_1.x)\n\n              tempV2_3.addVectors(tempV2_1, tempV2_2).add(center)\n              tempV2_4.subVectors(tempV2_2, tempV2_1).add(center)\n\n              // Modify already existing vertices\n              if (joinIsOnLeftSide) {\n                tempV2_3.toArray(vertices, 1 * 3)\n                tempV2_4.toArray(vertices, 0 * 3)\n                tempV2_4.toArray(vertices, 3 * 3)\n              } else {\n                tempV2_3.toArray(vertices, 1 * 3)\n                // using tempV2_4 to update 3rd vertex if the uv.y of 3rd vertex is 1\n                uvs[3 * 2 + 1] === 1 ? tempV2_4.toArray(vertices, 3 * 3) : tempV2_3.toArray(vertices, 3 * 3)\n                tempV2_4.toArray(vertices, 0 * 3)\n              }\n            } else {\n              tempV2_1.subVectors(p2, center)\n              tempV2_2.set(tempV2_1.y, -tempV2_1.x)\n\n              tempV2_3.addVectors(tempV2_1, tempV2_2).add(center)\n              tempV2_4.subVectors(tempV2_2, tempV2_1).add(center)\n\n              const vl = vertices.length\n\n              // Modify already existing vertices\n              if (joinIsOnLeftSide) {\n                tempV2_3.toArray(vertices, vl - 1 * 3)\n                tempV2_4.toArray(vertices, vl - 2 * 3)\n                tempV2_4.toArray(vertices, vl - 4 * 3)\n              } else {\n                tempV2_4.toArray(vertices, vl - 2 * 3)\n                tempV2_3.toArray(vertices, vl - 1 * 3)\n                tempV2_4.toArray(vertices, vl - 4 * 3)\n              }\n            }\n\n            break\n\n          case 'butt':\n          default:\n            // Nothing to do here\n            break\n        }\n      }\n\n      function removeDuplicatedPoints(points) {\n        // Creates a new array if necessary with duplicated points removed.\n        // This does not remove duplicated initial and ending points of a closed path.\n\n        let dupPoints = false\n        for (let i = 1, n = points.length - 1; i < n; i++) {\n          if (points[i].distanceTo(points[i + 1]) < minDistance) {\n            dupPoints = true\n            break\n          }\n        }\n\n        if (!dupPoints) return points\n\n        const newPoints = []\n        newPoints.push(points[0])\n\n        for (let i = 1, n = points.length - 1; i < n; i++) {\n          if (points[i].distanceTo(points[i + 1]) >= minDistance) {\n            newPoints.push(points[i])\n          }\n        }\n\n        newPoints.push(points[points.length - 1])\n\n        return newPoints\n      }\n    }\n  }\n\n  return SVGLoader\n})()\n\nexport { SVGLoader }\n"], "mappings": ";AAeA,MAAMA,eAAA,GAAkB;AAEnB,MAACC,SAAA,GAA6B,sBAAM;EACvC,MAAMC,UAAA,SAAkBC,MAAA,CAAO;IAC7BC,YAAYC,OAAA,EAAS;MACnB,MAAMA,OAAO;MAGb,KAAKC,UAAA,GAAa;MAGlB,KAAKC,WAAA,GAAc;IACpB;IAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;MACrC,MAAMC,KAAA,GAAQ;MAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAWF,KAAA,CAAMR,OAAO;MAC3CS,MAAA,CAAOE,OAAA,CAAQH,KAAA,CAAMI,IAAI;MACzBH,MAAA,CAAOI,gBAAA,CAAiBL,KAAA,CAAMM,aAAa;MAC3CL,MAAA,CAAOM,kBAAA,CAAmBP,KAAA,CAAMQ,eAAe;MAC/CP,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUa,IAAA,EAAM;QACd,IAAI;UACFZ,MAAA,CAAOG,KAAA,CAAMU,KAAA,CAAMD,IAAI,CAAC;QACzB,SAAQE,CAAA,EAAP;UACA,IAAIZ,OAAA,EAAS;YACXA,OAAA,CAAQY,CAAC;UACvB,OAAmB;YACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;UAChB;UAEDX,KAAA,CAAMR,OAAA,CAAQsB,SAAA,CAAUlB,GAAG;QAC5B;MACF,GACDE,UAAA,EACAC,OACD;IACF;IAEDW,MAAMD,IAAA,EAAM;MACV,MAAMT,KAAA,GAAQ;MAEd,SAASe,UAAUC,IAAA,EAAMC,KAAA,EAAO;QAC9B,IAAID,IAAA,CAAKE,QAAA,KAAa,GAAG;QAEzB,MAAMC,SAAA,GAAYC,gBAAA,CAAiBJ,IAAI;QAEvC,IAAIK,UAAA,GAAa;QAEjB,IAAIjB,IAAA,GAAO;QAEX,QAAQY,IAAA,CAAKM,QAAA;UACX,KAAK;YACHL,KAAA,GAAQM,UAAA,CAAWP,IAAA,EAAMC,KAAK;YAC9B;UAEF,KAAK;YACHO,kBAAA,CAAmBR,IAAI;YACvB;UAEF,KAAK;YACHC,KAAA,GAAQM,UAAA,CAAWP,IAAA,EAAMC,KAAK;YAC9B;UAEF,KAAK;YACHA,KAAA,GAAQM,UAAA,CAAWP,IAAA,EAAMC,KAAK;YAC9B,IAAID,IAAA,CAAKS,YAAA,CAAa,GAAG,GAAGrB,IAAA,GAAOsB,aAAA,CAAcV,IAAI;YACrD;UAEF,KAAK;YACHC,KAAA,GAAQM,UAAA,CAAWP,IAAA,EAAMC,KAAK;YAC9Bb,IAAA,GAAOuB,aAAA,CAAcX,IAAI;YACzB;UAEF,KAAK;YACHC,KAAA,GAAQM,UAAA,CAAWP,IAAA,EAAMC,KAAK;YAC9Bb,IAAA,GAAOwB,gBAAA,CAAiBZ,IAAI;YAC5B;UAEF,KAAK;YACHC,KAAA,GAAQM,UAAA,CAAWP,IAAA,EAAMC,KAAK;YAC9Bb,IAAA,GAAOyB,iBAAA,CAAkBb,IAAI;YAC7B;UAEF,KAAK;YACHC,KAAA,GAAQM,UAAA,CAAWP,IAAA,EAAMC,KAAK;YAC9Bb,IAAA,GAAO0B,eAAA,CAAgBd,IAAI;YAC3B;UAEF,KAAK;YACHC,KAAA,GAAQM,UAAA,CAAWP,IAAA,EAAMC,KAAK;YAC9Bb,IAAA,GAAO2B,gBAAA,CAAiBf,IAAI;YAC5B;UAEF,KAAK;YACHC,KAAA,GAAQM,UAAA,CAAWP,IAAA,EAAMC,KAAK;YAC9Bb,IAAA,GAAO4B,aAAA,CAAchB,IAAI;YACzB;UAEF,KAAK;YACHK,UAAA,GAAa;YACb;UAEF,KAAK;YACHJ,KAAA,GAAQM,UAAA,CAAWP,IAAA,EAAMC,KAAK;YAE9B,MAAMgB,IAAA,GAAOjB,IAAA,CAAKkB,cAAA,CAAe,gCAAgC,MAAM,KAAK;YAC5E,MAAMC,UAAA,GAAaF,IAAA,CAAKG,SAAA,CAAU,CAAC;YACnC,MAAMC,QAAA,GAAWrB,IAAA,CAAKsB,eAAA,CAAgBC,cAAA,CAAeJ,UAAU;YAC/D,IAAIE,QAAA,EAAU;cACZtB,SAAA,CAAUsB,QAAA,EAAUpB,KAAK;YACvC,OAAmB;cACLL,OAAA,CAAQ4B,IAAA,CAAK,4DAA4DL,UAAU;YACpF;YAED;QAIH;QAED,IAAI/B,IAAA,EAAM;UACR,IAAIa,KAAA,CAAMwB,IAAA,KAAS,UAAaxB,KAAA,CAAMwB,IAAA,KAAS,QAAQ;YACrDrC,IAAA,CAAKsC,KAAA,CAAMC,QAAA,CAAS1B,KAAA,CAAMwB,IAAA,EAAMtD,eAAe;UAChD;UAEDyD,aAAA,CAAcxC,IAAA,EAAMyC,gBAAgB;UAEpCC,KAAA,CAAMC,IAAA,CAAK3C,IAAI;UAEfA,IAAA,CAAK4C,QAAA,GAAW;YAAEhC,IAAA;YAAYC;UAAc;QAC7C;QAED,MAAMgC,UAAA,GAAajC,IAAA,CAAKiC,UAAA;QAExB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAID,UAAA,CAAWE,MAAA,EAAQD,CAAA,IAAK;UAC1C,MAAME,KAAA,GAAOH,UAAA,CAAWC,CAAC;UAEzB,IAAI7B,UAAA,IAAc+B,KAAA,CAAK9B,QAAA,KAAa,WAAW8B,KAAA,CAAK9B,QAAA,KAAa,QAAQ;YAIvE;UACD;UAEDP,SAAA,CAAUqC,KAAA,EAAMnC,KAAK;QACtB;QAED,IAAIE,SAAA,EAAW;UACbkC,cAAA,CAAeC,GAAA,CAAK;UAEpB,IAAID,cAAA,CAAeF,MAAA,GAAS,GAAG;YAC7BN,gBAAA,CAAiBU,IAAA,CAAKF,cAAA,CAAeA,cAAA,CAAeF,MAAA,GAAS,CAAC,CAAC;UAC3E,OAAiB;YACLN,gBAAA,CAAiBW,QAAA,CAAU;UAC5B;QACF;MACF;MAED,SAAS9B,cAAcV,IAAA,EAAM;QAC3B,MAAMZ,IAAA,GAAO,IAAIqD,SAAA,CAAW;QAE5B,MAAMC,KAAA,GAAQ,IAAIC,OAAA,CAAS;QAC3B,MAAMC,OAAA,GAAU,IAAID,OAAA,CAAS;QAE7B,MAAME,UAAA,GAAa,IAAIF,OAAA,CAAS;QAChC,IAAIG,YAAA,GAAe;QACnB,IAAIC,eAAA,GAAkB;QAEtB,MAAMC,CAAA,GAAIhD,IAAA,CAAKiD,YAAA,CAAa,GAAG;QAE/B,IAAID,CAAA,KAAM,MAAMA,CAAA,KAAM,QAAQ,OAAO;QAIrC,MAAME,QAAA,GAAWF,CAAA,CAAEG,KAAA,CAAM,sBAAsB;QAE/C,SAASjB,CAAA,GAAI,GAAGkB,CAAA,GAAIF,QAAA,CAASf,MAAA,EAAQD,CAAA,GAAIkB,CAAA,EAAGlB,CAAA,IAAK;UAC/C,MAAMmB,OAAA,GAAUH,QAAA,CAAShB,CAAC;UAE1B,MAAMoB,IAAA,GAAOD,OAAA,CAAQE,MAAA,CAAO,CAAC;UAC7B,MAAMC,KAAA,GAAOH,OAAA,CAAQI,KAAA,CAAM,CAAC,EAAEC,IAAA,CAAM;UAEpC,IAAIZ,YAAA,KAAiB,MAAM;YACzBC,eAAA,GAAkB;YAClBD,YAAA,GAAe;UAChB;UAED,IAAIa,OAAA;UAEJ,QAAQL,IAAA;YACN,KAAK;cACHK,OAAA,GAAUC,WAAA,CAAYJ,KAAI;cAC1B,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;gBACnDnB,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACvBnB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACvBjB,OAAA,CAAQmB,CAAA,GAAIrB,KAAA,CAAMqB,CAAA;gBAClBnB,OAAA,CAAQoB,CAAA,GAAItB,KAAA,CAAMsB,CAAA;gBAElB,IAAIH,CAAA,KAAM,GAAG;kBACXzE,IAAA,CAAK6E,MAAA,CAAOvB,KAAA,CAAMqB,CAAA,EAAGrB,KAAA,CAAMsB,CAAC;gBAC9C,OAAuB;kBACL5E,IAAA,CAAK8E,MAAA,CAAOxB,KAAA,CAAMqB,CAAA,EAAGrB,KAAA,CAAMsB,CAAC;gBAC7B;gBAED,IAAIH,CAAA,KAAM,GAAGhB,UAAA,CAAWN,IAAA,CAAKG,KAAK;cACnC;cAED;YAEF,KAAK;cACHiB,OAAA,GAAUC,WAAA,CAAYJ,KAAI;cAE1B,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;gBAChDnB,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAC;gBACnBjB,OAAA,CAAQmB,CAAA,GAAIrB,KAAA,CAAMqB,CAAA;gBAClBnB,OAAA,CAAQoB,CAAA,GAAItB,KAAA,CAAMsB,CAAA;gBAClB5E,IAAA,CAAK8E,MAAA,CAAOxB,KAAA,CAAMqB,CAAA,EAAGrB,KAAA,CAAMsB,CAAC;gBAE5B,IAAIH,CAAA,KAAM,KAAKd,eAAA,KAAoB,MAAMF,UAAA,CAAWN,IAAA,CAAKG,KAAK;cAC/D;cAED;YAEF,KAAK;cACHiB,OAAA,GAAUC,WAAA,CAAYJ,KAAI;cAE1B,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;gBAChDnB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAC;gBACnBjB,OAAA,CAAQmB,CAAA,GAAIrB,KAAA,CAAMqB,CAAA;gBAClBnB,OAAA,CAAQoB,CAAA,GAAItB,KAAA,CAAMsB,CAAA;gBAClB5E,IAAA,CAAK8E,MAAA,CAAOxB,KAAA,CAAMqB,CAAA,EAAGrB,KAAA,CAAMsB,CAAC;gBAE5B,IAAIH,CAAA,KAAM,KAAKd,eAAA,KAAoB,MAAMF,UAAA,CAAWN,IAAA,CAAKG,KAAK;cAC/D;cAED;YAEF,KAAK;cACHiB,OAAA,GAAUC,WAAA,CAAYJ,KAAI;cAE1B,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;gBACnDnB,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACvBnB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACvBjB,OAAA,CAAQmB,CAAA,GAAIrB,KAAA,CAAMqB,CAAA;gBAClBnB,OAAA,CAAQoB,CAAA,GAAItB,KAAA,CAAMsB,CAAA;gBAClB5E,IAAA,CAAK8E,MAAA,CAAOxB,KAAA,CAAMqB,CAAA,EAAGrB,KAAA,CAAMsB,CAAC;gBAE5B,IAAIH,CAAA,KAAM,KAAKd,eAAA,KAAoB,MAAMF,UAAA,CAAWN,IAAA,CAAKG,KAAK;cAC/D;cAED;YAEF,KAAK;cACHiB,OAAA,GAAUC,WAAA,CAAYJ,KAAI;cAE1B,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;gBACnDzE,IAAA,CAAK+E,aAAA,CACHR,OAAA,CAAQE,CAAA,GAAI,CAAC,GACbF,OAAA,CAAQE,CAAA,GAAI,CAAC,GACbF,OAAA,CAAQE,CAAA,GAAI,CAAC,GACbF,OAAA,CAAQE,CAAA,GAAI,CAAC,GACbF,OAAA,CAAQE,CAAA,GAAI,CAAC,GACbF,OAAA,CAAQE,CAAA,GAAI,CAAC,CACd;gBACDjB,OAAA,CAAQmB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACzBjB,OAAA,CAAQoB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACzBnB,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACvBnB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBAEvB,IAAIA,CAAA,KAAM,KAAKd,eAAA,KAAoB,MAAMF,UAAA,CAAWN,IAAA,CAAKG,KAAK;cAC/D;cAED;YAEF,KAAK;cACHiB,OAAA,GAAUC,WAAA,CAAYJ,KAAI;cAE1B,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;gBACnDzE,IAAA,CAAK+E,aAAA,CACHC,aAAA,CAAc1B,KAAA,CAAMqB,CAAA,EAAGnB,OAAA,CAAQmB,CAAC,GAChCK,aAAA,CAAc1B,KAAA,CAAMsB,CAAA,EAAGpB,OAAA,CAAQoB,CAAC,GAChCL,OAAA,CAAQE,CAAA,GAAI,CAAC,GACbF,OAAA,CAAQE,CAAA,GAAI,CAAC,GACbF,OAAA,CAAQE,CAAA,GAAI,CAAC,GACbF,OAAA,CAAQE,CAAA,GAAI,CAAC,CACd;gBACDjB,OAAA,CAAQmB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACzBjB,OAAA,CAAQoB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACzBnB,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACvBnB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBAEvB,IAAIA,CAAA,KAAM,KAAKd,eAAA,KAAoB,MAAMF,UAAA,CAAWN,IAAA,CAAKG,KAAK;cAC/D;cAED;YAEF,KAAK;cACHiB,OAAA,GAAUC,WAAA,CAAYJ,KAAI;cAE1B,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;gBACnDzE,IAAA,CAAKiF,gBAAA,CAAiBV,OAAA,CAAQE,CAAA,GAAI,CAAC,GAAGF,OAAA,CAAQE,CAAA,GAAI,CAAC,GAAGF,OAAA,CAAQE,CAAA,GAAI,CAAC,GAAGF,OAAA,CAAQE,CAAA,GAAI,CAAC,CAAC;gBACpFjB,OAAA,CAAQmB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACzBjB,OAAA,CAAQoB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACzBnB,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACvBnB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBAEvB,IAAIA,CAAA,KAAM,KAAKd,eAAA,KAAoB,MAAMF,UAAA,CAAWN,IAAA,CAAKG,KAAK;cAC/D;cAED;YAEF,KAAK;cACHiB,OAAA,GAAUC,WAAA,CAAYJ,KAAI;cAE1B,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;gBACnD,MAAMS,EAAA,GAAKF,aAAA,CAAc1B,KAAA,CAAMqB,CAAA,EAAGnB,OAAA,CAAQmB,CAAC;gBAC3C,MAAMQ,EAAA,GAAKH,aAAA,CAAc1B,KAAA,CAAMsB,CAAA,EAAGpB,OAAA,CAAQoB,CAAC;gBAC3C5E,IAAA,CAAKiF,gBAAA,CAAiBC,EAAA,EAAIC,EAAA,EAAIZ,OAAA,CAAQE,CAAA,GAAI,CAAC,GAAGF,OAAA,CAAQE,CAAA,GAAI,CAAC,CAAC;gBAC5DjB,OAAA,CAAQmB,CAAA,GAAIO,EAAA;gBACZ1B,OAAA,CAAQoB,CAAA,GAAIO,EAAA;gBACZ7B,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACvBnB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBAEvB,IAAIA,CAAA,KAAM,KAAKd,eAAA,KAAoB,MAAMF,UAAA,CAAWN,IAAA,CAAKG,KAAK;cAC/D;cAED;YAEF,KAAK;cACHiB,OAAA,GAAUC,WAAA,CAAYJ,KAAA,EAAM,CAAC,GAAG,CAAC,GAAG,CAAC;cAErC,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;gBAEnD,IAAIF,OAAA,CAAQE,CAAA,GAAI,CAAC,KAAKnB,KAAA,CAAMqB,CAAA,IAAKJ,OAAA,CAAQE,CAAA,GAAI,CAAC,KAAKnB,KAAA,CAAMsB,CAAA,EAAG;gBAE5D,MAAMQ,KAAA,GAAQ9B,KAAA,CAAM+B,KAAA,CAAO;gBAC3B/B,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACvBnB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACvBjB,OAAA,CAAQmB,CAAA,GAAIrB,KAAA,CAAMqB,CAAA;gBAClBnB,OAAA,CAAQoB,CAAA,GAAItB,KAAA,CAAMsB,CAAA;gBAClBU,eAAA,CACEtF,IAAA,EACAuE,OAAA,CAAQE,CAAC,GACTF,OAAA,CAAQE,CAAA,GAAI,CAAC,GACbF,OAAA,CAAQE,CAAA,GAAI,CAAC,GACbF,OAAA,CAAQE,CAAA,GAAI,CAAC,GACbF,OAAA,CAAQE,CAAA,GAAI,CAAC,GACbW,KAAA,EACA9B,KACD;gBAED,IAAImB,CAAA,KAAM,KAAKd,eAAA,KAAoB,MAAMF,UAAA,CAAWN,IAAA,CAAKG,KAAK;cAC/D;cAED;YAEF,KAAK;cACHiB,OAAA,GAAUC,WAAA,CAAYJ,KAAI;cAE1B,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;gBACnDnB,KAAA,CAAMqB,CAAA,IAAKJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACxBnB,KAAA,CAAMsB,CAAA,IAAKL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACxBjB,OAAA,CAAQmB,CAAA,GAAIrB,KAAA,CAAMqB,CAAA;gBAClBnB,OAAA,CAAQoB,CAAA,GAAItB,KAAA,CAAMsB,CAAA;gBAElB,IAAIH,CAAA,KAAM,GAAG;kBACXzE,IAAA,CAAK6E,MAAA,CAAOvB,KAAA,CAAMqB,CAAA,EAAGrB,KAAA,CAAMsB,CAAC;gBAC9C,OAAuB;kBACL5E,IAAA,CAAK8E,MAAA,CAAOxB,KAAA,CAAMqB,CAAA,EAAGrB,KAAA,CAAMsB,CAAC;gBAC7B;gBAED,IAAIH,CAAA,KAAM,GAAGhB,UAAA,CAAWN,IAAA,CAAKG,KAAK;cACnC;cAED;YAEF,KAAK;cACHiB,OAAA,GAAUC,WAAA,CAAYJ,KAAI;cAE1B,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;gBAChDnB,KAAA,CAAMqB,CAAA,IAAKJ,OAAA,CAAQE,CAAC;gBACpBjB,OAAA,CAAQmB,CAAA,GAAIrB,KAAA,CAAMqB,CAAA;gBAClBnB,OAAA,CAAQoB,CAAA,GAAItB,KAAA,CAAMsB,CAAA;gBAClB5E,IAAA,CAAK8E,MAAA,CAAOxB,KAAA,CAAMqB,CAAA,EAAGrB,KAAA,CAAMsB,CAAC;gBAE5B,IAAIH,CAAA,KAAM,KAAKd,eAAA,KAAoB,MAAMF,UAAA,CAAWN,IAAA,CAAKG,KAAK;cAC/D;cAED;YAEF,KAAK;cACHiB,OAAA,GAAUC,WAAA,CAAYJ,KAAI;cAE1B,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK;gBAChDnB,KAAA,CAAMsB,CAAA,IAAKL,OAAA,CAAQE,CAAC;gBACpBjB,OAAA,CAAQmB,CAAA,GAAIrB,KAAA,CAAMqB,CAAA;gBAClBnB,OAAA,CAAQoB,CAAA,GAAItB,KAAA,CAAMsB,CAAA;gBAClB5E,IAAA,CAAK8E,MAAA,CAAOxB,KAAA,CAAMqB,CAAA,EAAGrB,KAAA,CAAMsB,CAAC;gBAE5B,IAAIH,CAAA,KAAM,KAAKd,eAAA,KAAoB,MAAMF,UAAA,CAAWN,IAAA,CAAKG,KAAK;cAC/D;cAED;YAEF,KAAK;cACHiB,OAAA,GAAUC,WAAA,CAAYJ,KAAI;cAE1B,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;gBACnDnB,KAAA,CAAMqB,CAAA,IAAKJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACxBnB,KAAA,CAAMsB,CAAA,IAAKL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACxBjB,OAAA,CAAQmB,CAAA,GAAIrB,KAAA,CAAMqB,CAAA;gBAClBnB,OAAA,CAAQoB,CAAA,GAAItB,KAAA,CAAMsB,CAAA;gBAClB5E,IAAA,CAAK8E,MAAA,CAAOxB,KAAA,CAAMqB,CAAA,EAAGrB,KAAA,CAAMsB,CAAC;gBAE5B,IAAIH,CAAA,KAAM,KAAKd,eAAA,KAAoB,MAAMF,UAAA,CAAWN,IAAA,CAAKG,KAAK;cAC/D;cAED;YAEF,KAAK;cACHiB,OAAA,GAAUC,WAAA,CAAYJ,KAAI;cAE1B,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;gBACnDzE,IAAA,CAAK+E,aAAA,CACHzB,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC,GACvBnB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC,GACvBnB,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC,GACvBnB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC,GACvBnB,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC,GACvBnB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC,CACxB;gBACDjB,OAAA,CAAQmB,CAAA,GAAIrB,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACnCjB,OAAA,CAAQoB,CAAA,GAAItB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACnCnB,KAAA,CAAMqB,CAAA,IAAKJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACxBnB,KAAA,CAAMsB,CAAA,IAAKL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBAExB,IAAIA,CAAA,KAAM,KAAKd,eAAA,KAAoB,MAAMF,UAAA,CAAWN,IAAA,CAAKG,KAAK;cAC/D;cAED;YAEF,KAAK;cACHiB,OAAA,GAAUC,WAAA,CAAYJ,KAAI;cAE1B,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;gBACnDzE,IAAA,CAAK+E,aAAA,CACHC,aAAA,CAAc1B,KAAA,CAAMqB,CAAA,EAAGnB,OAAA,CAAQmB,CAAC,GAChCK,aAAA,CAAc1B,KAAA,CAAMsB,CAAA,EAAGpB,OAAA,CAAQoB,CAAC,GAChCtB,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC,GACvBnB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC,GACvBnB,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC,GACvBnB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC,CACxB;gBACDjB,OAAA,CAAQmB,CAAA,GAAIrB,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACnCjB,OAAA,CAAQoB,CAAA,GAAItB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACnCnB,KAAA,CAAMqB,CAAA,IAAKJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACxBnB,KAAA,CAAMsB,CAAA,IAAKL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBAExB,IAAIA,CAAA,KAAM,KAAKd,eAAA,KAAoB,MAAMF,UAAA,CAAWN,IAAA,CAAKG,KAAK;cAC/D;cAED;YAEF,KAAK;cACHiB,OAAA,GAAUC,WAAA,CAAYJ,KAAI;cAE1B,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;gBACnDzE,IAAA,CAAKiF,gBAAA,CACH3B,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC,GACvBnB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC,GACvBnB,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC,GACvBnB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC,CACxB;gBACDjB,OAAA,CAAQmB,CAAA,GAAIrB,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACnCjB,OAAA,CAAQoB,CAAA,GAAItB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACnCnB,KAAA,CAAMqB,CAAA,IAAKJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACxBnB,KAAA,CAAMsB,CAAA,IAAKL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBAExB,IAAIA,CAAA,KAAM,KAAKd,eAAA,KAAoB,MAAMF,UAAA,CAAWN,IAAA,CAAKG,KAAK;cAC/D;cAED;YAEF,KAAK;cACHiB,OAAA,GAAUC,WAAA,CAAYJ,KAAI;cAE1B,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;gBACnD,MAAMS,EAAA,GAAKF,aAAA,CAAc1B,KAAA,CAAMqB,CAAA,EAAGnB,OAAA,CAAQmB,CAAC;gBAC3C,MAAMQ,EAAA,GAAKH,aAAA,CAAc1B,KAAA,CAAMsB,CAAA,EAAGpB,OAAA,CAAQoB,CAAC;gBAC3C5E,IAAA,CAAKiF,gBAAA,CAAiBC,EAAA,EAAIC,EAAA,EAAI7B,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC,GAAGnB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC,CAAC;gBAChFjB,OAAA,CAAQmB,CAAA,GAAIO,EAAA;gBACZ1B,OAAA,CAAQoB,CAAA,GAAIO,EAAA;gBACZ7B,KAAA,CAAMqB,CAAA,GAAIrB,KAAA,CAAMqB,CAAA,GAAIJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACjCnB,KAAA,CAAMsB,CAAA,GAAItB,KAAA,CAAMsB,CAAA,GAAIL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBAEjC,IAAIA,CAAA,KAAM,KAAKd,eAAA,KAAoB,MAAMF,UAAA,CAAWN,IAAA,CAAKG,KAAK;cAC/D;cAED;YAEF,KAAK;cACHiB,OAAA,GAAUC,WAAA,CAAYJ,KAAA,EAAM,CAAC,GAAG,CAAC,GAAG,CAAC;cAErC,SAASK,CAAA,GAAI,GAAGC,EAAA,GAAKH,OAAA,CAAQxB,MAAA,EAAQ0B,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;gBAEnD,IAAIF,OAAA,CAAQE,CAAA,GAAI,CAAC,KAAK,KAAKF,OAAA,CAAQE,CAAA,GAAI,CAAC,KAAK,GAAG;gBAEhD,MAAMW,KAAA,GAAQ9B,KAAA,CAAM+B,KAAA,CAAO;gBAC3B/B,KAAA,CAAMqB,CAAA,IAAKJ,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACxBnB,KAAA,CAAMsB,CAAA,IAAKL,OAAA,CAAQE,CAAA,GAAI,CAAC;gBACxBjB,OAAA,CAAQmB,CAAA,GAAIrB,KAAA,CAAMqB,CAAA;gBAClBnB,OAAA,CAAQoB,CAAA,GAAItB,KAAA,CAAMsB,CAAA;gBAClBU,eAAA,CACEtF,IAAA,EACAuE,OAAA,CAAQE,CAAC,GACTF,OAAA,CAAQE,CAAA,GAAI,CAAC,GACbF,OAAA,CAAQE,CAAA,GAAI,CAAC,GACbF,OAAA,CAAQE,CAAA,GAAI,CAAC,GACbF,OAAA,CAAQE,CAAA,GAAI,CAAC,GACbW,KAAA,EACA9B,KACD;gBAED,IAAImB,CAAA,KAAM,KAAKd,eAAA,KAAoB,MAAMF,UAAA,CAAWN,IAAA,CAAKG,KAAK;cAC/D;cAED;YAEF,KAAK;YACL,KAAK;cACHtD,IAAA,CAAKuF,WAAA,CAAYC,SAAA,GAAY;cAE7B,IAAIxF,IAAA,CAAKuF,WAAA,CAAYE,MAAA,CAAO1C,MAAA,GAAS,GAAG;gBAEtCO,KAAA,CAAMH,IAAA,CAAKM,UAAU;gBACrBzD,IAAA,CAAKuF,WAAA,CAAYG,YAAA,CAAavC,IAAA,CAAKG,KAAK;gBACxCI,YAAA,GAAe;cAChB;cAED;YAEF;cACElD,OAAA,CAAQ4B,IAAA,CAAK6B,OAAO;UACvB;UAIDN,eAAA,GAAkB;QACnB;QAED,OAAO3D,IAAA;MACR;MAED,SAASoB,mBAAmBR,IAAA,EAAM;QAChC,IAAI,CAACA,IAAA,CAAK+E,KAAA,IAAS,CAAC/E,IAAA,CAAK+E,KAAA,CAAMC,QAAA,IAAY,CAAChF,IAAA,CAAK+E,KAAA,CAAMC,QAAA,CAAS7C,MAAA,EAAQ;QAExE,SAASD,CAAA,GAAI,GAAGA,CAAA,GAAIlC,IAAA,CAAK+E,KAAA,CAAMC,QAAA,CAAS7C,MAAA,EAAQD,CAAA,IAAK;UACnD,MAAM+C,UAAA,GAAajF,IAAA,CAAK+E,KAAA,CAAMC,QAAA,CAAS9C,CAAC;UAExC,IAAI+C,UAAA,CAAW3B,IAAA,KAAS,GAAG;UAE3B,MAAM4B,YAAA,GAAeD,UAAA,CAAWE,YAAA,CAC7BC,KAAA,CAAM,KAAK,EACXC,MAAA,CAAOC,OAAO,EACdC,GAAA,CAAKC,EAAA,IAAMA,EAAA,CAAE9B,IAAA,CAAI,CAAE;UAEtB,SAASG,CAAA,GAAI,GAAGA,CAAA,GAAIqB,YAAA,CAAa/C,MAAA,EAAQ0B,CAAA,IAAK;YAE5C,MAAM4B,WAAA,GAAcC,MAAA,CAAOC,WAAA,CAAYD,MAAA,CAAOE,OAAA,CAAQX,UAAA,CAAWhF,KAAK,EAAEoF,MAAA,CAAO,CAAC,GAAGQ,CAAC,MAAMA,CAAA,KAAM,EAAE,CAAC;YAEnGC,WAAA,CAAYZ,YAAA,CAAarB,CAAC,CAAC,IAAI6B,MAAA,CAAOK,MAAA,CAAOD,WAAA,CAAYZ,YAAA,CAAarB,CAAC,CAAC,KAAK,IAAI4B,WAAW;UAC7F;QACF;MACF;MAWD,SAASf,gBAAgBtF,IAAA,EAAMkF,EAAA,EAAIC,EAAA,EAAIyB,eAAA,EAAiBC,cAAA,EAAgBC,UAAA,EAAY1B,KAAA,EAAO2B,GAAA,EAAK;QAC9F,IAAI7B,EAAA,IAAM,KAAKC,EAAA,IAAM,GAAG;UAEtBnF,IAAA,CAAK8E,MAAA,CAAOiC,GAAA,CAAIpC,CAAA,EAAGoC,GAAA,CAAInC,CAAC;UACxB;QACD;QAEDgC,eAAA,GAAmBA,eAAA,GAAkBI,IAAA,CAAKC,EAAA,GAAM;QAGhD/B,EAAA,GAAK8B,IAAA,CAAKE,GAAA,CAAIhC,EAAE;QAChBC,EAAA,GAAK6B,IAAA,CAAKE,GAAA,CAAI/B,EAAE;QAGhB,MAAMgC,GAAA,IAAO/B,KAAA,CAAMT,CAAA,GAAIoC,GAAA,CAAIpC,CAAA,IAAK;QAChC,MAAMyC,GAAA,IAAOhC,KAAA,CAAMR,CAAA,GAAImC,GAAA,CAAInC,CAAA,IAAK;QAChC,MAAMyC,GAAA,GAAML,IAAA,CAAKM,GAAA,CAAIV,eAAe,IAAIO,GAAA,GAAMH,IAAA,CAAKO,GAAA,CAAIX,eAAe,IAAIQ,GAAA;QAC1E,MAAMI,GAAA,GAAM,CAACR,IAAA,CAAKO,GAAA,CAAIX,eAAe,IAAIO,GAAA,GAAMH,IAAA,CAAKM,GAAA,CAAIV,eAAe,IAAIQ,GAAA;QAG3E,IAAIK,GAAA,GAAMvC,EAAA,GAAKA,EAAA;QACf,IAAIwC,GAAA,GAAMvC,EAAA,GAAKA,EAAA;QACf,MAAMwC,IAAA,GAAON,GAAA,GAAMA,GAAA;QACnB,MAAMO,IAAA,GAAOJ,GAAA,GAAMA,GAAA;QAGnB,MAAMK,EAAA,GAAKF,IAAA,GAAOF,GAAA,GAAMG,IAAA,GAAOF,GAAA;QAE/B,IAAIG,EAAA,GAAK,GAAG;UAEV,MAAMC,CAAA,GAAId,IAAA,CAAKe,IAAA,CAAKF,EAAE;UACtB3C,EAAA,GAAK4C,CAAA,GAAI5C,EAAA;UACTC,EAAA,GAAK2C,CAAA,GAAI3C,EAAA;UACTsC,GAAA,GAAMvC,EAAA,GAAKA,EAAA;UACXwC,GAAA,GAAMvC,EAAA,GAAKA,EAAA;QACZ;QAED,MAAM6C,EAAA,GAAKP,GAAA,GAAMG,IAAA,GAAOF,GAAA,GAAMC,IAAA;QAC9B,MAAMM,EAAA,IAAMR,GAAA,GAAMC,GAAA,GAAMM,EAAA,IAAMA,EAAA;QAC9B,IAAIE,CAAA,GAAIlB,IAAA,CAAKe,IAAA,CAAKf,IAAA,CAAKmB,GAAA,CAAI,GAAGF,EAAE,CAAC;QACjC,IAAIpB,cAAA,KAAmBC,UAAA,EAAYoB,CAAA,GAAI,CAACA,CAAA;QACxC,MAAME,GAAA,GAAOF,CAAA,GAAIhD,EAAA,GAAKsC,GAAA,GAAOrC,EAAA;QAC7B,MAAMkD,GAAA,GAAO,CAACH,CAAA,GAAI/C,EAAA,GAAKkC,GAAA,GAAOnC,EAAA;QAG9B,MAAMoD,EAAA,GAAKtB,IAAA,CAAKM,GAAA,CAAIV,eAAe,IAAIwB,GAAA,GAAMpB,IAAA,CAAKO,GAAA,CAAIX,eAAe,IAAIyB,GAAA,IAAOjD,KAAA,CAAMT,CAAA,GAAIoC,GAAA,CAAIpC,CAAA,IAAK;QACnG,MAAM4D,EAAA,GAAKvB,IAAA,CAAKO,GAAA,CAAIX,eAAe,IAAIwB,GAAA,GAAMpB,IAAA,CAAKM,GAAA,CAAIV,eAAe,IAAIyB,GAAA,IAAOjD,KAAA,CAAMR,CAAA,GAAImC,GAAA,CAAInC,CAAA,IAAK;QAGnG,MAAM4D,KAAA,GAAQC,QAAA,CAAS,GAAG,IAAIpB,GAAA,GAAMe,GAAA,IAAOlD,EAAA,GAAKsC,GAAA,GAAMa,GAAA,IAAOlD,EAAE;QAC/D,MAAMuD,KAAA,GAAQD,QAAA,EAAUpB,GAAA,GAAMe,GAAA,IAAOlD,EAAA,GAAKsC,GAAA,GAAMa,GAAA,IAAOlD,EAAA,GAAK,CAACkC,GAAA,GAAMe,GAAA,IAAOlD,EAAA,GAAK,CAACsC,GAAA,GAAMa,GAAA,IAAOlD,EAAE,KAAK6B,IAAA,CAAKC,EAAA,GAAK;QAE9GjH,IAAA,CAAKuF,WAAA,CAAYoD,UAAA,CAAWL,EAAA,EAAIC,EAAA,EAAIrD,EAAA,EAAIC,EAAA,EAAIqD,KAAA,EAAOA,KAAA,GAAQE,KAAA,EAAO5B,UAAA,KAAe,GAAGF,eAAe;MACpG;MAED,SAAS6B,SAASG,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAI;QAChC,MAAMC,GAAA,GAAMJ,EAAA,GAAKE,EAAA,GAAKD,EAAA,GAAKE,EAAA;QAC3B,MAAME,GAAA,GAAMjC,IAAA,CAAKe,IAAA,CAAKa,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAE,IAAI7B,IAAA,CAAKe,IAAA,CAAKe,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAE;QACtE,IAAIG,GAAA,GAAMlC,IAAA,CAAKmC,IAAA,CAAKnC,IAAA,CAAKmB,GAAA,CAAI,IAAInB,IAAA,CAAKoC,GAAA,CAAI,GAAGJ,GAAA,GAAMC,GAAG,CAAC,CAAC;QACxD,IAAIL,EAAA,GAAKG,EAAA,GAAKF,EAAA,GAAKC,EAAA,GAAK,GAAGI,GAAA,GAAM,CAACA,GAAA;QAClC,OAAOA,GAAA;MACR;MAMD,SAAS3H,cAAcX,IAAA,EAAM;QAC3B,MAAM+D,CAAA,GAAI0E,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,GAAG,KAAK,CAAC;QACzD,MAAMe,CAAA,GAAIyE,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,GAAG,KAAK,CAAC;QACzD,MAAMqB,EAAA,GAAKmE,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,IAAI,KAAKjD,IAAA,CAAKiD,YAAA,CAAa,IAAI,KAAK,CAAC;QACtF,MAAMsB,EAAA,GAAKkE,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,IAAI,KAAKjD,IAAA,CAAKiD,YAAA,CAAa,IAAI,KAAK,CAAC;QACtF,MAAMyF,CAAA,GAAID,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,OAAO,CAAC;QACxD,MAAM0F,CAAA,GAAIF,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,QAAQ,CAAC;QAIzD,MAAM2F,GAAA,GAAM,IAAI;QAEhB,MAAMxJ,IAAA,GAAO,IAAIqD,SAAA,CAAW;QAG5BrD,IAAA,CAAK6E,MAAA,CAAOF,CAAA,GAAIO,EAAA,EAAIN,CAAC;QAGrB5E,IAAA,CAAK8E,MAAA,CAAOH,CAAA,GAAI2E,CAAA,GAAIpE,EAAA,EAAIN,CAAC;QACzB,IAAIM,EAAA,KAAO,KAAKC,EAAA,KAAO,GAAG;UACxBnF,IAAA,CAAK+E,aAAA,CAAcJ,CAAA,GAAI2E,CAAA,GAAIpE,EAAA,GAAKsE,GAAA,EAAK5E,CAAA,EAAGD,CAAA,GAAI2E,CAAA,EAAG1E,CAAA,GAAIO,EAAA,GAAKqE,GAAA,EAAK7E,CAAA,GAAI2E,CAAA,EAAG1E,CAAA,GAAIO,EAAE;QAC3E;QAGDnF,IAAA,CAAK8E,MAAA,CAAOH,CAAA,GAAI2E,CAAA,EAAG1E,CAAA,GAAI2E,CAAA,GAAIpE,EAAE;QAC7B,IAAID,EAAA,KAAO,KAAKC,EAAA,KAAO,GAAG;UACxBnF,IAAA,CAAK+E,aAAA,CAAcJ,CAAA,GAAI2E,CAAA,EAAG1E,CAAA,GAAI2E,CAAA,GAAIpE,EAAA,GAAKqE,GAAA,EAAK7E,CAAA,GAAI2E,CAAA,GAAIpE,EAAA,GAAKsE,GAAA,EAAK5E,CAAA,GAAI2E,CAAA,EAAG5E,CAAA,GAAI2E,CAAA,GAAIpE,EAAA,EAAIN,CAAA,GAAI2E,CAAC;QACvF;QAGDvJ,IAAA,CAAK8E,MAAA,CAAOH,CAAA,GAAIO,EAAA,EAAIN,CAAA,GAAI2E,CAAC;QACzB,IAAIrE,EAAA,KAAO,KAAKC,EAAA,KAAO,GAAG;UACxBnF,IAAA,CAAK+E,aAAA,CAAcJ,CAAA,GAAIO,EAAA,GAAKsE,GAAA,EAAK5E,CAAA,GAAI2E,CAAA,EAAG5E,CAAA,EAAGC,CAAA,GAAI2E,CAAA,GAAIpE,EAAA,GAAKqE,GAAA,EAAK7E,CAAA,EAAGC,CAAA,GAAI2E,CAAA,GAAIpE,EAAE;QAC3E;QAGDnF,IAAA,CAAK8E,MAAA,CAAOH,CAAA,EAAGC,CAAA,GAAIO,EAAE;QACrB,IAAID,EAAA,KAAO,KAAKC,EAAA,KAAO,GAAG;UACxBnF,IAAA,CAAK+E,aAAA,CAAcJ,CAAA,EAAGC,CAAA,GAAIO,EAAA,GAAKqE,GAAA,EAAK7E,CAAA,GAAIO,EAAA,GAAKsE,GAAA,EAAK5E,CAAA,EAAGD,CAAA,GAAIO,EAAA,EAAIN,CAAC;QAC/D;QAED,OAAO5E,IAAA;MACR;MAED,SAASwB,iBAAiBZ,IAAA,EAAM;QAC9B,SAAS6I,SAAS1F,KAAA,EAAO2F,CAAA,EAAGC,CAAA,EAAG;UAC7B,MAAMhF,CAAA,GAAI0E,mBAAA,CAAoBK,CAAC;UAC/B,MAAM9E,CAAA,GAAIyE,mBAAA,CAAoBM,CAAC;UAE/B,IAAIC,KAAA,KAAU,GAAG;YACf5J,IAAA,CAAK6E,MAAA,CAAOF,CAAA,EAAGC,CAAC;UAC5B,OAAiB;YACL5E,IAAA,CAAK8E,MAAA,CAAOH,CAAA,EAAGC,CAAC;UACjB;UAEDgF,KAAA;QACD;QAED,MAAMC,KAAA,GAAQ;QAEd,MAAM7J,IAAA,GAAO,IAAIqD,SAAA,CAAW;QAE5B,IAAIuG,KAAA,GAAQ;QAEZhJ,IAAA,CAAKiD,YAAA,CAAa,QAAQ,EAAEiG,OAAA,CAAQD,KAAA,EAAOJ,QAAQ;QAEnDzJ,IAAA,CAAKuF,WAAA,CAAYC,SAAA,GAAY;QAE7B,OAAOxF,IAAA;MACR;MAED,SAASyB,kBAAkBb,IAAA,EAAM;QAC/B,SAAS6I,SAAS1F,KAAA,EAAO2F,CAAA,EAAGC,CAAA,EAAG;UAC7B,MAAMhF,CAAA,GAAI0E,mBAAA,CAAoBK,CAAC;UAC/B,MAAM9E,CAAA,GAAIyE,mBAAA,CAAoBM,CAAC;UAE/B,IAAIC,KAAA,KAAU,GAAG;YACf5J,IAAA,CAAK6E,MAAA,CAAOF,CAAA,EAAGC,CAAC;UAC5B,OAAiB;YACL5E,IAAA,CAAK8E,MAAA,CAAOH,CAAA,EAAGC,CAAC;UACjB;UAEDgF,KAAA;QACD;QAED,MAAMC,KAAA,GAAQ;QAEd,MAAM7J,IAAA,GAAO,IAAIqD,SAAA,CAAW;QAE5B,IAAIuG,KAAA,GAAQ;QAEZhJ,IAAA,CAAKiD,YAAA,CAAa,QAAQ,EAAEiG,OAAA,CAAQD,KAAA,EAAOJ,QAAQ;QAEnDzJ,IAAA,CAAKuF,WAAA,CAAYC,SAAA,GAAY;QAE7B,OAAOxF,IAAA;MACR;MAED,SAAS0B,gBAAgBd,IAAA,EAAM;QAC7B,MAAM+D,CAAA,GAAI0E,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,IAAI,KAAK,CAAC;QAC1D,MAAMe,CAAA,GAAIyE,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,IAAI,KAAK,CAAC;QAC1D,MAAMkG,CAAA,GAAIV,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,GAAG,KAAK,CAAC;QAEzD,MAAMmG,OAAA,GAAU,IAAIC,IAAA,CAAM;QAC1BD,OAAA,CAAQE,MAAA,CAAOvF,CAAA,EAAGC,CAAA,EAAGmF,CAAA,EAAG,GAAG/C,IAAA,CAAKC,EAAA,GAAK,CAAC;QAEtC,MAAMjH,IAAA,GAAO,IAAIqD,SAAA,CAAW;QAC5BrD,IAAA,CAAKmK,QAAA,CAASxH,IAAA,CAAKqH,OAAO;QAE1B,OAAOhK,IAAA;MACR;MAED,SAAS2B,iBAAiBf,IAAA,EAAM;QAC9B,MAAM+D,CAAA,GAAI0E,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,IAAI,KAAK,CAAC;QAC1D,MAAMe,CAAA,GAAIyE,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,IAAI,KAAK,CAAC;QAC1D,MAAMqB,EAAA,GAAKmE,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,IAAI,KAAK,CAAC;QAC3D,MAAMsB,EAAA,GAAKkE,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,IAAI,KAAK,CAAC;QAE3D,MAAMmG,OAAA,GAAU,IAAIC,IAAA,CAAM;QAC1BD,OAAA,CAAQrB,UAAA,CAAWhE,CAAA,EAAGC,CAAA,EAAGM,EAAA,EAAIC,EAAA,EAAI,GAAG6B,IAAA,CAAKC,EAAA,GAAK,CAAC;QAE/C,MAAMjH,IAAA,GAAO,IAAIqD,SAAA,CAAW;QAC5BrD,IAAA,CAAKmK,QAAA,CAASxH,IAAA,CAAKqH,OAAO;QAE1B,OAAOhK,IAAA;MACR;MAED,SAAS4B,cAAchB,IAAA,EAAM;QAC3B,MAAMwJ,EAAA,GAAKf,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,IAAI,KAAK,CAAC;QAC3D,MAAMwG,EAAA,GAAKhB,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,IAAI,KAAK,CAAC;QAC3D,MAAMyG,EAAA,GAAKjB,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,IAAI,KAAK,CAAC;QAC3D,MAAM0G,EAAA,GAAKlB,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,IAAI,KAAK,CAAC;QAE3D,MAAM7D,IAAA,GAAO,IAAIqD,SAAA,CAAW;QAC5BrD,IAAA,CAAK6E,MAAA,CAAOuF,EAAA,EAAIC,EAAE;QAClBrK,IAAA,CAAK8E,MAAA,CAAOwF,EAAA,EAAIC,EAAE;QAClBvK,IAAA,CAAKuF,WAAA,CAAYC,SAAA,GAAY;QAE7B,OAAOxF,IAAA;MACR;MAID,SAASmB,WAAWP,IAAA,EAAMC,KAAA,EAAO;QAC/BA,KAAA,GAAQyF,MAAA,CAAOK,MAAA,CAAO,IAAI9F,KAAK;QAE/B,IAAI2J,gBAAA,GAAmB,CAAE;QAEzB,IAAI5J,IAAA,CAAKS,YAAA,CAAa,OAAO,GAAG;UAC9B,MAAMoJ,cAAA,GAAiB7J,IAAA,CACpBiD,YAAA,CAAa,OAAO,EACpBmC,KAAA,CAAM,IAAI,EACVC,MAAA,CAAOC,OAAO,EACdC,GAAA,CAAKrD,CAAA,IAAMA,CAAA,CAAEwB,IAAA,CAAI,CAAE;UAEtB,SAASxB,CAAA,GAAI,GAAGA,CAAA,GAAI2H,cAAA,CAAe1H,MAAA,EAAQD,CAAA,IAAK;YAC9C0H,gBAAA,GAAmBlE,MAAA,CAAOK,MAAA,CAAO6D,gBAAA,EAAkB9D,WAAA,CAAY,MAAM+D,cAAA,CAAe3H,CAAC,CAAC,CAAC;UACxF;QACF;QAED,IAAIlC,IAAA,CAAKS,YAAA,CAAa,IAAI,GAAG;UAC3BmJ,gBAAA,GAAmBlE,MAAA,CAAOK,MAAA,CAAO6D,gBAAA,EAAkB9D,WAAA,CAAY,MAAM9F,IAAA,CAAKiD,YAAA,CAAa,IAAI,CAAC,CAAC;QAC9F;QAED,SAAS6G,SAASC,OAAA,EAASC,MAAA,EAAQC,cAAA,EAAgB;UACjD,IAAIA,cAAA,KAAmB,QACrBA,cAAA,GAAiB,SAAS1H,KAAKsD,CAAA,EAAG;YAChC,IAAIA,CAAA,CAAEqE,UAAA,CAAW,KAAK,GAAGtK,OAAA,CAAQ4B,IAAA,CAAK,yDAAyD;YAE/F,OAAOqE,CAAA;UACR;UAEH,IAAI7F,IAAA,CAAKS,YAAA,CAAasJ,OAAO,GAAG9J,KAAA,CAAM+J,MAAM,IAAIC,cAAA,CAAejK,IAAA,CAAKiD,YAAA,CAAa8G,OAAO,CAAC;UACzF,IAAIH,gBAAA,CAAiBG,OAAO,GAAG9J,KAAA,CAAM+J,MAAM,IAAIC,cAAA,CAAeL,gBAAA,CAAiBG,OAAO,CAAC;UACvF,IAAI/J,IAAA,CAAKC,KAAA,IAASD,IAAA,CAAKC,KAAA,CAAM8J,OAAO,MAAM,IAAI9J,KAAA,CAAM+J,MAAM,IAAIC,cAAA,CAAejK,IAAA,CAAKC,KAAA,CAAM8J,OAAO,CAAC;QACjG;QAED,SAASI,MAAMtE,CAAA,EAAG;UAChB,OAAOO,IAAA,CAAKmB,GAAA,CAAI,GAAGnB,IAAA,CAAKoC,GAAA,CAAI,GAAGC,mBAAA,CAAoB5C,CAAC,CAAC,CAAC;QACvD;QAED,SAASuE,SAASvE,CAAA,EAAG;UACnB,OAAOO,IAAA,CAAKmB,GAAA,CAAI,GAAGkB,mBAAA,CAAoB5C,CAAC,CAAC;QAC1C;QAEDiE,QAAA,CAAS,QAAQ,MAAM;QACvBA,QAAA,CAAS,gBAAgB,eAAeK,KAAK;QAC7CL,QAAA,CAAS,aAAa,UAAU;QAChCA,QAAA,CAAS,WAAW,WAAWK,KAAK;QACpCL,QAAA,CAAS,UAAU,QAAQ;QAC3BA,QAAA,CAAS,kBAAkB,iBAAiBK,KAAK;QACjDL,QAAA,CAAS,gBAAgB,eAAeM,QAAQ;QAChDN,QAAA,CAAS,mBAAmB,gBAAgB;QAC5CA,QAAA,CAAS,kBAAkB,eAAe;QAC1CA,QAAA,CAAS,qBAAqB,oBAAoBM,QAAQ;QAC1DN,QAAA,CAAS,cAAc,YAAY;QAEnC,OAAO7J,KAAA;MACR;MAID,SAASmE,cAAc0E,CAAA,EAAGC,CAAA,EAAG;QAC3B,OAAOD,CAAA,IAAKC,CAAA,GAAID,CAAA;MACjB;MAID,SAASlF,YAAYyG,KAAA,EAAOC,KAAA,EAAOC,MAAA,EAAQ;QACzC,IAAI,OAAOF,KAAA,KAAU,UAAU;UAC7B,MAAM,IAAIG,SAAA,CAAU,oBAAoB,OAAOH,KAAK;QACrD;QAGD,MAAMI,EAAA,GAAK;UACTC,SAAA,EAAW;UACXC,UAAA,EAAY;UACZC,KAAA,EAAO;UACPC,IAAA,EAAM;UACNC,KAAA,EAAO;UACPC,KAAA,EAAO;UACPC,GAAA,EAAK;UACLC,KAAA,EAAO;QACR;QAGD,MAAMC,GAAA,GAAM;QACZ,MAAMC,GAAA,GAAM;QACZ,MAAMC,KAAA,GAAQ;QACd,MAAMJ,GAAA,GAAM;QAEZ,IAAIK,KAAA,GAAQH,GAAA;QACZ,IAAII,SAAA,GAAY;QAChB,IAAIC,MAAA,GAAS;UACXC,QAAA,GAAW;QACb,MAAMC,MAAA,GAAS,EAAE;QAEjB,SAASC,iBAAiBC,QAAA,EAASzJ,CAAA,EAAG0J,OAAA,EAAS;UAC7C,MAAM/L,KAAA,GAAQ,IAAIgM,WAAA,CAAY,2BAA2BF,QAAA,GAAU,gBAAgBzJ,CAAA,GAAI,GAAG;UAC1FrC,KAAA,CAAM+L,OAAA,GAAUA,OAAA;UAChB,MAAM/L,KAAA;QACP;QAED,SAASiM,UAAA,EAAY;UACnB,IAAIP,MAAA,KAAW,IAAI;YACjB,IAAIC,QAAA,KAAa,IAAIC,MAAA,CAAO1J,IAAA,CAAKgK,MAAA,CAAOR,MAAM,CAAC,OAC1CE,MAAA,CAAO1J,IAAA,CAAKgK,MAAA,CAAOR,MAAM,IAAInF,IAAA,CAAK4F,GAAA,CAAI,IAAID,MAAA,CAAOP,QAAQ,CAAC,CAAC;UACjE;UAEDD,MAAA,GAAS;UACTC,QAAA,GAAW;QACZ;QAED,IAAIS,OAAA;QACJ,MAAM9J,MAAA,GAASkI,KAAA,CAAMlI,MAAA;QAErB,SAASD,CAAA,GAAI,GAAGA,CAAA,GAAIC,MAAA,EAAQD,CAAA,IAAK;UAC/B+J,OAAA,GAAU5B,KAAA,CAAMnI,CAAC;UAGjB,IAAIgK,KAAA,CAAMC,OAAA,CAAQ7B,KAAK,KAAKA,KAAA,CAAM8B,QAAA,CAASX,MAAA,CAAOtJ,MAAA,GAASoI,MAAM,KAAKE,EAAA,CAAGQ,KAAA,CAAMoB,IAAA,CAAKJ,OAAO,GAAG;YAC5FZ,KAAA,GAAQF,GAAA;YACRI,MAAA,GAASU,OAAA;YACTH,SAAA,CAAW;YACX;UACD;UAGD,IAAIT,KAAA,KAAUH,GAAA,EAAK;YAEjB,IAAIT,EAAA,CAAGE,UAAA,CAAW0B,IAAA,CAAKJ,OAAO,GAAG;cAC/B;YACD;YAGD,IAAIxB,EAAA,CAAGG,KAAA,CAAMyB,IAAA,CAAKJ,OAAO,KAAKxB,EAAA,CAAGI,IAAA,CAAKwB,IAAA,CAAKJ,OAAO,GAAG;cACnDZ,KAAA,GAAQF,GAAA;cACRI,MAAA,GAASU,OAAA;cACT;YACD;YAED,IAAIxB,EAAA,CAAGK,KAAA,CAAMuB,IAAA,CAAKJ,OAAO,GAAG;cAC1BZ,KAAA,GAAQD,KAAA;cACRG,MAAA,GAASU,OAAA;cACT;YACD;YAGD,IAAIxB,EAAA,CAAGM,KAAA,CAAMsB,IAAA,CAAKJ,OAAO,GAAG;cAC1B,IAAIX,SAAA,EAAW;gBACbI,gBAAA,CAAiBO,OAAA,EAAS/J,CAAA,EAAGuJ,MAAM;cACpC;cAEDH,SAAA,GAAY;YACb;UACF;UAGD,IAAID,KAAA,KAAUF,GAAA,EAAK;YACjB,IAAIV,EAAA,CAAGG,KAAA,CAAMyB,IAAA,CAAKJ,OAAO,GAAG;cAC1BV,MAAA,IAAUU,OAAA;cACV;YACD;YAED,IAAIxB,EAAA,CAAGK,KAAA,CAAMuB,IAAA,CAAKJ,OAAO,GAAG;cAC1BV,MAAA,IAAUU,OAAA;cACVZ,KAAA,GAAQD,KAAA;cACR;YACD;YAED,IAAIX,EAAA,CAAGO,GAAA,CAAIqB,IAAA,CAAKJ,OAAO,GAAG;cACxBZ,KAAA,GAAQL,GAAA;cACR;YACD;YAGD,IAAIP,EAAA,CAAGI,IAAA,CAAKwB,IAAA,CAAKJ,OAAO,KAAKV,MAAA,CAAOpJ,MAAA,KAAW,KAAKsI,EAAA,CAAGI,IAAA,CAAKwB,IAAA,CAAKd,MAAA,CAAO,CAAC,CAAC,GAAG;cAC3EG,gBAAA,CAAiBO,OAAA,EAAS/J,CAAA,EAAGuJ,MAAM;YACpC;UACF;UAGD,IAAIJ,KAAA,KAAUD,KAAA,EAAO;YACnB,IAAIX,EAAA,CAAGG,KAAA,CAAMyB,IAAA,CAAKJ,OAAO,GAAG;cAC1BV,MAAA,IAAUU,OAAA;cACV;YACD;YAED,IAAIxB,EAAA,CAAGO,GAAA,CAAIqB,IAAA,CAAKJ,OAAO,GAAG;cACxBZ,KAAA,GAAQL,GAAA;cACR;YACD;YAGD,IAAIP,EAAA,CAAGK,KAAA,CAAMuB,IAAA,CAAKJ,OAAO,KAAKV,MAAA,CAAOA,MAAA,CAAOpJ,MAAA,GAAS,CAAC,MAAM,KAAK;cAC/DuJ,gBAAA,CAAiBO,OAAA,EAAS/J,CAAA,EAAGuJ,MAAM;YACpC;UACF;UAGD,IAAIJ,KAAA,KAAUL,GAAA,EAAK;YACjB,IAAIP,EAAA,CAAGG,KAAA,CAAMyB,IAAA,CAAKJ,OAAO,GAAG;cAC1BT,QAAA,IAAYS,OAAA;cACZ;YACD;YAED,IAAIxB,EAAA,CAAGI,IAAA,CAAKwB,IAAA,CAAKJ,OAAO,GAAG;cACzB,IAAIT,QAAA,KAAa,IAAI;gBACnBA,QAAA,IAAYS,OAAA;gBACZ;cACD;cAED,IAAIT,QAAA,CAASrJ,MAAA,KAAW,KAAKsI,EAAA,CAAGI,IAAA,CAAKwB,IAAA,CAAKb,QAAQ,GAAG;gBACnDE,gBAAA,CAAiBO,OAAA,EAAS/J,CAAA,EAAGuJ,MAAM;cACpC;YACF;UACF;UAGD,IAAIhB,EAAA,CAAGE,UAAA,CAAW0B,IAAA,CAAKJ,OAAO,GAAG;YAC/BH,SAAA,CAAW;YACXT,KAAA,GAAQH,GAAA;YACRI,SAAA,GAAY;UACb,WAAUb,EAAA,CAAGM,KAAA,CAAMsB,IAAA,CAAKJ,OAAO,GAAG;YACjCH,SAAA,CAAW;YACXT,KAAA,GAAQH,GAAA;YACRI,SAAA,GAAY;UACb,WAAUb,EAAA,CAAGI,IAAA,CAAKwB,IAAA,CAAKJ,OAAO,GAAG;YAChCH,SAAA,CAAW;YACXT,KAAA,GAAQF,GAAA;YACRI,MAAA,GAASU,OAAA;UACV,WAAUxB,EAAA,CAAGK,KAAA,CAAMuB,IAAA,CAAKJ,OAAO,GAAG;YACjCH,SAAA,CAAW;YACXT,KAAA,GAAQD,KAAA;YACRG,MAAA,GAASU,OAAA;UACrB,OAAiB;YACLP,gBAAA,CAAiBO,OAAA,EAAS/J,CAAA,EAAGuJ,MAAM;UACpC;QACF;QAGDK,SAAA,CAAW;QAEX,OAAOL,MAAA;MACR;MAID,MAAMa,KAAA,GAAQ,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;MAGjD,MAAMC,cAAA,GAAiB;QACrBC,EAAA,EAAI;UACFA,EAAA,EAAI;UACJC,EAAA,EAAI;UACJC,EAAA,EAAI,IAAI;UACRC,EAAA,EAAI,KAAK;UACTC,EAAA,EAAI,IAAI;UACRC,EAAA,EAAI;QACL;QACDJ,EAAA,EAAI;UACFD,EAAA,EAAI;UACJC,EAAA,EAAI;UACJC,EAAA,EAAI,IAAI;UACRC,EAAA,EAAI,KAAK;UACTC,EAAA,EAAI,IAAI;UACRC,EAAA,EAAI;QACL;QACDH,EAAA,EAAI;UACFF,EAAA,EAAI;UACJC,EAAA,EAAI;UACJC,EAAA,EAAI;UACJC,EAAA,EAAI;UACJC,EAAA,EAAI;UACJC,EAAA,EAAI;QACL;QACDF,EAAA,EAAI;UACFH,EAAA,EAAI,OAAO;UACXC,EAAA,EAAI,OAAO;UACXC,EAAA,EAAI,IAAI;UACRC,EAAA,EAAI;UACJC,EAAA,EAAI,IAAI;UACRC,EAAA,EAAI;QACL;QACDD,EAAA,EAAI;UACFJ,EAAA,EAAI,OAAO;UACXC,EAAA,EAAI,OAAO;UACXC,EAAA,EAAI,IAAI;UACRC,EAAA,EAAI,KAAK;UACTC,EAAA,EAAI;UACJC,EAAA,EAAI;QACL;QACDA,EAAA,EAAI;UACFA,EAAA,EAAI;QACL;MACF;MAED,SAASpE,oBAAoBqE,MAAA,EAAQ;QACnC,IAAIC,OAAA,GAAU;QAEd,IAAI,OAAOD,MAAA,KAAW,YAAYA,MAAA,YAAkBE,MAAA,EAAQ;UAC1D,SAAS9K,CAAA,GAAI,GAAG+K,CAAA,GAAIX,KAAA,CAAMnK,MAAA,EAAQD,CAAA,GAAI+K,CAAA,EAAG/K,CAAA,IAAK;YAC5C,MAAMgL,CAAA,GAAIZ,KAAA,CAAMpK,CAAC;YAEjB,IAAI4K,MAAA,CAAOK,QAAA,CAASD,CAAC,GAAG;cACtBH,OAAA,GAAUG,CAAA;cACVJ,MAAA,GAASA,MAAA,CAAO1L,SAAA,CAAU,GAAG0L,MAAA,CAAO3K,MAAA,GAAS+K,CAAA,CAAE/K,MAAM;cACrD;YACD;UACF;QACF;QAED,IAAIiL,KAAA,GAAQ;QAEZ,IAAIL,OAAA,KAAY,QAAQ/N,KAAA,CAAMN,WAAA,KAAgB,MAAM;UAGlD0O,KAAA,GAAQb,cAAA,CAAe,IAAI,EAAEvN,KAAA,CAAMN,WAAW,IAAIM,KAAA,CAAMP,UAAA;QAClE,OAAe;UACL2O,KAAA,GAAQb,cAAA,CAAeQ,OAAO,EAAE/N,KAAA,CAAMN,WAAW;UAEjD,IAAI0O,KAAA,GAAQ,GAAG;YAGbA,KAAA,GAAQb,cAAA,CAAeQ,OAAO,EAAE,IAAI,IAAI/N,KAAA,CAAMP,UAAA;UAC/C;QACF;QAED,OAAO2O,KAAA,GAAQC,UAAA,CAAWP,MAAM;MACjC;MAID,SAAS1M,iBAAiBJ,IAAA,EAAM;QAC9B,IACE,EACEA,IAAA,CAAKS,YAAA,CAAa,WAAW,KAC5BT,IAAA,CAAKM,QAAA,KAAa,UAAUN,IAAA,CAAKS,YAAA,CAAa,GAAG,KAAKT,IAAA,CAAKS,YAAA,CAAa,GAAG,KAE9E;UACA,OAAO;QACR;QAED,MAAMN,SAAA,GAAYmN,kBAAA,CAAmBtN,IAAI;QAEzC,IAAIqC,cAAA,CAAeF,MAAA,GAAS,GAAG;UAC7BhC,SAAA,CAAUoN,WAAA,CAAYlL,cAAA,CAAeA,cAAA,CAAeF,MAAA,GAAS,CAAC,CAAC;QAChE;QAEDN,gBAAA,CAAiBU,IAAA,CAAKpC,SAAS;QAC/BkC,cAAA,CAAeN,IAAA,CAAK5B,SAAS;QAE7B,OAAOA,SAAA;MACR;MAED,SAASmN,mBAAmBtN,IAAA,EAAM;QAChC,MAAMG,SAAA,GAAY,IAAIqN,OAAA,CAAS;QAC/B,MAAMC,iBAAA,GAAmBC,cAAA;QAEzB,IAAI1N,IAAA,CAAKM,QAAA,KAAa,UAAUN,IAAA,CAAKS,YAAA,CAAa,GAAG,KAAKT,IAAA,CAAKS,YAAA,CAAa,GAAG,IAAI;UACjF,MAAMkN,EAAA,GAAKlF,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,GAAG,CAAC;UACrD,MAAM2K,EAAA,GAAKnF,mBAAA,CAAoBzI,IAAA,CAAKiD,YAAA,CAAa,GAAG,CAAC;UAErD9C,SAAA,CAAU0N,SAAA,CAAUF,EAAA,EAAIC,EAAE;QAC3B;QAED,IAAI5N,IAAA,CAAKS,YAAA,CAAa,WAAW,GAAG;UAClC,MAAMqN,eAAA,GAAkB9N,IAAA,CAAKiD,YAAA,CAAa,WAAW,EAAEmC,KAAA,CAAM,GAAG;UAEhE,SAAS2I,MAAA,GAASD,eAAA,CAAgB3L,MAAA,GAAS,GAAG4L,MAAA,IAAU,GAAGA,MAAA,IAAU;YACnE,MAAMC,aAAA,GAAgBF,eAAA,CAAgBC,MAAM,EAAErK,IAAA,CAAM;YAEpD,IAAIsK,aAAA,KAAkB,IAAI;YAE1B,MAAMC,UAAA,GAAaD,aAAA,CAAcE,OAAA,CAAQ,GAAG;YAC5C,MAAMC,WAAA,GAAcH,aAAA,CAAc7L,MAAA;YAElC,IAAI8L,UAAA,GAAa,KAAKA,UAAA,GAAaE,WAAA,EAAa;cAC9C,MAAMC,aAAA,GAAgBJ,aAAA,CAAcvK,KAAA,CAAM,GAAGwK,UAAU;cAEvD,MAAMI,KAAA,GAAQzK,WAAA,CAAYoK,aAAA,CAAcvK,KAAA,CAAMwK,UAAA,GAAa,CAAC,CAAC;cAE7DR,iBAAA,CAAiBjL,QAAA,CAAU;cAE3B,QAAQ4L,aAAA;gBACN,KAAK;kBACH,IAAIC,KAAA,CAAMlM,MAAA,IAAU,GAAG;oBACrB,MAAMwL,EAAA,GAAKU,KAAA,CAAM,CAAC;oBAClB,IAAIT,EAAA,GAAK;oBAET,IAAIS,KAAA,CAAMlM,MAAA,IAAU,GAAG;sBACrByL,EAAA,GAAKS,KAAA,CAAM,CAAC;oBACb;oBAEDZ,iBAAA,CAAiBI,SAAA,CAAUF,EAAA,EAAIC,EAAE;kBAClC;kBAED;gBAEF,KAAK;kBACH,IAAIS,KAAA,CAAMlM,MAAA,IAAU,GAAG;oBACrB,IAAImM,KAAA,GAAQ;oBACZ,IAAI5G,EAAA,GAAK;oBACT,IAAIC,EAAA,GAAK;oBAGT2G,KAAA,GAASD,KAAA,CAAM,CAAC,IAAIjI,IAAA,CAAKC,EAAA,GAAM;oBAE/B,IAAIgI,KAAA,CAAMlM,MAAA,IAAU,GAAG;sBAErBuF,EAAA,GAAK2G,KAAA,CAAM,CAAC;sBACZ1G,EAAA,GAAK0G,KAAA,CAAM,CAAC;oBACb;oBAGDE,cAAA,CAAeC,eAAA,CAAgB,CAAC9G,EAAA,EAAI,CAACC,EAAE;oBACvC8G,cAAA,CAAeC,YAAA,CAAaJ,KAAK;oBACjCK,cAAA,CAAeC,gBAAA,CAAiBH,cAAA,EAAgBF,cAAc;oBAC9DA,cAAA,CAAeC,eAAA,CAAgB9G,EAAA,EAAIC,EAAE;oBACrC8F,iBAAA,CAAiBmB,gBAAA,CAAiBL,cAAA,EAAgBI,cAAc;kBACjE;kBAED;gBAEF,KAAK;kBACH,IAAIN,KAAA,CAAMlM,MAAA,IAAU,GAAG;oBACrB,MAAM0M,MAAA,GAASR,KAAA,CAAM,CAAC;oBACtB,IAAIS,MAAA,GAASD,MAAA;oBAEb,IAAIR,KAAA,CAAMlM,MAAA,IAAU,GAAG;sBACrB2M,MAAA,GAAST,KAAA,CAAM,CAAC;oBACjB;oBAEDZ,iBAAA,CAAiBL,KAAA,CAAMyB,MAAA,EAAQC,MAAM;kBACtC;kBAED;gBAEF,KAAK;kBACH,IAAIT,KAAA,CAAMlM,MAAA,KAAW,GAAG;oBACtBsL,iBAAA,CAAiBsB,GAAA,CAAI,GAAG3I,IAAA,CAAK4I,GAAA,CAAKX,KAAA,CAAM,CAAC,IAAIjI,IAAA,CAAKC,EAAA,GAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;kBAClF;kBAED;gBAEF,KAAK;kBACH,IAAIgI,KAAA,CAAMlM,MAAA,KAAW,GAAG;oBACtBsL,iBAAA,CAAiBsB,GAAA,CAAI,GAAG,GAAG,GAAG3I,IAAA,CAAK4I,GAAA,CAAKX,KAAA,CAAM,CAAC,IAAIjI,IAAA,CAAKC,EAAA,GAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;kBAClF;kBAED;gBAEF,KAAK;kBACH,IAAIgI,KAAA,CAAMlM,MAAA,KAAW,GAAG;oBACtBsL,iBAAA,CAAiBsB,GAAA,CAAIV,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAGA,KAAA,CAAM,CAAC,GAAG,GAAG,GAAG,CAAC;kBACzF;kBAED;cACH;YACF;YAEDlO,SAAA,CAAUoN,WAAA,CAAYE,iBAAgB;UACvC;QACF;QAED,OAAOtN,SAAA;MACR;MAED,SAASyB,cAAcxC,IAAA,EAAM6P,CAAA,EAAG;QAC9B,SAASC,WAAWC,EAAA,EAAI;UACtBC,MAAA,CAAOL,GAAA,CAAII,EAAA,CAAGpL,CAAA,EAAGoL,EAAA,CAAGnL,CAAA,EAAG,CAAC,EAAEqL,YAAA,CAAaJ,CAAC;UAExCE,EAAA,CAAGJ,GAAA,CAAIK,MAAA,CAAOrL,CAAA,EAAGqL,MAAA,CAAOpL,CAAC;QAC1B;QAED,SAASsL,qBAAqBC,KAAA,EAAO;UAInC,MAAMzG,CAAA,GAAIyG,KAAA,CAAMC,OAAA;UAChB,MAAMzG,CAAA,GAAIwG,KAAA,CAAME,OAAA;UAEhB,MAAMC,QAAA,GAAWtJ,IAAA,CAAKM,GAAA,CAAI6I,KAAA,CAAMI,SAAS;UACzC,MAAMC,QAAA,GAAWxJ,IAAA,CAAKO,GAAA,CAAI4I,KAAA,CAAMI,SAAS;UAEzC,MAAME,EAAA,GAAK,IAAIC,OAAA,CAAQhH,CAAA,GAAI4G,QAAA,EAAU5G,CAAA,GAAI8G,QAAA,EAAU,CAAC;UACpD,MAAMT,EAAA,GAAK,IAAIW,OAAA,CAAQ,CAAC/G,CAAA,GAAI6G,QAAA,EAAU7G,CAAA,GAAI2G,QAAA,EAAU,CAAC;UAErD,MAAMK,EAAA,GAAKF,EAAA,CAAGR,YAAA,CAAaJ,CAAC;UAC5B,MAAMe,EAAA,GAAKb,EAAA,CAAGE,YAAA,CAAaJ,CAAC;UAE5B,MAAMgB,EAAA,GAAKvC,cAAA,CAAeqB,GAAA,CAAIgB,EAAA,CAAGhM,CAAA,EAAGiM,EAAA,CAAGjM,CAAA,EAAG,GAAGgM,EAAA,CAAG/L,CAAA,EAAGgM,EAAA,CAAGhM,CAAA,EAAG,GAAG,GAAG,GAAG,CAAC;UAEnE,MAAMkM,KAAA,GAAQ3B,cAAA,CAAehM,IAAA,CAAK0N,EAAE,EAAEE,MAAA,CAAQ;UAC9C,MAAMC,MAAA,GAAS3B,cAAA,CAAelM,IAAA,CAAK2N,KAAK,EAAEG,SAAA,CAAW;UACrD,MAAMC,EAAA,GAAKF,MAAA,CAAOG,QAAA,CAASL,KAAK;UAChC,MAAMM,GAAA,GAAMF,EAAA,CAAGG,QAAA;UAEf,MAAMC,EAAA,GAAKC,kBAAA,CAAmBH,GAAA,CAAI,CAAC,GAAGA,GAAA,CAAI,CAAC,GAAGA,GAAA,CAAI,CAAC,CAAC;UACpD,MAAMI,OAAA,GAAUxK,IAAA,CAAKe,IAAA,CAAKuJ,EAAA,CAAGG,GAAG;UAChC,MAAMC,OAAA,GAAU1K,IAAA,CAAKe,IAAA,CAAKuJ,EAAA,CAAGK,GAAG;UAEhCxB,KAAA,CAAMC,OAAA,GAAU,IAAIoB,OAAA;UACpBrB,KAAA,CAAME,OAAA,GAAU,IAAIqB,OAAA;UACpBvB,KAAA,CAAMI,SAAA,GAAYvJ,IAAA,CAAK4K,KAAA,CAAMN,EAAA,CAAGO,EAAA,EAAIP,EAAA,CAAGQ,EAAE;UAEzC,MAAMC,aAAA,IAAiB5B,KAAA,CAAM6B,SAAA,GAAY7B,KAAA,CAAM8B,WAAA,KAAgB,IAAIjL,IAAA,CAAKC,EAAA,IAAM0F,MAAA,CAAOuF,OAAA;UAKrF,IAAI,CAACH,aAAA,EAAe;YAClB,MAAMI,MAAA,GAAShD,cAAA,CAAeQ,GAAA,CAAI6B,OAAA,EAAS,GAAG,GAAG,GAAGE,OAAA,EAAS,GAAG,GAAG,GAAG,CAAC;YAEvE,MAAMU,GAAA,GAAM/C,cAAA,CAAeM,GAAA,CAAI2B,EAAA,CAAGQ,EAAA,EAAIR,EAAA,CAAGO,EAAA,EAAI,GAAG,CAACP,EAAA,CAAGO,EAAA,EAAIP,EAAA,CAAGQ,EAAA,EAAI,GAAG,GAAG,GAAG,CAAC;YAEzE,MAAMO,IAAA,GAAOF,MAAA,CAAOhB,QAAA,CAASiB,GAAG,EAAEjB,QAAA,CAASN,EAAE;YAE7C,MAAMyB,cAAA,GAAkBC,GAAA,IAAQ;cAC9B,MAAM;gBAAE5N,CAAA,EAAG6N,IAAA;gBAAM5N,CAAA,EAAG6N;cAAA,IAAS,IAAI/B,OAAA,CAAQ1J,IAAA,CAAKM,GAAA,CAAIiL,GAAG,GAAGvL,IAAA,CAAKO,GAAA,CAAIgL,GAAG,GAAG,CAAC,EAAEtC,YAAA,CAAaoC,IAAI;cAE3F,OAAOrL,IAAA,CAAK4K,KAAA,CAAMa,IAAA,EAAMD,IAAI;YAC7B;YAEDrC,KAAA,CAAM8B,WAAA,GAAcK,cAAA,CAAenC,KAAA,CAAM8B,WAAW;YACpD9B,KAAA,CAAM6B,SAAA,GAAYM,cAAA,CAAenC,KAAA,CAAM6B,SAAS;YAEhD,IAAIU,kBAAA,CAAmB7C,CAAC,GAAG;cACzBM,KAAA,CAAMwC,UAAA,GAAa,CAACxC,KAAA,CAAMwC,UAAA;YAC3B;UACF;QACF;QAED,SAASC,oBAAoBzC,KAAA,EAAO;UAIlC,MAAM0C,EAAA,GAAKC,kBAAA,CAAmBjD,CAAC;UAC/B,MAAMkD,EAAA,GAAKC,kBAAA,CAAmBnD,CAAC;UAE/BM,KAAA,CAAMC,OAAA,IAAWyC,EAAA;UACjB1C,KAAA,CAAME,OAAA,IAAW0C,EAAA;UASjB,MAAMvK,KAAA,GACJqK,EAAA,GAAKlG,MAAA,CAAOuF,OAAA,GAAUlL,IAAA,CAAK4K,KAAA,CAAM/B,CAAA,CAAEwB,QAAA,CAAS,CAAC,GAAGxB,CAAA,CAAEwB,QAAA,CAAS,CAAC,CAAC,IAAIrK,IAAA,CAAK4K,KAAA,CAAM,CAAC/B,CAAA,CAAEwB,QAAA,CAAS,CAAC,GAAGxB,CAAA,CAAEwB,QAAA,CAAS,CAAC,CAAC;UAE3GlB,KAAA,CAAMI,SAAA,IAAa/H,KAAA;UAEnB,IAAIkK,kBAAA,CAAmB7C,CAAC,GAAG;YACzBM,KAAA,CAAM8B,WAAA,IAAe;YACrB9B,KAAA,CAAM6B,SAAA,IAAa;YACnB7B,KAAA,CAAMwC,UAAA,GAAa,CAACxC,KAAA,CAAMwC,UAAA;UAC3B;QACF;QAED,MAAMxI,QAAA,GAAWnK,IAAA,CAAKmK,QAAA;QAEtB,SAASrH,CAAA,GAAI,GAAG+K,CAAA,GAAI1D,QAAA,CAASpH,MAAA,EAAQD,CAAA,GAAI+K,CAAA,EAAG/K,CAAA,IAAK;UAC/C,MAAMmQ,OAAA,GAAU9I,QAAA,CAASrH,CAAC;UAC1B,MAAM2C,MAAA,GAASwN,OAAA,CAAQxN,MAAA;UAEvB,SAAShB,CAAA,GAAI,GAAGA,CAAA,GAAIgB,MAAA,CAAO1C,MAAA,EAAQ0B,CAAA,IAAK;YACtC,MAAM0L,KAAA,GAAQ1K,MAAA,CAAOhB,CAAC;YAEtB,IAAI0L,KAAA,CAAM+C,WAAA,EAAa;cACrBpD,UAAA,CAAWK,KAAA,CAAMM,EAAE;cACnBX,UAAA,CAAWK,KAAA,CAAMJ,EAAE;YACjC,WAAuBI,KAAA,CAAMgD,kBAAA,EAAoB;cACnCrD,UAAA,CAAWK,KAAA,CAAMiD,EAAE;cACnBtD,UAAA,CAAWK,KAAA,CAAMM,EAAE;cACnBX,UAAA,CAAWK,KAAA,CAAMJ,EAAE;cACnBD,UAAA,CAAWK,KAAA,CAAMkD,EAAE;YACjC,WAAuBlD,KAAA,CAAMmD,sBAAA,EAAwB;cACvCxD,UAAA,CAAWK,KAAA,CAAMiD,EAAE;cACnBtD,UAAA,CAAWK,KAAA,CAAMM,EAAE;cACnBX,UAAA,CAAWK,KAAA,CAAMJ,EAAE;YACjC,WAAuBI,KAAA,CAAMoD,cAAA,EAAgB;cAG/BC,MAAA,CAAO7D,GAAA,CAAIQ,KAAA,CAAMsD,EAAA,EAAItD,KAAA,CAAMuD,EAAE;cAC7B5D,UAAA,CAAW0D,MAAM;cACjBrD,KAAA,CAAMsD,EAAA,GAAKD,MAAA,CAAO7O,CAAA;cAClBwL,KAAA,CAAMuD,EAAA,GAAKF,MAAA,CAAO5O,CAAA;cAIlB,IAAI+O,iBAAA,CAAkB9D,CAAC,GAAG;gBACxBK,oBAAA,CAAqBC,KAAK;cAC1C,OAAqB;gBACLyC,mBAAA,CAAoBzC,KAAK;cAC1B;YACF;UACF;QACF;MACF;MAED,SAASuC,mBAAmB7C,CAAA,EAAG;QAC7B,MAAM+D,EAAA,GAAK/D,CAAA,CAAEwB,QAAA;QACb,OAAOuC,EAAA,CAAG,CAAC,IAAIA,EAAA,CAAG,CAAC,IAAIA,EAAA,CAAG,CAAC,IAAIA,EAAA,CAAG,CAAC,IAAI;MACxC;MAED,SAASD,kBAAkB9D,CAAA,EAAG;QAC5B,MAAM+D,EAAA,GAAK/D,CAAA,CAAEwB,QAAA;QACb,MAAMwC,QAAA,GAAWD,EAAA,CAAG,CAAC,IAAIA,EAAA,CAAG,CAAC,IAAIA,EAAA,CAAG,CAAC,IAAIA,EAAA,CAAG,CAAC;QAG7C,IAAIC,QAAA,KAAa,GAAG,OAAO;QAE3B,MAAMhB,EAAA,GAAKC,kBAAA,CAAmBjD,CAAC;QAC/B,MAAMkD,EAAA,GAAKC,kBAAA,CAAmBnD,CAAC;QAE/B,OAAO7I,IAAA,CAAKE,GAAA,CAAI2M,QAAA,IAAYhB,EAAA,GAAKE,EAAA,CAAG,IAAIpG,MAAA,CAAOuF,OAAA;MAChD;MAED,SAASY,mBAAmBjD,CAAA,EAAG;QAC7B,MAAM+D,EAAA,GAAK/D,CAAA,CAAEwB,QAAA;QACb,OAAOrK,IAAA,CAAKe,IAAA,CAAK6L,EAAA,CAAG,CAAC,IAAIA,EAAA,CAAG,CAAC,IAAIA,EAAA,CAAG,CAAC,IAAIA,EAAA,CAAG,CAAC,CAAC;MAC/C;MAED,SAASZ,mBAAmBnD,CAAA,EAAG;QAC7B,MAAM+D,EAAA,GAAK/D,CAAA,CAAEwB,QAAA;QACb,OAAOrK,IAAA,CAAKe,IAAA,CAAK6L,EAAA,CAAG,CAAC,IAAIA,EAAA,CAAG,CAAC,IAAIA,EAAA,CAAG,CAAC,IAAIA,EAAA,CAAG,CAAC,CAAC;MAC/C;MAYD,SAASrC,mBAAmBuC,CAAA,EAAGC,CAAA,EAAGC,CAAA,EAAG;QACnC,IAAIvC,GAAA,EAAKE,GAAA,EAAKG,EAAA,EAAID,EAAA,EAAIoC,CAAA;QACtB,MAAMC,EAAA,GAAKJ,CAAA,GAAIE,CAAA;QACf,MAAMG,EAAA,GAAKL,CAAA,GAAIE,CAAA;QACf,MAAMI,EAAA,GAAKpN,IAAA,CAAKe,IAAA,CAAKoM,EAAA,GAAKA,EAAA,GAAK,IAAIJ,CAAA,GAAIA,CAAC;QAExC,IAAIG,EAAA,GAAK,GAAG;UACVzC,GAAA,GAAM,OAAOyC,EAAA,GAAKE,EAAA;UAClBH,CAAA,GAAI,IAAIxC,GAAA;UACRE,GAAA,GAAMmC,CAAA,GAAIG,CAAA,GAAID,CAAA,GAAID,CAAA,GAAIE,CAAA,GAAIF,CAAA;QACpC,WAAmBG,EAAA,GAAK,GAAG;UACjBvC,GAAA,GAAM,OAAOuC,EAAA,GAAKE,EAAA;QAC5B,OAAe;UAGL3C,GAAA,GAAM,MAAM2C,EAAA;UACZzC,GAAA,GAAM,OAAOyC,EAAA;QACd;QAID,IAAID,EAAA,GAAK,GAAG;UACVrC,EAAA,GAAKqC,EAAA,GAAKC,EAAA;QACpB,OAAe;UACLtC,EAAA,GAAKqC,EAAA,GAAKC,EAAA;QACX;QAED,IAAIpN,IAAA,CAAKE,GAAA,CAAI4K,EAAE,IAAI,IAAI9K,IAAA,CAAKE,GAAA,CAAI6M,CAAC,GAAG;UAClCE,CAAA,GAAK,KAAKF,CAAA,GAAKjC,EAAA;UACfD,EAAA,GAAK,IAAI7K,IAAA,CAAKe,IAAA,CAAK,IAAIkM,CAAA,GAAIA,CAAC;UAC5BnC,EAAA,GAAKmC,CAAA,GAAIpC,EAAA;QACV,WAAU7K,IAAA,CAAKE,GAAA,CAAI6M,CAAC,MAAM,GAAG;UAC5BjC,EAAA,GAAK;UACLD,EAAA,GAAK;QACf,OAAe;UACLoC,CAAA,GAAK,OAAOnC,EAAA,GAAMiC,CAAA;UAClBjC,EAAA,GAAK,IAAI9K,IAAA,CAAKe,IAAA,CAAK,IAAIkM,CAAA,GAAIA,CAAC;UAC5BpC,EAAA,GAAKoC,CAAA,GAAInC,EAAA;QACV;QAED,IAAIqC,EAAA,GAAK,GAAG;UACVF,CAAA,GAAInC,EAAA;UACJA,EAAA,GAAK,CAACD,EAAA;UACNA,EAAA,GAAKoC,CAAA;QACN;QAED,OAAO;UAAExC,GAAA;UAAKE,GAAA;UAAKG,EAAA;UAAID;QAAI;MAC5B;MAID,MAAMnP,KAAA,GAAQ,EAAE;MAChB,MAAMgE,WAAA,GAAc,CAAE;MAEtB,MAAMzD,cAAA,GAAiB,EAAE;MAEzB,MAAMqL,cAAA,GAAiB,IAAIF,OAAA,CAAS;MACpC,MAAMe,cAAA,GAAiB,IAAIf,OAAA,CAAS;MACpC,MAAMiB,cAAA,GAAiB,IAAIjB,OAAA,CAAS;MACpC,MAAMmB,cAAA,GAAiB,IAAInB,OAAA,CAAS;MACpC,MAAMoF,MAAA,GAAS,IAAIjQ,OAAA,CAAS;MAC5B,MAAMyM,MAAA,GAAS,IAAIU,OAAA,CAAS;MAE5B,MAAMjO,gBAAA,GAAmB,IAAI2L,OAAA,CAAS;MAEtC,MAAMiG,GAAA,GAAM,IAAIC,SAAA,CAAS,EAAGC,eAAA,CAAgBlU,IAAA,EAAM,eAAe;MAEjEM,SAAA,CAAU0T,GAAA,CAAIG,eAAA,EAAiB;QAC7BnS,IAAA,EAAM;QACNoS,WAAA,EAAa;QACbC,aAAA,EAAe;QACfC,WAAA,EAAa;QACbC,cAAA,EAAgB;QAChBC,aAAA,EAAe;QACfC,gBAAA,EAAkB;MAC1B,CAAO;MAED,MAAMC,IAAA,GAAO;QAAErS,KAAA;QAAc2R,GAAA,EAAKA,GAAA,CAAIG;MAAiB;MAGvD,OAAOO,IAAA;IACR;IAED,OAAOC,aAAaC,SAAA,EAAW;MAI7B,MAAMC,SAAA,GAAY;MAElB,MAAMC,wBAAA,GAA2B;QAC/BC,MAAA,EAAQ;QACRC,WAAA,EAAa;QACbC,OAAA,EAAS;QACTC,IAAA,EAAM;QACNC,KAAA,EAAO;QACPC,MAAA,EAAQ;QACRC,MAAA,EAAQ;MACT;MAED,MAAMC,cAAA,GAAiB;QACrBC,GAAA,EAAKT,wBAAA,CAAyBC,MAAA;QAC9BnB,CAAA,EAAG;MACJ;MAED,SAAS4B,qBAAqBC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAIC,EAAA,EAAI;QAC5C,MAAM7L,EAAA,GAAK0L,EAAA,CAAGnR,CAAA;QACd,MAAM2F,EAAA,GAAKyL,EAAA,CAAGpR,CAAA;QACd,MAAMuR,EAAA,GAAKF,EAAA,CAAGrR,CAAA;QACd,MAAMwR,EAAA,GAAKF,EAAA,CAAGtR,CAAA;QACd,MAAM0F,EAAA,GAAKyL,EAAA,CAAGlR,CAAA;QACd,MAAM2F,EAAA,GAAKwL,EAAA,CAAGnR,CAAA;QACd,MAAMwR,EAAA,GAAKJ,EAAA,CAAGpR,CAAA;QACd,MAAMyR,EAAA,GAAKJ,EAAA,CAAGrR,CAAA;QACd,MAAM0R,IAAA,IAAQH,EAAA,GAAKD,EAAA,KAAO7L,EAAA,GAAK+L,EAAA,KAAOC,EAAA,GAAKD,EAAA,KAAOhM,EAAA,GAAK8L,EAAA;QACvD,MAAMK,IAAA,IAAQjM,EAAA,GAAKF,EAAA,KAAOC,EAAA,GAAK+L,EAAA,KAAO7L,EAAA,GAAKF,EAAA,KAAOD,EAAA,GAAK8L,EAAA;QACvD,MAAMM,KAAA,IAASH,EAAA,GAAKD,EAAA,KAAO9L,EAAA,GAAKF,EAAA,KAAO+L,EAAA,GAAKD,EAAA,KAAO3L,EAAA,GAAKF,EAAA;QACxD,MAAMoM,EAAA,GAAKH,IAAA,GAAOE,KAAA;QAClB,MAAME,EAAA,GAAKH,IAAA,GAAOC,KAAA;QAElB,IAAKA,KAAA,KAAU,KAAKF,IAAA,KAAS,KAAMG,EAAA,IAAM,KAAKA,EAAA,IAAM,KAAKC,EAAA,GAAK,KAAKA,EAAA,GAAK,GAAG;UAGzE,OAAO;QACR,WAAUJ,IAAA,KAAS,KAAKE,KAAA,KAAU,GAAG;UAIpC,SAAS1T,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;YAC1B6T,aAAA,CAAc7T,CAAA,KAAM,IAAIkT,EAAA,GAAKC,EAAA,EAAIH,EAAA,EAAIC,EAAE;YAEvC,IAAIJ,cAAA,CAAeC,GAAA,IAAOT,wBAAA,CAAyBC,MAAA,EAAQ;cACzD,MAAM9R,KAAA,GAAQR,CAAA,KAAM,IAAIkT,EAAA,GAAKC,EAAA;cAC7B,OAAO;gBAAEtR,CAAA,EAAGrB,KAAA,CAAMqB,CAAA;gBAAGC,CAAA,EAAGtB,KAAA,CAAMsB,CAAA;gBAAGqP,CAAA,EAAG0B,cAAA,CAAe1B;cAAG;YACvD,WAAU0B,cAAA,CAAeC,GAAA,IAAOT,wBAAA,CAAyBG,OAAA,EAAS;cACjE,MAAM3Q,CAAA,GAAI,EAAEyF,EAAA,GAAKuL,cAAA,CAAe1B,CAAA,IAAK3J,EAAA,GAAKF,EAAA,GAAKwM,WAAA,CAAY,EAAE;cAC7D,MAAMhS,CAAA,GAAI,EAAEyF,EAAA,GAAKsL,cAAA,CAAe1B,CAAA,IAAK1J,EAAA,GAAKF,EAAA,GAAKuM,WAAA,CAAY,EAAE;cAC7D,OAAO;gBAAEjS,CAAA;gBAAMC,CAAA;gBAAMqP,CAAA,EAAG0B,cAAA,CAAe1B;cAAG;YAC3C;UACF;UAED,OAAO;QACjB,OAAe;UAGL,SAASnR,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;YAC1B6T,aAAA,CAAc7T,CAAA,KAAM,IAAIkT,EAAA,GAAKC,EAAA,EAAIH,EAAA,EAAIC,EAAE;YAEvC,IAAIJ,cAAA,CAAeC,GAAA,IAAOT,wBAAA,CAAyBC,MAAA,EAAQ;cACzD,MAAM9R,KAAA,GAAQR,CAAA,KAAM,IAAIkT,EAAA,GAAKC,EAAA;cAC7B,OAAO;gBAAEtR,CAAA,EAAGrB,KAAA,CAAMqB,CAAA;gBAAGC,CAAA,EAAGtB,KAAA,CAAMsB,CAAA;gBAAGqP,CAAA,EAAG0B,cAAA,CAAe1B;cAAG;YACvD;UACF;UAED,MAAMtP,CAAA,GAAI,EAAEyF,EAAA,GAAKqM,EAAA,IAAMnM,EAAA,GAAKF,EAAA,GAAKwM,WAAA,CAAY,EAAE;UAC/C,MAAMhS,CAAA,GAAI,EAAEyF,EAAA,GAAKoM,EAAA,IAAMlM,EAAA,GAAKF,EAAA,GAAKuM,WAAA,CAAY,EAAE;UAC/C,OAAO;YAAEjS,CAAA;YAAMC,CAAA;YAAMqP,CAAA,EAAGwC;UAAI;QAC7B;MACF;MAED,SAASE,cAAcE,CAAA,EAAGC,SAAA,EAAWC,OAAA,EAAS;QAC5C,MAAMC,EAAA,GAAKD,OAAA,CAAQpS,CAAA,GAAImS,SAAA,CAAUnS,CAAA;QACjC,MAAMsS,EAAA,GAAKF,OAAA,CAAQnS,CAAA,GAAIkS,SAAA,CAAUlS,CAAA;QACjC,MAAMsS,EAAA,GAAKL,CAAA,CAAElS,CAAA,GAAImS,SAAA,CAAUnS,CAAA;QAC3B,MAAMwS,EAAA,GAAKN,CAAA,CAAEjS,CAAA,GAAIkS,SAAA,CAAUlS,CAAA;QAC3B,MAAMwS,EAAA,GAAKJ,EAAA,GAAKG,EAAA,GAAKD,EAAA,GAAKD,EAAA;QAE1B,IAAIJ,CAAA,CAAElS,CAAA,KAAMmS,SAAA,CAAUnS,CAAA,IAAKkS,CAAA,CAAEjS,CAAA,KAAMkS,SAAA,CAAUlS,CAAA,EAAG;UAC9C+Q,cAAA,CAAeC,GAAA,GAAMT,wBAAA,CAAyBC,MAAA;UAC9CO,cAAA,CAAe1B,CAAA,GAAI;UACnB;QACD;QAED,IAAI4C,CAAA,CAAElS,CAAA,KAAMoS,OAAA,CAAQpS,CAAA,IAAKkS,CAAA,CAAEjS,CAAA,KAAMmS,OAAA,CAAQnS,CAAA,EAAG;UAC1C+Q,cAAA,CAAeC,GAAA,GAAMT,wBAAA,CAAyBE,WAAA;UAC9CM,cAAA,CAAe1B,CAAA,GAAI;UACnB;QACD;QAED,IAAImD,EAAA,GAAK,CAACzK,MAAA,CAAOuF,OAAA,EAAS;UACxByD,cAAA,CAAeC,GAAA,GAAMT,wBAAA,CAAyBI,IAAA;UAC9C;QACD;QAED,IAAI6B,EAAA,GAAKzK,MAAA,CAAOuF,OAAA,EAAS;UACvByD,cAAA,CAAeC,GAAA,GAAMT,wBAAA,CAAyBK,KAAA;UAC9C;QACD;QAED,IAAIwB,EAAA,GAAKE,EAAA,GAAK,KAAKD,EAAA,GAAKE,EAAA,GAAK,GAAG;UAC9BxB,cAAA,CAAeC,GAAA,GAAMT,wBAAA,CAAyBM,MAAA;UAC9C;QACD;QAED,IAAIzO,IAAA,CAAKe,IAAA,CAAKiP,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAE,IAAIjQ,IAAA,CAAKe,IAAA,CAAKmP,EAAA,GAAKA,EAAA,GAAKC,EAAA,GAAKA,EAAE,GAAG;UAC/DxB,cAAA,CAAeC,GAAA,GAAMT,wBAAA,CAAyBO,MAAA;UAC9C;QACD;QAED,IAAIzB,CAAA;QAEJ,IAAI+C,EAAA,KAAO,GAAG;UACZ/C,CAAA,GAAIiD,EAAA,GAAKF,EAAA;QACnB,OAAe;UACL/C,CAAA,GAAIkD,EAAA,GAAKF,EAAA;QACV;QAEDtB,cAAA,CAAeC,GAAA,GAAMT,wBAAA,CAAyBG,OAAA;QAC9CK,cAAA,CAAe1B,CAAA,GAAIA,CAAA;MACpB;MAED,SAASoD,iBAAiBC,KAAA,EAAOC,KAAA,EAAO;QACtC,MAAMC,gBAAA,GAAmB,EAAE;QAC3B,MAAMC,aAAA,GAAgB,EAAE;QAExB,SAAS7N,KAAA,GAAQ,GAAGA,KAAA,GAAQ0N,KAAA,CAAMvU,MAAA,EAAQ6G,KAAA,IAAS;UACjD,MAAM8N,cAAA,GAAiBJ,KAAA,CAAM1N,KAAA,GAAQ,CAAC;UACtC,MAAM+N,YAAA,GAAeL,KAAA,CAAM1N,KAAK;UAEhC,SAASgO,MAAA,GAAS,GAAGA,MAAA,GAASL,KAAA,CAAMxU,MAAA,EAAQ6U,MAAA,IAAU;YACpD,MAAMC,cAAA,GAAiBN,KAAA,CAAMK,MAAA,GAAS,CAAC;YACvC,MAAME,YAAA,GAAeP,KAAA,CAAMK,MAAM;YAEjC,MAAMG,YAAA,GAAelC,oBAAA,CAAqB6B,cAAA,EAAgBC,YAAA,EAAcE,cAAA,EAAgBC,YAAY;YAEpG,IACEC,YAAA,KAAiB,QACjBP,gBAAA,CAAiBQ,IAAA,CACdlV,CAAA,IAAMA,CAAA,CAAEmR,CAAA,IAAK8D,YAAA,CAAa9D,CAAA,GAAItH,MAAA,CAAOuF,OAAA,IAAWpP,CAAA,CAAEmR,CAAA,IAAK8D,YAAA,CAAa9D,CAAA,GAAItH,MAAA,CAAOuF,OAChG,MAAoB,QACN;cACAsF,gBAAA,CAAiB7U,IAAA,CAAKoV,YAAY;cAClCN,aAAA,CAAc9U,IAAA,CAAK,IAAIY,OAAA,CAAQwU,YAAA,CAAapT,CAAA,EAAGoT,YAAA,CAAanT,CAAC,CAAC;YAC/D;UACF;QACF;QAED,OAAO6S,aAAA;MACR;MAED,SAASQ,yBAAyBC,QAAA,EAAUC,WAAA,EAAazV,KAAA,EAAO;QAC9D,MAAM0V,MAAA,GAAS,IAAI7U,OAAA,CAAS;QAC5B4U,WAAA,CAAYE,SAAA,CAAUD,MAAM;QAE5B,MAAME,gBAAA,GAAmB,EAAE;QAE3B5V,KAAA,CAAM6V,OAAA,CAASvY,IAAA,IAAS;UAItB,IAAIA,IAAA,CAAKmY,WAAA,CAAYK,aAAA,CAAcJ,MAAM,GAAG;YAC1C,MAAMX,aAAA,GAAgBJ,gBAAA,CAAiBa,QAAA,EAAUlY,IAAA,CAAKyY,MAAM;YAE5DhB,aAAA,CAAcc,OAAA,CAAS1B,CAAA,IAAM;cAC3ByB,gBAAA,CAAiB3V,IAAA,CAAK;gBAAE+V,UAAA,EAAY1Y,IAAA,CAAK0Y,UAAA;gBAAYC,IAAA,EAAM3Y,IAAA,CAAK2Y,IAAA;gBAAMrV,KAAA,EAAOuT;cAAC,CAAE;YAC9F,CAAa;UACF;QACX,CAAS;QAEDyB,gBAAA,CAAiBM,IAAA,CAAK,CAACC,EAAA,EAAIzS,EAAA,KAAO;UAChC,OAAOyS,EAAA,CAAGvV,KAAA,CAAMqB,CAAA,GAAIyB,EAAA,CAAG9C,KAAA,CAAMqB,CAAA;QACvC,CAAS;QAED,OAAO2T,gBAAA;MACR;MAED,SAASQ,SAASC,UAAA,EAAYC,QAAA,EAAUC,aAAA,EAAcC,aAAA,EAAcC,SAAA,EAAW;QAC7E,IAAIA,SAAA,KAAc,QAAQA,SAAA,KAAc,UAAaA,SAAA,KAAc,IAAI;UACrEA,SAAA,GAAY;QACb;QAED,MAAMC,iBAAA,GAAoB,IAAI7V,OAAA,CAAS;QACvCwV,UAAA,CAAWZ,WAAA,CAAYE,SAAA,CAAUe,iBAAiB;QAElD,MAAMlB,QAAA,GAAW,CACf,IAAI3U,OAAA,CAAQ0V,aAAA,EAAcG,iBAAA,CAAkBxU,CAAC,GAC7C,IAAIrB,OAAA,CAAQ2V,aAAA,EAAcE,iBAAA,CAAkBxU,CAAC,EAC9C;QAED,MAAMyU,qBAAA,GAAwBpB,wBAAA,CAAyBC,QAAA,EAAUa,UAAA,CAAWZ,WAAA,EAAaa,QAAQ;QAEjGK,qBAAA,CAAsBT,IAAA,CAAK,CAACC,EAAA,EAAIzS,EAAA,KAAO;UACrC,OAAOyS,EAAA,CAAGvV,KAAA,CAAMqB,CAAA,GAAIyB,EAAA,CAAG9C,KAAA,CAAMqB,CAAA;QACvC,CAAS;QAED,MAAM2U,iBAAA,GAAoB,EAAE;QAC5B,MAAMC,kBAAA,GAAqB,EAAE;QAE7BF,qBAAA,CAAsBd,OAAA,CAASnS,EAAA,IAAM;UACnC,IAAIA,EAAA,CAAEsS,UAAA,KAAeK,UAAA,CAAWL,UAAA,EAAY;YAC1CY,iBAAA,CAAkB3W,IAAA,CAAKyD,EAAC;UACpC,OAAiB;YACLmT,kBAAA,CAAmB5W,IAAA,CAAKyD,EAAC;UAC1B;QACX,CAAS;QAED,MAAMoT,YAAA,GAAeF,iBAAA,CAAkB,CAAC,EAAEhW,KAAA,CAAMqB,CAAA;QAGhD,MAAM8U,KAAA,GAAQ,EAAE;QAChB,IAAI3W,CAAA,GAAI;QAER,OAAOA,CAAA,GAAIyW,kBAAA,CAAmBxW,MAAA,IAAUwW,kBAAA,CAAmBzW,CAAC,EAAEQ,KAAA,CAAMqB,CAAA,GAAI6U,YAAA,EAAc;UACpF,IAAIC,KAAA,CAAM1W,MAAA,GAAS,KAAK0W,KAAA,CAAMA,KAAA,CAAM1W,MAAA,GAAS,CAAC,MAAMwW,kBAAA,CAAmBzW,CAAC,EAAE4V,UAAA,EAAY;YACpFe,KAAA,CAAMvW,GAAA,CAAK;UACvB,OAAiB;YACLuW,KAAA,CAAM9W,IAAA,CAAK4W,kBAAA,CAAmBzW,CAAC,EAAE4V,UAAU;UAC5C;UAED5V,CAAA;QACD;QAED2W,KAAA,CAAM9W,IAAA,CAAKoW,UAAA,CAAWL,UAAU;QAEhC,IAAIS,SAAA,KAAc,WAAW;UAC3B,MAAMO,MAAA,GAASD,KAAA,CAAM1W,MAAA,GAAS,MAAM,IAAI,OAAO;UAC/C,MAAM4W,SAAA,GAAYF,KAAA,CAAMA,KAAA,CAAM1W,MAAA,GAAS,CAAC;UAExC,OAAO;YAAE2V,UAAA,EAAYK,UAAA,CAAWL,UAAA;YAAYgB,MAAA;YAAgBE,GAAA,EAAKD;UAAW;QACtF,WAAmBR,SAAA,KAAc,WAAW;UAElC,IAAIO,MAAA,GAAS;UACb,IAAIC,SAAA,GAAY;UAChB,IAAIE,WAAA,GAAc;UAElB,SAASzT,EAAA,GAAI,GAAGA,EAAA,GAAIqT,KAAA,CAAM1W,MAAA,EAAQqD,EAAA,IAAK;YACrC,MAAMsS,UAAA,GAAae,KAAA,CAAMrT,EAAC;YAC1B,IAAIsT,MAAA,EAAQ;cACVG,WAAA,GAAcb,QAAA,CAASN,UAAU,EAAEC,IAAA;cACnCe,MAAA,GAAS;cACTC,SAAA,GAAYjB,UAAA;YACb,WAAUmB,WAAA,KAAgBb,QAAA,CAASN,UAAU,EAAEC,IAAA,EAAM;cACpDkB,WAAA,GAAcb,QAAA,CAASN,UAAU,EAAEC,IAAA;cACnCe,MAAA,GAAS;YACV;UACF;UAED,OAAO;YAAEhB,UAAA,EAAYK,UAAA,CAAWL,UAAA;YAAYgB,MAAA;YAAgBE,GAAA,EAAKD;UAAW;QACtF,OAAe;UACLnZ,OAAA,CAAQ4B,IAAA,CAAK,iBAAiB+W,SAAA,GAAY,iCAAiC;QAC5E;MACF;MASD,IAAIW,YAAA,GAAe5E,SAAA;MACnB,IAAI6E,YAAA,GAAe,CAAC7E,SAAA;MAEpB,IAAI8E,WAAA,GAAc/E,SAAA,CAAU9K,QAAA,CAAShE,GAAA,CAAK0Q,CAAA,IAAM;QAC9C,MAAM4B,MAAA,GAAS5B,CAAA,CAAEoD,SAAA,CAAW;QAC5B,IAAIC,IAAA,GAAO,CAAChF,SAAA;QACZ,IAAIiF,IAAA,GAAOjF,SAAA;QACX,IAAIkF,IAAA,GAAO,CAAClF,SAAA;QACZ,IAAImF,IAAA,GAAOnF,SAAA;QAIX,SAASpS,CAAA,GAAI,GAAGA,CAAA,GAAI2V,MAAA,CAAO1V,MAAA,EAAQD,CAAA,IAAK;UACtC,MAAMwX,EAAA,GAAI7B,MAAA,CAAO3V,CAAC;UAElB,IAAIwX,EAAA,CAAE1V,CAAA,GAAIsV,IAAA,EAAM;YACdA,IAAA,GAAOI,EAAA,CAAE1V,CAAA;UACV;UAED,IAAI0V,EAAA,CAAE1V,CAAA,GAAIuV,IAAA,EAAM;YACdA,IAAA,GAAOG,EAAA,CAAE1V,CAAA;UACV;UAED,IAAI0V,EAAA,CAAE3V,CAAA,GAAIyV,IAAA,EAAM;YACdA,IAAA,GAAOE,EAAA,CAAE3V,CAAA;UACV;UAED,IAAI2V,EAAA,CAAE3V,CAAA,GAAI0V,IAAA,EAAM;YACdA,IAAA,GAAOC,EAAA,CAAE3V,CAAA;UACV;QACF;QAGD,IAAIoV,YAAA,IAAgBK,IAAA,EAAM;UACxBL,YAAA,GAAeK,IAAA,GAAO;QACvB;QAED,IAAIN,YAAA,IAAgBO,IAAA,EAAM;UACxBP,YAAA,GAAeO,IAAA,GAAO;QACvB;QAED,OAAO;UACL5U,MAAA,EAAQoR,CAAA,CAAEpR,MAAA;UACVgT,MAAA;UACAE,IAAA,EAAM4B,UAAA,CAAWC,WAAA,CAAY/B,MAAM;UACnCC,UAAA,EAAY;UACZP,WAAA,EAAa,IAAIsC,IAAA,CAAK,IAAIlX,OAAA,CAAQ8W,IAAA,EAAMF,IAAI,GAAG,IAAI5W,OAAA,CAAQ6W,IAAA,EAAMF,IAAI,CAAC;QACvE;MACT,CAAO;MAEDF,WAAA,GAAcA,WAAA,CAAY/T,MAAA,CAAQyU,EAAA,IAAOA,EAAA,CAAGjC,MAAA,CAAO1V,MAAA,GAAS,CAAC;MAE7D,SAAS2V,UAAA,GAAa,GAAGA,UAAA,GAAasB,WAAA,CAAYjX,MAAA,EAAQ2V,UAAA,IAAc;QACtEsB,WAAA,CAAYtB,UAAU,EAAEA,UAAA,GAAaA,UAAA;MACtC;MAGD,MAAMiC,OAAA,GAAUX,WAAA,CAAY7T,GAAA,CAAK0Q,CAAA,IAC/BiC,QAAA,CACEjC,CAAA,EACAmD,WAAA,EACAF,YAAA,EACAC,YAAA,EACA9E,SAAA,CAAUrS,QAAA,GAAWqS,SAAA,CAAUrS,QAAA,CAAS/B,KAAA,CAAM+Z,QAAA,GAAW,MAC1D,CACF;MAED,MAAMC,cAAA,GAAiB,EAAE;MACzBb,WAAA,CAAYzB,OAAA,CAAS1B,CAAA,IAAM;QACzB,MAAMiE,QAAA,GAAWH,OAAA,CAAQ9D,CAAA,CAAE6B,UAAU;QAErC,IAAI,CAACoC,QAAA,CAASpB,MAAA,EAAQ;UACpB,MAAMqB,KAAA,GAAQ,IAAIC,KAAA,CAAO;UACzBD,KAAA,CAAMtV,MAAA,GAASoR,CAAA,CAAEpR,MAAA;UACjB,MAAMwV,KAAA,GAAQN,OAAA,CAAQ1U,MAAA,CAAQsD,CAAA,IAAMA,CAAA,CAAEmQ,MAAA,IAAUnQ,CAAA,CAAEqQ,GAAA,KAAQ/C,CAAA,CAAE6B,UAAU;UACtEuC,KAAA,CAAM1C,OAAA,CAAShP,CAAA,IAAM;YACnB,MAAM2R,IAAA,GAAOlB,WAAA,CAAYzQ,CAAA,CAAEmP,UAAU;YACrC,MAAM1Y,IAAA,GAAO,IAAIiK,IAAA,CAAM;YACvBjK,IAAA,CAAKyF,MAAA,GAASyV,IAAA,CAAKzV,MAAA;YACnBsV,KAAA,CAAME,KAAA,CAAMtY,IAAA,CAAK3C,IAAI;UACjC,CAAW;UACD6a,cAAA,CAAelY,IAAA,CAAKoY,KAAK;QAC1B;MACT,CAAO;MAED,OAAOF,cAAA;IACR;IAED,OAAOM,eAAeC,KAAA,EAAO9Y,KAAA,EAAO+Y,QAAA,EAAUC,OAAA,EAASC,UAAA,EAAY;MAQjEH,KAAA,GAAQA,KAAA,KAAU,SAAYA,KAAA,GAAQ;MACtC9Y,KAAA,GAAQA,KAAA,KAAU,SAAYA,KAAA,GAAQ;MACtC+Y,QAAA,GAAWA,QAAA,KAAa,SAAYA,QAAA,GAAW;MAC/CC,OAAA,GAAUA,OAAA,KAAY,SAAYA,OAAA,GAAU;MAC5CC,UAAA,GAAaA,UAAA,KAAe,SAAYA,UAAA,GAAa;MAErD,OAAO;QACLC,WAAA,EAAalZ,KAAA;QACbqS,WAAA,EAAayG,KAAA;QACbxG,cAAA,EAAgByG,QAAA;QAChBxG,aAAA,EAAeyG,OAAA;QACfxG,gBAAA,EAAkByG;MACnB;IACF;IAED,OAAOE,eAAehD,MAAA,EAAQ5X,KAAA,EAAO6a,YAAA,EAAcC,WAAA,EAAa;MAS9D,MAAMC,QAAA,GAAW,EAAE;MACnB,MAAMC,OAAA,GAAU,EAAE;MAClB,MAAMC,GAAA,GAAM,EAAE;MAEd,IAAI7c,UAAA,CAAU8c,yBAAA,CAA0BtD,MAAA,EAAQ5X,KAAA,EAAO6a,YAAA,EAAcC,WAAA,EAAaC,QAAA,EAAUC,OAAA,EAASC,GAAG,MAAM,GAAG;QAC/G,OAAO;MACR;MAED,MAAME,QAAA,GAAW,IAAIC,cAAA,CAAgB;MACrCD,QAAA,CAASE,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBP,QAAA,EAAU,CAAC,CAAC;MACzEI,QAAA,CAASE,YAAA,CAAa,UAAU,IAAIC,sBAAA,CAAuBN,OAAA,EAAS,CAAC,CAAC;MACtEG,QAAA,CAASE,YAAA,CAAa,MAAM,IAAIC,sBAAA,CAAuBL,GAAA,EAAK,CAAC,CAAC;MAE9D,OAAOE,QAAA;IACR;IAED,OAAOD,0BAA0BtD,MAAA,EAAQ5X,KAAA,EAAO6a,YAAA,EAAcC,WAAA,EAAaC,QAAA,EAAUC,OAAA,EAASC,GAAA,EAAKM,YAAA,EAAc;MAQ/G,MAAMC,QAAA,GAAW,IAAI9Y,OAAA,CAAS;MAC9B,MAAM+Y,QAAA,GAAW,IAAI/Y,OAAA,CAAS;MAC9B,MAAMgZ,QAAA,GAAW,IAAIhZ,OAAA,CAAS;MAC9B,MAAMiZ,QAAA,GAAW,IAAIjZ,OAAA,CAAS;MAC9B,MAAMkZ,QAAA,GAAW,IAAIlZ,OAAA,CAAS;MAC9B,MAAMmZ,QAAA,GAAW,IAAInZ,OAAA,CAAS;MAC9B,MAAMoZ,QAAA,GAAW,IAAIpZ,OAAA,CAAS;MAC9B,MAAMqZ,UAAA,GAAa,IAAIrZ,OAAA,CAAS;MAChC,MAAMsZ,UAAA,GAAa,IAAItZ,OAAA,CAAS;MAChC,MAAMuZ,OAAA,GAAU,IAAIvZ,OAAA,CAAS;MAC7B,MAAMwZ,OAAA,GAAU,IAAIxZ,OAAA,CAAS;MAC7B,MAAMyZ,aAAA,GAAgB,IAAIzZ,OAAA,CAAS;MACnC,MAAM0Z,aAAA,GAAgB,IAAI1Z,OAAA,CAAS;MACnC,MAAM2Z,UAAA,GAAa,IAAI3Z,OAAA,CAAS;MAChC,MAAM4Z,UAAA,GAAa,IAAI5Z,OAAA,CAAS;MAChC,MAAM6Z,UAAA,GAAa,IAAI7Z,OAAA,CAAS;MAChC,MAAM8Z,UAAA,GAAa,IAAI9Z,OAAA,CAAS;MAEhCmY,YAAA,GAAeA,YAAA,KAAiB,SAAYA,YAAA,GAAe;MAC3DC,WAAA,GAAcA,WAAA,KAAgB,SAAYA,WAAA,GAAc;MACxDS,YAAA,GAAeA,YAAA,KAAiB,SAAYA,YAAA,GAAe;MAG3D3D,MAAA,GAAS6E,sBAAA,CAAuB7E,MAAM;MAEtC,MAAM8E,SAAA,GAAY9E,MAAA,CAAO1V,MAAA;MAEzB,IAAIwa,SAAA,GAAY,GAAG,OAAO;MAE1B,MAAMC,QAAA,GAAW/E,MAAA,CAAO,CAAC,EAAEgF,MAAA,CAAOhF,MAAA,CAAO8E,SAAA,GAAY,CAAC,CAAC;MAEvD,IAAI7X,YAAA;MACJ,IAAIgY,aAAA,GAAgBjF,MAAA,CAAO,CAAC;MAC5B,IAAIkF,SAAA;MAEJ,MAAMC,YAAA,GAAe/c,KAAA,CAAM8T,WAAA,GAAc;MAEzC,MAAMkJ,MAAA,GAAS,KAAKN,SAAA,GAAY;MAChC,IAAIO,EAAA,GAAK;QACPC,EAAA;MAEF,IAAIC,iBAAA;MACJ,IAAIC,gBAAA;MACJ,IAAIC,OAAA;MACJ,IAAIC,uBAAA,GAA0B;MAE9B,IAAIC,WAAA,GAAc;MAClB,IAAIC,iBAAA,GAAoBjC,YAAA,GAAe;MACvC,IAAIkC,mBAAA,GAAsBlC,YAAA,GAAe;MAGzCmC,SAAA,CAAU9F,MAAA,CAAO,CAAC,GAAGA,MAAA,CAAO,CAAC,GAAG4D,QAAQ,EAAEmC,cAAA,CAAeZ,YAAY;MACrEhB,UAAA,CAAWzZ,IAAA,CAAKsV,MAAA,CAAO,CAAC,CAAC,EAAEgG,GAAA,CAAIpC,QAAQ;MACvCQ,UAAA,CAAW1Z,IAAA,CAAKsV,MAAA,CAAO,CAAC,CAAC,EAAEiG,GAAA,CAAIrC,QAAQ;MACvCS,OAAA,CAAQ3Z,IAAA,CAAKyZ,UAAU;MACvBG,OAAA,CAAQ5Z,IAAA,CAAK0Z,UAAU;MAEvB,SAAS8B,MAAA,GAAS,GAAGA,MAAA,GAASpB,SAAA,EAAWoB,MAAA,IAAU;QACjDjZ,YAAA,GAAe+S,MAAA,CAAOkG,MAAM;QAG5B,IAAIA,MAAA,KAAWpB,SAAA,GAAY,GAAG;UAC5B,IAAIC,QAAA,EAAU;YAEZG,SAAA,GAAYlF,MAAA,CAAO,CAAC;UACrB,OAAMkF,SAAA,GAAY;QAC7B,OAAe;UACLA,SAAA,GAAYlF,MAAA,CAAOkG,MAAA,GAAS,CAAC;QAC9B;QAGD,MAAMC,OAAA,GAAUvC,QAAA;QAChBkC,SAAA,CAAUb,aAAA,EAAehY,YAAA,EAAckZ,OAAO;QAE9CrC,QAAA,CAASpZ,IAAA,CAAKyb,OAAO,EAAEJ,cAAA,CAAeZ,YAAY;QAClDZ,aAAA,CAAc7Z,IAAA,CAAKuC,YAAY,EAAE+Y,GAAA,CAAIlC,QAAQ;QAC7CU,aAAA,CAAc9Z,IAAA,CAAKuC,YAAY,EAAEgZ,GAAA,CAAInC,QAAQ;QAE7CwB,EAAA,GAAKD,EAAA,GAAKD,MAAA;QAEVG,iBAAA,GAAoB;QAEpB,IAAIL,SAAA,KAAc,QAAW;UAE3BY,SAAA,CAAU7Y,YAAA,EAAciY,SAAA,EAAWrB,QAAQ;UAE3CC,QAAA,CAASpZ,IAAA,CAAKmZ,QAAQ,EAAEkC,cAAA,CAAeZ,YAAY;UACnDV,UAAA,CAAW/Z,IAAA,CAAKuC,YAAY,EAAE+Y,GAAA,CAAIlC,QAAQ;UAC1CY,UAAA,CAAWha,IAAA,CAAKuC,YAAY,EAAEgZ,GAAA,CAAInC,QAAQ;UAE1C0B,gBAAA,GAAmB;UACnB1B,QAAA,CAASsC,UAAA,CAAWlB,SAAA,EAAWD,aAAa;UAC5C,IAAIkB,OAAA,CAAQ5V,GAAA,CAAIuT,QAAQ,IAAI,GAAG;YAC7B0B,gBAAA,GAAmB;UACpB;UAED,IAAIU,MAAA,KAAW,GAAGR,uBAAA,GAA0BF,gBAAA;UAE5C1B,QAAA,CAASsC,UAAA,CAAWlB,SAAA,EAAWjY,YAAY;UAC3C6W,QAAA,CAASuC,SAAA,CAAW;UACpB,MAAM9V,GAAA,GAAMhC,IAAA,CAAKE,GAAA,CAAI0X,OAAA,CAAQ5V,GAAA,CAAIuT,QAAQ,CAAC;UAG1C,IAAIvT,GAAA,GAAM2D,MAAA,CAAOuF,OAAA,EAAS;YAExB,MAAM6M,SAAA,GAAYnB,YAAA,GAAe5U,GAAA;YACjCuT,QAAA,CAASiC,cAAA,CAAe,CAACO,SAAS;YAClCvC,QAAA,CAASqC,UAAA,CAAWnZ,YAAA,EAAcgY,aAAa;YAC/CjB,QAAA,CAAStZ,IAAA,CAAKqZ,QAAQ,EAAEwC,SAAA,CAAUD,SAAS,EAAEL,GAAA,CAAInC,QAAQ;YACzDa,UAAA,CAAWja,IAAA,CAAKsZ,QAAQ,EAAEwC,MAAA,CAAQ;YAClC,MAAMC,YAAA,GAAezC,QAAA,CAAS1Z,MAAA,CAAQ;YACtC,MAAMoc,iBAAA,GAAoB3C,QAAA,CAASzZ,MAAA,CAAQ;YAC3CyZ,QAAA,CAAS4C,YAAA,CAAaD,iBAAiB;YACvCzC,QAAA,CAASmC,UAAA,CAAWlB,SAAA,EAAWjY,YAAY;YAC3C,MAAM2Z,iBAAA,GAAoB3C,QAAA,CAAS3Z,MAAA,CAAQ;YAC3C2Z,QAAA,CAAS0C,YAAA,CAAaC,iBAAiB;YAEvC,IAAI7C,QAAA,CAASxT,GAAA,CAAIoU,UAAU,IAAI+B,iBAAA,IAAqBzC,QAAA,CAAS1T,GAAA,CAAIoU,UAAU,IAAIiC,iBAAA,EAAmB;cAChGrB,iBAAA,GAAoB;YACrB;YAEDX,UAAA,CAAWla,IAAA,CAAKsZ,QAAQ,EAAEiC,GAAA,CAAIhZ,YAAY;YAC1C0X,UAAA,CAAWsB,GAAA,CAAIhZ,YAAY;YAE3BwY,OAAA,GAAU;YAEV,IAAIF,iBAAA,EAAmB;cACrB,IAAIC,gBAAA,EAAkB;gBACpBd,UAAA,CAAWha,IAAA,CAAKia,UAAU;gBAC1BH,aAAA,CAAc9Z,IAAA,CAAKia,UAAU;cAC7C,OAAqB;gBACLF,UAAA,CAAW/Z,IAAA,CAAKia,UAAU;gBAC1BJ,aAAA,CAAc7Z,IAAA,CAAKia,UAAU;cAC9B;YACf,OAAmB;cAGLkC,oBAAA,CAAsB;YACvB;YAED,QAAQze,KAAA,CAAM+T,cAAA;cACZ,KAAK;gBACH2K,wBAAA,CAAyBtB,gBAAA,EAAkBD,iBAAA,EAAmBD,EAAE;gBAEhE;cAEF,KAAK;gBAGHyB,uCAAA,CAAwCvB,gBAAA,EAAkBD,iBAAiB;gBAI3E,IAAIC,gBAAA,EAAkB;kBACpBwB,kBAAA,CAAmB/Z,YAAA,EAAcsX,aAAA,EAAeE,UAAA,EAAYa,EAAA,EAAI,CAAC;gBACnF,OAAuB;kBACL0B,kBAAA,CAAmB/Z,YAAA,EAAcyX,UAAA,EAAYF,aAAA,EAAec,EAAA,EAAI,CAAC;gBAClE;gBAED;cAEF,KAAK;cACL,KAAK;cACL;gBACE,MAAM2B,aAAA,GAAiB9B,YAAA,GAAe/c,KAAA,CAAMiU,gBAAA,GAAoBoK,YAAA;gBAEhE,IAAIQ,aAAA,GAAgB,GAAG;kBAGrB,IAAI7e,KAAA,CAAM+T,cAAA,KAAmB,cAAc;oBACzC2K,wBAAA,CAAyBtB,gBAAA,EAAkBD,iBAAA,EAAmBD,EAAE;oBAChE;kBACpB,OAAyB;oBAGLyB,uCAAA,CAAwCvB,gBAAA,EAAkBD,iBAAiB;oBAI3E,IAAIC,gBAAA,EAAkB;sBACpBvB,QAAA,CAASmC,UAAA,CAAWxB,UAAA,EAAYL,aAAa,EAAEwB,cAAA,CAAekB,aAAa,EAAEhB,GAAA,CAAI1B,aAAa;sBAC9FL,QAAA,CAASkC,UAAA,CAAWxB,UAAA,EAAYH,UAAU,EAAEsB,cAAA,CAAekB,aAAa,EAAEhB,GAAA,CAAIxB,UAAU;sBAExFyC,SAAA,CAAU3C,aAAA,EAAee,EAAA,EAAI,CAAC;sBAC9B4B,SAAA,CAAUjD,QAAA,EAAUqB,EAAA,EAAI,CAAC;sBACzB4B,SAAA,CAAUja,YAAA,EAAcqY,EAAA,EAAI,GAAG;sBAE/B4B,SAAA,CAAUja,YAAA,EAAcqY,EAAA,EAAI,GAAG;sBAC/B4B,SAAA,CAAUjD,QAAA,EAAUqB,EAAA,EAAI,CAAC;sBACzB4B,SAAA,CAAUhD,QAAA,EAAUoB,EAAA,EAAI,CAAC;sBAEzB4B,SAAA,CAAUja,YAAA,EAAcqY,EAAA,EAAI,GAAG;sBAC/B4B,SAAA,CAAUhD,QAAA,EAAUoB,EAAA,EAAI,CAAC;sBACzB4B,SAAA,CAAUzC,UAAA,EAAYa,EAAA,EAAI,CAAC;oBACjD,OAA2B;sBACLrB,QAAA,CAASmC,UAAA,CAAWxB,UAAA,EAAYJ,aAAa,EAAEuB,cAAA,CAAekB,aAAa,EAAEhB,GAAA,CAAIzB,aAAa;sBAC9FN,QAAA,CAASkC,UAAA,CAAWxB,UAAA,EAAYF,UAAU,EAAEqB,cAAA,CAAekB,aAAa,EAAEhB,GAAA,CAAIvB,UAAU;sBAExFwC,SAAA,CAAU1C,aAAA,EAAec,EAAA,EAAI,CAAC;sBAC9B4B,SAAA,CAAUjD,QAAA,EAAUqB,EAAA,EAAI,CAAC;sBACzB4B,SAAA,CAAUja,YAAA,EAAcqY,EAAA,EAAI,GAAG;sBAE/B4B,SAAA,CAAUja,YAAA,EAAcqY,EAAA,EAAI,GAAG;sBAC/B4B,SAAA,CAAUjD,QAAA,EAAUqB,EAAA,EAAI,CAAC;sBACzB4B,SAAA,CAAUhD,QAAA,EAAUoB,EAAA,EAAI,CAAC;sBAEzB4B,SAAA,CAAUja,YAAA,EAAcqY,EAAA,EAAI,GAAG;sBAC/B4B,SAAA,CAAUhD,QAAA,EAAUoB,EAAA,EAAI,CAAC;sBACzB4B,SAAA,CAAUxC,UAAA,EAAYY,EAAA,EAAI,CAAC;oBAC5B;kBACF;gBACnB,OAAuB;kBAGL,IAAIC,iBAAA,EAAmB;oBAGrB,IAAIC,gBAAA,EAAkB;sBACpB0B,SAAA,CAAU9C,UAAA,EAAYiB,EAAA,EAAI,CAAC;sBAC3B6B,SAAA,CAAU/C,UAAA,EAAYkB,EAAA,EAAI,CAAC;sBAC3B6B,SAAA,CAAUtC,UAAA,EAAYU,EAAA,EAAI,CAAC;sBAE3B4B,SAAA,CAAU9C,UAAA,EAAYiB,EAAA,EAAI,CAAC;sBAC3B6B,SAAA,CAAUtC,UAAA,EAAYU,EAAA,EAAI,CAAC;sBAC3B4B,SAAA,CAAUvC,UAAA,EAAYW,EAAA,EAAI,CAAC;oBACjD,OAA2B;sBACL4B,SAAA,CAAU9C,UAAA,EAAYiB,EAAA,EAAI,CAAC;sBAC3B6B,SAAA,CAAU/C,UAAA,EAAYkB,EAAA,EAAI,CAAC;sBAC3B6B,SAAA,CAAUtC,UAAA,EAAYU,EAAA,EAAI,CAAC;sBAE3B4B,SAAA,CAAU/C,UAAA,EAAYkB,EAAA,EAAI,CAAC;sBAC3B6B,SAAA,CAAUvC,UAAA,EAAYW,EAAA,EAAI,CAAC;sBAC3B4B,SAAA,CAAUtC,UAAA,EAAYU,EAAA,EAAI,CAAC;oBAC5B;oBAED,IAAIE,gBAAA,EAAkB;sBACpBf,UAAA,CAAW/Z,IAAA,CAAKka,UAAU;oBAChD,OAA2B;sBACLF,UAAA,CAAWha,IAAA,CAAKka,UAAU;oBAC3B;kBACrB,OAAyB;oBAGL,IAAIY,gBAAA,EAAkB;sBACpB0B,SAAA,CAAU3C,aAAA,EAAee,EAAA,EAAI,CAAC;sBAC9B4B,SAAA,CAAUtC,UAAA,EAAYU,EAAA,EAAI,CAAC;sBAC3B4B,SAAA,CAAUja,YAAA,EAAcqY,EAAA,EAAI,GAAG;sBAE/B4B,SAAA,CAAUja,YAAA,EAAcqY,EAAA,EAAI,GAAG;sBAC/B4B,SAAA,CAAUtC,UAAA,EAAYU,EAAA,EAAI,CAAC;sBAC3B4B,SAAA,CAAUzC,UAAA,EAAYa,EAAA,EAAI,CAAC;oBACjD,OAA2B;sBACL4B,SAAA,CAAU1C,aAAA,EAAec,EAAA,EAAI,CAAC;sBAC9B4B,SAAA,CAAUtC,UAAA,EAAYU,EAAA,EAAI,CAAC;sBAC3B4B,SAAA,CAAUja,YAAA,EAAcqY,EAAA,EAAI,GAAG;sBAE/B4B,SAAA,CAAUja,YAAA,EAAcqY,EAAA,EAAI,GAAG;sBAC/B4B,SAAA,CAAUtC,UAAA,EAAYU,EAAA,EAAI,CAAC;sBAC3B4B,SAAA,CAAUxC,UAAA,EAAYY,EAAA,EAAI,CAAC;oBAC5B;kBACF;kBAEDG,OAAA,GAAU;gBACX;gBAED;YACH;UACb,OAAiB;YAGLoB,oBAAA,CAAsB;UACvB;QACX,OAAe;UAGLA,oBAAA,CAAsB;QACvB;QAED,IAAI,CAAC9B,QAAA,IAAYmB,MAAA,KAAWpB,SAAA,GAAY,GAAG;UAEzCqC,cAAA,CAAenH,MAAA,CAAO,CAAC,GAAGqE,OAAA,EAASC,OAAA,EAASkB,gBAAA,EAAkB,MAAMH,EAAE;QACvE;QAIDA,EAAA,GAAKC,EAAA;QAELL,aAAA,GAAgBhY,YAAA;QAEhBkX,UAAA,CAAWzZ,IAAA,CAAK+Z,UAAU;QAC1BL,UAAA,CAAW1Z,IAAA,CAAKga,UAAU;MAC3B;MAED,IAAI,CAACK,QAAA,EAAU;QAEboC,cAAA,CAAela,YAAA,EAAcsX,aAAA,EAAeC,aAAA,EAAegB,gBAAA,EAAkB,OAAOF,EAAE;MAC9F,WAAiBC,iBAAA,IAAqBpC,QAAA,EAAU;QAGxC,IAAIiE,SAAA,GAAYxC,UAAA;QAChB,IAAIyC,SAAA,GAAY1C,UAAA;QAEhB,IAAIe,uBAAA,KAA4BF,gBAAA,EAAkB;UAChD4B,SAAA,GAAYzC,UAAA;UACZ0C,SAAA,GAAYzC,UAAA;QACb;QAED,IAAIY,gBAAA,EAAkB;UACpB,IAAIC,OAAA,IAAWC,uBAAA,EAAyB;YACtC2B,SAAA,CAAUC,OAAA,CAAQnE,QAAA,EAAU,IAAI,CAAC;YACjCkE,SAAA,CAAUC,OAAA,CAAQnE,QAAA,EAAU,IAAI,CAAC;YAEjC,IAAIsC,OAAA,EAAS;cACX2B,SAAA,CAAUE,OAAA,CAAQnE,QAAA,EAAU,IAAI,CAAC;YAClC;UACF;QACX,OAAe;UACL,IAAIsC,OAAA,IAAW,CAACC,uBAAA,EAAyB;YACvC2B,SAAA,CAAUC,OAAA,CAAQnE,QAAA,EAAU,IAAI,CAAC;YACjCkE,SAAA,CAAUC,OAAA,CAAQnE,QAAA,EAAU,IAAI,CAAC;YAEjC,IAAIsC,OAAA,EAAS;cACX2B,SAAA,CAAUE,OAAA,CAAQnE,QAAA,EAAU,IAAI,CAAC;YAClC;UACF;QACF;MACF;MAED,OAAOwC,WAAA;MAMP,SAASG,UAAUyB,EAAA,EAAI1F,EAAA,EAAIjO,MAAA,EAAQ;QACjCA,MAAA,CAAOwS,UAAA,CAAWvE,EAAA,EAAI0F,EAAE;QACxB,OAAO3T,MAAA,CAAOsD,GAAA,CAAI,CAACtD,MAAA,CAAOzH,CAAA,EAAGyH,MAAA,CAAO1H,CAAC,EAAEma,SAAA,CAAW;MACnD;MAED,SAASa,UAAUM,QAAA,EAAUnS,CAAA,EAAGrH,CAAA,EAAG;QACjC,IAAImV,QAAA,EAAU;UACZA,QAAA,CAASyC,iBAAiB,IAAI4B,QAAA,CAAStb,CAAA;UACvCiX,QAAA,CAASyC,iBAAA,GAAoB,CAAC,IAAI4B,QAAA,CAASrb,CAAA;UAC3CgX,QAAA,CAASyC,iBAAA,GAAoB,CAAC,IAAI;UAElC,IAAIxC,OAAA,EAAS;YACXA,OAAA,CAAQwC,iBAAiB,IAAI;YAC7BxC,OAAA,CAAQwC,iBAAA,GAAoB,CAAC,IAAI;YACjCxC,OAAA,CAAQwC,iBAAA,GAAoB,CAAC,IAAI;UAClC;UAEDA,iBAAA,IAAqB;UAErB,IAAIvC,GAAA,EAAK;YACPA,GAAA,CAAIwC,mBAAmB,IAAIxQ,CAAA;YAC3BgO,GAAA,CAAIwC,mBAAA,GAAsB,CAAC,IAAI7X,CAAA;YAE/B6X,mBAAA,IAAuB;UACxB;QACF;QAEDF,WAAA,IAAe;MAChB;MAED,SAASqB,mBAAmBrH,MAAA,EAAQ4H,EAAA,EAAI1F,EAAA,EAAIxM,CAAA,EAAGrH,CAAA,EAAG;QAIhD4V,QAAA,CAASlZ,IAAA,CAAK6c,EAAE,EAAEvB,GAAA,CAAIrG,MAAM,EAAE0G,SAAA,CAAW;QACzCxC,QAAA,CAASnZ,IAAA,CAAKmX,EAAE,EAAEmE,GAAA,CAAIrG,MAAM,EAAE0G,SAAA,CAAW;QAEzC,IAAI5P,KAAA,GAAQlI,IAAA,CAAKC,EAAA;QACjB,MAAM+B,GAAA,GAAMqT,QAAA,CAASrT,GAAA,CAAIsT,QAAQ;QACjC,IAAItV,IAAA,CAAKE,GAAA,CAAI8B,GAAG,IAAI,GAAGkG,KAAA,GAAQlI,IAAA,CAAKE,GAAA,CAAIF,IAAA,CAAKmC,IAAA,CAAKH,GAAG,CAAC;QAEtDkG,KAAA,IAASwM,YAAA;QAETa,QAAA,CAASpZ,IAAA,CAAK6c,EAAE;QAEhB,SAASld,CAAA,GAAI,GAAGod,EAAA,GAAKxE,YAAA,GAAe,GAAG5Y,CAAA,GAAIod,EAAA,EAAIpd,CAAA,IAAK;UAClD0Z,QAAA,CAASrZ,IAAA,CAAKoZ,QAAQ,EAAE4D,YAAA,CAAa/H,MAAA,EAAQlJ,KAAK;UAElDyQ,SAAA,CAAUpD,QAAA,EAAUzO,CAAA,EAAGrH,CAAC;UACxBkZ,SAAA,CAAUnD,QAAA,EAAU1O,CAAA,EAAGrH,CAAC;UACxBkZ,SAAA,CAAUvH,MAAA,EAAQtK,CAAA,EAAG,GAAG;UAExByO,QAAA,CAASpZ,IAAA,CAAKqZ,QAAQ;QACvB;QAEDmD,SAAA,CAAUnD,QAAA,EAAU1O,CAAA,EAAGrH,CAAC;QACxBkZ,SAAA,CAAUrF,EAAA,EAAIxM,CAAA,EAAGrH,CAAC;QAClBkZ,SAAA,CAAUvH,MAAA,EAAQtK,CAAA,EAAG,GAAG;MACzB;MAED,SAASwR,qBAAA,EAAuB;QAC9BK,SAAA,CAAU9C,UAAA,EAAYiB,EAAA,EAAI,CAAC;QAC3B6B,SAAA,CAAU/C,UAAA,EAAYkB,EAAA,EAAI,CAAC;QAC3B6B,SAAA,CAAU3C,aAAA,EAAee,EAAA,EAAI,CAAC;QAE9B4B,SAAA,CAAU9C,UAAA,EAAYiB,EAAA,EAAI,CAAC;QAC3B6B,SAAA,CAAU3C,aAAA,EAAee,EAAA,EAAI,CAAC;QAC9B4B,SAAA,CAAU1C,aAAA,EAAec,EAAA,EAAI,CAAC;MAC/B;MAED,SAASwB,yBAAyBa,iBAAA,EAAkBC,kBAAA,EAAmBvS,CAAA,EAAG;QACxE,IAAIuS,kBAAA,EAAmB;UAGrB,IAAID,iBAAA,EAAkB;YAGpBT,SAAA,CAAU9C,UAAA,EAAYiB,EAAA,EAAI,CAAC;YAC3B6B,SAAA,CAAU/C,UAAA,EAAYkB,EAAA,EAAI,CAAC;YAC3B6B,SAAA,CAAU3C,aAAA,EAAee,EAAA,EAAI,CAAC;YAE9B4B,SAAA,CAAU9C,UAAA,EAAYiB,EAAA,EAAI,CAAC;YAC3B6B,SAAA,CAAU3C,aAAA,EAAee,EAAA,EAAI,CAAC;YAC9B4B,SAAA,CAAUvC,UAAA,EAAYW,EAAA,EAAI,CAAC;YAI3B4B,SAAA,CAAU3C,aAAA,EAAelP,CAAA,EAAG,CAAC;YAC7B6R,SAAA,CAAUzC,UAAA,EAAYpP,CAAA,EAAG,CAAC;YAC1B6R,SAAA,CAAUvC,UAAA,EAAYtP,CAAA,EAAG,GAAG;UACxC,OAAiB;YAGL6R,SAAA,CAAU9C,UAAA,EAAYiB,EAAA,EAAI,CAAC;YAC3B6B,SAAA,CAAU/C,UAAA,EAAYkB,EAAA,EAAI,CAAC;YAC3B6B,SAAA,CAAU1C,aAAA,EAAec,EAAA,EAAI,CAAC;YAE9B4B,SAAA,CAAU/C,UAAA,EAAYkB,EAAA,EAAI,CAAC;YAC3B6B,SAAA,CAAUvC,UAAA,EAAYW,EAAA,EAAI,CAAC;YAC3B4B,SAAA,CAAU1C,aAAA,EAAec,EAAA,EAAI,CAAC;YAI9B4B,SAAA,CAAU1C,aAAA,EAAenP,CAAA,EAAG,CAAC;YAC7B6R,SAAA,CAAUvC,UAAA,EAAYtP,CAAA,EAAG,CAAC;YAC1B6R,SAAA,CAAUxC,UAAA,EAAYrP,CAAA,EAAG,CAAC;UAC3B;QACX,OAAe;UAGL,IAAIsS,iBAAA,EAAkB;YACpBT,SAAA,CAAU3C,aAAA,EAAelP,CAAA,EAAG,CAAC;YAC7B6R,SAAA,CAAUzC,UAAA,EAAYpP,CAAA,EAAG,CAAC;YAC1B6R,SAAA,CAAUja,YAAA,EAAcoI,CAAA,EAAG,GAAG;UAC1C,OAAiB;YACL6R,SAAA,CAAU1C,aAAA,EAAenP,CAAA,EAAG,CAAC;YAC7B6R,SAAA,CAAUxC,UAAA,EAAYrP,CAAA,EAAG,CAAC;YAC1B6R,SAAA,CAAUja,YAAA,EAAcoI,CAAA,EAAG,GAAG;UAC/B;QACF;MACF;MAED,SAAS0R,wCAAwCY,iBAAA,EAAkBC,kBAAA,EAAmB;QACpF,IAAIA,kBAAA,EAAmB;UACrB,IAAID,iBAAA,EAAkB;YACpBT,SAAA,CAAU9C,UAAA,EAAYiB,EAAA,EAAI,CAAC;YAC3B6B,SAAA,CAAU/C,UAAA,EAAYkB,EAAA,EAAI,CAAC;YAC3B6B,SAAA,CAAU3C,aAAA,EAAee,EAAA,EAAI,CAAC;YAE9B4B,SAAA,CAAU9C,UAAA,EAAYiB,EAAA,EAAI,CAAC;YAC3B6B,SAAA,CAAU3C,aAAA,EAAee,EAAA,EAAI,CAAC;YAC9B4B,SAAA,CAAUvC,UAAA,EAAYW,EAAA,EAAI,CAAC;YAE3B4B,SAAA,CAAU3C,aAAA,EAAec,EAAA,EAAI,CAAC;YAC9B6B,SAAA,CAAUja,YAAA,EAAcqY,EAAA,EAAI,GAAG;YAC/B4B,SAAA,CAAUvC,UAAA,EAAYW,EAAA,EAAI,CAAC;YAE3B4B,SAAA,CAAUja,YAAA,EAAcqY,EAAA,EAAI,GAAG;YAC/B4B,SAAA,CAAUzC,UAAA,EAAYY,EAAA,EAAI,CAAC;YAC3B6B,SAAA,CAAUvC,UAAA,EAAYW,EAAA,EAAI,CAAC;UACvC,OAAiB;YACL4B,SAAA,CAAU9C,UAAA,EAAYiB,EAAA,EAAI,CAAC;YAC3B6B,SAAA,CAAU/C,UAAA,EAAYkB,EAAA,EAAI,CAAC;YAC3B6B,SAAA,CAAU1C,aAAA,EAAec,EAAA,EAAI,CAAC;YAE9B4B,SAAA,CAAU/C,UAAA,EAAYkB,EAAA,EAAI,CAAC;YAC3B6B,SAAA,CAAUvC,UAAA,EAAYW,EAAA,EAAI,CAAC;YAC3B4B,SAAA,CAAU1C,aAAA,EAAec,EAAA,EAAI,CAAC;YAE9B4B,SAAA,CAAU1C,aAAA,EAAea,EAAA,EAAI,CAAC;YAC9B6B,SAAA,CAAUvC,UAAA,EAAYW,EAAA,EAAI,CAAC;YAC3B4B,SAAA,CAAUja,YAAA,EAAcqY,EAAA,EAAI,GAAG;YAE/B4B,SAAA,CAAUja,YAAA,EAAcqY,EAAA,EAAI,GAAG;YAC/B4B,SAAA,CAAUvC,UAAA,EAAYW,EAAA,EAAI,CAAC;YAC3B4B,SAAA,CAAUxC,UAAA,EAAYW,EAAA,EAAI,CAAC;UAC5B;QACF;MACF;MAED,SAAS8B,eAAexH,MAAA,EAAQ4H,EAAA,EAAI1F,EAAA,EAAI8F,iBAAA,EAAkBhb,KAAA,EAAO0I,CAAA,EAAG;QAIlE,QAAQjN,KAAA,CAAMgU,aAAA;UACZ,KAAK;YACH,IAAIzP,KAAA,EAAO;cACTqa,kBAAA,CAAmBrH,MAAA,EAAQkC,EAAA,EAAI0F,EAAA,EAAIlS,CAAA,EAAG,GAAG;YACvD,OAAmB;cACL2R,kBAAA,CAAmBrH,MAAA,EAAQ4H,EAAA,EAAI1F,EAAA,EAAIxM,CAAA,EAAG,GAAG;YAC1C;YAED;UAEF,KAAK;YACH,IAAI1I,KAAA,EAAO;cACTiX,QAAA,CAASwC,UAAA,CAAWmB,EAAA,EAAI5H,MAAM;cAC9BkE,QAAA,CAAS3M,GAAA,CAAI0M,QAAA,CAASzX,CAAA,EAAG,CAACyX,QAAA,CAAS1X,CAAC;cAEpC4X,QAAA,CAAS+D,UAAA,CAAWjE,QAAA,EAAUC,QAAQ,EAAEoC,GAAA,CAAItG,MAAM;cAClDoE,QAAA,CAASqC,UAAA,CAAWvC,QAAA,EAAUD,QAAQ,EAAEqC,GAAA,CAAItG,MAAM;cAGlD,IAAIgI,iBAAA,EAAkB;gBACpB7D,QAAA,CAASwD,OAAA,CAAQnE,QAAA,EAAU,IAAI,CAAC;gBAChCY,QAAA,CAASuD,OAAA,CAAQnE,QAAA,EAAU,IAAI,CAAC;gBAChCY,QAAA,CAASuD,OAAA,CAAQnE,QAAA,EAAU,IAAI,CAAC;cAChD,OAAqB;gBACLW,QAAA,CAASwD,OAAA,CAAQnE,QAAA,EAAU,IAAI,CAAC;gBAEhCE,GAAA,CAAI,IAAI,IAAI,CAAC,MAAM,IAAIU,QAAA,CAASuD,OAAA,CAAQnE,QAAA,EAAU,IAAI,CAAC,IAAIW,QAAA,CAASwD,OAAA,CAAQnE,QAAA,EAAU,IAAI,CAAC;gBAC3FY,QAAA,CAASuD,OAAA,CAAQnE,QAAA,EAAU,IAAI,CAAC;cACjC;YACf,OAAmB;cACLS,QAAA,CAASwC,UAAA,CAAWvE,EAAA,EAAIlC,MAAM;cAC9BkE,QAAA,CAAS3M,GAAA,CAAI0M,QAAA,CAASzX,CAAA,EAAG,CAACyX,QAAA,CAAS1X,CAAC;cAEpC4X,QAAA,CAAS+D,UAAA,CAAWjE,QAAA,EAAUC,QAAQ,EAAEoC,GAAA,CAAItG,MAAM;cAClDoE,QAAA,CAASqC,UAAA,CAAWvC,QAAA,EAAUD,QAAQ,EAAEqC,GAAA,CAAItG,MAAM;cAElD,MAAMmI,EAAA,GAAK3E,QAAA,CAAS7Y,MAAA;cAGpB,IAAIqd,iBAAA,EAAkB;gBACpB7D,QAAA,CAASwD,OAAA,CAAQnE,QAAA,EAAU2E,EAAA,GAAK,IAAI,CAAC;gBACrC/D,QAAA,CAASuD,OAAA,CAAQnE,QAAA,EAAU2E,EAAA,GAAK,IAAI,CAAC;gBACrC/D,QAAA,CAASuD,OAAA,CAAQnE,QAAA,EAAU2E,EAAA,GAAK,IAAI,CAAC;cACrD,OAAqB;gBACL/D,QAAA,CAASuD,OAAA,CAAQnE,QAAA,EAAU2E,EAAA,GAAK,IAAI,CAAC;gBACrChE,QAAA,CAASwD,OAAA,CAAQnE,QAAA,EAAU2E,EAAA,GAAK,IAAI,CAAC;gBACrC/D,QAAA,CAASuD,OAAA,CAAQnE,QAAA,EAAU2E,EAAA,GAAK,IAAI,CAAC;cACtC;YACF;YAED;QAMH;MACF;MAED,SAASjD,uBAAuBkD,OAAA,EAAQ;QAItC,IAAIC,SAAA,GAAY;QAChB,SAAS3d,CAAA,GAAI,GAAG+K,CAAA,GAAI2S,OAAA,CAAOzd,MAAA,GAAS,GAAGD,CAAA,GAAI+K,CAAA,EAAG/K,CAAA,IAAK;UACjD,IAAI0d,OAAA,CAAO1d,CAAC,EAAE4d,UAAA,CAAWF,OAAA,CAAO1d,CAAA,GAAI,CAAC,CAAC,IAAI6Y,WAAA,EAAa;YACrD8E,SAAA,GAAY;YACZ;UACD;QACF;QAED,IAAI,CAACA,SAAA,EAAW,OAAOD,OAAA;QAEvB,MAAMG,SAAA,GAAY,EAAE;QACpBA,SAAA,CAAUhe,IAAA,CAAK6d,OAAA,CAAO,CAAC,CAAC;QAExB,SAAS1d,CAAA,GAAI,GAAG+K,CAAA,GAAI2S,OAAA,CAAOzd,MAAA,GAAS,GAAGD,CAAA,GAAI+K,CAAA,EAAG/K,CAAA,IAAK;UACjD,IAAI0d,OAAA,CAAO1d,CAAC,EAAE4d,UAAA,CAAWF,OAAA,CAAO1d,CAAA,GAAI,CAAC,CAAC,KAAK6Y,WAAA,EAAa;YACtDgF,SAAA,CAAUhe,IAAA,CAAK6d,OAAA,CAAO1d,CAAC,CAAC;UACzB;QACF;QAED6d,SAAA,CAAUhe,IAAA,CAAK6d,OAAA,CAAOA,OAAA,CAAOzd,MAAA,GAAS,CAAC,CAAC;QAExC,OAAO4d,SAAA;MACR;IACF;EACF;EAED,OAAO1hB,UAAA;AACT,GAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}