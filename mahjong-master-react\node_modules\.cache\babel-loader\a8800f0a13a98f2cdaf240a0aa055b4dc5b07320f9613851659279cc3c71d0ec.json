{"ast": null, "code": "import { color } from '../color/index.mjs';\nimport { complex } from '../complex/index.mjs';\nimport { dimensionValueTypes } from '../dimensions.mjs';\nimport { testValueType } from '../test.mjs';\n\n/**\n * A list of all ValueTypes\n */\nconst valueTypes = [...dimensionValueTypes, color, complex];\n/**\n * Tests a value against the list of ValueTypes\n */\nconst findValueType = v => valueTypes.find(testValueType(v));\nexport { findValueType };", "map": {"version": 3, "names": ["color", "complex", "dimensionValueTypes", "testValueType", "valueTypes", "findValueType", "v", "find"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/motion-dom/dist/es/value/types/utils/find.mjs"], "sourcesContent": ["import { color } from '../color/index.mjs';\nimport { complex } from '../complex/index.mjs';\nimport { dimensionValueTypes } from '../dimensions.mjs';\nimport { testValueType } from '../test.mjs';\n\n/**\n * A list of all ValueTypes\n */\nconst valueTypes = [...dimensionValueTypes, color, complex];\n/**\n * Tests a value against the list of ValueTypes\n */\nconst findValueType = (v) => valueTypes.find(testValueType(v));\n\nexport { findValueType };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,SAASC,aAAa,QAAQ,aAAa;;AAE3C;AACA;AACA;AACA,MAAMC,UAAU,GAAG,CAAC,GAAGF,mBAAmB,EAAEF,KAAK,EAAEC,OAAO,CAAC;AAC3D;AACA;AACA;AACA,MAAMI,aAAa,GAAIC,CAAC,IAAKF,UAAU,CAACG,IAAI,CAACJ,aAAa,CAACG,CAAC,CAAC,CAAC;AAE9D,SAASD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}