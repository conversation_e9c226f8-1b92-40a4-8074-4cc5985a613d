{"ast": null, "code": "export function isSharedArrayBufferSupported() {\n  return typeof SharedArrayBuffer !== 'undefined';\n}\nexport function convertToBufferType(array, BufferConstructor) {\n  if (array === null) {\n    return array;\n  } else if (array.buffer) {\n    const buffer = array.buffer;\n    if (buffer.constructor === BufferConstructor) {\n      return array;\n    }\n    const ArrayConstructor = array.constructor;\n    const result = new ArrayConstructor(new BufferConstructor(buffer.byteLength));\n    result.set(array);\n    return result;\n  } else {\n    if (array.constructor === BufferConstructor) {\n      return array;\n    }\n    const result = new BufferConstructor(array.byteLength);\n    new Uint8Array(result).set(new Uint8Array(array));\n    return result;\n  }\n}", "map": {"version": 3, "names": ["isSharedArrayBufferSupported", "SharedArrayBuffer", "convertToBufferType", "array", "BufferConstructor", "buffer", "constructor", "ArrayConstructor", "result", "byteLength", "set", "Uint8Array"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/three-mesh-bvh/src/utils/BufferUtils.js"], "sourcesContent": ["export function isSharedArrayBufferSupported() {\n\n\treturn typeof SharedArrayBuffer !== 'undefined';\n\n}\n\nexport function convertToBufferType( array, BufferConstructor ) {\n\n\tif ( array === null ) {\n\n\t\treturn array;\n\n\t} else if ( array.buffer ) {\n\n\t\tconst buffer = array.buffer;\n\t\tif ( buffer.constructor === BufferConstructor ) {\n\n\t\t\treturn array;\n\n\t\t}\n\n\t\tconst ArrayConstructor = array.constructor;\n\t\tconst result = new ArrayConstructor( new BufferConstructor( buffer.byteLength ) );\n\t\tresult.set( array );\n\t\treturn result;\n\n\t} else {\n\n\t\tif ( array.constructor === BufferConstructor ) {\n\n\t\t\treturn array;\n\n\t\t}\n\n\t\tconst result = new BufferConstructor( array.byteLength );\n\t\tnew Uint8Array( result ).set( new Uint8Array( array ) );\n\t\treturn result;\n\n\t}\n\n}\n"], "mappings": "AAAA,OAAO,SAASA,4BAA4BA,CAAA,EAAG;EAE9C,OAAO,OAAOC,iBAAiB,KAAK,WAAW;AAEhD;AAEA,OAAO,SAASC,mBAAmBA,CAAEC,KAAK,EAAEC,iBAAiB,EAAG;EAE/D,IAAKD,KAAK,KAAK,IAAI,EAAG;IAErB,OAAOA,KAAK;EAEb,CAAC,MAAM,IAAKA,KAAK,CAACE,MAAM,EAAG;IAE1B,MAAMA,MAAM,GAAGF,KAAK,CAACE,MAAM;IAC3B,IAAKA,MAAM,CAACC,WAAW,KAAKF,iBAAiB,EAAG;MAE/C,OAAOD,KAAK;IAEb;IAEA,MAAMI,gBAAgB,GAAGJ,KAAK,CAACG,WAAW;IAC1C,MAAME,MAAM,GAAG,IAAID,gBAAgB,CAAE,IAAIH,iBAAiB,CAAEC,MAAM,CAACI,UAAW,CAAE,CAAC;IACjFD,MAAM,CAACE,GAAG,CAAEP,KAAM,CAAC;IACnB,OAAOK,MAAM;EAEd,CAAC,MAAM;IAEN,IAAKL,KAAK,CAACG,WAAW,KAAKF,iBAAiB,EAAG;MAE9C,OAAOD,KAAK;IAEb;IAEA,MAAMK,MAAM,GAAG,IAAIJ,iBAAiB,CAAED,KAAK,CAACM,UAAW,CAAC;IACxD,IAAIE,UAAU,CAAEH,MAAO,CAAC,CAACE,GAAG,CAAE,IAAIC,UAAU,CAAER,KAAM,CAAE,CAAC;IACvD,OAAOK,MAAM;EAEd;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}