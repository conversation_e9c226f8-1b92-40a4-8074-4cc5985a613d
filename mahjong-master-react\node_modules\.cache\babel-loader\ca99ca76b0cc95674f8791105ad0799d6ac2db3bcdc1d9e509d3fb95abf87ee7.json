{"ast": null, "code": "import _objectSpread from \"F:/= \\u795E\\u706F\\u667A\\u5E93/- AI \\u521B\\u4F5C/AI APP/\\u795E\\u706F\\u9EBB\\u5C06\\u5927\\u5E08/mahjong-master-react/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { number } from './numbers/index.mjs';\nconst int = _objectSpread(_objectSpread({}, number), {}, {\n  transform: Math.round\n});\nexport { int };", "map": {"version": 3, "names": ["number", "int", "_objectSpread", "transform", "Math", "round"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/motion-dom/dist/es/value/types/int.mjs"], "sourcesContent": ["import { number } from './numbers/index.mjs';\n\nconst int = {\n    ...number,\n    transform: Math.round,\n};\n\nexport { int };\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,qBAAqB;AAE5C,MAAMC,GAAG,GAAAC,aAAA,CAAAA,aAAA,KACFF,MAAM;EACTG,SAAS,EAAEC,IAAI,CAACC;AAAK,EACxB;AAED,SAASJ,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}