{"ast": null, "code": "const floatRegex = /-?(?:[0-9]+(?:\\.[0-9]+)?|\\.[0-9]+)/g;\nexport { floatRegex };", "map": {"version": 3, "names": ["floatRegex"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/motion-dom/dist/es/value/types/utils/float-regex.mjs"], "sourcesContent": ["const floatRegex = /-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/gu;\n\nexport { floatRegex };\n"], "mappings": "AAAA,MAAMA,UAAU,GAAG,qCAA6B;AAEhD,SAASA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}