{"ast": null, "code": "import _objectSpread from \"F:/= \\u795E\\u706F\\u667A\\u5E93/- AI \\u521B\\u4F5C/AI APP/\\u795E\\u706F\\u9EBB\\u5C06\\u5927\\u5E08/mahjong-master-react/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { createHtmlRenderState } from '../../html/utils/create-render-state.mjs';\nconst createSvgRenderState = () => _objectSpread(_objectSpread({}, createHtmlRenderState()), {}, {\n  attrs: {}\n});\nexport { createSvgRenderState };", "map": {"version": 3, "names": ["createHtmlRenderState", "createSvgRenderState", "_objectSpread", "attrs"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/framer-motion/dist/es/render/svg/utils/create-render-state.mjs"], "sourcesContent": ["import { createHtmlRenderState } from '../../html/utils/create-render-state.mjs';\n\nconst createSvgRenderState = () => ({\n    ...createHtmlRenderState(),\n    attrs: {},\n});\n\nexport { createSvgRenderState };\n"], "mappings": ";AAAA,SAASA,qBAAqB,QAAQ,0CAA0C;AAEhF,MAAMC,oBAAoB,GAAGA,CAAA,KAAAC,aAAA,CAAAA,aAAA,KACtBF,qBAAqB,CAAC,CAAC;EAC1BG,KAAK,EAAE,CAAC;AAAC,EACX;AAEF,SAASF,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}