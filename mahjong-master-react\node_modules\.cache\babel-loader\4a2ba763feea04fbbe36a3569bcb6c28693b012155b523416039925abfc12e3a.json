{"ast": null, "code": "import { getLongestEdgeIndex, computeSurfaceArea, copyBounds, unionBounds, expandByTriangleBounds } from '../../utils/ArrayBoxUtilities.js';\nimport { CENTER, AVERAGE, SAH, TRIANGLE_INTERSECT_COST, TRAVERSAL_COST } from '../Constants.js';\nconst BIN_COUNT = 32;\nconst binsSort = (a, b) => a.candidate - b.candidate;\nconst sahBins = new Array(BIN_COUNT).fill().map(() => {\n  return {\n    count: 0,\n    bounds: new Float32Array(6),\n    rightCacheBounds: new Float32Array(6),\n    leftCacheBounds: new Float32Array(6),\n    candidate: 0\n  };\n});\nconst leftBounds = new Float32Array(6);\nexport function getOptimalSplit(nodeBoundingData, centroidBoundingData, triangleBounds, offset, count, strategy) {\n  let axis = -1;\n  let pos = 0;\n\n  // Center\n  if (strategy === CENTER) {\n    axis = getLongestEdgeIndex(centroidBoundingData);\n    if (axis !== -1) {\n      pos = (centroidBoundingData[axis] + centroidBoundingData[axis + 3]) / 2;\n    }\n  } else if (strategy === AVERAGE) {\n    axis = getLongestEdgeIndex(nodeBoundingData);\n    if (axis !== -1) {\n      pos = getAverage(triangleBounds, offset, count, axis);\n    }\n  } else if (strategy === SAH) {\n    const rootSurfaceArea = computeSurfaceArea(nodeBoundingData);\n    let bestCost = TRIANGLE_INTERSECT_COST * count;\n\n    // iterate over all axes\n    const cStart = offset * 6;\n    const cEnd = (offset + count) * 6;\n    for (let a = 0; a < 3; a++) {\n      const axisLeft = centroidBoundingData[a];\n      const axisRight = centroidBoundingData[a + 3];\n      const axisLength = axisRight - axisLeft;\n      const binWidth = axisLength / BIN_COUNT;\n\n      // If we have fewer triangles than we're planning to split then just check all\n      // the triangle positions because it will be faster.\n      if (count < BIN_COUNT / 4) {\n        // initialize the bin candidates\n        const truncatedBins = [...sahBins];\n        truncatedBins.length = count;\n\n        // set the candidates\n        let b = 0;\n        for (let c = cStart; c < cEnd; c += 6, b++) {\n          const bin = truncatedBins[b];\n          bin.candidate = triangleBounds[c + 2 * a];\n          bin.count = 0;\n          const {\n            bounds,\n            leftCacheBounds,\n            rightCacheBounds\n          } = bin;\n          for (let d = 0; d < 3; d++) {\n            rightCacheBounds[d] = Infinity;\n            rightCacheBounds[d + 3] = -Infinity;\n            leftCacheBounds[d] = Infinity;\n            leftCacheBounds[d + 3] = -Infinity;\n            bounds[d] = Infinity;\n            bounds[d + 3] = -Infinity;\n          }\n          expandByTriangleBounds(c, triangleBounds, bounds);\n        }\n        truncatedBins.sort(binsSort);\n\n        // remove redundant splits\n        let splitCount = count;\n        for (let bi = 0; bi < splitCount; bi++) {\n          const bin = truncatedBins[bi];\n          while (bi + 1 < splitCount && truncatedBins[bi + 1].candidate === bin.candidate) {\n            truncatedBins.splice(bi + 1, 1);\n            splitCount--;\n          }\n        }\n\n        // find the appropriate bin for each triangle and expand the bounds.\n        for (let c = cStart; c < cEnd; c += 6) {\n          const center = triangleBounds[c + 2 * a];\n          for (let bi = 0; bi < splitCount; bi++) {\n            const bin = truncatedBins[bi];\n            if (center >= bin.candidate) {\n              expandByTriangleBounds(c, triangleBounds, bin.rightCacheBounds);\n            } else {\n              expandByTriangleBounds(c, triangleBounds, bin.leftCacheBounds);\n              bin.count++;\n            }\n          }\n        }\n\n        // expand all the bounds\n        for (let bi = 0; bi < splitCount; bi++) {\n          const bin = truncatedBins[bi];\n          const leftCount = bin.count;\n          const rightCount = count - bin.count;\n\n          // check the cost of this split\n          const leftBounds = bin.leftCacheBounds;\n          const rightBounds = bin.rightCacheBounds;\n          let leftProb = 0;\n          if (leftCount !== 0) {\n            leftProb = computeSurfaceArea(leftBounds) / rootSurfaceArea;\n          }\n          let rightProb = 0;\n          if (rightCount !== 0) {\n            rightProb = computeSurfaceArea(rightBounds) / rootSurfaceArea;\n          }\n          const cost = TRAVERSAL_COST + TRIANGLE_INTERSECT_COST * (leftProb * leftCount + rightProb * rightCount);\n          if (cost < bestCost) {\n            axis = a;\n            bestCost = cost;\n            pos = bin.candidate;\n          }\n        }\n      } else {\n        // reset the bins\n        for (let i = 0; i < BIN_COUNT; i++) {\n          const bin = sahBins[i];\n          bin.count = 0;\n          bin.candidate = axisLeft + binWidth + i * binWidth;\n          const bounds = bin.bounds;\n          for (let d = 0; d < 3; d++) {\n            bounds[d] = Infinity;\n            bounds[d + 3] = -Infinity;\n          }\n        }\n\n        // iterate over all center positions\n        for (let c = cStart; c < cEnd; c += 6) {\n          const triCenter = triangleBounds[c + 2 * a];\n          const relativeCenter = triCenter - axisLeft;\n\n          // in the partition function if the centroid lies on the split plane then it is\n          // considered to be on the right side of the split\n          let binIndex = ~~(relativeCenter / binWidth);\n          if (binIndex >= BIN_COUNT) binIndex = BIN_COUNT - 1;\n          const bin = sahBins[binIndex];\n          bin.count++;\n          expandByTriangleBounds(c, triangleBounds, bin.bounds);\n        }\n\n        // cache the unioned bounds from right to left so we don't have to regenerate them each time\n        const lastBin = sahBins[BIN_COUNT - 1];\n        copyBounds(lastBin.bounds, lastBin.rightCacheBounds);\n        for (let i = BIN_COUNT - 2; i >= 0; i--) {\n          const bin = sahBins[i];\n          const nextBin = sahBins[i + 1];\n          unionBounds(bin.bounds, nextBin.rightCacheBounds, bin.rightCacheBounds);\n        }\n        let leftCount = 0;\n        for (let i = 0; i < BIN_COUNT - 1; i++) {\n          const bin = sahBins[i];\n          const binCount = bin.count;\n          const bounds = bin.bounds;\n          const nextBin = sahBins[i + 1];\n          const rightBounds = nextBin.rightCacheBounds;\n\n          // don't do anything with the bounds if the new bounds have no triangles\n          if (binCount !== 0) {\n            if (leftCount === 0) {\n              copyBounds(bounds, leftBounds);\n            } else {\n              unionBounds(bounds, leftBounds, leftBounds);\n            }\n          }\n          leftCount += binCount;\n\n          // check the cost of this split\n          let leftProb = 0;\n          let rightProb = 0;\n          if (leftCount !== 0) {\n            leftProb = computeSurfaceArea(leftBounds) / rootSurfaceArea;\n          }\n          const rightCount = count - leftCount;\n          if (rightCount !== 0) {\n            rightProb = computeSurfaceArea(rightBounds) / rootSurfaceArea;\n          }\n          const cost = TRAVERSAL_COST + TRIANGLE_INTERSECT_COST * (leftProb * leftCount + rightProb * rightCount);\n          if (cost < bestCost) {\n            axis = a;\n            bestCost = cost;\n            pos = bin.candidate;\n          }\n        }\n      }\n    }\n  } else {\n    console.warn(`MeshBVH: Invalid build strategy value ${strategy} used.`);\n  }\n  return {\n    axis,\n    pos\n  };\n}\n\n// returns the average coordinate on the specified axis of the all the provided triangles\nfunction getAverage(triangleBounds, offset, count, axis) {\n  let avg = 0;\n  for (let i = offset, end = offset + count; i < end; i++) {\n    avg += triangleBounds[i * 6 + axis * 2];\n  }\n  return avg / count;\n}", "map": {"version": 3, "names": ["getLongestEdgeIndex", "computeSurfaceArea", "copyBounds", "unionBounds", "expandByTriangleBounds", "CENTER", "AVERAGE", "SAH", "TRIANGLE_INTERSECT_COST", "TRAVERSAL_COST", "BIN_COUNT", "binsSort", "a", "b", "candidate", "sahBins", "Array", "fill", "map", "count", "bounds", "Float32Array", "rightCacheBounds", "leftCacheBounds", "leftBounds", "getOptimalSplit", "nodeBoundingData", "centroidBoundingData", "triangleBounds", "offset", "strategy", "axis", "pos", "getAverage", "rootSurfaceArea", "bestCost", "cStart", "cEnd", "axisLeft", "axisRight", "axisLength", "<PERSON><PERSON><PERSON><PERSON>", "truncatedBins", "length", "c", "bin", "d", "Infinity", "sort", "splitCount", "bi", "splice", "center", "leftCount", "rightCount", "rightBounds", "leftProb", "rightProb", "cost", "i", "triCenter", "relativeCenter", "binIndex", "lastBin", "nextBin", "binCount", "console", "warn", "avg", "end"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/three-mesh-bvh/src/core/build/splitUtils.js"], "sourcesContent": ["import { getLongestEdgeIndex, computeSurfaceArea, copyBounds, unionBounds, expandByTriangleBounds } from '../../utils/ArrayBoxUtilities.js';\nimport { CENTER, AVERAGE, SAH, TRIANGLE_INTERSECT_COST, TRAVERSAL_COST } from '../Constants.js';\n\nconst BIN_COUNT = 32;\nconst binsSort = ( a, b ) => a.candidate - b.candidate;\nconst sahBins = new Array( BIN_COUNT ).fill().map( () => {\n\n\treturn {\n\n\t\tcount: 0,\n\t\tbounds: new Float32Array( 6 ),\n\t\trightCacheBounds: new Float32Array( 6 ),\n\t\tleftCacheBounds: new Float32Array( 6 ),\n\t\tcandidate: 0,\n\n\t};\n\n} );\nconst leftBounds = new Float32Array( 6 );\n\nexport function getOptimalSplit( nodeBoundingData, centroidBoundingData, triangleBounds, offset, count, strategy ) {\n\n\tlet axis = - 1;\n\tlet pos = 0;\n\n\t// Center\n\tif ( strategy === CENTER ) {\n\n\t\taxis = getLongestEdgeIndex( centroidBoundingData );\n\t\tif ( axis !== - 1 ) {\n\n\t\t\tpos = ( centroidBoundingData[ axis ] + centroidBoundingData[ axis + 3 ] ) / 2;\n\n\t\t}\n\n\t} else if ( strategy === AVERAGE ) {\n\n\t\taxis = getLongestEdgeIndex( nodeBoundingData );\n\t\tif ( axis !== - 1 ) {\n\n\t\t\tpos = getAverage( triangleBounds, offset, count, axis );\n\n\t\t}\n\n\t} else if ( strategy === SAH ) {\n\n\t\tconst rootSurfaceArea = computeSurfaceArea( nodeBoundingData );\n\t\tlet bestCost = TRIANGLE_INTERSECT_COST * count;\n\n\t\t// iterate over all axes\n\t\tconst cStart = offset * 6;\n\t\tconst cEnd = ( offset + count ) * 6;\n\t\tfor ( let a = 0; a < 3; a ++ ) {\n\n\t\t\tconst axisLeft = centroidBoundingData[ a ];\n\t\t\tconst axisRight = centroidBoundingData[ a + 3 ];\n\t\t\tconst axisLength = axisRight - axisLeft;\n\t\t\tconst binWidth = axisLength / BIN_COUNT;\n\n\t\t\t// If we have fewer triangles than we're planning to split then just check all\n\t\t\t// the triangle positions because it will be faster.\n\t\t\tif ( count < BIN_COUNT / 4 ) {\n\n\t\t\t\t// initialize the bin candidates\n\t\t\t\tconst truncatedBins = [ ...sahBins ];\n\t\t\t\ttruncatedBins.length = count;\n\n\t\t\t\t// set the candidates\n\t\t\t\tlet b = 0;\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6, b ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ b ];\n\t\t\t\t\tbin.candidate = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tbin.count = 0;\n\n\t\t\t\t\tconst {\n\t\t\t\t\t\tbounds,\n\t\t\t\t\t\tleftCacheBounds,\n\t\t\t\t\t\trightCacheBounds,\n\t\t\t\t\t} = bin;\n\t\t\t\t\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\t\t\t\t\trightCacheBounds[ d ] = Infinity;\n\t\t\t\t\t\trightCacheBounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t\tleftCacheBounds[ d ] = Infinity;\n\t\t\t\t\t\tleftCacheBounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t\tbounds[ d ] = Infinity;\n\t\t\t\t\t\tbounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t}\n\n\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bounds );\n\n\t\t\t\t}\n\n\t\t\t\ttruncatedBins.sort( binsSort );\n\n\t\t\t\t// remove redundant splits\n\t\t\t\tlet splitCount = count;\n\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\twhile ( bi + 1 < splitCount && truncatedBins[ bi + 1 ].candidate === bin.candidate ) {\n\n\t\t\t\t\t\ttruncatedBins.splice( bi + 1, 1 );\n\t\t\t\t\t\tsplitCount --;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// find the appropriate bin for each triangle and expand the bounds.\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6 ) {\n\n\t\t\t\t\tconst center = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\t\tif ( center >= bin.candidate ) {\n\n\t\t\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.rightCacheBounds );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.leftCacheBounds );\n\t\t\t\t\t\t\tbin.count ++;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// expand all the bounds\n\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\tconst leftCount = bin.count;\n\t\t\t\t\tconst rightCount = count - bin.count;\n\n\t\t\t\t\t// check the cost of this split\n\t\t\t\t\tconst leftBounds = bin.leftCacheBounds;\n\t\t\t\t\tconst rightBounds = bin.rightCacheBounds;\n\n\t\t\t\t\tlet leftProb = 0;\n\t\t\t\t\tif ( leftCount !== 0 ) {\n\n\t\t\t\t\t\tleftProb = computeSurfaceArea( leftBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tlet rightProb = 0;\n\t\t\t\t\tif ( rightCount !== 0 ) {\n\n\t\t\t\t\t\trightProb = computeSurfaceArea( rightBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst cost = TRAVERSAL_COST + TRIANGLE_INTERSECT_COST * (\n\t\t\t\t\t\tleftProb * leftCount + rightProb * rightCount\n\t\t\t\t\t);\n\n\t\t\t\t\tif ( cost < bestCost ) {\n\n\t\t\t\t\t\taxis = a;\n\t\t\t\t\t\tbestCost = cost;\n\t\t\t\t\t\tpos = bin.candidate;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\t// reset the bins\n\t\t\t\tfor ( let i = 0; i < BIN_COUNT; i ++ ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tbin.count = 0;\n\t\t\t\t\tbin.candidate = axisLeft + binWidth + i * binWidth;\n\n\t\t\t\t\tconst bounds = bin.bounds;\n\t\t\t\t\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\t\t\t\t\tbounds[ d ] = Infinity;\n\t\t\t\t\t\tbounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// iterate over all center positions\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6 ) {\n\n\t\t\t\t\tconst triCenter = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tconst relativeCenter = triCenter - axisLeft;\n\n\t\t\t\t\t// in the partition function if the centroid lies on the split plane then it is\n\t\t\t\t\t// considered to be on the right side of the split\n\t\t\t\t\tlet binIndex = ~ ~ ( relativeCenter / binWidth );\n\t\t\t\t\tif ( binIndex >= BIN_COUNT ) binIndex = BIN_COUNT - 1;\n\n\t\t\t\t\tconst bin = sahBins[ binIndex ];\n\t\t\t\t\tbin.count ++;\n\n\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.bounds );\n\n\t\t\t\t}\n\n\t\t\t\t// cache the unioned bounds from right to left so we don't have to regenerate them each time\n\t\t\t\tconst lastBin = sahBins[ BIN_COUNT - 1 ];\n\t\t\t\tcopyBounds( lastBin.bounds, lastBin.rightCacheBounds );\n\t\t\t\tfor ( let i = BIN_COUNT - 2; i >= 0; i -- ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tconst nextBin = sahBins[ i + 1 ];\n\t\t\t\t\tunionBounds( bin.bounds, nextBin.rightCacheBounds, bin.rightCacheBounds );\n\n\t\t\t\t}\n\n\t\t\t\tlet leftCount = 0;\n\t\t\t\tfor ( let i = 0; i < BIN_COUNT - 1; i ++ ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tconst binCount = bin.count;\n\t\t\t\t\tconst bounds = bin.bounds;\n\n\t\t\t\t\tconst nextBin = sahBins[ i + 1 ];\n\t\t\t\t\tconst rightBounds = nextBin.rightCacheBounds;\n\n\t\t\t\t\t// don't do anything with the bounds if the new bounds have no triangles\n\t\t\t\t\tif ( binCount !== 0 ) {\n\n\t\t\t\t\t\tif ( leftCount === 0 ) {\n\n\t\t\t\t\t\t\tcopyBounds( bounds, leftBounds );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\tunionBounds( bounds, leftBounds, leftBounds );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\tleftCount += binCount;\n\n\t\t\t\t\t// check the cost of this split\n\t\t\t\t\tlet leftProb = 0;\n\t\t\t\t\tlet rightProb = 0;\n\n\t\t\t\t\tif ( leftCount !== 0 ) {\n\n\t\t\t\t\t\tleftProb = computeSurfaceArea( leftBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst rightCount = count - leftCount;\n\t\t\t\t\tif ( rightCount !== 0 ) {\n\n\t\t\t\t\t\trightProb = computeSurfaceArea( rightBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst cost = TRAVERSAL_COST + TRIANGLE_INTERSECT_COST * (\n\t\t\t\t\t\tleftProb * leftCount + rightProb * rightCount\n\t\t\t\t\t);\n\n\t\t\t\t\tif ( cost < bestCost ) {\n\n\t\t\t\t\t\taxis = a;\n\t\t\t\t\t\tbestCost = cost;\n\t\t\t\t\t\tpos = bin.candidate;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t} else {\n\n\t\tconsole.warn( `MeshBVH: Invalid build strategy value ${ strategy } used.` );\n\n\t}\n\n\treturn { axis, pos };\n\n}\n\n// returns the average coordinate on the specified axis of the all the provided triangles\nfunction getAverage( triangleBounds, offset, count, axis ) {\n\n\tlet avg = 0;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tavg += triangleBounds[ i * 6 + axis * 2 ];\n\n\t}\n\n\treturn avg / count;\n\n}\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,kBAAkB,EAAEC,UAAU,EAAEC,WAAW,EAAEC,sBAAsB,QAAQ,kCAAkC;AAC3I,SAASC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,uBAAuB,EAAEC,cAAc,QAAQ,iBAAiB;AAE/F,MAAMC,SAAS,GAAG,EAAE;AACpB,MAAMC,QAAQ,GAAGA,CAAEC,CAAC,EAAEC,CAAC,KAAMD,CAAC,CAACE,SAAS,GAAGD,CAAC,CAACC,SAAS;AACtD,MAAMC,OAAO,GAAG,IAAIC,KAAK,CAAEN,SAAU,CAAC,CAACO,IAAI,CAAC,CAAC,CAACC,GAAG,CAAE,MAAM;EAExD,OAAO;IAENC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,IAAIC,YAAY,CAAE,CAAE,CAAC;IAC7BC,gBAAgB,EAAE,IAAID,YAAY,CAAE,CAAE,CAAC;IACvCE,eAAe,EAAE,IAAIF,YAAY,CAAE,CAAE,CAAC;IACtCP,SAAS,EAAE;EAEZ,CAAC;AAEF,CAAE,CAAC;AACH,MAAMU,UAAU,GAAG,IAAIH,YAAY,CAAE,CAAE,CAAC;AAExC,OAAO,SAASI,eAAeA,CAAEC,gBAAgB,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,MAAM,EAAEV,KAAK,EAAEW,QAAQ,EAAG;EAElH,IAAIC,IAAI,GAAG,CAAE,CAAC;EACd,IAAIC,GAAG,GAAG,CAAC;;EAEX;EACA,IAAKF,QAAQ,KAAKzB,MAAM,EAAG;IAE1B0B,IAAI,GAAG/B,mBAAmB,CAAE2B,oBAAqB,CAAC;IAClD,IAAKI,IAAI,KAAK,CAAE,CAAC,EAAG;MAEnBC,GAAG,GAAG,CAAEL,oBAAoB,CAAEI,IAAI,CAAE,GAAGJ,oBAAoB,CAAEI,IAAI,GAAG,CAAC,CAAE,IAAK,CAAC;IAE9E;EAED,CAAC,MAAM,IAAKD,QAAQ,KAAKxB,OAAO,EAAG;IAElCyB,IAAI,GAAG/B,mBAAmB,CAAE0B,gBAAiB,CAAC;IAC9C,IAAKK,IAAI,KAAK,CAAE,CAAC,EAAG;MAEnBC,GAAG,GAAGC,UAAU,CAAEL,cAAc,EAAEC,MAAM,EAAEV,KAAK,EAAEY,IAAK,CAAC;IAExD;EAED,CAAC,MAAM,IAAKD,QAAQ,KAAKvB,GAAG,EAAG;IAE9B,MAAM2B,eAAe,GAAGjC,kBAAkB,CAAEyB,gBAAiB,CAAC;IAC9D,IAAIS,QAAQ,GAAG3B,uBAAuB,GAAGW,KAAK;;IAE9C;IACA,MAAMiB,MAAM,GAAGP,MAAM,GAAG,CAAC;IACzB,MAAMQ,IAAI,GAAG,CAAER,MAAM,GAAGV,KAAK,IAAK,CAAC;IACnC,KAAM,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B,MAAM0B,QAAQ,GAAGX,oBAAoB,CAAEf,CAAC,CAAE;MAC1C,MAAM2B,SAAS,GAAGZ,oBAAoB,CAAEf,CAAC,GAAG,CAAC,CAAE;MAC/C,MAAM4B,UAAU,GAAGD,SAAS,GAAGD,QAAQ;MACvC,MAAMG,QAAQ,GAAGD,UAAU,GAAG9B,SAAS;;MAEvC;MACA;MACA,IAAKS,KAAK,GAAGT,SAAS,GAAG,CAAC,EAAG;QAE5B;QACA,MAAMgC,aAAa,GAAG,CAAE,GAAG3B,OAAO,CAAE;QACpC2B,aAAa,CAACC,MAAM,GAAGxB,KAAK;;QAE5B;QACA,IAAIN,CAAC,GAAG,CAAC;QACT,KAAM,IAAI+B,CAAC,GAAGR,MAAM,EAAEQ,CAAC,GAAGP,IAAI,EAAEO,CAAC,IAAI,CAAC,EAAE/B,CAAC,EAAG,EAAG;UAE9C,MAAMgC,GAAG,GAAGH,aAAa,CAAE7B,CAAC,CAAE;UAC9BgC,GAAG,CAAC/B,SAAS,GAAGc,cAAc,CAAEgB,CAAC,GAAG,CAAC,GAAGhC,CAAC,CAAE;UAC3CiC,GAAG,CAAC1B,KAAK,GAAG,CAAC;UAEb,MAAM;YACLC,MAAM;YACNG,eAAe;YACfD;UACD,CAAC,GAAGuB,GAAG;UACP,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;YAE9BxB,gBAAgB,CAAEwB,CAAC,CAAE,GAAGC,QAAQ;YAChCzB,gBAAgB,CAAEwB,CAAC,GAAG,CAAC,CAAE,GAAG,CAAEC,QAAQ;YAEtCxB,eAAe,CAAEuB,CAAC,CAAE,GAAGC,QAAQ;YAC/BxB,eAAe,CAAEuB,CAAC,GAAG,CAAC,CAAE,GAAG,CAAEC,QAAQ;YAErC3B,MAAM,CAAE0B,CAAC,CAAE,GAAGC,QAAQ;YACtB3B,MAAM,CAAE0B,CAAC,GAAG,CAAC,CAAE,GAAG,CAAEC,QAAQ;UAE7B;UAEA3C,sBAAsB,CAAEwC,CAAC,EAAEhB,cAAc,EAAER,MAAO,CAAC;QAEpD;QAEAsB,aAAa,CAACM,IAAI,CAAErC,QAAS,CAAC;;QAE9B;QACA,IAAIsC,UAAU,GAAG9B,KAAK;QACtB,KAAM,IAAI+B,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGD,UAAU,EAAEC,EAAE,EAAG,EAAG;UAE1C,MAAML,GAAG,GAAGH,aAAa,CAAEQ,EAAE,CAAE;UAC/B,OAAQA,EAAE,GAAG,CAAC,GAAGD,UAAU,IAAIP,aAAa,CAAEQ,EAAE,GAAG,CAAC,CAAE,CAACpC,SAAS,KAAK+B,GAAG,CAAC/B,SAAS,EAAG;YAEpF4B,aAAa,CAACS,MAAM,CAAED,EAAE,GAAG,CAAC,EAAE,CAAE,CAAC;YACjCD,UAAU,EAAG;UAEd;QAED;;QAEA;QACA,KAAM,IAAIL,CAAC,GAAGR,MAAM,EAAEQ,CAAC,GAAGP,IAAI,EAAEO,CAAC,IAAI,CAAC,EAAG;UAExC,MAAMQ,MAAM,GAAGxB,cAAc,CAAEgB,CAAC,GAAG,CAAC,GAAGhC,CAAC,CAAE;UAC1C,KAAM,IAAIsC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGD,UAAU,EAAEC,EAAE,EAAG,EAAG;YAE1C,MAAML,GAAG,GAAGH,aAAa,CAAEQ,EAAE,CAAE;YAC/B,IAAKE,MAAM,IAAIP,GAAG,CAAC/B,SAAS,EAAG;cAE9BV,sBAAsB,CAAEwC,CAAC,EAAEhB,cAAc,EAAEiB,GAAG,CAACvB,gBAAiB,CAAC;YAElE,CAAC,MAAM;cAENlB,sBAAsB,CAAEwC,CAAC,EAAEhB,cAAc,EAAEiB,GAAG,CAACtB,eAAgB,CAAC;cAChEsB,GAAG,CAAC1B,KAAK,EAAG;YAEb;UAED;QAED;;QAEA;QACA,KAAM,IAAI+B,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGD,UAAU,EAAEC,EAAE,EAAG,EAAG;UAE1C,MAAML,GAAG,GAAGH,aAAa,CAAEQ,EAAE,CAAE;UAC/B,MAAMG,SAAS,GAAGR,GAAG,CAAC1B,KAAK;UAC3B,MAAMmC,UAAU,GAAGnC,KAAK,GAAG0B,GAAG,CAAC1B,KAAK;;UAEpC;UACA,MAAMK,UAAU,GAAGqB,GAAG,CAACtB,eAAe;UACtC,MAAMgC,WAAW,GAAGV,GAAG,CAACvB,gBAAgB;UAExC,IAAIkC,QAAQ,GAAG,CAAC;UAChB,IAAKH,SAAS,KAAK,CAAC,EAAG;YAEtBG,QAAQ,GAAGvD,kBAAkB,CAAEuB,UAAW,CAAC,GAAGU,eAAe;UAE9D;UAEA,IAAIuB,SAAS,GAAG,CAAC;UACjB,IAAKH,UAAU,KAAK,CAAC,EAAG;YAEvBG,SAAS,GAAGxD,kBAAkB,CAAEsD,WAAY,CAAC,GAAGrB,eAAe;UAEhE;UAEA,MAAMwB,IAAI,GAAGjD,cAAc,GAAGD,uBAAuB,IACpDgD,QAAQ,GAAGH,SAAS,GAAGI,SAAS,GAAGH,UAAU,CAC7C;UAED,IAAKI,IAAI,GAAGvB,QAAQ,EAAG;YAEtBJ,IAAI,GAAGnB,CAAC;YACRuB,QAAQ,GAAGuB,IAAI;YACf1B,GAAG,GAAGa,GAAG,CAAC/B,SAAS;UAEpB;QAED;MAED,CAAC,MAAM;QAEN;QACA,KAAM,IAAI6C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjD,SAAS,EAAEiD,CAAC,EAAG,EAAG;UAEtC,MAAMd,GAAG,GAAG9B,OAAO,CAAE4C,CAAC,CAAE;UACxBd,GAAG,CAAC1B,KAAK,GAAG,CAAC;UACb0B,GAAG,CAAC/B,SAAS,GAAGwB,QAAQ,GAAGG,QAAQ,GAAGkB,CAAC,GAAGlB,QAAQ;UAElD,MAAMrB,MAAM,GAAGyB,GAAG,CAACzB,MAAM;UACzB,KAAM,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;YAE9B1B,MAAM,CAAE0B,CAAC,CAAE,GAAGC,QAAQ;YACtB3B,MAAM,CAAE0B,CAAC,GAAG,CAAC,CAAE,GAAG,CAAEC,QAAQ;UAE7B;QAED;;QAEA;QACA,KAAM,IAAIH,CAAC,GAAGR,MAAM,EAAEQ,CAAC,GAAGP,IAAI,EAAEO,CAAC,IAAI,CAAC,EAAG;UAExC,MAAMgB,SAAS,GAAGhC,cAAc,CAAEgB,CAAC,GAAG,CAAC,GAAGhC,CAAC,CAAE;UAC7C,MAAMiD,cAAc,GAAGD,SAAS,GAAGtB,QAAQ;;UAE3C;UACA;UACA,IAAIwB,QAAQ,GAAG,CAAE,EAAID,cAAc,GAAGpB,QAAQ,CAAE;UAChD,IAAKqB,QAAQ,IAAIpD,SAAS,EAAGoD,QAAQ,GAAGpD,SAAS,GAAG,CAAC;UAErD,MAAMmC,GAAG,GAAG9B,OAAO,CAAE+C,QAAQ,CAAE;UAC/BjB,GAAG,CAAC1B,KAAK,EAAG;UAEZf,sBAAsB,CAAEwC,CAAC,EAAEhB,cAAc,EAAEiB,GAAG,CAACzB,MAAO,CAAC;QAExD;;QAEA;QACA,MAAM2C,OAAO,GAAGhD,OAAO,CAAEL,SAAS,GAAG,CAAC,CAAE;QACxCR,UAAU,CAAE6D,OAAO,CAAC3C,MAAM,EAAE2C,OAAO,CAACzC,gBAAiB,CAAC;QACtD,KAAM,IAAIqC,CAAC,GAAGjD,SAAS,GAAG,CAAC,EAAEiD,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAG,EAAG;UAE3C,MAAMd,GAAG,GAAG9B,OAAO,CAAE4C,CAAC,CAAE;UACxB,MAAMK,OAAO,GAAGjD,OAAO,CAAE4C,CAAC,GAAG,CAAC,CAAE;UAChCxD,WAAW,CAAE0C,GAAG,CAACzB,MAAM,EAAE4C,OAAO,CAAC1C,gBAAgB,EAAEuB,GAAG,CAACvB,gBAAiB,CAAC;QAE1E;QAEA,IAAI+B,SAAS,GAAG,CAAC;QACjB,KAAM,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjD,SAAS,GAAG,CAAC,EAAEiD,CAAC,EAAG,EAAG;UAE1C,MAAMd,GAAG,GAAG9B,OAAO,CAAE4C,CAAC,CAAE;UACxB,MAAMM,QAAQ,GAAGpB,GAAG,CAAC1B,KAAK;UAC1B,MAAMC,MAAM,GAAGyB,GAAG,CAACzB,MAAM;UAEzB,MAAM4C,OAAO,GAAGjD,OAAO,CAAE4C,CAAC,GAAG,CAAC,CAAE;UAChC,MAAMJ,WAAW,GAAGS,OAAO,CAAC1C,gBAAgB;;UAE5C;UACA,IAAK2C,QAAQ,KAAK,CAAC,EAAG;YAErB,IAAKZ,SAAS,KAAK,CAAC,EAAG;cAEtBnD,UAAU,CAAEkB,MAAM,EAAEI,UAAW,CAAC;YAEjC,CAAC,MAAM;cAENrB,WAAW,CAAEiB,MAAM,EAAEI,UAAU,EAAEA,UAAW,CAAC;YAE9C;UAED;UAEA6B,SAAS,IAAIY,QAAQ;;UAErB;UACA,IAAIT,QAAQ,GAAG,CAAC;UAChB,IAAIC,SAAS,GAAG,CAAC;UAEjB,IAAKJ,SAAS,KAAK,CAAC,EAAG;YAEtBG,QAAQ,GAAGvD,kBAAkB,CAAEuB,UAAW,CAAC,GAAGU,eAAe;UAE9D;UAEA,MAAMoB,UAAU,GAAGnC,KAAK,GAAGkC,SAAS;UACpC,IAAKC,UAAU,KAAK,CAAC,EAAG;YAEvBG,SAAS,GAAGxD,kBAAkB,CAAEsD,WAAY,CAAC,GAAGrB,eAAe;UAEhE;UAEA,MAAMwB,IAAI,GAAGjD,cAAc,GAAGD,uBAAuB,IACpDgD,QAAQ,GAAGH,SAAS,GAAGI,SAAS,GAAGH,UAAU,CAC7C;UAED,IAAKI,IAAI,GAAGvB,QAAQ,EAAG;YAEtBJ,IAAI,GAAGnB,CAAC;YACRuB,QAAQ,GAAGuB,IAAI;YACf1B,GAAG,GAAGa,GAAG,CAAC/B,SAAS;UAEpB;QAED;MAED;IAED;EAED,CAAC,MAAM;IAENoD,OAAO,CAACC,IAAI,CAAE,yCAA0CrC,QAAQ,QAAU,CAAC;EAE5E;EAEA,OAAO;IAAEC,IAAI;IAAEC;EAAI,CAAC;AAErB;;AAEA;AACA,SAASC,UAAUA,CAAEL,cAAc,EAAEC,MAAM,EAAEV,KAAK,EAAEY,IAAI,EAAG;EAE1D,IAAIqC,GAAG,GAAG,CAAC;EACX,KAAM,IAAIT,CAAC,GAAG9B,MAAM,EAAEwC,GAAG,GAAGxC,MAAM,GAAGV,KAAK,EAAEwC,CAAC,GAAGU,GAAG,EAAEV,CAAC,EAAG,EAAG;IAE3DS,GAAG,IAAIxC,cAAc,CAAE+B,CAAC,GAAG,CAAC,GAAG5B,IAAI,GAAG,CAAC,CAAE;EAE1C;EAEA,OAAOqC,GAAG,GAAGjD,KAAK;AAEnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}