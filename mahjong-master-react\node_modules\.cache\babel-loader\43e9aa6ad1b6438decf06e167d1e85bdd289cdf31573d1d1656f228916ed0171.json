{"ast": null, "code": "import { <PERSON><PERSON>, <PERSON>Loader, BufferGeometry, Float32BufferAttribute, PointsMaterial, Points } from \"three\";\nimport { decodeText } from \"../_polyfill/LoaderUtils.js\";\nclass PCDLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n    this.littleEndian = true;\n  }\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const loader = new FileLoader(scope.manager);\n    loader.setPath(scope.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setRequestHeader(scope.requestHeader);\n    loader.setWithCredentials(scope.withCredentials);\n    loader.load(url, function (data) {\n      try {\n        onLoad(scope.parse(data, url));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(data, url) {\n    function decompressLZF(inData, outLength) {\n      const inLength = inData.length;\n      const outData = new Uint8Array(outLength);\n      let inPtr = 0;\n      let outPtr = 0;\n      let ctrl;\n      let len;\n      let ref;\n      do {\n        ctrl = inData[inPtr++];\n        if (ctrl < 1 << 5) {\n          ctrl++;\n          if (outPtr + ctrl > outLength) throw new Error(\"Output buffer is not large enough\");\n          if (inPtr + ctrl > inLength) throw new Error(\"Invalid compressed data\");\n          do {\n            outData[outPtr++] = inData[inPtr++];\n          } while (--ctrl);\n        } else {\n          len = ctrl >> 5;\n          ref = outPtr - ((ctrl & 31) << 8) - 1;\n          if (inPtr >= inLength) throw new Error(\"Invalid compressed data\");\n          if (len === 7) {\n            len += inData[inPtr++];\n            if (inPtr >= inLength) throw new Error(\"Invalid compressed data\");\n          }\n          ref -= inData[inPtr++];\n          if (outPtr + len + 2 > outLength) throw new Error(\"Output buffer is not large enough\");\n          if (ref < 0) throw new Error(\"Invalid compressed data\");\n          if (ref >= outPtr) throw new Error(\"Invalid compressed data\");\n          do {\n            outData[outPtr++] = outData[ref++];\n          } while (--len + 2);\n        }\n      } while (inPtr < inLength);\n      return outData;\n    }\n    function parseHeader(data2) {\n      const PCDheader2 = {};\n      const result1 = data2.search(/[\\r\\n]DATA\\s(\\S*)\\s/i);\n      const result2 = /[\\r\\n]DATA\\s(\\S*)\\s/i.exec(data2.substr(result1 - 1));\n      PCDheader2.data = result2[1];\n      PCDheader2.headerLen = result2[0].length + result1;\n      PCDheader2.str = data2.substr(0, PCDheader2.headerLen);\n      PCDheader2.str = PCDheader2.str.replace(/\\#.*/gi, \"\");\n      PCDheader2.version = /VERSION (.*)/i.exec(PCDheader2.str);\n      PCDheader2.fields = /FIELDS (.*)/i.exec(PCDheader2.str);\n      PCDheader2.size = /SIZE (.*)/i.exec(PCDheader2.str);\n      PCDheader2.type = /TYPE (.*)/i.exec(PCDheader2.str);\n      PCDheader2.count = /COUNT (.*)/i.exec(PCDheader2.str);\n      PCDheader2.width = /WIDTH (.*)/i.exec(PCDheader2.str);\n      PCDheader2.height = /HEIGHT (.*)/i.exec(PCDheader2.str);\n      PCDheader2.viewpoint = /VIEWPOINT (.*)/i.exec(PCDheader2.str);\n      PCDheader2.points = /POINTS (.*)/i.exec(PCDheader2.str);\n      if (PCDheader2.version !== null) PCDheader2.version = parseFloat(PCDheader2.version[1]);\n      if (PCDheader2.fields !== null) PCDheader2.fields = PCDheader2.fields[1].split(\" \");\n      if (PCDheader2.type !== null) PCDheader2.type = PCDheader2.type[1].split(\" \");\n      if (PCDheader2.width !== null) PCDheader2.width = parseInt(PCDheader2.width[1]);\n      if (PCDheader2.height !== null) PCDheader2.height = parseInt(PCDheader2.height[1]);\n      if (PCDheader2.viewpoint !== null) PCDheader2.viewpoint = PCDheader2.viewpoint[1];\n      if (PCDheader2.points !== null) PCDheader2.points = parseInt(PCDheader2.points[1], 10);\n      if (PCDheader2.points === null) PCDheader2.points = PCDheader2.width * PCDheader2.height;\n      if (PCDheader2.size !== null) {\n        PCDheader2.size = PCDheader2.size[1].split(\" \").map(function (x) {\n          return parseInt(x, 10);\n        });\n      }\n      if (PCDheader2.count !== null) {\n        PCDheader2.count = PCDheader2.count[1].split(\" \").map(function (x) {\n          return parseInt(x, 10);\n        });\n      } else {\n        PCDheader2.count = [];\n        for (let i = 0, l = PCDheader2.fields.length; i < l; i++) {\n          PCDheader2.count.push(1);\n        }\n      }\n      PCDheader2.offset = {};\n      let sizeSum = 0;\n      for (let i = 0, l = PCDheader2.fields.length; i < l; i++) {\n        if (PCDheader2.data === \"ascii\") {\n          PCDheader2.offset[PCDheader2.fields[i]] = i;\n        } else {\n          PCDheader2.offset[PCDheader2.fields[i]] = sizeSum;\n          sizeSum += PCDheader2.size[i] * PCDheader2.count[i];\n        }\n      }\n      PCDheader2.rowSize = sizeSum;\n      return PCDheader2;\n    }\n    const textData = decodeText(new Uint8Array(data));\n    const PCDheader = parseHeader(textData);\n    const position = [];\n    const normal = [];\n    const color = [];\n    if (PCDheader.data === \"ascii\") {\n      const offset = PCDheader.offset;\n      const pcdData = textData.substr(PCDheader.headerLen);\n      const lines = pcdData.split(\"\\n\");\n      for (let i = 0, l = lines.length; i < l; i++) {\n        if (lines[i] === \"\") continue;\n        const line = lines[i].split(\" \");\n        if (offset.x !== void 0) {\n          position.push(parseFloat(line[offset.x]));\n          position.push(parseFloat(line[offset.y]));\n          position.push(parseFloat(line[offset.z]));\n        }\n        if (offset.rgb !== void 0) {\n          const rgb = parseFloat(line[offset.rgb]);\n          const r = rgb >> 16 & 255;\n          const g = rgb >> 8 & 255;\n          const b = rgb >> 0 & 255;\n          color.push(r / 255, g / 255, b / 255);\n        }\n        if (offset.normal_x !== void 0) {\n          normal.push(parseFloat(line[offset.normal_x]));\n          normal.push(parseFloat(line[offset.normal_y]));\n          normal.push(parseFloat(line[offset.normal_z]));\n        }\n      }\n    }\n    if (PCDheader.data === \"binary_compressed\") {\n      const sizes = new Uint32Array(data.slice(PCDheader.headerLen, PCDheader.headerLen + 8));\n      const compressedSize = sizes[0];\n      const decompressedSize = sizes[1];\n      const decompressed = decompressLZF(new Uint8Array(data, PCDheader.headerLen + 8, compressedSize), decompressedSize);\n      const dataview = new DataView(decompressed.buffer);\n      const offset = PCDheader.offset;\n      for (let i = 0; i < PCDheader.points; i++) {\n        if (offset.x !== void 0) {\n          position.push(dataview.getFloat32(PCDheader.points * offset.x + PCDheader.size[0] * i, this.littleEndian));\n          position.push(dataview.getFloat32(PCDheader.points * offset.y + PCDheader.size[1] * i, this.littleEndian));\n          position.push(dataview.getFloat32(PCDheader.points * offset.z + PCDheader.size[2] * i, this.littleEndian));\n        }\n        if (offset.rgb !== void 0) {\n          color.push(dataview.getUint8(PCDheader.points * offset.rgb + PCDheader.size[3] * i + 2) / 255);\n          color.push(dataview.getUint8(PCDheader.points * offset.rgb + PCDheader.size[3] * i + 1) / 255);\n          color.push(dataview.getUint8(PCDheader.points * offset.rgb + PCDheader.size[3] * i + 0) / 255);\n        }\n        if (offset.normal_x !== void 0) {\n          normal.push(dataview.getFloat32(PCDheader.points * offset.normal_x + PCDheader.size[4] * i, this.littleEndian));\n          normal.push(dataview.getFloat32(PCDheader.points * offset.normal_y + PCDheader.size[5] * i, this.littleEndian));\n          normal.push(dataview.getFloat32(PCDheader.points * offset.normal_z + PCDheader.size[6] * i, this.littleEndian));\n        }\n      }\n    }\n    if (PCDheader.data === \"binary\") {\n      const dataview = new DataView(data, PCDheader.headerLen);\n      const offset = PCDheader.offset;\n      for (let i = 0, row = 0; i < PCDheader.points; i++, row += PCDheader.rowSize) {\n        if (offset.x !== void 0) {\n          position.push(dataview.getFloat32(row + offset.x, this.littleEndian));\n          position.push(dataview.getFloat32(row + offset.y, this.littleEndian));\n          position.push(dataview.getFloat32(row + offset.z, this.littleEndian));\n        }\n        if (offset.rgb !== void 0) {\n          color.push(dataview.getUint8(row + offset.rgb + 2) / 255);\n          color.push(dataview.getUint8(row + offset.rgb + 1) / 255);\n          color.push(dataview.getUint8(row + offset.rgb + 0) / 255);\n        }\n        if (offset.normal_x !== void 0) {\n          normal.push(dataview.getFloat32(row + offset.normal_x, this.littleEndian));\n          normal.push(dataview.getFloat32(row + offset.normal_y, this.littleEndian));\n          normal.push(dataview.getFloat32(row + offset.normal_z, this.littleEndian));\n        }\n      }\n    }\n    const geometry = new BufferGeometry();\n    if (position.length > 0) geometry.setAttribute(\"position\", new Float32BufferAttribute(position, 3));\n    if (normal.length > 0) geometry.setAttribute(\"normal\", new Float32BufferAttribute(normal, 3));\n    if (color.length > 0) geometry.setAttribute(\"color\", new Float32BufferAttribute(color, 3));\n    geometry.computeBoundingSphere();\n    const material = new PointsMaterial({\n      size: 5e-3\n    });\n    if (color.length > 0) {\n      material.vertexColors = true;\n    } else {\n      material.color.setHex(Math.random() * 16777215);\n    }\n    const mesh = new Points(geometry, material);\n    let name = url.split(\"\").reverse().join(\"\");\n    name = /([^\\/]*)/.exec(name);\n    name = name[1].split(\"\").reverse().join(\"\");\n    mesh.name = name;\n    return mesh;\n  }\n}\nexport { PCDLoader };", "map": {"version": 3, "names": ["PCDLoader", "Loader", "constructor", "manager", "littleEndian", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "data", "parse", "e", "console", "error", "itemError", "decompressLZF", "inData", "outLength", "inLength", "length", "outData", "Uint8Array", "inPtr", "outPtr", "ctrl", "len", "ref", "Error", "parse<PERSON><PERSON><PERSON>", "data2", "PCDheader2", "result1", "search", "result2", "exec", "substr", "headerLen", "str", "replace", "version", "fields", "size", "type", "count", "width", "height", "viewpoint", "points", "parseFloat", "split", "parseInt", "map", "x", "i", "l", "push", "offset", "sizeSum", "rowSize", "textData", "decodeText", "PCD<PERSON>er", "position", "normal", "color", "pcdData", "lines", "line", "y", "z", "rgb", "r", "g", "b", "normal_x", "normal_y", "normal_z", "sizes", "Uint32Array", "slice", "compressedSize", "decompressedSize", "decompressed", "dataview", "DataView", "buffer", "getFloat32", "getUint8", "row", "geometry", "BufferGeometry", "setAttribute", "Float32BufferAttribute", "computeBoundingSphere", "material", "PointsMaterial", "vertexColors", "setHex", "Math", "random", "mesh", "Points", "name", "reverse", "join"], "sources": ["F:\\= 神灯智库\\- AI 创作\\AI APP\\神灯麻将大师\\mahjong-master-react\\node_modules\\src\\loaders\\PCDLoader.js"], "sourcesContent": ["import { BufferGeometry, FileLoader, Float32BufferAttribute, Loader, LoaderUtils, Points, PointsMaterial } from 'three'\nimport { decodeText } from '../_polyfill/LoaderUtils'\n\nclass PCDLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.littleEndian = true\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (data) {\n        try {\n          onLoad(scope.parse(data, url))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(data, url) {\n    // from https://gitlab.com/taketwo/three-pcd-loader/blob/master/decompress-lzf.js\n\n    function decompressLZF(inData, outLength) {\n      const inLength = inData.length\n      const outData = new Uint8Array(outLength)\n      let inPtr = 0\n      let outPtr = 0\n      let ctrl\n      let len\n      let ref\n      do {\n        ctrl = inData[inPtr++]\n        if (ctrl < 1 << 5) {\n          ctrl++\n          if (outPtr + ctrl > outLength) throw new Error('Output buffer is not large enough')\n          if (inPtr + ctrl > inLength) throw new Error('Invalid compressed data')\n          do {\n            outData[outPtr++] = inData[inPtr++]\n          } while (--ctrl)\n        } else {\n          len = ctrl >> 5\n          ref = outPtr - ((ctrl & 0x1f) << 8) - 1\n          if (inPtr >= inLength) throw new Error('Invalid compressed data')\n          if (len === 7) {\n            len += inData[inPtr++]\n            if (inPtr >= inLength) throw new Error('Invalid compressed data')\n          }\n\n          ref -= inData[inPtr++]\n          if (outPtr + len + 2 > outLength) throw new Error('Output buffer is not large enough')\n          if (ref < 0) throw new Error('Invalid compressed data')\n          if (ref >= outPtr) throw new Error('Invalid compressed data')\n          do {\n            outData[outPtr++] = outData[ref++]\n          } while (--len + 2)\n        }\n      } while (inPtr < inLength)\n\n      return outData\n    }\n\n    function parseHeader(data) {\n      const PCDheader = {}\n      const result1 = data.search(/[\\r\\n]DATA\\s(\\S*)\\s/i)\n      const result2 = /[\\r\\n]DATA\\s(\\S*)\\s/i.exec(data.substr(result1 - 1))\n\n      PCDheader.data = result2[1]\n      PCDheader.headerLen = result2[0].length + result1\n      PCDheader.str = data.substr(0, PCDheader.headerLen)\n\n      // remove comments\n\n      PCDheader.str = PCDheader.str.replace(/\\#.*/gi, '')\n\n      // parse\n\n      PCDheader.version = /VERSION (.*)/i.exec(PCDheader.str)\n      PCDheader.fields = /FIELDS (.*)/i.exec(PCDheader.str)\n      PCDheader.size = /SIZE (.*)/i.exec(PCDheader.str)\n      PCDheader.type = /TYPE (.*)/i.exec(PCDheader.str)\n      PCDheader.count = /COUNT (.*)/i.exec(PCDheader.str)\n      PCDheader.width = /WIDTH (.*)/i.exec(PCDheader.str)\n      PCDheader.height = /HEIGHT (.*)/i.exec(PCDheader.str)\n      PCDheader.viewpoint = /VIEWPOINT (.*)/i.exec(PCDheader.str)\n      PCDheader.points = /POINTS (.*)/i.exec(PCDheader.str)\n\n      // evaluate\n\n      if (PCDheader.version !== null) PCDheader.version = parseFloat(PCDheader.version[1])\n\n      if (PCDheader.fields !== null) PCDheader.fields = PCDheader.fields[1].split(' ')\n\n      if (PCDheader.type !== null) PCDheader.type = PCDheader.type[1].split(' ')\n\n      if (PCDheader.width !== null) PCDheader.width = parseInt(PCDheader.width[1])\n\n      if (PCDheader.height !== null) PCDheader.height = parseInt(PCDheader.height[1])\n\n      if (PCDheader.viewpoint !== null) PCDheader.viewpoint = PCDheader.viewpoint[1]\n\n      if (PCDheader.points !== null) PCDheader.points = parseInt(PCDheader.points[1], 10)\n\n      if (PCDheader.points === null) PCDheader.points = PCDheader.width * PCDheader.height\n\n      if (PCDheader.size !== null) {\n        PCDheader.size = PCDheader.size[1].split(' ').map(function (x) {\n          return parseInt(x, 10)\n        })\n      }\n\n      if (PCDheader.count !== null) {\n        PCDheader.count = PCDheader.count[1].split(' ').map(function (x) {\n          return parseInt(x, 10)\n        })\n      } else {\n        PCDheader.count = []\n\n        for (let i = 0, l = PCDheader.fields.length; i < l; i++) {\n          PCDheader.count.push(1)\n        }\n      }\n\n      PCDheader.offset = {}\n\n      let sizeSum = 0\n\n      for (let i = 0, l = PCDheader.fields.length; i < l; i++) {\n        if (PCDheader.data === 'ascii') {\n          PCDheader.offset[PCDheader.fields[i]] = i\n        } else {\n          PCDheader.offset[PCDheader.fields[i]] = sizeSum\n          sizeSum += PCDheader.size[i] * PCDheader.count[i]\n        }\n      }\n\n      // for binary only\n\n      PCDheader.rowSize = sizeSum\n\n      return PCDheader\n    }\n\n    const textData = decodeText(new Uint8Array(data))\n\n    // parse header (always ascii format)\n\n    const PCDheader = parseHeader(textData)\n\n    // parse data\n\n    const position = []\n    const normal = []\n    const color = []\n\n    // ascii\n\n    if (PCDheader.data === 'ascii') {\n      const offset = PCDheader.offset\n      const pcdData = textData.substr(PCDheader.headerLen)\n      const lines = pcdData.split('\\n')\n\n      for (let i = 0, l = lines.length; i < l; i++) {\n        if (lines[i] === '') continue\n\n        const line = lines[i].split(' ')\n\n        if (offset.x !== undefined) {\n          position.push(parseFloat(line[offset.x]))\n          position.push(parseFloat(line[offset.y]))\n          position.push(parseFloat(line[offset.z]))\n        }\n\n        if (offset.rgb !== undefined) {\n          const rgb = parseFloat(line[offset.rgb])\n          const r = (rgb >> 16) & 0x0000ff\n          const g = (rgb >> 8) & 0x0000ff\n          const b = (rgb >> 0) & 0x0000ff\n          color.push(r / 255, g / 255, b / 255)\n        }\n\n        if (offset.normal_x !== undefined) {\n          normal.push(parseFloat(line[offset.normal_x]))\n          normal.push(parseFloat(line[offset.normal_y]))\n          normal.push(parseFloat(line[offset.normal_z]))\n        }\n      }\n    }\n\n    // binary-compressed\n\n    // normally data in PCD files are organized as array of structures: XYZRGBXYZRGB\n    // binary compressed PCD files organize their data as structure of arrays: XXYYZZRGBRGB\n    // that requires a totally different parsing approach compared to non-compressed data\n\n    if (PCDheader.data === 'binary_compressed') {\n      const sizes = new Uint32Array(data.slice(PCDheader.headerLen, PCDheader.headerLen + 8))\n      const compressedSize = sizes[0]\n      const decompressedSize = sizes[1]\n      const decompressed = decompressLZF(\n        new Uint8Array(data, PCDheader.headerLen + 8, compressedSize),\n        decompressedSize,\n      )\n      const dataview = new DataView(decompressed.buffer)\n\n      const offset = PCDheader.offset\n\n      for (let i = 0; i < PCDheader.points; i++) {\n        if (offset.x !== undefined) {\n          position.push(dataview.getFloat32(PCDheader.points * offset.x + PCDheader.size[0] * i, this.littleEndian))\n          position.push(dataview.getFloat32(PCDheader.points * offset.y + PCDheader.size[1] * i, this.littleEndian))\n          position.push(dataview.getFloat32(PCDheader.points * offset.z + PCDheader.size[2] * i, this.littleEndian))\n        }\n\n        if (offset.rgb !== undefined) {\n          color.push(dataview.getUint8(PCDheader.points * offset.rgb + PCDheader.size[3] * i + 2) / 255.0)\n          color.push(dataview.getUint8(PCDheader.points * offset.rgb + PCDheader.size[3] * i + 1) / 255.0)\n          color.push(dataview.getUint8(PCDheader.points * offset.rgb + PCDheader.size[3] * i + 0) / 255.0)\n        }\n\n        if (offset.normal_x !== undefined) {\n          normal.push(\n            dataview.getFloat32(PCDheader.points * offset.normal_x + PCDheader.size[4] * i, this.littleEndian),\n          )\n          normal.push(\n            dataview.getFloat32(PCDheader.points * offset.normal_y + PCDheader.size[5] * i, this.littleEndian),\n          )\n          normal.push(\n            dataview.getFloat32(PCDheader.points * offset.normal_z + PCDheader.size[6] * i, this.littleEndian),\n          )\n        }\n      }\n    }\n\n    // binary\n\n    if (PCDheader.data === 'binary') {\n      const dataview = new DataView(data, PCDheader.headerLen)\n      const offset = PCDheader.offset\n\n      for (let i = 0, row = 0; i < PCDheader.points; i++, row += PCDheader.rowSize) {\n        if (offset.x !== undefined) {\n          position.push(dataview.getFloat32(row + offset.x, this.littleEndian))\n          position.push(dataview.getFloat32(row + offset.y, this.littleEndian))\n          position.push(dataview.getFloat32(row + offset.z, this.littleEndian))\n        }\n\n        if (offset.rgb !== undefined) {\n          color.push(dataview.getUint8(row + offset.rgb + 2) / 255.0)\n          color.push(dataview.getUint8(row + offset.rgb + 1) / 255.0)\n          color.push(dataview.getUint8(row + offset.rgb + 0) / 255.0)\n        }\n\n        if (offset.normal_x !== undefined) {\n          normal.push(dataview.getFloat32(row + offset.normal_x, this.littleEndian))\n          normal.push(dataview.getFloat32(row + offset.normal_y, this.littleEndian))\n          normal.push(dataview.getFloat32(row + offset.normal_z, this.littleEndian))\n        }\n      }\n    }\n\n    // build geometry\n\n    const geometry = new BufferGeometry()\n\n    if (position.length > 0) geometry.setAttribute('position', new Float32BufferAttribute(position, 3))\n    if (normal.length > 0) geometry.setAttribute('normal', new Float32BufferAttribute(normal, 3))\n    if (color.length > 0) geometry.setAttribute('color', new Float32BufferAttribute(color, 3))\n\n    geometry.computeBoundingSphere()\n\n    // build material\n\n    const material = new PointsMaterial({ size: 0.005 })\n\n    if (color.length > 0) {\n      material.vertexColors = true\n    } else {\n      material.color.setHex(Math.random() * 0xffffff)\n    }\n\n    // build point cloud\n\n    const mesh = new Points(geometry, material)\n    let name = url.split('').reverse().join('')\n    name = /([^\\/]*)/.exec(name)\n    name = name[1].split('').reverse().join('')\n    mesh.name = name\n\n    return mesh\n  }\n}\n\nexport { PCDLoader }\n"], "mappings": ";;AAGA,MAAMA,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IAEb,KAAKC,YAAA,GAAe;EACrB;EAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAWF,KAAA,CAAMP,OAAO;IAC3CQ,MAAA,CAAOE,OAAA,CAAQH,KAAA,CAAMI,IAAI;IACzBH,MAAA,CAAOI,eAAA,CAAgB,aAAa;IACpCJ,MAAA,CAAOK,gBAAA,CAAiBN,KAAA,CAAMO,aAAa;IAC3CN,MAAA,CAAOO,kBAAA,CAAmBR,KAAA,CAAMS,eAAe;IAC/CR,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUc,IAAA,EAAM;MACd,IAAI;QACFb,MAAA,CAAOG,KAAA,CAAMW,KAAA,CAAMD,IAAA,EAAMd,GAAG,CAAC;MAC9B,SAAQgB,CAAA,EAAP;QACA,IAAIb,OAAA,EAAS;UACXA,OAAA,CAAQa,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDZ,KAAA,CAAMP,OAAA,CAAQsB,SAAA,CAAUnB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDY,MAAMD,IAAA,EAAMd,GAAA,EAAK;IAGf,SAASoB,cAAcC,MAAA,EAAQC,SAAA,EAAW;MACxC,MAAMC,QAAA,GAAWF,MAAA,CAAOG,MAAA;MACxB,MAAMC,OAAA,GAAU,IAAIC,UAAA,CAAWJ,SAAS;MACxC,IAAIK,KAAA,GAAQ;MACZ,IAAIC,MAAA,GAAS;MACb,IAAIC,IAAA;MACJ,IAAIC,GAAA;MACJ,IAAIC,GAAA;MACJ,GAAG;QACDF,IAAA,GAAOR,MAAA,CAAOM,KAAA,EAAO;QACrB,IAAIE,IAAA,GAAO,KAAK,GAAG;UACjBA,IAAA;UACA,IAAID,MAAA,GAASC,IAAA,GAAOP,SAAA,EAAW,MAAM,IAAIU,KAAA,CAAM,mCAAmC;UAClF,IAAIL,KAAA,GAAQE,IAAA,GAAON,QAAA,EAAU,MAAM,IAAIS,KAAA,CAAM,yBAAyB;UACtE,GAAG;YACDP,OAAA,CAAQG,MAAA,EAAQ,IAAIP,MAAA,CAAOM,KAAA,EAAO;UACnC,SAAQ,EAAEE,IAAA;QACrB,OAAe;UACLC,GAAA,GAAMD,IAAA,IAAQ;UACdE,GAAA,GAAMH,MAAA,KAAWC,IAAA,GAAO,OAAS,KAAK;UACtC,IAAIF,KAAA,IAASJ,QAAA,EAAU,MAAM,IAAIS,KAAA,CAAM,yBAAyB;UAChE,IAAIF,GAAA,KAAQ,GAAG;YACbA,GAAA,IAAOT,MAAA,CAAOM,KAAA,EAAO;YACrB,IAAIA,KAAA,IAASJ,QAAA,EAAU,MAAM,IAAIS,KAAA,CAAM,yBAAyB;UACjE;UAEDD,GAAA,IAAOV,MAAA,CAAOM,KAAA,EAAO;UACrB,IAAIC,MAAA,GAASE,GAAA,GAAM,IAAIR,SAAA,EAAW,MAAM,IAAIU,KAAA,CAAM,mCAAmC;UACrF,IAAID,GAAA,GAAM,GAAG,MAAM,IAAIC,KAAA,CAAM,yBAAyB;UACtD,IAAID,GAAA,IAAOH,MAAA,EAAQ,MAAM,IAAII,KAAA,CAAM,yBAAyB;UAC5D,GAAG;YACDP,OAAA,CAAQG,MAAA,EAAQ,IAAIH,OAAA,CAAQM,GAAA,EAAK;UAC7C,SAAmB,EAAED,GAAA,GAAM;QAClB;MACT,SAAeH,KAAA,GAAQJ,QAAA;MAEjB,OAAOE,OAAA;IACR;IAED,SAASQ,YAAYC,KAAA,EAAM;MACzB,MAAMC,UAAA,GAAY,CAAE;MACpB,MAAMC,OAAA,GAAUF,KAAA,CAAKG,MAAA,CAAO,sBAAsB;MAClD,MAAMC,OAAA,GAAU,uBAAuBC,IAAA,CAAKL,KAAA,CAAKM,MAAA,CAAOJ,OAAA,GAAU,CAAC,CAAC;MAEpED,UAAA,CAAUrB,IAAA,GAAOwB,OAAA,CAAQ,CAAC;MAC1BH,UAAA,CAAUM,SAAA,GAAYH,OAAA,CAAQ,CAAC,EAAEd,MAAA,GAASY,OAAA;MAC1CD,UAAA,CAAUO,GAAA,GAAMR,KAAA,CAAKM,MAAA,CAAO,GAAGL,UAAA,CAAUM,SAAS;MAIlDN,UAAA,CAAUO,GAAA,GAAMP,UAAA,CAAUO,GAAA,CAAIC,OAAA,CAAQ,UAAU,EAAE;MAIlDR,UAAA,CAAUS,OAAA,GAAU,gBAAgBL,IAAA,CAAKJ,UAAA,CAAUO,GAAG;MACtDP,UAAA,CAAUU,MAAA,GAAS,eAAeN,IAAA,CAAKJ,UAAA,CAAUO,GAAG;MACpDP,UAAA,CAAUW,IAAA,GAAO,aAAaP,IAAA,CAAKJ,UAAA,CAAUO,GAAG;MAChDP,UAAA,CAAUY,IAAA,GAAO,aAAaR,IAAA,CAAKJ,UAAA,CAAUO,GAAG;MAChDP,UAAA,CAAUa,KAAA,GAAQ,cAAcT,IAAA,CAAKJ,UAAA,CAAUO,GAAG;MAClDP,UAAA,CAAUc,KAAA,GAAQ,cAAcV,IAAA,CAAKJ,UAAA,CAAUO,GAAG;MAClDP,UAAA,CAAUe,MAAA,GAAS,eAAeX,IAAA,CAAKJ,UAAA,CAAUO,GAAG;MACpDP,UAAA,CAAUgB,SAAA,GAAY,kBAAkBZ,IAAA,CAAKJ,UAAA,CAAUO,GAAG;MAC1DP,UAAA,CAAUiB,MAAA,GAAS,eAAeb,IAAA,CAAKJ,UAAA,CAAUO,GAAG;MAIpD,IAAIP,UAAA,CAAUS,OAAA,KAAY,MAAMT,UAAA,CAAUS,OAAA,GAAUS,UAAA,CAAWlB,UAAA,CAAUS,OAAA,CAAQ,CAAC,CAAC;MAEnF,IAAIT,UAAA,CAAUU,MAAA,KAAW,MAAMV,UAAA,CAAUU,MAAA,GAASV,UAAA,CAAUU,MAAA,CAAO,CAAC,EAAES,KAAA,CAAM,GAAG;MAE/E,IAAInB,UAAA,CAAUY,IAAA,KAAS,MAAMZ,UAAA,CAAUY,IAAA,GAAOZ,UAAA,CAAUY,IAAA,CAAK,CAAC,EAAEO,KAAA,CAAM,GAAG;MAEzE,IAAInB,UAAA,CAAUc,KAAA,KAAU,MAAMd,UAAA,CAAUc,KAAA,GAAQM,QAAA,CAASpB,UAAA,CAAUc,KAAA,CAAM,CAAC,CAAC;MAE3E,IAAId,UAAA,CAAUe,MAAA,KAAW,MAAMf,UAAA,CAAUe,MAAA,GAASK,QAAA,CAASpB,UAAA,CAAUe,MAAA,CAAO,CAAC,CAAC;MAE9E,IAAIf,UAAA,CAAUgB,SAAA,KAAc,MAAMhB,UAAA,CAAUgB,SAAA,GAAYhB,UAAA,CAAUgB,SAAA,CAAU,CAAC;MAE7E,IAAIhB,UAAA,CAAUiB,MAAA,KAAW,MAAMjB,UAAA,CAAUiB,MAAA,GAASG,QAAA,CAASpB,UAAA,CAAUiB,MAAA,CAAO,CAAC,GAAG,EAAE;MAElF,IAAIjB,UAAA,CAAUiB,MAAA,KAAW,MAAMjB,UAAA,CAAUiB,MAAA,GAASjB,UAAA,CAAUc,KAAA,GAAQd,UAAA,CAAUe,MAAA;MAE9E,IAAIf,UAAA,CAAUW,IAAA,KAAS,MAAM;QAC3BX,UAAA,CAAUW,IAAA,GAAOX,UAAA,CAAUW,IAAA,CAAK,CAAC,EAAEQ,KAAA,CAAM,GAAG,EAAEE,GAAA,CAAI,UAAUC,CAAA,EAAG;UAC7D,OAAOF,QAAA,CAASE,CAAA,EAAG,EAAE;QAC/B,CAAS;MACF;MAED,IAAItB,UAAA,CAAUa,KAAA,KAAU,MAAM;QAC5Bb,UAAA,CAAUa,KAAA,GAAQb,UAAA,CAAUa,KAAA,CAAM,CAAC,EAAEM,KAAA,CAAM,GAAG,EAAEE,GAAA,CAAI,UAAUC,CAAA,EAAG;UAC/D,OAAOF,QAAA,CAASE,CAAA,EAAG,EAAE;QAC/B,CAAS;MACT,OAAa;QACLtB,UAAA,CAAUa,KAAA,GAAQ,EAAE;QAEpB,SAASU,CAAA,GAAI,GAAGC,CAAA,GAAIxB,UAAA,CAAUU,MAAA,CAAOrB,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;UACvDvB,UAAA,CAAUa,KAAA,CAAMY,IAAA,CAAK,CAAC;QACvB;MACF;MAEDzB,UAAA,CAAU0B,MAAA,GAAS,CAAE;MAErB,IAAIC,OAAA,GAAU;MAEd,SAASJ,CAAA,GAAI,GAAGC,CAAA,GAAIxB,UAAA,CAAUU,MAAA,CAAOrB,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QACvD,IAAIvB,UAAA,CAAUrB,IAAA,KAAS,SAAS;UAC9BqB,UAAA,CAAU0B,MAAA,CAAO1B,UAAA,CAAUU,MAAA,CAAOa,CAAC,CAAC,IAAIA,CAAA;QAClD,OAAe;UACLvB,UAAA,CAAU0B,MAAA,CAAO1B,UAAA,CAAUU,MAAA,CAAOa,CAAC,CAAC,IAAII,OAAA;UACxCA,OAAA,IAAW3B,UAAA,CAAUW,IAAA,CAAKY,CAAC,IAAIvB,UAAA,CAAUa,KAAA,CAAMU,CAAC;QACjD;MACF;MAIDvB,UAAA,CAAU4B,OAAA,GAAUD,OAAA;MAEpB,OAAO3B,UAAA;IACR;IAED,MAAM6B,QAAA,GAAWC,UAAA,CAAW,IAAIvC,UAAA,CAAWZ,IAAI,CAAC;IAIhD,MAAMoD,SAAA,GAAYjC,WAAA,CAAY+B,QAAQ;IAItC,MAAMG,QAAA,GAAW,EAAE;IACnB,MAAMC,MAAA,GAAS,EAAE;IACjB,MAAMC,KAAA,GAAQ,EAAE;IAIhB,IAAIH,SAAA,CAAUpD,IAAA,KAAS,SAAS;MAC9B,MAAM+C,MAAA,GAASK,SAAA,CAAUL,MAAA;MACzB,MAAMS,OAAA,GAAUN,QAAA,CAASxB,MAAA,CAAO0B,SAAA,CAAUzB,SAAS;MACnD,MAAM8B,KAAA,GAAQD,OAAA,CAAQhB,KAAA,CAAM,IAAI;MAEhC,SAASI,CAAA,GAAI,GAAGC,CAAA,GAAIY,KAAA,CAAM/C,MAAA,EAAQkC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;QAC5C,IAAIa,KAAA,CAAMb,CAAC,MAAM,IAAI;QAErB,MAAMc,IAAA,GAAOD,KAAA,CAAMb,CAAC,EAAEJ,KAAA,CAAM,GAAG;QAE/B,IAAIO,MAAA,CAAOJ,CAAA,KAAM,QAAW;UAC1BU,QAAA,CAASP,IAAA,CAAKP,UAAA,CAAWmB,IAAA,CAAKX,MAAA,CAAOJ,CAAC,CAAC,CAAC;UACxCU,QAAA,CAASP,IAAA,CAAKP,UAAA,CAAWmB,IAAA,CAAKX,MAAA,CAAOY,CAAC,CAAC,CAAC;UACxCN,QAAA,CAASP,IAAA,CAAKP,UAAA,CAAWmB,IAAA,CAAKX,MAAA,CAAOa,CAAC,CAAC,CAAC;QACzC;QAED,IAAIb,MAAA,CAAOc,GAAA,KAAQ,QAAW;UAC5B,MAAMA,GAAA,GAAMtB,UAAA,CAAWmB,IAAA,CAAKX,MAAA,CAAOc,GAAG,CAAC;UACvC,MAAMC,CAAA,GAAKD,GAAA,IAAO,KAAM;UACxB,MAAME,CAAA,GAAKF,GAAA,IAAO,IAAK;UACvB,MAAMG,CAAA,GAAKH,GAAA,IAAO,IAAK;UACvBN,KAAA,CAAMT,IAAA,CAAKgB,CAAA,GAAI,KAAKC,CAAA,GAAI,KAAKC,CAAA,GAAI,GAAG;QACrC;QAED,IAAIjB,MAAA,CAAOkB,QAAA,KAAa,QAAW;UACjCX,MAAA,CAAOR,IAAA,CAAKP,UAAA,CAAWmB,IAAA,CAAKX,MAAA,CAAOkB,QAAQ,CAAC,CAAC;UAC7CX,MAAA,CAAOR,IAAA,CAAKP,UAAA,CAAWmB,IAAA,CAAKX,MAAA,CAAOmB,QAAQ,CAAC,CAAC;UAC7CZ,MAAA,CAAOR,IAAA,CAAKP,UAAA,CAAWmB,IAAA,CAAKX,MAAA,CAAOoB,QAAQ,CAAC,CAAC;QAC9C;MACF;IACF;IAQD,IAAIf,SAAA,CAAUpD,IAAA,KAAS,qBAAqB;MAC1C,MAAMoE,KAAA,GAAQ,IAAIC,WAAA,CAAYrE,IAAA,CAAKsE,KAAA,CAAMlB,SAAA,CAAUzB,SAAA,EAAWyB,SAAA,CAAUzB,SAAA,GAAY,CAAC,CAAC;MACtF,MAAM4C,cAAA,GAAiBH,KAAA,CAAM,CAAC;MAC9B,MAAMI,gBAAA,GAAmBJ,KAAA,CAAM,CAAC;MAChC,MAAMK,YAAA,GAAenE,aAAA,CACnB,IAAIM,UAAA,CAAWZ,IAAA,EAAMoD,SAAA,CAAUzB,SAAA,GAAY,GAAG4C,cAAc,GAC5DC,gBACD;MACD,MAAME,QAAA,GAAW,IAAIC,QAAA,CAASF,YAAA,CAAaG,MAAM;MAEjD,MAAM7B,MAAA,GAASK,SAAA,CAAUL,MAAA;MAEzB,SAASH,CAAA,GAAI,GAAGA,CAAA,GAAIQ,SAAA,CAAUd,MAAA,EAAQM,CAAA,IAAK;QACzC,IAAIG,MAAA,CAAOJ,CAAA,KAAM,QAAW;UAC1BU,QAAA,CAASP,IAAA,CAAK4B,QAAA,CAASG,UAAA,CAAWzB,SAAA,CAAUd,MAAA,GAASS,MAAA,CAAOJ,CAAA,GAAIS,SAAA,CAAUpB,IAAA,CAAK,CAAC,IAAIY,CAAA,EAAG,KAAK5D,YAAY,CAAC;UACzGqE,QAAA,CAASP,IAAA,CAAK4B,QAAA,CAASG,UAAA,CAAWzB,SAAA,CAAUd,MAAA,GAASS,MAAA,CAAOY,CAAA,GAAIP,SAAA,CAAUpB,IAAA,CAAK,CAAC,IAAIY,CAAA,EAAG,KAAK5D,YAAY,CAAC;UACzGqE,QAAA,CAASP,IAAA,CAAK4B,QAAA,CAASG,UAAA,CAAWzB,SAAA,CAAUd,MAAA,GAASS,MAAA,CAAOa,CAAA,GAAIR,SAAA,CAAUpB,IAAA,CAAK,CAAC,IAAIY,CAAA,EAAG,KAAK5D,YAAY,CAAC;QAC1G;QAED,IAAI+D,MAAA,CAAOc,GAAA,KAAQ,QAAW;UAC5BN,KAAA,CAAMT,IAAA,CAAK4B,QAAA,CAASI,QAAA,CAAS1B,SAAA,CAAUd,MAAA,GAASS,MAAA,CAAOc,GAAA,GAAMT,SAAA,CAAUpB,IAAA,CAAK,CAAC,IAAIY,CAAA,GAAI,CAAC,IAAI,GAAK;UAC/FW,KAAA,CAAMT,IAAA,CAAK4B,QAAA,CAASI,QAAA,CAAS1B,SAAA,CAAUd,MAAA,GAASS,MAAA,CAAOc,GAAA,GAAMT,SAAA,CAAUpB,IAAA,CAAK,CAAC,IAAIY,CAAA,GAAI,CAAC,IAAI,GAAK;UAC/FW,KAAA,CAAMT,IAAA,CAAK4B,QAAA,CAASI,QAAA,CAAS1B,SAAA,CAAUd,MAAA,GAASS,MAAA,CAAOc,GAAA,GAAMT,SAAA,CAAUpB,IAAA,CAAK,CAAC,IAAIY,CAAA,GAAI,CAAC,IAAI,GAAK;QAChG;QAED,IAAIG,MAAA,CAAOkB,QAAA,KAAa,QAAW;UACjCX,MAAA,CAAOR,IAAA,CACL4B,QAAA,CAASG,UAAA,CAAWzB,SAAA,CAAUd,MAAA,GAASS,MAAA,CAAOkB,QAAA,GAAWb,SAAA,CAAUpB,IAAA,CAAK,CAAC,IAAIY,CAAA,EAAG,KAAK5D,YAAY,CAClG;UACDsE,MAAA,CAAOR,IAAA,CACL4B,QAAA,CAASG,UAAA,CAAWzB,SAAA,CAAUd,MAAA,GAASS,MAAA,CAAOmB,QAAA,GAAWd,SAAA,CAAUpB,IAAA,CAAK,CAAC,IAAIY,CAAA,EAAG,KAAK5D,YAAY,CAClG;UACDsE,MAAA,CAAOR,IAAA,CACL4B,QAAA,CAASG,UAAA,CAAWzB,SAAA,CAAUd,MAAA,GAASS,MAAA,CAAOoB,QAAA,GAAWf,SAAA,CAAUpB,IAAA,CAAK,CAAC,IAAIY,CAAA,EAAG,KAAK5D,YAAY,CAClG;QACF;MACF;IACF;IAID,IAAIoE,SAAA,CAAUpD,IAAA,KAAS,UAAU;MAC/B,MAAM0E,QAAA,GAAW,IAAIC,QAAA,CAAS3E,IAAA,EAAMoD,SAAA,CAAUzB,SAAS;MACvD,MAAMoB,MAAA,GAASK,SAAA,CAAUL,MAAA;MAEzB,SAASH,CAAA,GAAI,GAAGmC,GAAA,GAAM,GAAGnC,CAAA,GAAIQ,SAAA,CAAUd,MAAA,EAAQM,CAAA,IAAKmC,GAAA,IAAO3B,SAAA,CAAUH,OAAA,EAAS;QAC5E,IAAIF,MAAA,CAAOJ,CAAA,KAAM,QAAW;UAC1BU,QAAA,CAASP,IAAA,CAAK4B,QAAA,CAASG,UAAA,CAAWE,GAAA,GAAMhC,MAAA,CAAOJ,CAAA,EAAG,KAAK3D,YAAY,CAAC;UACpEqE,QAAA,CAASP,IAAA,CAAK4B,QAAA,CAASG,UAAA,CAAWE,GAAA,GAAMhC,MAAA,CAAOY,CAAA,EAAG,KAAK3E,YAAY,CAAC;UACpEqE,QAAA,CAASP,IAAA,CAAK4B,QAAA,CAASG,UAAA,CAAWE,GAAA,GAAMhC,MAAA,CAAOa,CAAA,EAAG,KAAK5E,YAAY,CAAC;QACrE;QAED,IAAI+D,MAAA,CAAOc,GAAA,KAAQ,QAAW;UAC5BN,KAAA,CAAMT,IAAA,CAAK4B,QAAA,CAASI,QAAA,CAASC,GAAA,GAAMhC,MAAA,CAAOc,GAAA,GAAM,CAAC,IAAI,GAAK;UAC1DN,KAAA,CAAMT,IAAA,CAAK4B,QAAA,CAASI,QAAA,CAASC,GAAA,GAAMhC,MAAA,CAAOc,GAAA,GAAM,CAAC,IAAI,GAAK;UAC1DN,KAAA,CAAMT,IAAA,CAAK4B,QAAA,CAASI,QAAA,CAASC,GAAA,GAAMhC,MAAA,CAAOc,GAAA,GAAM,CAAC,IAAI,GAAK;QAC3D;QAED,IAAId,MAAA,CAAOkB,QAAA,KAAa,QAAW;UACjCX,MAAA,CAAOR,IAAA,CAAK4B,QAAA,CAASG,UAAA,CAAWE,GAAA,GAAMhC,MAAA,CAAOkB,QAAA,EAAU,KAAKjF,YAAY,CAAC;UACzEsE,MAAA,CAAOR,IAAA,CAAK4B,QAAA,CAASG,UAAA,CAAWE,GAAA,GAAMhC,MAAA,CAAOmB,QAAA,EAAU,KAAKlF,YAAY,CAAC;UACzEsE,MAAA,CAAOR,IAAA,CAAK4B,QAAA,CAASG,UAAA,CAAWE,GAAA,GAAMhC,MAAA,CAAOoB,QAAA,EAAU,KAAKnF,YAAY,CAAC;QAC1E;MACF;IACF;IAID,MAAMgG,QAAA,GAAW,IAAIC,cAAA,CAAgB;IAErC,IAAI5B,QAAA,CAAS3C,MAAA,GAAS,GAAGsE,QAAA,CAASE,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuB9B,QAAA,EAAU,CAAC,CAAC;IAClG,IAAIC,MAAA,CAAO5C,MAAA,GAAS,GAAGsE,QAAA,CAASE,YAAA,CAAa,UAAU,IAAIC,sBAAA,CAAuB7B,MAAA,EAAQ,CAAC,CAAC;IAC5F,IAAIC,KAAA,CAAM7C,MAAA,GAAS,GAAGsE,QAAA,CAASE,YAAA,CAAa,SAAS,IAAIC,sBAAA,CAAuB5B,KAAA,EAAO,CAAC,CAAC;IAEzFyB,QAAA,CAASI,qBAAA,CAAuB;IAIhC,MAAMC,QAAA,GAAW,IAAIC,cAAA,CAAe;MAAEtD,IAAA,EAAM;IAAK,CAAE;IAEnD,IAAIuB,KAAA,CAAM7C,MAAA,GAAS,GAAG;MACpB2E,QAAA,CAASE,YAAA,GAAe;IAC9B,OAAW;MACLF,QAAA,CAAS9B,KAAA,CAAMiC,MAAA,CAAOC,IAAA,CAAKC,MAAA,CAAM,IAAK,QAAQ;IAC/C;IAID,MAAMC,IAAA,GAAO,IAAIC,MAAA,CAAOZ,QAAA,EAAUK,QAAQ;IAC1C,IAAIQ,IAAA,GAAO3G,GAAA,CAAIsD,KAAA,CAAM,EAAE,EAAEsD,OAAA,CAAS,EAACC,IAAA,CAAK,EAAE;IAC1CF,IAAA,GAAO,WAAWpE,IAAA,CAAKoE,IAAI;IAC3BA,IAAA,GAAOA,IAAA,CAAK,CAAC,EAAErD,KAAA,CAAM,EAAE,EAAEsD,OAAA,CAAO,EAAGC,IAAA,CAAK,EAAE;IAC1CJ,IAAA,CAAKE,IAAA,GAAOA,IAAA;IAEZ,OAAOF,IAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}