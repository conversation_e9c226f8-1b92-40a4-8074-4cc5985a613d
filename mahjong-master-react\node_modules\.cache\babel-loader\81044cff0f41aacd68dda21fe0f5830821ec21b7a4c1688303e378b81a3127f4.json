{"ast": null, "code": "const clamp = (min, max, v) => {\n  if (v > max) return max;\n  if (v < min) return min;\n  return v;\n};\nexport { clamp };", "map": {"version": 3, "names": ["clamp", "min", "max", "v"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/motion-utils/dist/es/clamp.mjs"], "sourcesContent": ["const clamp = (min, max, v) => {\n    if (v > max)\n        return max;\n    if (v < min)\n        return min;\n    return v;\n};\n\nexport { clamp };\n"], "mappings": "AAAA,MAAMA,KAAK,GAAGA,CAACC,GAAG,EAAEC,GAAG,EAAEC,CAAC,KAAK;EAC3B,IAAIA,CAAC,GAAGD,GAAG,EACP,OAAOA,GAAG;EACd,IAAIC,CAAC,GAAGF,GAAG,EACP,OAAOA,GAAG;EACd,OAAOE,CAAC;AACZ,CAAC;AAED,SAASH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}