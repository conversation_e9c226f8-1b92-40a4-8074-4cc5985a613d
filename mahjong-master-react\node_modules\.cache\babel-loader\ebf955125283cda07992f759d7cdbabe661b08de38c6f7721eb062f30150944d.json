{"ast": null, "code": "import _objectSpread from \"F:/= \\u795E\\u706F\\u667A\\u5E93/- AI \\u521B\\u4F5C/AI APP/\\u795E\\u706F\\u9EBB\\u5C06\\u5927\\u5E08/mahjong-master-react/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { noop } from 'motion-utils';\nimport { addToQueue } from './queue.mjs';\nclass ViewTransitionBuilder {\n  constructor(update) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.currentSubject = \"root\";\n    this.targets = new Map();\n    this.notifyReady = noop;\n    this.readyPromise = new Promise(resolve => {\n      this.notifyReady = resolve;\n    });\n    this.update = update;\n    this.options = _objectSpread({\n      interrupt: \"wait\"\n    }, options);\n    addToQueue(this);\n  }\n  get(subject) {\n    this.currentSubject = subject;\n    return this;\n  }\n  layout(keyframes, options) {\n    this.updateTarget(\"layout\", keyframes, options);\n    return this;\n  }\n  new(keyframes, options) {\n    this.updateTarget(\"new\", keyframes, options);\n    return this;\n  }\n  old(keyframes, options) {\n    this.updateTarget(\"old\", keyframes, options);\n    return this;\n  }\n  enter(keyframes, options) {\n    this.updateTarget(\"enter\", keyframes, options);\n    return this;\n  }\n  exit(keyframes, options) {\n    this.updateTarget(\"exit\", keyframes, options);\n    return this;\n  }\n  crossfade(options) {\n    this.updateTarget(\"enter\", {\n      opacity: 1\n    }, options);\n    this.updateTarget(\"exit\", {\n      opacity: 0\n    }, options);\n    return this;\n  }\n  updateTarget(target, keyframes) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    const {\n      currentSubject,\n      targets\n    } = this;\n    if (!targets.has(currentSubject)) {\n      targets.set(currentSubject, {});\n    }\n    const targetData = targets.get(currentSubject);\n    targetData[target] = {\n      keyframes,\n      options\n    };\n  }\n  then(resolve, reject) {\n    return this.readyPromise.then(resolve, reject);\n  }\n}\nfunction animateView(update) {\n  let defaultOptions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return new ViewTransitionBuilder(update, defaultOptions);\n}\nexport { ViewTransitionBuilder, animateView };", "map": {"version": 3, "names": ["noop", "addToQueue", "ViewTransitionBuilder", "constructor", "update", "options", "arguments", "length", "undefined", "currentSubject", "targets", "Map", "notifyReady", "readyPromise", "Promise", "resolve", "_objectSpread", "interrupt", "get", "subject", "layout", "keyframes", "updateTarget", "new", "old", "enter", "exit", "crossfade", "opacity", "target", "has", "set", "targetData", "then", "reject", "animate<PERSON><PERSON><PERSON>", "defaultOptions"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/motion-dom/dist/es/view/index.mjs"], "sourcesContent": ["import { noop } from 'motion-utils';\nimport { addToQueue } from './queue.mjs';\n\nclass ViewTransitionBuilder {\n    constructor(update, options = {}) {\n        this.currentSubject = \"root\";\n        this.targets = new Map();\n        this.notifyReady = noop;\n        this.readyPromise = new Promise((resolve) => {\n            this.notifyReady = resolve;\n        });\n        this.update = update;\n        this.options = {\n            interrupt: \"wait\",\n            ...options,\n        };\n        addToQueue(this);\n    }\n    get(subject) {\n        this.currentSubject = subject;\n        return this;\n    }\n    layout(keyframes, options) {\n        this.updateTarget(\"layout\", keyframes, options);\n        return this;\n    }\n    new(keyframes, options) {\n        this.updateTarget(\"new\", keyframes, options);\n        return this;\n    }\n    old(keyframes, options) {\n        this.updateTarget(\"old\", keyframes, options);\n        return this;\n    }\n    enter(keyframes, options) {\n        this.updateTarget(\"enter\", keyframes, options);\n        return this;\n    }\n    exit(keyframes, options) {\n        this.updateTarget(\"exit\", keyframes, options);\n        return this;\n    }\n    crossfade(options) {\n        this.updateTarget(\"enter\", { opacity: 1 }, options);\n        this.updateTarget(\"exit\", { opacity: 0 }, options);\n        return this;\n    }\n    updateTarget(target, keyframes, options = {}) {\n        const { currentSubject, targets } = this;\n        if (!targets.has(currentSubject)) {\n            targets.set(currentSubject, {});\n        }\n        const targetData = targets.get(currentSubject);\n        targetData[target] = { keyframes, options };\n    }\n    then(resolve, reject) {\n        return this.readyPromise.then(resolve, reject);\n    }\n}\nfunction animateView(update, defaultOptions = {}) {\n    return new ViewTransitionBuilder(update, defaultOptions);\n}\n\nexport { ViewTransitionBuilder, animateView };\n"], "mappings": ";AAAA,SAASA,IAAI,QAAQ,cAAc;AACnC,SAASC,UAAU,QAAQ,aAAa;AAExC,MAAMC,qBAAqB,CAAC;EACxBC,WAAWA,CAACC,MAAM,EAAgB;IAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC5B,IAAI,CAACG,cAAc,GAAG,MAAM;IAC5B,IAAI,CAACC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACxB,IAAI,CAACC,WAAW,GAAGZ,IAAI;IACvB,IAAI,CAACa,YAAY,GAAG,IAAIC,OAAO,CAAEC,OAAO,IAAK;MACzC,IAAI,CAACH,WAAW,GAAGG,OAAO;IAC9B,CAAC,CAAC;IACF,IAAI,CAACX,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAAW,aAAA;MACRC,SAAS,EAAE;IAAM,GACdZ,OAAO,CACb;IACDJ,UAAU,CAAC,IAAI,CAAC;EACpB;EACAiB,GAAGA,CAACC,OAAO,EAAE;IACT,IAAI,CAACV,cAAc,GAAGU,OAAO;IAC7B,OAAO,IAAI;EACf;EACAC,MAAMA,CAACC,SAAS,EAAEhB,OAAO,EAAE;IACvB,IAAI,CAACiB,YAAY,CAAC,QAAQ,EAAED,SAAS,EAAEhB,OAAO,CAAC;IAC/C,OAAO,IAAI;EACf;EACAkB,GAAGA,CAACF,SAAS,EAAEhB,OAAO,EAAE;IACpB,IAAI,CAACiB,YAAY,CAAC,KAAK,EAAED,SAAS,EAAEhB,OAAO,CAAC;IAC5C,OAAO,IAAI;EACf;EACAmB,GAAGA,CAACH,SAAS,EAAEhB,OAAO,EAAE;IACpB,IAAI,CAACiB,YAAY,CAAC,KAAK,EAAED,SAAS,EAAEhB,OAAO,CAAC;IAC5C,OAAO,IAAI;EACf;EACAoB,KAAKA,CAACJ,SAAS,EAAEhB,OAAO,EAAE;IACtB,IAAI,CAACiB,YAAY,CAAC,OAAO,EAAED,SAAS,EAAEhB,OAAO,CAAC;IAC9C,OAAO,IAAI;EACf;EACAqB,IAAIA,CAACL,SAAS,EAAEhB,OAAO,EAAE;IACrB,IAAI,CAACiB,YAAY,CAAC,MAAM,EAAED,SAAS,EAAEhB,OAAO,CAAC;IAC7C,OAAO,IAAI;EACf;EACAsB,SAASA,CAACtB,OAAO,EAAE;IACf,IAAI,CAACiB,YAAY,CAAC,OAAO,EAAE;MAAEM,OAAO,EAAE;IAAE,CAAC,EAAEvB,OAAO,CAAC;IACnD,IAAI,CAACiB,YAAY,CAAC,MAAM,EAAE;MAAEM,OAAO,EAAE;IAAE,CAAC,EAAEvB,OAAO,CAAC;IAClD,OAAO,IAAI;EACf;EACAiB,YAAYA,CAACO,MAAM,EAAER,SAAS,EAAgB;IAAA,IAAdhB,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACxC,MAAM;MAAEG,cAAc;MAAEC;IAAQ,CAAC,GAAG,IAAI;IACxC,IAAI,CAACA,OAAO,CAACoB,GAAG,CAACrB,cAAc,CAAC,EAAE;MAC9BC,OAAO,CAACqB,GAAG,CAACtB,cAAc,EAAE,CAAC,CAAC,CAAC;IACnC;IACA,MAAMuB,UAAU,GAAGtB,OAAO,CAACQ,GAAG,CAACT,cAAc,CAAC;IAC9CuB,UAAU,CAACH,MAAM,CAAC,GAAG;MAAER,SAAS;MAAEhB;IAAQ,CAAC;EAC/C;EACA4B,IAAIA,CAAClB,OAAO,EAAEmB,MAAM,EAAE;IAClB,OAAO,IAAI,CAACrB,YAAY,CAACoB,IAAI,CAAClB,OAAO,EAAEmB,MAAM,CAAC;EAClD;AACJ;AACA,SAASC,WAAWA,CAAC/B,MAAM,EAAuB;EAAA,IAArBgC,cAAc,GAAA9B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC5C,OAAO,IAAIJ,qBAAqB,CAACE,MAAM,EAAEgC,cAAc,CAAC;AAC5D;AAEA,SAASlC,qBAAqB,EAAEiC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}