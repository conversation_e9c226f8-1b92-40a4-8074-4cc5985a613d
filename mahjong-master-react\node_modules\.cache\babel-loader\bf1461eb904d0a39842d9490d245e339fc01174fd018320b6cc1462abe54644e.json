{"ast": null, "code": "import { Triangle, Vector3, Line3, Sphere, Plane } from 'three';\nimport { SeparatingAxisBounds } from './SeparatingAxisBounds.js';\nimport { closestPointsSegmentToSegment, sphereIntersectTriangle } from './MathUtilities.js';\nconst ZERO_EPSILON = 1e-15;\nfunction isNearZero(value) {\n  return Math.abs(value) < ZERO_EPSILON;\n}\nexport class ExtendedTriangle extends Triangle {\n  constructor(...args) {\n    super(...args);\n    this.isExtendedTriangle = true;\n    this.satAxes = new Array(4).fill().map(() => new Vector3());\n    this.satBounds = new Array(4).fill().map(() => new SeparatingAxisBounds());\n    this.points = [this.a, this.b, this.c];\n    this.sphere = new Sphere();\n    this.plane = new Plane();\n    this.needsUpdate = true;\n  }\n  intersectsSphere(sphere) {\n    return sphereIntersectTriangle(sphere, this);\n  }\n  update() {\n    const a = this.a;\n    const b = this.b;\n    const c = this.c;\n    const points = this.points;\n    const satAxes = this.satAxes;\n    const satBounds = this.satBounds;\n    const axis0 = satAxes[0];\n    const sab0 = satBounds[0];\n    this.getNormal(axis0);\n    sab0.setFromPoints(axis0, points);\n    const axis1 = satAxes[1];\n    const sab1 = satBounds[1];\n    axis1.subVectors(a, b);\n    sab1.setFromPoints(axis1, points);\n    const axis2 = satAxes[2];\n    const sab2 = satBounds[2];\n    axis2.subVectors(b, c);\n    sab2.setFromPoints(axis2, points);\n    const axis3 = satAxes[3];\n    const sab3 = satBounds[3];\n    axis3.subVectors(c, a);\n    sab3.setFromPoints(axis3, points);\n    this.sphere.setFromPoints(this.points);\n    this.plane.setFromNormalAndCoplanarPoint(axis0, a);\n    this.needsUpdate = false;\n  }\n}\nExtendedTriangle.prototype.closestPointToSegment = function () {\n  const point1 = new Vector3();\n  const point2 = new Vector3();\n  const edge = new Line3();\n  return function distanceToSegment(segment, target1 = null, target2 = null) {\n    const {\n      start,\n      end\n    } = segment;\n    const points = this.points;\n    let distSq;\n    let closestDistanceSq = Infinity;\n\n    // check the triangle edges\n    for (let i = 0; i < 3; i++) {\n      const nexti = (i + 1) % 3;\n      edge.start.copy(points[i]);\n      edge.end.copy(points[nexti]);\n      closestPointsSegmentToSegment(edge, segment, point1, point2);\n      distSq = point1.distanceToSquared(point2);\n      if (distSq < closestDistanceSq) {\n        closestDistanceSq = distSq;\n        if (target1) target1.copy(point1);\n        if (target2) target2.copy(point2);\n      }\n    }\n\n    // check end points\n    this.closestPointToPoint(start, point1);\n    distSq = start.distanceToSquared(point1);\n    if (distSq < closestDistanceSq) {\n      closestDistanceSq = distSq;\n      if (target1) target1.copy(point1);\n      if (target2) target2.copy(start);\n    }\n    this.closestPointToPoint(end, point1);\n    distSq = end.distanceToSquared(point1);\n    if (distSq < closestDistanceSq) {\n      closestDistanceSq = distSq;\n      if (target1) target1.copy(point1);\n      if (target2) target2.copy(end);\n    }\n    return Math.sqrt(closestDistanceSq);\n  };\n}();\nExtendedTriangle.prototype.intersectsTriangle = function () {\n  const saTri2 = new ExtendedTriangle();\n  const arr1 = new Array(3);\n  const arr2 = new Array(3);\n  const cachedSatBounds = new SeparatingAxisBounds();\n  const cachedSatBounds2 = new SeparatingAxisBounds();\n  const cachedAxis = new Vector3();\n  const dir = new Vector3();\n  const dir1 = new Vector3();\n  const dir2 = new Vector3();\n  const tempDir = new Vector3();\n  const edge = new Line3();\n  const edge1 = new Line3();\n  const edge2 = new Line3();\n  const tempPoint = new Vector3();\n  function triIntersectPlane(tri, plane, targetEdge) {\n    // find the edge that intersects the other triangle plane\n    const points = tri.points;\n    let count = 0;\n    let startPointIntersection = -1;\n    for (let i = 0; i < 3; i++) {\n      const {\n        start,\n        end\n      } = edge;\n      start.copy(points[i]);\n      end.copy(points[(i + 1) % 3]);\n      edge.delta(dir);\n      const startIntersects = isNearZero(plane.distanceToPoint(start));\n      if (isNearZero(plane.normal.dot(dir)) && startIntersects) {\n        // if the edge lies on the plane then take the line\n        targetEdge.copy(edge);\n        count = 2;\n        break;\n      }\n\n      // check if the start point is near the plane because \"intersectLine\" is not robust to that case\n      const doesIntersect = plane.intersectLine(edge, tempPoint);\n      if (!doesIntersect && startIntersects) {\n        tempPoint.copy(start);\n      }\n\n      // ignore the end point\n      if ((doesIntersect || startIntersects) && !isNearZero(tempPoint.distanceTo(end))) {\n        if (count <= 1) {\n          // assign to the start or end point and save which index was snapped to\n          // the start point if necessary\n          const point = count === 1 ? targetEdge.start : targetEdge.end;\n          point.copy(tempPoint);\n          if (startIntersects) {\n            startPointIntersection = count;\n          }\n        } else if (count >= 2) {\n          // if we're here that means that there must have been one point that had\n          // snapped to the start point so replace it here\n          const point = startPointIntersection === 1 ? targetEdge.start : targetEdge.end;\n          point.copy(tempPoint);\n          count = 2;\n          break;\n        }\n        count++;\n        if (count === 2 && startPointIntersection === -1) {\n          break;\n        }\n      }\n    }\n    return count;\n  }\n\n  // TODO: If the triangles are coplanar and intersecting the target is nonsensical. It should at least\n  // be a line contained by both triangles if not a different special case somehow represented in the return result.\n  return function intersectsTriangle(other, target = null, suppressLog = false) {\n    if (this.needsUpdate) {\n      this.update();\n    }\n    if (!other.isExtendedTriangle) {\n      saTri2.copy(other);\n      saTri2.update();\n      other = saTri2;\n    } else if (other.needsUpdate) {\n      other.update();\n    }\n    const plane1 = this.plane;\n    const plane2 = other.plane;\n    if (Math.abs(plane1.normal.dot(plane2.normal)) > 1.0 - 1e-10) {\n      // perform separating axis intersection test only for coplanar triangles\n      const satBounds1 = this.satBounds;\n      const satAxes1 = this.satAxes;\n      arr2[0] = other.a;\n      arr2[1] = other.b;\n      arr2[2] = other.c;\n      for (let i = 0; i < 4; i++) {\n        const sb = satBounds1[i];\n        const sa = satAxes1[i];\n        cachedSatBounds.setFromPoints(sa, arr2);\n        if (sb.isSeparated(cachedSatBounds)) return false;\n      }\n      const satBounds2 = other.satBounds;\n      const satAxes2 = other.satAxes;\n      arr1[0] = this.a;\n      arr1[1] = this.b;\n      arr1[2] = this.c;\n      for (let i = 0; i < 4; i++) {\n        const sb = satBounds2[i];\n        const sa = satAxes2[i];\n        cachedSatBounds.setFromPoints(sa, arr1);\n        if (sb.isSeparated(cachedSatBounds)) return false;\n      }\n\n      // check crossed axes\n      for (let i = 0; i < 4; i++) {\n        const sa1 = satAxes1[i];\n        for (let i2 = 0; i2 < 4; i2++) {\n          const sa2 = satAxes2[i2];\n          cachedAxis.crossVectors(sa1, sa2);\n          cachedSatBounds.setFromPoints(cachedAxis, arr1);\n          cachedSatBounds2.setFromPoints(cachedAxis, arr2);\n          if (cachedSatBounds.isSeparated(cachedSatBounds2)) return false;\n        }\n      }\n      if (target) {\n        // TODO find two points that intersect on the edges and make that the result\n        if (!suppressLog) {\n          console.warn('ExtendedTriangle.intersectsTriangle: Triangles are coplanar which does not support an output edge. Setting edge to 0, 0, 0.');\n        }\n        target.start.set(0, 0, 0);\n        target.end.set(0, 0, 0);\n      }\n      return true;\n    } else {\n      // find the edge that intersects the other triangle plane\n      const count1 = triIntersectPlane(this, plane2, edge1);\n      if (count1 === 1 && other.containsPoint(edge1.end)) {\n        if (target) {\n          target.start.copy(edge1.end);\n          target.end.copy(edge1.end);\n        }\n        return true;\n      } else if (count1 !== 2) {\n        return false;\n      }\n\n      // find the other triangles edge that intersects this plane\n      const count2 = triIntersectPlane(other, plane1, edge2);\n      if (count2 === 1 && this.containsPoint(edge2.end)) {\n        if (target) {\n          target.start.copy(edge2.end);\n          target.end.copy(edge2.end);\n        }\n        return true;\n      } else if (count2 !== 2) {\n        return false;\n      }\n\n      // find swap the second edge so both lines are running the same direction\n      edge1.delta(dir1);\n      edge2.delta(dir2);\n      if (dir1.dot(dir2) < 0) {\n        let tmp = edge2.start;\n        edge2.start = edge2.end;\n        edge2.end = tmp;\n      }\n\n      // check if the edges are overlapping\n      const s1 = edge1.start.dot(dir1);\n      const e1 = edge1.end.dot(dir1);\n      const s2 = edge2.start.dot(dir1);\n      const e2 = edge2.end.dot(dir1);\n      const separated1 = e1 < s2;\n      const separated2 = s1 < e2;\n      if (s1 !== e2 && s2 !== e1 && separated1 === separated2) {\n        return false;\n      }\n\n      // assign the target output\n      if (target) {\n        tempDir.subVectors(edge1.start, edge2.start);\n        if (tempDir.dot(dir1) > 0) {\n          target.start.copy(edge1.start);\n        } else {\n          target.start.copy(edge2.start);\n        }\n        tempDir.subVectors(edge1.end, edge2.end);\n        if (tempDir.dot(dir1) < 0) {\n          target.end.copy(edge1.end);\n        } else {\n          target.end.copy(edge2.end);\n        }\n      }\n      return true;\n    }\n  };\n}();\nExtendedTriangle.prototype.distanceToPoint = function () {\n  const target = new Vector3();\n  return function distanceToPoint(point) {\n    this.closestPointToPoint(point, target);\n    return point.distanceTo(target);\n  };\n}();\nExtendedTriangle.prototype.distanceToTriangle = function () {\n  const point = new Vector3();\n  const point2 = new Vector3();\n  const cornerFields = ['a', 'b', 'c'];\n  const line1 = new Line3();\n  const line2 = new Line3();\n  return function distanceToTriangle(other, target1 = null, target2 = null) {\n    const lineTarget = target1 || target2 ? line1 : null;\n    if (this.intersectsTriangle(other, lineTarget)) {\n      if (target1 || target2) {\n        if (target1) lineTarget.getCenter(target1);\n        if (target2) lineTarget.getCenter(target2);\n      }\n      return 0;\n    }\n    let closestDistanceSq = Infinity;\n\n    // check all point distances\n    for (let i = 0; i < 3; i++) {\n      let dist;\n      const field = cornerFields[i];\n      const otherVec = other[field];\n      this.closestPointToPoint(otherVec, point);\n      dist = otherVec.distanceToSquared(point);\n      if (dist < closestDistanceSq) {\n        closestDistanceSq = dist;\n        if (target1) target1.copy(point);\n        if (target2) target2.copy(otherVec);\n      }\n      const thisVec = this[field];\n      other.closestPointToPoint(thisVec, point);\n      dist = thisVec.distanceToSquared(point);\n      if (dist < closestDistanceSq) {\n        closestDistanceSq = dist;\n        if (target1) target1.copy(thisVec);\n        if (target2) target2.copy(point);\n      }\n    }\n    for (let i = 0; i < 3; i++) {\n      const f11 = cornerFields[i];\n      const f12 = cornerFields[(i + 1) % 3];\n      line1.set(this[f11], this[f12]);\n      for (let i2 = 0; i2 < 3; i2++) {\n        const f21 = cornerFields[i2];\n        const f22 = cornerFields[(i2 + 1) % 3];\n        line2.set(other[f21], other[f22]);\n        closestPointsSegmentToSegment(line1, line2, point, point2);\n        const dist = point.distanceToSquared(point2);\n        if (dist < closestDistanceSq) {\n          closestDistanceSq = dist;\n          if (target1) target1.copy(point);\n          if (target2) target2.copy(point2);\n        }\n      }\n    }\n    return Math.sqrt(closestDistanceSq);\n  };\n}();", "map": {"version": 3, "names": ["Triangle", "Vector3", "Line3", "Sphere", "Plane", "SeparatingAxisBounds", "closestPointsSegmentToSegment", "sphereIntersectTriangle", "ZERO_EPSILON", "isNearZero", "value", "Math", "abs", "ExtendedTriangle", "constructor", "args", "isExtendedTriangle", "satAxes", "Array", "fill", "map", "satBounds", "points", "a", "b", "c", "sphere", "plane", "needsUpdate", "intersectsSphere", "update", "axis0", "sab0", "getNormal", "setFromPoints", "axis1", "sab1", "subVectors", "axis2", "sab2", "axis3", "sab3", "setFromNormalAndCoplanarPoint", "prototype", "closestPointToSegment", "point1", "point2", "edge", "distanceToSegment", "segment", "target1", "target2", "start", "end", "distSq", "closestDistanceSq", "Infinity", "i", "nexti", "copy", "distanceToSquared", "closestPointToPoint", "sqrt", "intersectsTriangle", "saTri2", "arr1", "arr2", "cachedSatBounds", "cachedSatBounds2", "cachedAxis", "dir", "dir1", "dir2", "tempDir", "edge1", "edge2", "tempPoint", "triIntersectPlane", "tri", "targetEdge", "count", "startPointIntersection", "delta", "startIntersects", "distanceToPoint", "normal", "dot", "doesIntersect", "intersectLine", "distanceTo", "point", "other", "target", "suppressLog", "plane1", "plane2", "satBounds1", "satAxes1", "sb", "sa", "isSeparated", "satBounds2", "satAxes2", "sa1", "i2", "sa2", "crossVectors", "console", "warn", "set", "count1", "containsPoint", "count2", "tmp", "s1", "e1", "s2", "e2", "separated1", "separated2", "distanceToTriangle", "cornerFields", "line1", "line2", "lineTarget", "getCenter", "dist", "field", "otherVec", "thisVec", "f11", "f12", "f21", "f22"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/three-mesh-bvh/src/math/ExtendedTriangle.js"], "sourcesContent": ["import { Triangle, Vector3, Line3, Sphere, Plane } from 'three';\nimport { SeparatingAxisBounds } from './SeparatingAxisBounds.js';\nimport { closestPointsSegmentToSegment, sphereIntersectTriangle } from './MathUtilities.js';\n\nconst ZERO_EPSILON = 1e-15;\nfunction isNearZero( value ) {\n\n\treturn Math.abs( value ) < ZERO_EPSILON;\n\n}\n\nexport class ExtendedTriangle extends Triangle {\n\n\tconstructor( ...args ) {\n\n\t\tsuper( ...args );\n\n\t\tthis.isExtendedTriangle = true;\n\t\tthis.satAxes = new Array( 4 ).fill().map( () => new Vector3() );\n\t\tthis.satBounds = new Array( 4 ).fill().map( () => new SeparatingAxisBounds() );\n\t\tthis.points = [ this.a, this.b, this.c ];\n\t\tthis.sphere = new Sphere();\n\t\tthis.plane = new Plane();\n\t\tthis.needsUpdate = true;\n\n\t}\n\n\tintersectsSphere( sphere ) {\n\n\t\treturn sphereIntersectTriangle( sphere, this );\n\n\t}\n\n\tupdate() {\n\n\t\tconst a = this.a;\n\t\tconst b = this.b;\n\t\tconst c = this.c;\n\t\tconst points = this.points;\n\n\t\tconst satAxes = this.satAxes;\n\t\tconst satBounds = this.satBounds;\n\n\t\tconst axis0 = satAxes[ 0 ];\n\t\tconst sab0 = satBounds[ 0 ];\n\t\tthis.getNormal( axis0 );\n\t\tsab0.setFromPoints( axis0, points );\n\n\t\tconst axis1 = satAxes[ 1 ];\n\t\tconst sab1 = satBounds[ 1 ];\n\t\taxis1.subVectors( a, b );\n\t\tsab1.setFromPoints( axis1, points );\n\n\t\tconst axis2 = satAxes[ 2 ];\n\t\tconst sab2 = satBounds[ 2 ];\n\t\taxis2.subVectors( b, c );\n\t\tsab2.setFromPoints( axis2, points );\n\n\t\tconst axis3 = satAxes[ 3 ];\n\t\tconst sab3 = satBounds[ 3 ];\n\t\taxis3.subVectors( c, a );\n\t\tsab3.setFromPoints( axis3, points );\n\n\t\tthis.sphere.setFromPoints( this.points );\n\t\tthis.plane.setFromNormalAndCoplanarPoint( axis0, a );\n\t\tthis.needsUpdate = false;\n\n\t}\n\n}\n\nExtendedTriangle.prototype.closestPointToSegment = ( function () {\n\n\tconst point1 = new Vector3();\n\tconst point2 = new Vector3();\n\tconst edge = new Line3();\n\n\treturn function distanceToSegment( segment, target1 = null, target2 = null ) {\n\n\t\tconst { start, end } = segment;\n\t\tconst points = this.points;\n\t\tlet distSq;\n\t\tlet closestDistanceSq = Infinity;\n\n\t\t// check the triangle edges\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst nexti = ( i + 1 ) % 3;\n\t\t\tedge.start.copy( points[ i ] );\n\t\t\tedge.end.copy( points[ nexti ] );\n\n\t\t\tclosestPointsSegmentToSegment( edge, segment, point1, point2 );\n\n\t\t\tdistSq = point1.distanceToSquared( point2 );\n\t\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = distSq;\n\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// check end points\n\t\tthis.closestPointToPoint( start, point1 );\n\t\tdistSq = start.distanceToSquared( point1 );\n\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\tclosestDistanceSq = distSq;\n\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\tif ( target2 ) target2.copy( start );\n\n\t\t}\n\n\t\tthis.closestPointToPoint( end, point1 );\n\t\tdistSq = end.distanceToSquared( point1 );\n\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\tclosestDistanceSq = distSq;\n\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\tif ( target2 ) target2.copy( end );\n\n\t\t}\n\n\t\treturn Math.sqrt( closestDistanceSq );\n\n\t};\n\n} )();\n\nExtendedTriangle.prototype.intersectsTriangle = ( function () {\n\n\tconst saTri2 = new ExtendedTriangle();\n\tconst arr1 = new Array( 3 );\n\tconst arr2 = new Array( 3 );\n\tconst cachedSatBounds = new SeparatingAxisBounds();\n\tconst cachedSatBounds2 = new SeparatingAxisBounds();\n\tconst cachedAxis = new Vector3();\n\tconst dir = new Vector3();\n\tconst dir1 = new Vector3();\n\tconst dir2 = new Vector3();\n\tconst tempDir = new Vector3();\n\tconst edge = new Line3();\n\tconst edge1 = new Line3();\n\tconst edge2 = new Line3();\n\tconst tempPoint = new Vector3();\n\n\tfunction triIntersectPlane( tri, plane, targetEdge ) {\n\n\t\t// find the edge that intersects the other triangle plane\n\t\tconst points = tri.points;\n\t\tlet count = 0;\n\t\tlet startPointIntersection = - 1;\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst { start, end } = edge;\n\t\t\tstart.copy( points[ i ] );\n\t\t\tend.copy( points[ ( i + 1 ) % 3 ] );\n\t\t\tedge.delta( dir );\n\n\t\t\tconst startIntersects = isNearZero( plane.distanceToPoint( start ) );\n\t\t\tif ( isNearZero( plane.normal.dot( dir ) ) && startIntersects ) {\n\n\t\t\t\t// if the edge lies on the plane then take the line\n\t\t\t\ttargetEdge.copy( edge );\n\t\t\t\tcount = 2;\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\t// check if the start point is near the plane because \"intersectLine\" is not robust to that case\n\t\t\tconst doesIntersect = plane.intersectLine( edge, tempPoint );\n\t\t\tif ( ! doesIntersect && startIntersects ) {\n\n\t\t\t\ttempPoint.copy( start );\n\n\t\t\t}\n\n\t\t\t// ignore the end point\n\t\t\tif ( ( doesIntersect || startIntersects ) && ! isNearZero( tempPoint.distanceTo( end ) ) ) {\n\n\t\t\t\tif ( count <= 1 ) {\n\n\t\t\t\t\t// assign to the start or end point and save which index was snapped to\n\t\t\t\t\t// the start point if necessary\n\t\t\t\t\tconst point = count === 1 ? targetEdge.start : targetEdge.end;\n\t\t\t\t\tpoint.copy( tempPoint );\n\t\t\t\t\tif ( startIntersects ) {\n\n\t\t\t\t\t\tstartPointIntersection = count;\n\n\t\t\t\t\t}\n\n\t\t\t\t} else if ( count >= 2 ) {\n\n\t\t\t\t\t// if we're here that means that there must have been one point that had\n\t\t\t\t\t// snapped to the start point so replace it here\n\t\t\t\t\tconst point = startPointIntersection === 1 ? targetEdge.start : targetEdge.end;\n\t\t\t\t\tpoint.copy( tempPoint );\n\t\t\t\t\tcount = 2;\n\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t\tcount ++;\n\t\t\t\tif ( count === 2 && startPointIntersection === - 1 ) {\n\n\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn count;\n\n\t}\n\n\t// TODO: If the triangles are coplanar and intersecting the target is nonsensical. It should at least\n\t// be a line contained by both triangles if not a different special case somehow represented in the return result.\n\treturn function intersectsTriangle( other, target = null, suppressLog = false ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tif ( ! other.isExtendedTriangle ) {\n\n\t\t\tsaTri2.copy( other );\n\t\t\tsaTri2.update();\n\t\t\tother = saTri2;\n\n\t\t} else if ( other.needsUpdate ) {\n\n\t\t\tother.update();\n\n\t\t}\n\n\t\tconst plane1 = this.plane;\n\t\tconst plane2 = other.plane;\n\n\t\tif ( Math.abs( plane1.normal.dot( plane2.normal ) ) > 1.0 - 1e-10 ) {\n\n\t\t\t// perform separating axis intersection test only for coplanar triangles\n\t\t\tconst satBounds1 = this.satBounds;\n\t\t\tconst satAxes1 = this.satAxes;\n\t\t\tarr2[ 0 ] = other.a;\n\t\t\tarr2[ 1 ] = other.b;\n\t\t\tarr2[ 2 ] = other.c;\n\t\t\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\t\t\tconst sb = satBounds1[ i ];\n\t\t\t\tconst sa = satAxes1[ i ];\n\t\t\t\tcachedSatBounds.setFromPoints( sa, arr2 );\n\t\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t\t}\n\n\t\t\tconst satBounds2 = other.satBounds;\n\t\t\tconst satAxes2 = other.satAxes;\n\t\t\tarr1[ 0 ] = this.a;\n\t\t\tarr1[ 1 ] = this.b;\n\t\t\tarr1[ 2 ] = this.c;\n\t\t\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\t\t\tconst sb = satBounds2[ i ];\n\t\t\t\tconst sa = satAxes2[ i ];\n\t\t\t\tcachedSatBounds.setFromPoints( sa, arr1 );\n\t\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t\t}\n\n\t\t\t// check crossed axes\n\t\t\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\t\t\tconst sa1 = satAxes1[ i ];\n\t\t\t\tfor ( let i2 = 0; i2 < 4; i2 ++ ) {\n\n\t\t\t\t\tconst sa2 = satAxes2[ i2 ];\n\t\t\t\t\tcachedAxis.crossVectors( sa1, sa2 );\n\t\t\t\t\tcachedSatBounds.setFromPoints( cachedAxis, arr1 );\n\t\t\t\t\tcachedSatBounds2.setFromPoints( cachedAxis, arr2 );\n\t\t\t\t\tif ( cachedSatBounds.isSeparated( cachedSatBounds2 ) ) return false;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( target ) {\n\n\t\t\t\t// TODO find two points that intersect on the edges and make that the result\n\t\t\t\tif ( ! suppressLog ) {\n\n\t\t\t\t\tconsole.warn( 'ExtendedTriangle.intersectsTriangle: Triangles are coplanar which does not support an output edge. Setting edge to 0, 0, 0.' );\n\n\t\t\t\t}\n\n\t\t\t\ttarget.start.set( 0, 0, 0 );\n\t\t\t\ttarget.end.set( 0, 0, 0 );\n\n\t\t\t}\n\n\t\t\treturn true;\n\n\t\t} else {\n\n\t\t\t// find the edge that intersects the other triangle plane\n\t\t\tconst count1 = triIntersectPlane( this, plane2, edge1 );\n\t\t\tif ( count1 === 1 && other.containsPoint( edge1.end ) ) {\n\n\t\t\t\tif ( target ) {\n\n\t\t\t\t\ttarget.start.copy( edge1.end );\n\t\t\t\t\ttarget.end.copy( edge1.end );\n\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\n\t\t\t} else if ( count1 !== 2 ) {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\t// find the other triangles edge that intersects this plane\n\t\t\tconst count2 = triIntersectPlane( other, plane1, edge2 );\n\t\t\tif ( count2 === 1 && this.containsPoint( edge2.end ) ) {\n\n\t\t\t\tif ( target ) {\n\n\t\t\t\t\ttarget.start.copy( edge2.end );\n\t\t\t\t\ttarget.end.copy( edge2.end );\n\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\n\t\t\t} else if ( count2 !== 2 ) {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\t// find swap the second edge so both lines are running the same direction\n\t\t\tedge1.delta( dir1 );\n\t\t\tedge2.delta( dir2 );\n\n\t\t\tif ( dir1.dot( dir2 ) < 0 ) {\n\n\t\t\t\tlet tmp = edge2.start;\n\t\t\t\tedge2.start = edge2.end;\n\t\t\t\tedge2.end = tmp;\n\n\t\t\t}\n\n\t\t\t// check if the edges are overlapping\n\t\t\tconst s1 = edge1.start.dot( dir1 );\n\t\t\tconst e1 = edge1.end.dot( dir1 );\n\t\t\tconst s2 = edge2.start.dot( dir1 );\n\t\t\tconst e2 = edge2.end.dot( dir1 );\n\t\t\tconst separated1 = e1 < s2;\n\t\t\tconst separated2 = s1 < e2;\n\n\t\t\tif ( s1 !== e2 && s2 !== e1 && separated1 === separated2 ) {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\t// assign the target output\n\t\t\tif ( target ) {\n\n\t\t\t\ttempDir.subVectors( edge1.start, edge2.start );\n\t\t\t\tif ( tempDir.dot( dir1 ) > 0 ) {\n\n\t\t\t\t\ttarget.start.copy( edge1.start );\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttarget.start.copy( edge2.start );\n\n\t\t\t\t}\n\n\t\t\t\ttempDir.subVectors( edge1.end, edge2.end );\n\t\t\t\tif ( tempDir.dot( dir1 ) < 0 ) {\n\n\t\t\t\t\ttarget.end.copy( edge1.end );\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttarget.end.copy( edge2.end );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn true;\n\n\t\t}\n\n\t};\n\n} )();\n\n\nExtendedTriangle.prototype.distanceToPoint = ( function () {\n\n\tconst target = new Vector3();\n\treturn function distanceToPoint( point ) {\n\n\t\tthis.closestPointToPoint( point, target );\n\t\treturn point.distanceTo( target );\n\n\t};\n\n} )();\n\n\nExtendedTriangle.prototype.distanceToTriangle = ( function () {\n\n\tconst point = new Vector3();\n\tconst point2 = new Vector3();\n\tconst cornerFields = [ 'a', 'b', 'c' ];\n\tconst line1 = new Line3();\n\tconst line2 = new Line3();\n\n\treturn function distanceToTriangle( other, target1 = null, target2 = null ) {\n\n\t\tconst lineTarget = target1 || target2 ? line1 : null;\n\t\tif ( this.intersectsTriangle( other, lineTarget ) ) {\n\n\t\t\tif ( target1 || target2 ) {\n\n\t\t\t\tif ( target1 ) lineTarget.getCenter( target1 );\n\t\t\t\tif ( target2 ) lineTarget.getCenter( target2 );\n\n\t\t\t}\n\n\t\t\treturn 0;\n\n\t\t}\n\n\t\tlet closestDistanceSq = Infinity;\n\n\t\t// check all point distances\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tlet dist;\n\t\t\tconst field = cornerFields[ i ];\n\t\t\tconst otherVec = other[ field ];\n\t\t\tthis.closestPointToPoint( otherVec, point );\n\n\t\t\tdist = otherVec.distanceToSquared( point );\n\n\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\tif ( target1 ) target1.copy( point );\n\t\t\t\tif ( target2 ) target2.copy( otherVec );\n\n\t\t\t}\n\n\n\t\t\tconst thisVec = this[ field ];\n\t\t\tother.closestPointToPoint( thisVec, point );\n\n\t\t\tdist = thisVec.distanceToSquared( point );\n\n\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\tif ( target1 ) target1.copy( thisVec );\n\t\t\t\tif ( target2 ) target2.copy( point );\n\n\t\t\t}\n\n\t\t}\n\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst f11 = cornerFields[ i ];\n\t\t\tconst f12 = cornerFields[ ( i + 1 ) % 3 ];\n\t\t\tline1.set( this[ f11 ], this[ f12 ] );\n\t\t\tfor ( let i2 = 0; i2 < 3; i2 ++ ) {\n\n\t\t\t\tconst f21 = cornerFields[ i2 ];\n\t\t\t\tconst f22 = cornerFields[ ( i2 + 1 ) % 3 ];\n\t\t\t\tline2.set( other[ f21 ], other[ f22 ] );\n\n\t\t\t\tclosestPointsSegmentToSegment( line1, line2, point, point2 );\n\n\t\t\t\tconst dist = point.distanceToSquared( point2 );\n\t\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\t\tif ( target1 ) target1.copy( point );\n\t\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Math.sqrt( closestDistanceSq );\n\n\t};\n\n} )();\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,OAAO,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,QAAQ,OAAO;AAC/D,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,6BAA6B,EAAEC,uBAAuB,QAAQ,oBAAoB;AAE3F,MAAMC,YAAY,GAAG,KAAK;AAC1B,SAASC,UAAUA,CAAEC,KAAK,EAAG;EAE5B,OAAOC,IAAI,CAACC,GAAG,CAAEF,KAAM,CAAC,GAAGF,YAAY;AAExC;AAEA,OAAO,MAAMK,gBAAgB,SAASb,QAAQ,CAAC;EAE9Cc,WAAWA,CAAE,GAAGC,IAAI,EAAG;IAEtB,KAAK,CAAE,GAAGA,IAAK,CAAC;IAEhB,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,OAAO,GAAG,IAAIC,KAAK,CAAE,CAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,GAAG,CAAE,MAAM,IAAInB,OAAO,CAAC,CAAE,CAAC;IAC/D,IAAI,CAACoB,SAAS,GAAG,IAAIH,KAAK,CAAE,CAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,GAAG,CAAE,MAAM,IAAIf,oBAAoB,CAAC,CAAE,CAAC;IAC9E,IAAI,CAACiB,MAAM,GAAG,CAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,CAAE;IACxC,IAAI,CAACC,MAAM,GAAG,IAAIvB,MAAM,CAAC,CAAC;IAC1B,IAAI,CAACwB,KAAK,GAAG,IAAIvB,KAAK,CAAC,CAAC;IACxB,IAAI,CAACwB,WAAW,GAAG,IAAI;EAExB;EAEAC,gBAAgBA,CAAEH,MAAM,EAAG;IAE1B,OAAOnB,uBAAuB,CAAEmB,MAAM,EAAE,IAAK,CAAC;EAE/C;EAEAI,MAAMA,CAAA,EAAG;IAER,MAAMP,CAAC,GAAG,IAAI,CAACA,CAAC;IAChB,MAAMC,CAAC,GAAG,IAAI,CAACA,CAAC;IAChB,MAAMC,CAAC,GAAG,IAAI,CAACA,CAAC;IAChB,MAAMH,MAAM,GAAG,IAAI,CAACA,MAAM;IAE1B,MAAML,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMI,SAAS,GAAG,IAAI,CAACA,SAAS;IAEhC,MAAMU,KAAK,GAAGd,OAAO,CAAE,CAAC,CAAE;IAC1B,MAAMe,IAAI,GAAGX,SAAS,CAAE,CAAC,CAAE;IAC3B,IAAI,CAACY,SAAS,CAAEF,KAAM,CAAC;IACvBC,IAAI,CAACE,aAAa,CAAEH,KAAK,EAAET,MAAO,CAAC;IAEnC,MAAMa,KAAK,GAAGlB,OAAO,CAAE,CAAC,CAAE;IAC1B,MAAMmB,IAAI,GAAGf,SAAS,CAAE,CAAC,CAAE;IAC3Bc,KAAK,CAACE,UAAU,CAAEd,CAAC,EAAEC,CAAE,CAAC;IACxBY,IAAI,CAACF,aAAa,CAAEC,KAAK,EAAEb,MAAO,CAAC;IAEnC,MAAMgB,KAAK,GAAGrB,OAAO,CAAE,CAAC,CAAE;IAC1B,MAAMsB,IAAI,GAAGlB,SAAS,CAAE,CAAC,CAAE;IAC3BiB,KAAK,CAACD,UAAU,CAAEb,CAAC,EAAEC,CAAE,CAAC;IACxBc,IAAI,CAACL,aAAa,CAAEI,KAAK,EAAEhB,MAAO,CAAC;IAEnC,MAAMkB,KAAK,GAAGvB,OAAO,CAAE,CAAC,CAAE;IAC1B,MAAMwB,IAAI,GAAGpB,SAAS,CAAE,CAAC,CAAE;IAC3BmB,KAAK,CAACH,UAAU,CAAEZ,CAAC,EAAEF,CAAE,CAAC;IACxBkB,IAAI,CAACP,aAAa,CAAEM,KAAK,EAAElB,MAAO,CAAC;IAEnC,IAAI,CAACI,MAAM,CAACQ,aAAa,CAAE,IAAI,CAACZ,MAAO,CAAC;IACxC,IAAI,CAACK,KAAK,CAACe,6BAA6B,CAAEX,KAAK,EAAER,CAAE,CAAC;IACpD,IAAI,CAACK,WAAW,GAAG,KAAK;EAEzB;AAED;AAEAf,gBAAgB,CAAC8B,SAAS,CAACC,qBAAqB,GAAK,YAAY;EAEhE,MAAMC,MAAM,GAAG,IAAI5C,OAAO,CAAC,CAAC;EAC5B,MAAM6C,MAAM,GAAG,IAAI7C,OAAO,CAAC,CAAC;EAC5B,MAAM8C,IAAI,GAAG,IAAI7C,KAAK,CAAC,CAAC;EAExB,OAAO,SAAS8C,iBAAiBA,CAAEC,OAAO,EAAEC,OAAO,GAAG,IAAI,EAAEC,OAAO,GAAG,IAAI,EAAG;IAE5E,MAAM;MAAEC,KAAK;MAAEC;IAAI,CAAC,GAAGJ,OAAO;IAC9B,MAAM3B,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAIgC,MAAM;IACV,IAAIC,iBAAiB,GAAGC,QAAQ;;IAEhC;IACA,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B,MAAMC,KAAK,GAAG,CAAED,CAAC,GAAG,CAAC,IAAK,CAAC;MAC3BV,IAAI,CAACK,KAAK,CAACO,IAAI,CAAErC,MAAM,CAAEmC,CAAC,CAAG,CAAC;MAC9BV,IAAI,CAACM,GAAG,CAACM,IAAI,CAAErC,MAAM,CAAEoC,KAAK,CAAG,CAAC;MAEhCpD,6BAA6B,CAAEyC,IAAI,EAAEE,OAAO,EAAEJ,MAAM,EAAEC,MAAO,CAAC;MAE9DQ,MAAM,GAAGT,MAAM,CAACe,iBAAiB,CAAEd,MAAO,CAAC;MAC3C,IAAKQ,MAAM,GAAGC,iBAAiB,EAAG;QAEjCA,iBAAiB,GAAGD,MAAM;QAC1B,IAAKJ,OAAO,EAAGA,OAAO,CAACS,IAAI,CAAEd,MAAO,CAAC;QACrC,IAAKM,OAAO,EAAGA,OAAO,CAACQ,IAAI,CAAEb,MAAO,CAAC;MAEtC;IAED;;IAEA;IACA,IAAI,CAACe,mBAAmB,CAAET,KAAK,EAAEP,MAAO,CAAC;IACzCS,MAAM,GAAGF,KAAK,CAACQ,iBAAiB,CAAEf,MAAO,CAAC;IAC1C,IAAKS,MAAM,GAAGC,iBAAiB,EAAG;MAEjCA,iBAAiB,GAAGD,MAAM;MAC1B,IAAKJ,OAAO,EAAGA,OAAO,CAACS,IAAI,CAAEd,MAAO,CAAC;MACrC,IAAKM,OAAO,EAAGA,OAAO,CAACQ,IAAI,CAAEP,KAAM,CAAC;IAErC;IAEA,IAAI,CAACS,mBAAmB,CAAER,GAAG,EAAER,MAAO,CAAC;IACvCS,MAAM,GAAGD,GAAG,CAACO,iBAAiB,CAAEf,MAAO,CAAC;IACxC,IAAKS,MAAM,GAAGC,iBAAiB,EAAG;MAEjCA,iBAAiB,GAAGD,MAAM;MAC1B,IAAKJ,OAAO,EAAGA,OAAO,CAACS,IAAI,CAAEd,MAAO,CAAC;MACrC,IAAKM,OAAO,EAAGA,OAAO,CAACQ,IAAI,CAAEN,GAAI,CAAC;IAEnC;IAEA,OAAO1C,IAAI,CAACmD,IAAI,CAAEP,iBAAkB,CAAC;EAEtC,CAAC;AAEF,CAAC,CAAG,CAAC;AAEL1C,gBAAgB,CAAC8B,SAAS,CAACoB,kBAAkB,GAAK,YAAY;EAE7D,MAAMC,MAAM,GAAG,IAAInD,gBAAgB,CAAC,CAAC;EACrC,MAAMoD,IAAI,GAAG,IAAI/C,KAAK,CAAE,CAAE,CAAC;EAC3B,MAAMgD,IAAI,GAAG,IAAIhD,KAAK,CAAE,CAAE,CAAC;EAC3B,MAAMiD,eAAe,GAAG,IAAI9D,oBAAoB,CAAC,CAAC;EAClD,MAAM+D,gBAAgB,GAAG,IAAI/D,oBAAoB,CAAC,CAAC;EACnD,MAAMgE,UAAU,GAAG,IAAIpE,OAAO,CAAC,CAAC;EAChC,MAAMqE,GAAG,GAAG,IAAIrE,OAAO,CAAC,CAAC;EACzB,MAAMsE,IAAI,GAAG,IAAItE,OAAO,CAAC,CAAC;EAC1B,MAAMuE,IAAI,GAAG,IAAIvE,OAAO,CAAC,CAAC;EAC1B,MAAMwE,OAAO,GAAG,IAAIxE,OAAO,CAAC,CAAC;EAC7B,MAAM8C,IAAI,GAAG,IAAI7C,KAAK,CAAC,CAAC;EACxB,MAAMwE,KAAK,GAAG,IAAIxE,KAAK,CAAC,CAAC;EACzB,MAAMyE,KAAK,GAAG,IAAIzE,KAAK,CAAC,CAAC;EACzB,MAAM0E,SAAS,GAAG,IAAI3E,OAAO,CAAC,CAAC;EAE/B,SAAS4E,iBAAiBA,CAAEC,GAAG,EAAEnD,KAAK,EAAEoD,UAAU,EAAG;IAEpD;IACA,MAAMzD,MAAM,GAAGwD,GAAG,CAACxD,MAAM;IACzB,IAAI0D,KAAK,GAAG,CAAC;IACb,IAAIC,sBAAsB,GAAG,CAAE,CAAC;IAChC,KAAM,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B,MAAM;QAAEL,KAAK;QAAEC;MAAI,CAAC,GAAGN,IAAI;MAC3BK,KAAK,CAACO,IAAI,CAAErC,MAAM,CAAEmC,CAAC,CAAG,CAAC;MACzBJ,GAAG,CAACM,IAAI,CAAErC,MAAM,CAAE,CAAEmC,CAAC,GAAG,CAAC,IAAK,CAAC,CAAG,CAAC;MACnCV,IAAI,CAACmC,KAAK,CAAEZ,GAAI,CAAC;MAEjB,MAAMa,eAAe,GAAG1E,UAAU,CAAEkB,KAAK,CAACyD,eAAe,CAAEhC,KAAM,CAAE,CAAC;MACpE,IAAK3C,UAAU,CAAEkB,KAAK,CAAC0D,MAAM,CAACC,GAAG,CAAEhB,GAAI,CAAE,CAAC,IAAIa,eAAe,EAAG;QAE/D;QACAJ,UAAU,CAACpB,IAAI,CAAEZ,IAAK,CAAC;QACvBiC,KAAK,GAAG,CAAC;QACT;MAED;;MAEA;MACA,MAAMO,aAAa,GAAG5D,KAAK,CAAC6D,aAAa,CAAEzC,IAAI,EAAE6B,SAAU,CAAC;MAC5D,IAAK,CAAEW,aAAa,IAAIJ,eAAe,EAAG;QAEzCP,SAAS,CAACjB,IAAI,CAAEP,KAAM,CAAC;MAExB;;MAEA;MACA,IAAK,CAAEmC,aAAa,IAAIJ,eAAe,KAAM,CAAE1E,UAAU,CAAEmE,SAAS,CAACa,UAAU,CAAEpC,GAAI,CAAE,CAAC,EAAG;QAE1F,IAAK2B,KAAK,IAAI,CAAC,EAAG;UAEjB;UACA;UACA,MAAMU,KAAK,GAAGV,KAAK,KAAK,CAAC,GAAGD,UAAU,CAAC3B,KAAK,GAAG2B,UAAU,CAAC1B,GAAG;UAC7DqC,KAAK,CAAC/B,IAAI,CAAEiB,SAAU,CAAC;UACvB,IAAKO,eAAe,EAAG;YAEtBF,sBAAsB,GAAGD,KAAK;UAE/B;QAED,CAAC,MAAM,IAAKA,KAAK,IAAI,CAAC,EAAG;UAExB;UACA;UACA,MAAMU,KAAK,GAAGT,sBAAsB,KAAK,CAAC,GAAGF,UAAU,CAAC3B,KAAK,GAAG2B,UAAU,CAAC1B,GAAG;UAC9EqC,KAAK,CAAC/B,IAAI,CAAEiB,SAAU,CAAC;UACvBI,KAAK,GAAG,CAAC;UACT;QAED;QAEAA,KAAK,EAAG;QACR,IAAKA,KAAK,KAAK,CAAC,IAAIC,sBAAsB,KAAK,CAAE,CAAC,EAAG;UAEpD;QAED;MAED;IAED;IAEA,OAAOD,KAAK;EAEb;;EAEA;EACA;EACA,OAAO,SAASjB,kBAAkBA,CAAE4B,KAAK,EAAEC,MAAM,GAAG,IAAI,EAAEC,WAAW,GAAG,KAAK,EAAG;IAE/E,IAAK,IAAI,CAACjE,WAAW,EAAG;MAEvB,IAAI,CAACE,MAAM,CAAC,CAAC;IAEd;IAEA,IAAK,CAAE6D,KAAK,CAAC3E,kBAAkB,EAAG;MAEjCgD,MAAM,CAACL,IAAI,CAAEgC,KAAM,CAAC;MACpB3B,MAAM,CAAClC,MAAM,CAAC,CAAC;MACf6D,KAAK,GAAG3B,MAAM;IAEf,CAAC,MAAM,IAAK2B,KAAK,CAAC/D,WAAW,EAAG;MAE/B+D,KAAK,CAAC7D,MAAM,CAAC,CAAC;IAEf;IAEA,MAAMgE,MAAM,GAAG,IAAI,CAACnE,KAAK;IACzB,MAAMoE,MAAM,GAAGJ,KAAK,CAAChE,KAAK;IAE1B,IAAKhB,IAAI,CAACC,GAAG,CAAEkF,MAAM,CAACT,MAAM,CAACC,GAAG,CAAES,MAAM,CAACV,MAAO,CAAE,CAAC,GAAG,GAAG,GAAG,KAAK,EAAG;MAEnE;MACA,MAAMW,UAAU,GAAG,IAAI,CAAC3E,SAAS;MACjC,MAAM4E,QAAQ,GAAG,IAAI,CAAChF,OAAO;MAC7BiD,IAAI,CAAE,CAAC,CAAE,GAAGyB,KAAK,CAACpE,CAAC;MACnB2C,IAAI,CAAE,CAAC,CAAE,GAAGyB,KAAK,CAACnE,CAAC;MACnB0C,IAAI,CAAE,CAAC,CAAE,GAAGyB,KAAK,CAAClE,CAAC;MACnB,KAAM,IAAIgC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;QAE9B,MAAMyC,EAAE,GAAGF,UAAU,CAAEvC,CAAC,CAAE;QAC1B,MAAM0C,EAAE,GAAGF,QAAQ,CAAExC,CAAC,CAAE;QACxBU,eAAe,CAACjC,aAAa,CAAEiE,EAAE,EAAEjC,IAAK,CAAC;QACzC,IAAKgC,EAAE,CAACE,WAAW,CAAEjC,eAAgB,CAAC,EAAG,OAAO,KAAK;MAEtD;MAEA,MAAMkC,UAAU,GAAGV,KAAK,CAACtE,SAAS;MAClC,MAAMiF,QAAQ,GAAGX,KAAK,CAAC1E,OAAO;MAC9BgD,IAAI,CAAE,CAAC,CAAE,GAAG,IAAI,CAAC1C,CAAC;MAClB0C,IAAI,CAAE,CAAC,CAAE,GAAG,IAAI,CAACzC,CAAC;MAClByC,IAAI,CAAE,CAAC,CAAE,GAAG,IAAI,CAACxC,CAAC;MAClB,KAAM,IAAIgC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;QAE9B,MAAMyC,EAAE,GAAGG,UAAU,CAAE5C,CAAC,CAAE;QAC1B,MAAM0C,EAAE,GAAGG,QAAQ,CAAE7C,CAAC,CAAE;QACxBU,eAAe,CAACjC,aAAa,CAAEiE,EAAE,EAAElC,IAAK,CAAC;QACzC,IAAKiC,EAAE,CAACE,WAAW,CAAEjC,eAAgB,CAAC,EAAG,OAAO,KAAK;MAEtD;;MAEA;MACA,KAAM,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;QAE9B,MAAM8C,GAAG,GAAGN,QAAQ,CAAExC,CAAC,CAAE;QACzB,KAAM,IAAI+C,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,EAAEA,EAAE,EAAG,EAAG;UAEjC,MAAMC,GAAG,GAAGH,QAAQ,CAAEE,EAAE,CAAE;UAC1BnC,UAAU,CAACqC,YAAY,CAAEH,GAAG,EAAEE,GAAI,CAAC;UACnCtC,eAAe,CAACjC,aAAa,CAAEmC,UAAU,EAAEJ,IAAK,CAAC;UACjDG,gBAAgB,CAAClC,aAAa,CAAEmC,UAAU,EAAEH,IAAK,CAAC;UAClD,IAAKC,eAAe,CAACiC,WAAW,CAAEhC,gBAAiB,CAAC,EAAG,OAAO,KAAK;QAEpE;MAED;MAEA,IAAKwB,MAAM,EAAG;QAEb;QACA,IAAK,CAAEC,WAAW,EAAG;UAEpBc,OAAO,CAACC,IAAI,CAAE,6HAA8H,CAAC;QAE9I;QAEAhB,MAAM,CAACxC,KAAK,CAACyD,GAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;QAC3BjB,MAAM,CAACvC,GAAG,CAACwD,GAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;MAE1B;MAEA,OAAO,IAAI;IAEZ,CAAC,MAAM;MAEN;MACA,MAAMC,MAAM,GAAGjC,iBAAiB,CAAE,IAAI,EAAEkB,MAAM,EAAErB,KAAM,CAAC;MACvD,IAAKoC,MAAM,KAAK,CAAC,IAAInB,KAAK,CAACoB,aAAa,CAAErC,KAAK,CAACrB,GAAI,CAAC,EAAG;QAEvD,IAAKuC,MAAM,EAAG;UAEbA,MAAM,CAACxC,KAAK,CAACO,IAAI,CAAEe,KAAK,CAACrB,GAAI,CAAC;UAC9BuC,MAAM,CAACvC,GAAG,CAACM,IAAI,CAAEe,KAAK,CAACrB,GAAI,CAAC;QAE7B;QAEA,OAAO,IAAI;MAEZ,CAAC,MAAM,IAAKyD,MAAM,KAAK,CAAC,EAAG;QAE1B,OAAO,KAAK;MAEb;;MAEA;MACA,MAAME,MAAM,GAAGnC,iBAAiB,CAAEc,KAAK,EAAEG,MAAM,EAAEnB,KAAM,CAAC;MACxD,IAAKqC,MAAM,KAAK,CAAC,IAAI,IAAI,CAACD,aAAa,CAAEpC,KAAK,CAACtB,GAAI,CAAC,EAAG;QAEtD,IAAKuC,MAAM,EAAG;UAEbA,MAAM,CAACxC,KAAK,CAACO,IAAI,CAAEgB,KAAK,CAACtB,GAAI,CAAC;UAC9BuC,MAAM,CAACvC,GAAG,CAACM,IAAI,CAAEgB,KAAK,CAACtB,GAAI,CAAC;QAE7B;QAEA,OAAO,IAAI;MAEZ,CAAC,MAAM,IAAK2D,MAAM,KAAK,CAAC,EAAG;QAE1B,OAAO,KAAK;MAEb;;MAEA;MACAtC,KAAK,CAACQ,KAAK,CAAEX,IAAK,CAAC;MACnBI,KAAK,CAACO,KAAK,CAAEV,IAAK,CAAC;MAEnB,IAAKD,IAAI,CAACe,GAAG,CAAEd,IAAK,CAAC,GAAG,CAAC,EAAG;QAE3B,IAAIyC,GAAG,GAAGtC,KAAK,CAACvB,KAAK;QACrBuB,KAAK,CAACvB,KAAK,GAAGuB,KAAK,CAACtB,GAAG;QACvBsB,KAAK,CAACtB,GAAG,GAAG4D,GAAG;MAEhB;;MAEA;MACA,MAAMC,EAAE,GAAGxC,KAAK,CAACtB,KAAK,CAACkC,GAAG,CAAEf,IAAK,CAAC;MAClC,MAAM4C,EAAE,GAAGzC,KAAK,CAACrB,GAAG,CAACiC,GAAG,CAAEf,IAAK,CAAC;MAChC,MAAM6C,EAAE,GAAGzC,KAAK,CAACvB,KAAK,CAACkC,GAAG,CAAEf,IAAK,CAAC;MAClC,MAAM8C,EAAE,GAAG1C,KAAK,CAACtB,GAAG,CAACiC,GAAG,CAAEf,IAAK,CAAC;MAChC,MAAM+C,UAAU,GAAGH,EAAE,GAAGC,EAAE;MAC1B,MAAMG,UAAU,GAAGL,EAAE,GAAGG,EAAE;MAE1B,IAAKH,EAAE,KAAKG,EAAE,IAAID,EAAE,KAAKD,EAAE,IAAIG,UAAU,KAAKC,UAAU,EAAG;QAE1D,OAAO,KAAK;MAEb;;MAEA;MACA,IAAK3B,MAAM,EAAG;QAEbnB,OAAO,CAACpC,UAAU,CAAEqC,KAAK,CAACtB,KAAK,EAAEuB,KAAK,CAACvB,KAAM,CAAC;QAC9C,IAAKqB,OAAO,CAACa,GAAG,CAAEf,IAAK,CAAC,GAAG,CAAC,EAAG;UAE9BqB,MAAM,CAACxC,KAAK,CAACO,IAAI,CAAEe,KAAK,CAACtB,KAAM,CAAC;QAEjC,CAAC,MAAM;UAENwC,MAAM,CAACxC,KAAK,CAACO,IAAI,CAAEgB,KAAK,CAACvB,KAAM,CAAC;QAEjC;QAEAqB,OAAO,CAACpC,UAAU,CAAEqC,KAAK,CAACrB,GAAG,EAAEsB,KAAK,CAACtB,GAAI,CAAC;QAC1C,IAAKoB,OAAO,CAACa,GAAG,CAAEf,IAAK,CAAC,GAAG,CAAC,EAAG;UAE9BqB,MAAM,CAACvC,GAAG,CAACM,IAAI,CAAEe,KAAK,CAACrB,GAAI,CAAC;QAE7B,CAAC,MAAM;UAENuC,MAAM,CAACvC,GAAG,CAACM,IAAI,CAAEgB,KAAK,CAACtB,GAAI,CAAC;QAE7B;MAED;MAEA,OAAO,IAAI;IAEZ;EAED,CAAC;AAEF,CAAC,CAAG,CAAC;AAGLxC,gBAAgB,CAAC8B,SAAS,CAACyC,eAAe,GAAK,YAAY;EAE1D,MAAMQ,MAAM,GAAG,IAAI3F,OAAO,CAAC,CAAC;EAC5B,OAAO,SAASmF,eAAeA,CAAEM,KAAK,EAAG;IAExC,IAAI,CAAC7B,mBAAmB,CAAE6B,KAAK,EAAEE,MAAO,CAAC;IACzC,OAAOF,KAAK,CAACD,UAAU,CAAEG,MAAO,CAAC;EAElC,CAAC;AAEF,CAAC,CAAG,CAAC;AAGL/E,gBAAgB,CAAC8B,SAAS,CAAC6E,kBAAkB,GAAK,YAAY;EAE7D,MAAM9B,KAAK,GAAG,IAAIzF,OAAO,CAAC,CAAC;EAC3B,MAAM6C,MAAM,GAAG,IAAI7C,OAAO,CAAC,CAAC;EAC5B,MAAMwH,YAAY,GAAG,CAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;EACtC,MAAMC,KAAK,GAAG,IAAIxH,KAAK,CAAC,CAAC;EACzB,MAAMyH,KAAK,GAAG,IAAIzH,KAAK,CAAC,CAAC;EAEzB,OAAO,SAASsH,kBAAkBA,CAAE7B,KAAK,EAAEzC,OAAO,GAAG,IAAI,EAAEC,OAAO,GAAG,IAAI,EAAG;IAE3E,MAAMyE,UAAU,GAAG1E,OAAO,IAAIC,OAAO,GAAGuE,KAAK,GAAG,IAAI;IACpD,IAAK,IAAI,CAAC3D,kBAAkB,CAAE4B,KAAK,EAAEiC,UAAW,CAAC,EAAG;MAEnD,IAAK1E,OAAO,IAAIC,OAAO,EAAG;QAEzB,IAAKD,OAAO,EAAG0E,UAAU,CAACC,SAAS,CAAE3E,OAAQ,CAAC;QAC9C,IAAKC,OAAO,EAAGyE,UAAU,CAACC,SAAS,CAAE1E,OAAQ,CAAC;MAE/C;MAEA,OAAO,CAAC;IAET;IAEA,IAAII,iBAAiB,GAAGC,QAAQ;;IAEhC;IACA,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B,IAAIqE,IAAI;MACR,MAAMC,KAAK,GAAGN,YAAY,CAAEhE,CAAC,CAAE;MAC/B,MAAMuE,QAAQ,GAAGrC,KAAK,CAAEoC,KAAK,CAAE;MAC/B,IAAI,CAAClE,mBAAmB,CAAEmE,QAAQ,EAAEtC,KAAM,CAAC;MAE3CoC,IAAI,GAAGE,QAAQ,CAACpE,iBAAiB,CAAE8B,KAAM,CAAC;MAE1C,IAAKoC,IAAI,GAAGvE,iBAAiB,EAAG;QAE/BA,iBAAiB,GAAGuE,IAAI;QACxB,IAAK5E,OAAO,EAAGA,OAAO,CAACS,IAAI,CAAE+B,KAAM,CAAC;QACpC,IAAKvC,OAAO,EAAGA,OAAO,CAACQ,IAAI,CAAEqE,QAAS,CAAC;MAExC;MAGA,MAAMC,OAAO,GAAG,IAAI,CAAEF,KAAK,CAAE;MAC7BpC,KAAK,CAAC9B,mBAAmB,CAAEoE,OAAO,EAAEvC,KAAM,CAAC;MAE3CoC,IAAI,GAAGG,OAAO,CAACrE,iBAAiB,CAAE8B,KAAM,CAAC;MAEzC,IAAKoC,IAAI,GAAGvE,iBAAiB,EAAG;QAE/BA,iBAAiB,GAAGuE,IAAI;QACxB,IAAK5E,OAAO,EAAGA,OAAO,CAACS,IAAI,CAAEsE,OAAQ,CAAC;QACtC,IAAK9E,OAAO,EAAGA,OAAO,CAACQ,IAAI,CAAE+B,KAAM,CAAC;MAErC;IAED;IAEA,KAAM,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B,MAAMyE,GAAG,GAAGT,YAAY,CAAEhE,CAAC,CAAE;MAC7B,MAAM0E,GAAG,GAAGV,YAAY,CAAE,CAAEhE,CAAC,GAAG,CAAC,IAAK,CAAC,CAAE;MACzCiE,KAAK,CAACb,GAAG,CAAE,IAAI,CAAEqB,GAAG,CAAE,EAAE,IAAI,CAAEC,GAAG,CAAG,CAAC;MACrC,KAAM,IAAI3B,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,EAAEA,EAAE,EAAG,EAAG;QAEjC,MAAM4B,GAAG,GAAGX,YAAY,CAAEjB,EAAE,CAAE;QAC9B,MAAM6B,GAAG,GAAGZ,YAAY,CAAE,CAAEjB,EAAE,GAAG,CAAC,IAAK,CAAC,CAAE;QAC1CmB,KAAK,CAACd,GAAG,CAAElB,KAAK,CAAEyC,GAAG,CAAE,EAAEzC,KAAK,CAAE0C,GAAG,CAAG,CAAC;QAEvC/H,6BAA6B,CAAEoH,KAAK,EAAEC,KAAK,EAAEjC,KAAK,EAAE5C,MAAO,CAAC;QAE5D,MAAMgF,IAAI,GAAGpC,KAAK,CAAC9B,iBAAiB,CAAEd,MAAO,CAAC;QAC9C,IAAKgF,IAAI,GAAGvE,iBAAiB,EAAG;UAE/BA,iBAAiB,GAAGuE,IAAI;UACxB,IAAK5E,OAAO,EAAGA,OAAO,CAACS,IAAI,CAAE+B,KAAM,CAAC;UACpC,IAAKvC,OAAO,EAAGA,OAAO,CAACQ,IAAI,CAAEb,MAAO,CAAC;QAEtC;MAED;IAED;IAEA,OAAOnC,IAAI,CAACmD,IAAI,CAAEP,iBAAkB,CAAC;EAEtC,CAAC;AAEF,CAAC,CAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}