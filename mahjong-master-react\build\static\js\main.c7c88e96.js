/*! For license information please see main.c7c88e96.js.LICENSE.txt */
(()=>{"use strict";var e={4:(e,t,n)=>{var r=n(853),i=n(43),a=n(950);function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function l(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function u(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function c(e){if(l(e)!==e)throw Error(o(188))}function d(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=d(e)))return t;e=e.sibling}return null}var f=Object.assign,h=Symbol.for("react.element"),p=Symbol.for("react.transitional.element"),m=Symbol.for("react.portal"),g=Symbol.for("react.fragment"),v=Symbol.for("react.strict_mode"),y=Symbol.for("react.profiler"),b=Symbol.for("react.provider"),w=Symbol.for("react.consumer"),x=Symbol.for("react.context"),S=Symbol.for("react.forward_ref"),k=Symbol.for("react.suspense"),E=Symbol.for("react.suspense_list"),P=Symbol.for("react.memo"),T=Symbol.for("react.lazy");Symbol.for("react.scope");var C=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var F=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var A=Symbol.iterator;function D(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=A&&e[A]||e["@@iterator"])?e:null}var M=Symbol.for("react.client.reference");function L(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===M?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case g:return"Fragment";case y:return"Profiler";case v:return"StrictMode";case k:return"Suspense";case E:return"SuspenseList";case C:return"Activity"}if("object"===typeof e)switch(e.$$typeof){case m:return"Portal";case x:return(e.displayName||"Context")+".Provider";case w:return(e._context.displayName||"Context")+".Consumer";case S:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case P:return null!==(t=e.displayName||null)?t:L(e.type)||"Memo";case T:t=e._payload,e=e._init;try{return L(e(t))}catch(n){}}return null}var N=Array.isArray,R=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,O=a.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,z={pending:!1,data:null,method:null,action:null},_=[],j=-1;function V(e){return{current:e}}function I(e){0>j||(e.current=_[j],_[j]=null,j--)}function B(e,t){j++,_[j]=e.current,e.current=t}var U=V(null),H=V(null),W=V(null),$=V(null);function q(e,t){switch(B(W,t),B(H,e),B(U,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?id(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=ad(t=id(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}I(U),B(U,e)}function Y(){I(U),I(H),I(W)}function K(e){null!==e.memoizedState&&B($,e);var t=U.current,n=ad(t,e.type);t!==n&&(B(H,e),B(U,n))}function Q(e){H.current===e&&(I(U),I(H)),$.current===e&&(I($),Kd._currentValue=z)}var X=Object.prototype.hasOwnProperty,G=r.unstable_scheduleCallback,Z=r.unstable_cancelCallback,J=r.unstable_shouldYield,ee=r.unstable_requestPaint,te=r.unstable_now,ne=r.unstable_getCurrentPriorityLevel,re=r.unstable_ImmediatePriority,ie=r.unstable_UserBlockingPriority,ae=r.unstable_NormalPriority,oe=r.unstable_LowPriority,se=r.unstable_IdlePriority,le=r.log,ue=r.unstable_setDisableYieldValue,ce=null,de=null;function fe(e){if("function"===typeof le&&ue(e),de&&"function"===typeof de.setStrictMode)try{de.setStrictMode(ce,e)}catch(t){}}var he=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(pe(e)/me|0)|0},pe=Math.log,me=Math.LN2;var ge=256,ve=4194304;function ye(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function be(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var i=0,a=e.suspendedLanes,o=e.pingedLanes;e=e.warmLanes;var s=134217727&r;return 0!==s?0!==(r=s&~a)?i=ye(r):0!==(o&=s)?i=ye(o):n||0!==(n=s&~e)&&(i=ye(n)):0!==(s=r&~a)?i=ye(s):0!==o?i=ye(o):n||0!==(n=r&~e)&&(i=ye(n)),0===i?0:0!==t&&t!==i&&0===(t&a)&&((a=i&-i)>=(n=t&-t)||32===a&&0!==(4194048&n))?t:i}function we(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function xe(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function Se(){var e=ge;return 0===(4194048&(ge<<=1))&&(ge=256),e}function ke(){var e=ve;return 0===(62914560&(ve<<=1))&&(ve=4194304),e}function Ee(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Pe(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Te(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-he(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Ce(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-he(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}function Fe(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Ae(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function De(){var e=O.p;return 0!==e?e:void 0===(e=window.event)?32:cf(e.type)}var Me=Math.random().toString(36).slice(2),Le="__reactFiber$"+Me,Ne="__reactProps$"+Me,Re="__reactContainer$"+Me,Oe="__reactEvents$"+Me,ze="__reactListeners$"+Me,_e="__reactHandles$"+Me,je="__reactResources$"+Me,Ve="__reactMarker$"+Me;function Ie(e){delete e[Le],delete e[Ne],delete e[Oe],delete e[ze],delete e[_e]}function Be(e){var t=e[Le];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Re]||n[Le]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=bd(e);null!==e;){if(n=e[Le])return n;e=bd(e)}return t}n=(e=n).parentNode}return null}function Ue(e){if(e=e[Le]||e[Re]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function He(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(o(33))}function We(e){var t=e[je];return t||(t=e[je]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function $e(e){e[Ve]=!0}var qe=new Set,Ye={};function Ke(e,t){Qe(e,t),Qe(e+"Capture",t)}function Qe(e,t){for(Ye[e]=t,e=0;e<t.length;e++)qe.add(t[e])}var Xe,Ge,Ze=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Je={},et={};function tt(e,t,n){if(i=t,X.call(et,i)||!X.call(Je,i)&&(Ze.test(i)?et[i]=!0:(Je[i]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var i}function nt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function rt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function it(e){if(void 0===Xe)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Xe=t&&t[1]||"",Ge=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Xe+e+Ge}var at=!1;function ot(e,t){if(!e||at)return"";at=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(i){var r=i}Reflect.construct(e,[],n)}else{try{n.call()}catch(a){r=a}e.call(n.prototype)}}else{try{throw Error()}catch(o){r=o}(n=e())&&"function"===typeof n.catch&&n.catch(function(){})}}catch(s){if(s&&r&&"string"===typeof s.stack)return[s.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var a=r.DetermineComponentFrameRoot(),o=a[0],s=a[1];if(o&&s){var l=o.split("\n"),u=s.split("\n");for(i=r=0;r<l.length&&!l[r].includes("DetermineComponentFrameRoot");)r++;for(;i<u.length&&!u[i].includes("DetermineComponentFrameRoot");)i++;if(r===l.length||i===u.length)for(r=l.length-1,i=u.length-1;1<=r&&0<=i&&l[r]!==u[i];)i--;for(;1<=r&&0<=i;r--,i--)if(l[r]!==u[i]){if(1!==r||1!==i)do{if(r--,0>--i||l[r]!==u[i]){var c="\n"+l[r].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}}while(1<=r&&0<=i);break}}}finally{at=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?it(n):""}function st(e){switch(e.tag){case 26:case 27:case 5:return it(e.type);case 16:return it("Lazy");case 13:return it("Suspense");case 19:return it("SuspenseList");case 0:case 15:return ot(e.type,!1);case 11:return ot(e.type.render,!1);case 1:return ot(e.type,!0);case 31:return it("Activity");default:return""}}function lt(e){try{var t="";do{t+=st(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function ut(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ct(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function dt(e){e._valueTracker||(e._valueTracker=function(e){var t=ct(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var i=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ft(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ct(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function ht(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var pt=/[\n"\\]/g;function mt(e){return e.replace(pt,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function gt(e,t,n,r,i,a,o,s){e.name="",null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o?e.type=o:e.removeAttribute("type"),null!=t?"number"===o?(0===t&&""===e.value||e.value!=t)&&(e.value=""+ut(t)):e.value!==""+ut(t)&&(e.value=""+ut(t)):"submit"!==o&&"reset"!==o||e.removeAttribute("value"),null!=t?yt(e,o,ut(t)):null!=n?yt(e,o,ut(n)):null!=r&&e.removeAttribute("value"),null==i&&null!=a&&(e.defaultChecked=!!a),null!=i&&(e.checked=i&&"function"!==typeof i&&"symbol"!==typeof i),null!=s&&"function"!==typeof s&&"symbol"!==typeof s&&"boolean"!==typeof s?e.name=""+ut(s):e.removeAttribute("name")}function vt(e,t,n,r,i,a,o,s){if(null!=a&&"function"!==typeof a&&"symbol"!==typeof a&&"boolean"!==typeof a&&(e.type=a),null!=t||null!=n){if(!("submit"!==a&&"reset"!==a||void 0!==t&&null!==t))return;n=null!=n?""+ut(n):"",t=null!=t?""+ut(t):n,s||t===e.value||(e.value=t),e.defaultValue=t}r="function"!==typeof(r=null!=r?r:i)&&"symbol"!==typeof r&&!!r,e.checked=s?e.checked:!!r,e.defaultChecked=!!r,null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o&&(e.name=o)}function yt(e,t,n){"number"===t&&ht(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function bt(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ut(n),t=null,i=0;i<e.length;i++){if(e[i].value===n)return e[i].selected=!0,void(r&&(e[i].defaultSelected=!0));null!==t||e[i].disabled||(t=e[i])}null!==t&&(t.selected=!0)}}function wt(e,t,n){null==t||((t=""+ut(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+ut(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function xt(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(o(92));if(N(r)){if(1<r.length)throw Error(o(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=ut(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function St(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var kt=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Et(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"===typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!==typeof n||0===n||kt.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Pt(e,t,n){if(null!=t&&"object"!==typeof t)throw Error(o(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var i in t)r=t[i],t.hasOwnProperty(i)&&n[i]!==r&&Et(e,i,r)}else for(var a in t)t.hasOwnProperty(a)&&Et(e,a,t[a])}function Tt(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ct=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ft=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function At(e){return Ft.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Dt=null;function Mt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Lt=null,Nt=null;function Rt(e){var t=Ue(e);if(t&&(e=t.stateNode)){var n=e[Ne]||null;e:switch(e=t.stateNode,t.type){case"input":if(gt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+mt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=r[Ne]||null;if(!i)throw Error(o(90));gt(r,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&ft(r)}break e;case"textarea":wt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&bt(e,!!n.multiple,t,!1)}}}var Ot=!1;function zt(e,t,n){if(Ot)return e(t,n);Ot=!0;try{return e(t)}finally{if(Ot=!1,(null!==Lt||null!==Nt)&&(Bu(),Lt&&(t=Lt,e=Nt,Nt=Lt=null,Rt(t),e)))for(t=0;t<e.length;t++)Rt(e[t])}}function _t(e,t){var n=e.stateNode;if(null===n)return null;var r=n[Ne]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(o(231,t,typeof n));return n}var jt=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),Vt=!1;if(jt)try{var It={};Object.defineProperty(It,"passive",{get:function(){Vt=!0}}),window.addEventListener("test",It,It),window.removeEventListener("test",It,It)}catch(Rf){Vt=!1}var Bt=null,Ut=null,Ht=null;function Wt(){if(Ht)return Ht;var e,t,n=Ut,r=n.length,i="value"in Bt?Bt.value:Bt.textContent,a=i.length;for(e=0;e<r&&n[e]===i[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===i[a-t];t++);return Ht=i.slice(e,1<t?1-t:void 0)}function $t(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function qt(){return!0}function Yt(){return!1}function Kt(e){function t(t,n,r,i,a){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=i,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(i):i[o]);return this.isDefaultPrevented=(null!=i.defaultPrevented?i.defaultPrevented:!1===i.returnValue)?qt:Yt,this.isPropagationStopped=Yt,this}return f(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=qt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=qt)},persist:function(){},isPersistent:qt}),t}var Qt,Xt,Gt,Zt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Jt=Kt(Zt),en=f({},Zt,{view:0,detail:0}),tn=Kt(en),nn=f({},en,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:pn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Gt&&(Gt&&"mousemove"===e.type?(Qt=e.screenX-Gt.screenX,Xt=e.screenY-Gt.screenY):Xt=Qt=0,Gt=e),Qt)},movementY:function(e){return"movementY"in e?e.movementY:Xt}}),rn=Kt(nn),an=Kt(f({},nn,{dataTransfer:0})),on=Kt(f({},en,{relatedTarget:0})),sn=Kt(f({},Zt,{animationName:0,elapsedTime:0,pseudoElement:0})),ln=Kt(f({},Zt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),un=Kt(f({},Zt,{data:0})),cn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},fn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function hn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=fn[e])&&!!t[e]}function pn(){return hn}var mn=Kt(f({},en,{key:function(e){if(e.key){var t=cn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=$t(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?dn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:pn,charCode:function(e){return"keypress"===e.type?$t(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?$t(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),gn=Kt(f({},nn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),vn=Kt(f({},en,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:pn})),yn=Kt(f({},Zt,{propertyName:0,elapsedTime:0,pseudoElement:0})),bn=Kt(f({},nn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),wn=Kt(f({},Zt,{newState:0,oldState:0})),xn=[9,13,27,32],Sn=jt&&"CompositionEvent"in window,kn=null;jt&&"documentMode"in document&&(kn=document.documentMode);var En=jt&&"TextEvent"in window&&!kn,Pn=jt&&(!Sn||kn&&8<kn&&11>=kn),Tn=String.fromCharCode(32),Cn=!1;function Fn(e,t){switch(e){case"keyup":return-1!==xn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function An(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Dn=!1;var Mn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ln(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Mn[e.type]:"textarea"===t}function Nn(e,t,n,r){Lt?Nt?Nt.push(r):Nt=[r]:Lt=r,0<(t=Wc(t,"onChange")).length&&(n=new Jt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Rn=null,On=null;function zn(e){zc(e,0)}function _n(e){if(ft(He(e)))return e}function jn(e,t){if("change"===e)return t}var Vn=!1;if(jt){var In;if(jt){var Bn="oninput"in document;if(!Bn){var Un=document.createElement("div");Un.setAttribute("oninput","return;"),Bn="function"===typeof Un.oninput}In=Bn}else In=!1;Vn=In&&(!document.documentMode||9<document.documentMode)}function Hn(){Rn&&(Rn.detachEvent("onpropertychange",Wn),On=Rn=null)}function Wn(e){if("value"===e.propertyName&&_n(On)){var t=[];Nn(t,On,e,Mt(e)),zt(zn,t)}}function $n(e,t,n){"focusin"===e?(Hn(),On=n,(Rn=t).attachEvent("onpropertychange",Wn)):"focusout"===e&&Hn()}function qn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return _n(On)}function Yn(e,t){if("click"===e)return _n(t)}function Kn(e,t){if("input"===e||"change"===e)return _n(t)}var Qn="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function Xn(e,t){if(Qn(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!X.call(t,i)||!Qn(e[i],t[i]))return!1}return!0}function Gn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Zn(e,t){var n,r=Gn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Gn(r)}}function Jn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Jn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function er(e){for(var t=ht((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=ht((e=t.contentWindow).document)}return t}function tr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var nr=jt&&"documentMode"in document&&11>=document.documentMode,rr=null,ir=null,ar=null,or=!1;function sr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;or||null==rr||rr!==ht(r)||("selectionStart"in(r=rr)&&tr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},ar&&Xn(ar,r)||(ar=r,0<(r=Wc(ir,"onSelect")).length&&(t=new Jt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rr)))}function lr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ur={animationend:lr("Animation","AnimationEnd"),animationiteration:lr("Animation","AnimationIteration"),animationstart:lr("Animation","AnimationStart"),transitionrun:lr("Transition","TransitionRun"),transitionstart:lr("Transition","TransitionStart"),transitioncancel:lr("Transition","TransitionCancel"),transitionend:lr("Transition","TransitionEnd")},cr={},dr={};function fr(e){if(cr[e])return cr[e];if(!ur[e])return e;var t,n=ur[e];for(t in n)if(n.hasOwnProperty(t)&&t in dr)return cr[e]=n[t];return e}jt&&(dr=document.createElement("div").style,"AnimationEvent"in window||(delete ur.animationend.animation,delete ur.animationiteration.animation,delete ur.animationstart.animation),"TransitionEvent"in window||delete ur.transitionend.transition);var hr=fr("animationend"),pr=fr("animationiteration"),mr=fr("animationstart"),gr=fr("transitionrun"),vr=fr("transitionstart"),yr=fr("transitioncancel"),br=fr("transitionend"),wr=new Map,xr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Sr(e,t){wr.set(e,t),Ke(t,[e])}xr.push("scrollEnd");var kr=new WeakMap;function Er(e,t){if("object"===typeof e&&null!==e){var n=kr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:lt(t)},kr.set(e,t),t)}return{value:e,source:t,stack:lt(t)}}var Pr=[],Tr=0,Cr=0;function Fr(){for(var e=Tr,t=Cr=Tr=0;t<e;){var n=Pr[t];Pr[t++]=null;var r=Pr[t];Pr[t++]=null;var i=Pr[t];Pr[t++]=null;var a=Pr[t];if(Pr[t++]=null,null!==r&&null!==i){var o=r.pending;null===o?i.next=i:(i.next=o.next,o.next=i),r.pending=i}0!==a&&Lr(n,i,a)}}function Ar(e,t,n,r){Pr[Tr++]=e,Pr[Tr++]=t,Pr[Tr++]=n,Pr[Tr++]=r,Cr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Dr(e,t,n,r){return Ar(e,t,n,r),Nr(e)}function Mr(e,t){return Ar(e,null,null,t),Nr(e)}function Lr(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var i=!1,a=e.return;null!==a;)a.childLanes|=n,null!==(r=a.alternate)&&(r.childLanes|=n),22===a.tag&&(null===(e=a.stateNode)||1&e._visibility||(i=!0)),e=a,a=a.return;return 3===e.tag?(a=e.stateNode,i&&null!==t&&(i=31-he(n),null===(r=(e=a.hiddenUpdates)[i])?e[i]=[t]:r.push(t),t.lane=536870912|n),a):null}function Nr(e){if(50<Lu)throw Lu=0,Nu=null,Error(o(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Rr={};function Or(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function zr(e,t,n,r){return new Or(e,t,n,r)}function _r(e){return!(!(e=e.prototype)||!e.isReactComponent)}function jr(e,t){var n=e.alternate;return null===n?((n=zr(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Vr(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ir(e,t,n,r,i,a){var s=0;if(r=e,"function"===typeof e)_r(e)&&(s=1);else if("string"===typeof e)s=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof t.precedence||"string"!==typeof t.href||""===t.href)break;return!0;case"link":if("string"!==typeof t.rel||"string"!==typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"===typeof t.precedence&&null==e);case"script":if(t.async&&"function"!==typeof t.async&&"symbol"!==typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"===typeof t.src)return!0}return!1}(e,n,U.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case C:return(e=zr(31,n,t,i)).elementType=C,e.lanes=a,e;case g:return Br(n.children,i,a,t);case v:s=8,i|=24;break;case y:return(e=zr(12,n,t,2|i)).elementType=y,e.lanes=a,e;case k:return(e=zr(13,n,t,i)).elementType=k,e.lanes=a,e;case E:return(e=zr(19,n,t,i)).elementType=E,e.lanes=a,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case b:case x:s=10;break e;case w:s=9;break e;case S:s=11;break e;case P:s=14;break e;case T:s=16,r=null;break e}s=29,n=Error(o(130,null===e?"null":typeof e,"")),r=null}return(t=zr(s,n,t,i)).elementType=e,t.type=r,t.lanes=a,t}function Br(e,t,n,r){return(e=zr(7,e,r,t)).lanes=n,e}function Ur(e,t,n){return(e=zr(6,e,null,t)).lanes=n,e}function Hr(e,t,n){return(t=zr(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Wr=[],$r=0,qr=null,Yr=0,Kr=[],Qr=0,Xr=null,Gr=1,Zr="";function Jr(e,t){Wr[$r++]=Yr,Wr[$r++]=qr,qr=e,Yr=t}function ei(e,t,n){Kr[Qr++]=Gr,Kr[Qr++]=Zr,Kr[Qr++]=Xr,Xr=e;var r=Gr;e=Zr;var i=32-he(r)-1;r&=~(1<<i),n+=1;var a=32-he(t)+i;if(30<a){var o=i-i%5;a=(r&(1<<o)-1).toString(32),r>>=o,i-=o,Gr=1<<32-he(t)+i|n<<i|r,Zr=a+e}else Gr=1<<a|n<<i|r,Zr=e}function ti(e){null!==e.return&&(Jr(e,1),ei(e,1,0))}function ni(e){for(;e===qr;)qr=Wr[--$r],Wr[$r]=null,Yr=Wr[--$r],Wr[$r]=null;for(;e===Xr;)Xr=Kr[--Qr],Kr[Qr]=null,Zr=Kr[--Qr],Kr[Qr]=null,Gr=Kr[--Qr],Kr[Qr]=null}var ri=null,ii=null,ai=!1,oi=null,si=!1,li=Error(o(519));function ui(e){throw mi(Er(Error(o(418,"")),e)),li}function ci(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Le]=e,t[Ne]=r,n){case"dialog":_c("cancel",t),_c("close",t);break;case"iframe":case"object":case"embed":_c("load",t);break;case"video":case"audio":for(n=0;n<Rc.length;n++)_c(Rc[n],t);break;case"source":_c("error",t);break;case"img":case"image":case"link":_c("error",t),_c("load",t);break;case"details":_c("toggle",t);break;case"input":_c("invalid",t),vt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),dt(t);break;case"select":_c("invalid",t);break;case"textarea":_c("invalid",t),xt(t,r.value,r.defaultValue,r.children),dt(t)}"string"!==typeof(n=r.children)&&"number"!==typeof n&&"bigint"!==typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Xc(t.textContent,n)?(null!=r.popover&&(_c("beforetoggle",t),_c("toggle",t)),null!=r.onScroll&&_c("scroll",t),null!=r.onScrollEnd&&_c("scrollend",t),null!=r.onClick&&(t.onclick=Gc),t=!0):t=!1,t||ui(e)}function di(e){for(ri=e.return;ri;)switch(ri.tag){case 5:case 13:return void(si=!1);case 27:case 3:return void(si=!0);default:ri=ri.return}}function fi(e){if(e!==ri)return!1;if(!ai)return di(e),ai=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||od(e.type,e.memoizedProps)),t=!t),t&&ii&&ui(e),di(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){ii=vd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}ii=null}}else 27===n?(n=ii,hd(e.type)?(e=yd,yd=null,ii=e):ii=n):ii=ri?vd(e.stateNode.nextSibling):null;return!0}function hi(){ii=ri=null,ai=!1}function pi(){var e=oi;return null!==e&&(null===bu?bu=e:bu.push.apply(bu,e),oi=null),e}function mi(e){null===oi?oi=[e]:oi.push(e)}var gi=V(null),vi=null,yi=null;function bi(e,t,n){B(gi,t._currentValue),t._currentValue=n}function wi(e){e._currentValue=gi.current,I(gi)}function xi(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Si(e,t,n,r){var i=e.child;for(null!==i&&(i.return=e);null!==i;){var a=i.dependencies;if(null!==a){var s=i.child;a=a.firstContext;e:for(;null!==a;){var l=a;a=i;for(var u=0;u<t.length;u++)if(l.context===t[u]){a.lanes|=n,null!==(l=a.alternate)&&(l.lanes|=n),xi(a.return,n,e),r||(s=null);break e}a=l.next}}else if(18===i.tag){if(null===(s=i.return))throw Error(o(341));s.lanes|=n,null!==(a=s.alternate)&&(a.lanes|=n),xi(s,n,e),s=null}else s=i.child;if(null!==s)s.return=i;else for(s=i;null!==s;){if(s===e){s=null;break}if(null!==(i=s.sibling)){i.return=s.return,s=i;break}s=s.return}i=s}}function ki(e,t,n,r){e=null;for(var i=t,a=!1;null!==i;){if(!a)if(0!==(524288&i.flags))a=!0;else if(0!==(262144&i.flags))break;if(10===i.tag){var s=i.alternate;if(null===s)throw Error(o(387));if(null!==(s=s.memoizedProps)){var l=i.type;Qn(i.pendingProps.value,s.value)||(null!==e?e.push(l):e=[l])}}else if(i===$.current){if(null===(s=i.alternate))throw Error(o(387));s.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(null!==e?e.push(Kd):e=[Kd])}i=i.return}null!==e&&Si(t,e,n,r),t.flags|=262144}function Ei(e){for(e=e.firstContext;null!==e;){if(!Qn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Pi(e){vi=e,yi=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Ti(e){return Fi(vi,e)}function Ci(e,t){return null===vi&&Pi(e),Fi(e,t)}function Fi(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===yi){if(null===e)throw Error(o(308));yi=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else yi=yi.next=t;return n}var Ai="undefined"!==typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(e){return e()})}},Di=r.unstable_scheduleCallback,Mi=r.unstable_NormalPriority,Li={$$typeof:x,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ni(){return{controller:new Ai,data:new Map,refCount:0}}function Ri(e){e.refCount--,0===e.refCount&&Di(Mi,function(){e.controller.abort()})}var Oi=null,zi=0,_i=0,ji=null;function Vi(){if(0===--zi&&null!==Oi){null!==ji&&(ji.status="fulfilled");var e=Oi;Oi=null,_i=0,ji=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Ii=R.S;R.S=function(e,t){"object"===typeof t&&null!==t&&"function"===typeof t.then&&function(e,t){if(null===Oi){var n=Oi=[];zi=0,_i=Ac(),ji={status:"pending",value:void 0,then:function(e){n.push(e)}}}zi++,t.then(Vi,Vi)}(0,t),null!==Ii&&Ii(e,t)};var Bi=V(null);function Ui(){var e=Bi.current;return null!==e?e:ru.pooledCache}function Hi(e,t){B(Bi,null===t?Bi.current:t.pool)}function Wi(){var e=Ui();return null===e?null:{parent:Li._currentValue,pool:e}}var $i=Error(o(460)),qi=Error(o(474)),Yi=Error(o(542)),Ki={then:function(){}};function Qi(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Xi(){}function Gi(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Xi,Xi),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw ea(e=t.reason),e;default:if("string"===typeof t.status)t.then(Xi,Xi);else{if(null!==(e=ru)&&100<e.shellSuspendCounter)throw Error(o(482));(e=t).status="pending",e.then(function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}},function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw ea(e=t.reason),e}throw Zi=t,$i}}var Zi=null;function Ji(){if(null===Zi)throw Error(o(459));var e=Zi;return Zi=null,e}function ea(e){if(e===$i||e===Yi)throw Error(o(483))}var ta=!1;function na(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ra(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ia(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function aa(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&nu)){var i=r.pending;return null===i?t.next=t:(t.next=i.next,i.next=t),r.pending=t,t=Nr(e),Lr(e,null,n),t}return Ar(e,r,t,n),Nr(e)}function oa(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194048&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Ce(e,n)}}function sa(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var i=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var o={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===a?i=a=o:a=a.next=o,n=n.next}while(null!==n);null===a?i=a=t:a=a.next=t}else i=a=t;return n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:a,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var la=!1;function ua(){if(la){if(null!==ji)throw ji}}function ca(e,t,n,r){la=!1;var i=e.updateQueue;ta=!1;var a=i.firstBaseUpdate,o=i.lastBaseUpdate,s=i.shared.pending;if(null!==s){i.shared.pending=null;var l=s,u=l.next;l.next=null,null===o?a=u:o.next=u,o=l;var c=e.alternate;null!==c&&((s=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===s?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(null!==a){var d=i.baseState;for(o=0,c=u=l=null,s=a;;){var h=-536870913&s.lane,p=h!==s.lane;if(p?(au&h)===h:(r&h)===h){0!==h&&h===_i&&(la=!0),null!==c&&(c=c.next={lane:0,tag:s.tag,payload:s.payload,callback:null,next:null});e:{var m=e,g=s;h=t;var v=n;switch(g.tag){case 1:if("function"===typeof(m=g.payload)){d=m.call(v,d,h);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(h="function"===typeof(m=g.payload)?m.call(v,d,h):m)||void 0===h)break e;d=f({},d,h);break e;case 2:ta=!0}}null!==(h=s.callback)&&(e.flags|=64,p&&(e.flags|=8192),null===(p=i.callbacks)?i.callbacks=[h]:p.push(h))}else p={lane:h,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===c?(u=c=p,l=d):c=c.next=p,o|=h;if(null===(s=s.next)){if(null===(s=i.shared.pending))break;s=(p=s).next,p.next=null,i.lastBaseUpdate=p,i.shared.pending=null}}null===c&&(l=d),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,null===a&&(i.shared.lanes=0),hu|=o,e.lanes=o,e.memoizedState=d}}function da(e,t){if("function"!==typeof e)throw Error(o(191,e));e.call(t)}function fa(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)da(n[e],t)}var ha=V(null),pa=V(0);function ma(e,t){B(pa,e=du),B(ha,t),du=e|t.baseLanes}function ga(){B(pa,du),B(ha,ha.current)}function va(){du=pa.current,I(ha),I(pa)}var ya=0,ba=null,wa=null,xa=null,Sa=!1,ka=!1,Ea=!1,Pa=0,Ta=0,Ca=null,Fa=0;function Aa(){throw Error(o(321))}function Da(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Qn(e[n],t[n]))return!1;return!0}function Ma(e,t,n,r,i,a){return ya=a,ba=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,R.H=null===e||null===e.memoizedState?qo:Yo,Ea=!1,a=n(r,i),Ea=!1,ka&&(a=Na(t,n,r,i)),La(e),a}function La(e){R.H=$o;var t=null!==wa&&null!==wa.next;if(ya=0,xa=wa=ba=null,Sa=!1,Ta=0,Ca=null,t)throw Error(o(300));null===e||Cs||null!==(e=e.dependencies)&&Ei(e)&&(Cs=!0)}function Na(e,t,n,r){ba=e;var i=0;do{if(ka&&(Ca=null),Ta=0,ka=!1,25<=i)throw Error(o(301));if(i+=1,xa=wa=null,null!=e.updateQueue){var a=e.updateQueue;a.lastEffect=null,a.events=null,a.stores=null,null!=a.memoCache&&(a.memoCache.index=0)}R.H=Ko,a=t(n,r)}while(ka);return a}function Ra(){var e=R.H,t=e.useState()[0];return t="function"===typeof t.then?Ia(t):t,e=e.useState()[0],(null!==wa?wa.memoizedState:null)!==e&&(ba.flags|=1024),t}function Oa(){var e=0!==Pa;return Pa=0,e}function za(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function _a(e){if(Sa){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}Sa=!1}ya=0,xa=wa=ba=null,ka=!1,Ta=Pa=0,Ca=null}function ja(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===xa?ba.memoizedState=xa=e:xa=xa.next=e,xa}function Va(){if(null===wa){var e=ba.alternate;e=null!==e?e.memoizedState:null}else e=wa.next;var t=null===xa?ba.memoizedState:xa.next;if(null!==t)xa=t,wa=e;else{if(null===e){if(null===ba.alternate)throw Error(o(467));throw Error(o(310))}e={memoizedState:(wa=e).memoizedState,baseState:wa.baseState,baseQueue:wa.baseQueue,queue:wa.queue,next:null},null===xa?ba.memoizedState=xa=e:xa=xa.next=e}return xa}function Ia(e){var t=Ta;return Ta+=1,null===Ca&&(Ca=[]),e=Gi(Ca,e,t),t=ba,null===(null===xa?t.memoizedState:xa.next)&&(t=t.alternate,R.H=null===t||null===t.memoizedState?qo:Yo),e}function Ba(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return Ia(e);if(e.$$typeof===x)return Ti(e)}throw Error(o(438,String(e)))}function Ua(e){var t=null,n=ba.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=ba.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map(function(e){return e.slice()}),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},ba.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=F;return t.index++,n}function Ha(e,t){return"function"===typeof t?t(e):t}function Wa(e){return $a(Va(),wa,e)}function $a(e,t,n){var r=e.queue;if(null===r)throw Error(o(311));r.lastRenderedReducer=n;var i=e.baseQueue,a=r.pending;if(null!==a){if(null!==i){var s=i.next;i.next=a.next,a.next=s}t.baseQueue=i=a,r.pending=null}if(a=e.baseState,null===i)e.memoizedState=a;else{var l=s=null,u=null,c=t=i.next,d=!1;do{var f=-536870913&c.lane;if(f!==c.lane?(au&f)===f:(ya&f)===f){var h=c.revertLane;if(0===h)null!==u&&(u=u.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),f===_i&&(d=!0);else{if((ya&h)===h){c=c.next,h===_i&&(d=!0);continue}f={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(l=u=f,s=a):u=u.next=f,ba.lanes|=h,hu|=h}f=c.action,Ea&&n(a,f),a=c.hasEagerState?c.eagerState:n(a,f)}else h={lane:f,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(l=u=h,s=a):u=u.next=h,ba.lanes|=f,hu|=f;c=c.next}while(null!==c&&c!==t);if(null===u?s=a:u.next=l,!Qn(a,e.memoizedState)&&(Cs=!0,d&&null!==(n=ji)))throw n;e.memoizedState=a,e.baseState=s,e.baseQueue=u,r.lastRenderedState=a}return null===i&&(r.lanes=0),[e.memoizedState,r.dispatch]}function qa(e){var t=Va(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,a=t.memoizedState;if(null!==i){n.pending=null;var s=i=i.next;do{a=e(a,s.action),s=s.next}while(s!==i);Qn(a,t.memoizedState)||(Cs=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function Ya(e,t,n){var r=ba,i=Va(),a=ai;if(a){if(void 0===n)throw Error(o(407));n=n()}else n=t();var s=!Qn((wa||i).memoizedState,n);if(s&&(i.memoizedState=n,Cs=!0),i=i.queue,vo(2048,8,Xa.bind(null,r,i,e),[e]),i.getSnapshot!==t||s||null!==xa&&1&xa.memoizedState.tag){if(r.flags|=2048,po(9,{destroy:void 0,resource:void 0},Qa.bind(null,r,i,n,t),null),null===ru)throw Error(o(349));a||0!==(124&ya)||Ka(r,t,n)}return n}function Ka(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=ba.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},ba.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Qa(e,t,n,r){t.value=n,t.getSnapshot=r,Ga(t)&&Za(e)}function Xa(e,t,n){return n(function(){Ga(t)&&Za(e)})}function Ga(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Qn(e,n)}catch(r){return!0}}function Za(e){var t=Mr(e,2);null!==t&&zu(t,e,2)}function Ja(e){var t=ja();if("function"===typeof e){var n=e;if(e=n(),Ea){fe(!0);try{n()}finally{fe(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ha,lastRenderedState:e},t}function eo(e,t,n,r){return e.baseState=n,$a(e,wa,"function"===typeof r?r:Ha)}function to(e,t,n,r,i){if(Uo(e))throw Error(o(485));if(null!==(e=t.action)){var a={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){a.listeners.push(e)}};null!==R.T?n(!0):a.isTransition=!1,r(a),null===(n=t.pending)?(a.next=t.pending=a,no(t,a)):(a.next=n.next,t.pending=n.next=a)}}function no(e,t){var n=t.action,r=t.payload,i=e.state;if(t.isTransition){var a=R.T,o={};R.T=o;try{var s=n(i,r),l=R.S;null!==l&&l(o,s),ro(e,t,s)}catch(u){ao(e,t,u)}finally{R.T=a}}else try{ro(e,t,a=n(i,r))}catch(c){ao(e,t,c)}}function ro(e,t,n){null!==n&&"object"===typeof n&&"function"===typeof n.then?n.then(function(n){io(e,t,n)},function(n){return ao(e,t,n)}):io(e,t,n)}function io(e,t,n){t.status="fulfilled",t.value=n,oo(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,no(e,n)))}function ao(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,oo(t),t=t.next}while(t!==r)}e.action=null}function oo(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function so(e,t){return t}function lo(e,t){if(ai){var n=ru.formState;if(null!==n){e:{var r=ba;if(ai){if(ii){t:{for(var i=ii,a=si;8!==i.nodeType;){if(!a){i=null;break t}if(null===(i=vd(i.nextSibling))){i=null;break t}}i="F!"===(a=i.data)||"F"===a?i:null}if(i){ii=vd(i.nextSibling),r="F!"===i.data;break e}}ui(r)}r=!1}r&&(t=n[0])}}return(n=ja()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:so,lastRenderedState:t},n.queue=r,n=Vo.bind(null,ba,r),r.dispatch=n,r=Ja(!1),a=Bo.bind(null,ba,!1,r.queue),i={state:t,dispatch:null,action:e,pending:null},(r=ja()).queue=i,n=to.bind(null,ba,i,a,n),i.dispatch=n,r.memoizedState=e,[t,n,!1]}function uo(e){return co(Va(),wa,e)}function co(e,t,n){if(t=$a(e,t,so)[0],e=Wa(Ha)[0],"object"===typeof t&&null!==t&&"function"===typeof t.then)try{var r=Ia(t)}catch(o){if(o===$i)throw Yi;throw o}else r=t;var i=(t=Va()).queue,a=i.dispatch;return n!==t.memoizedState&&(ba.flags|=2048,po(9,{destroy:void 0,resource:void 0},fo.bind(null,i,n),null)),[r,a,e]}function fo(e,t){e.action=t}function ho(e){var t=Va(),n=wa;if(null!==n)return co(t,n,e);Va(),t=t.memoizedState;var r=(n=Va()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function po(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=ba.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},ba.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function mo(){return Va().memoizedState}function go(e,t,n,r){var i=ja();r=void 0===r?null:r,ba.flags|=e,i.memoizedState=po(1|t,{destroy:void 0,resource:void 0},n,r)}function vo(e,t,n,r){var i=Va();r=void 0===r?null:r;var a=i.memoizedState.inst;null!==wa&&null!==r&&Da(r,wa.memoizedState.deps)?i.memoizedState=po(t,a,n,r):(ba.flags|=e,i.memoizedState=po(1|t,a,n,r))}function yo(e,t){go(8390656,8,e,t)}function bo(e,t){vo(2048,8,e,t)}function wo(e,t){return vo(4,2,e,t)}function xo(e,t){return vo(4,4,e,t)}function So(e,t){if("function"===typeof t){e=e();var n=t(e);return function(){"function"===typeof n?n():t(null)}}if(null!==t&&void 0!==t)return e=e(),t.current=e,function(){t.current=null}}function ko(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,vo(4,4,So.bind(null,t,e),n)}function Eo(){}function Po(e,t){var n=Va();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&Da(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function To(e,t){var n=Va();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&Da(t,r[1]))return r[0];if(r=e(),Ea){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r}function Co(e,t,n){return void 0===n||0!==(1073741824&ya)?e.memoizedState=t:(e.memoizedState=n,e=Ou(),ba.lanes|=e,hu|=e,n)}function Fo(e,t,n,r){return Qn(n,t)?n:null!==ha.current?(e=Co(e,n,r),Qn(e,t)||(Cs=!0),e):0===(42&ya)?(Cs=!0,e.memoizedState=n):(e=Ou(),ba.lanes|=e,hu|=e,t)}function Ao(e,t,n,r,i){var a=O.p;O.p=0!==a&&8>a?a:8;var o=R.T,s={};R.T=s,Bo(e,!1,t,n);try{var l=i(),u=R.S;if(null!==u&&u(s,l),null!==l&&"object"===typeof l&&"function"===typeof l.then)Io(e,t,function(e,t){var n=[],r={status:"pending",value:null,reason:null,then:function(e){n.push(e)}};return e.then(function(){r.status="fulfilled",r.value=t;for(var e=0;e<n.length;e++)(0,n[e])(t)},function(e){for(r.status="rejected",r.reason=e,e=0;e<n.length;e++)(0,n[e])(void 0)}),r}(l,r),Ru());else Io(e,t,r,Ru())}catch(c){Io(e,t,{then:function(){},status:"rejected",reason:c},Ru())}finally{O.p=a,R.T=o}}function Do(){}function Mo(e,t,n,r){if(5!==e.tag)throw Error(o(476));var i=Lo(e).queue;Ao(e,i,t,z,null===n?Do:function(){return No(e),n(r)})}function Lo(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:z,baseState:z,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ha,lastRenderedState:z},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ha,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function No(e){Io(e,Lo(e).next.queue,{},Ru())}function Ro(){return Ti(Kd)}function Oo(){return Va().memoizedState}function zo(){return Va().memoizedState}function _o(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Ru(),r=aa(t,e=ia(n),n);return null!==r&&(zu(r,t,n),oa(r,t,n)),t={cache:Ni()},void(e.payload=t)}t=t.return}}function jo(e,t,n){var r=Ru();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Uo(e)?Ho(t,n):null!==(n=Dr(e,t,n,r))&&(zu(n,e,r),Wo(n,t,r))}function Vo(e,t,n){Io(e,t,n,Ru())}function Io(e,t,n,r){var i={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Uo(e))Ho(t,i);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var o=t.lastRenderedState,s=a(o,n);if(i.hasEagerState=!0,i.eagerState=s,Qn(s,o))return Ar(e,t,i,0),null===ru&&Fr(),!1}catch(l){}if(null!==(n=Dr(e,t,i,r)))return zu(n,e,r),Wo(n,t,r),!0}return!1}function Bo(e,t,n,r){if(r={lane:2,revertLane:Ac(),action:r,hasEagerState:!1,eagerState:null,next:null},Uo(e)){if(t)throw Error(o(479))}else null!==(t=Dr(e,n,r,2))&&zu(t,e,2)}function Uo(e){var t=e.alternate;return e===ba||null!==t&&t===ba}function Ho(e,t){ka=Sa=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Wo(e,t,n){if(0!==(4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Ce(e,n)}}var $o={readContext:Ti,use:Ba,useCallback:Aa,useContext:Aa,useEffect:Aa,useImperativeHandle:Aa,useLayoutEffect:Aa,useInsertionEffect:Aa,useMemo:Aa,useReducer:Aa,useRef:Aa,useState:Aa,useDebugValue:Aa,useDeferredValue:Aa,useTransition:Aa,useSyncExternalStore:Aa,useId:Aa,useHostTransitionStatus:Aa,useFormState:Aa,useActionState:Aa,useOptimistic:Aa,useMemoCache:Aa,useCacheRefresh:Aa},qo={readContext:Ti,use:Ba,useCallback:function(e,t){return ja().memoizedState=[e,void 0===t?null:t],e},useContext:Ti,useEffect:yo,useImperativeHandle:function(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,go(4194308,4,So.bind(null,t,e),n)},useLayoutEffect:function(e,t){return go(4194308,4,e,t)},useInsertionEffect:function(e,t){go(4,2,e,t)},useMemo:function(e,t){var n=ja();t=void 0===t?null:t;var r=e();if(Ea){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=ja();if(void 0!==n){var i=n(t);if(Ea){fe(!0);try{n(t)}finally{fe(!1)}}}else i=t;return r.memoizedState=r.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},r.queue=e,e=e.dispatch=jo.bind(null,ba,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},ja().memoizedState=e},useState:function(e){var t=(e=Ja(e)).queue,n=Vo.bind(null,ba,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Eo,useDeferredValue:function(e,t){return Co(ja(),e,t)},useTransition:function(){var e=Ja(!1);return e=Ao.bind(null,ba,e.queue,!0,!1),ja().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=ba,i=ja();if(ai){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===ru)throw Error(o(349));0!==(124&au)||Ka(r,t,n)}i.memoizedState=n;var a={value:n,getSnapshot:t};return i.queue=a,yo(Xa.bind(null,r,a,e),[e]),r.flags|=2048,po(9,{destroy:void 0,resource:void 0},Qa.bind(null,r,a,n,t),null),n},useId:function(){var e=ja(),t=ru.identifierPrefix;if(ai){var n=Zr;t="\xab"+t+"R"+(n=(Gr&~(1<<32-he(Gr)-1)).toString(32)+n),0<(n=Pa++)&&(t+="H"+n.toString(32)),t+="\xbb"}else t="\xab"+t+"r"+(n=Fa++).toString(32)+"\xbb";return e.memoizedState=t},useHostTransitionStatus:Ro,useFormState:lo,useActionState:lo,useOptimistic:function(e){var t=ja();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Bo.bind(null,ba,!0,n),n.dispatch=t,[e,t]},useMemoCache:Ua,useCacheRefresh:function(){return ja().memoizedState=_o.bind(null,ba)}},Yo={readContext:Ti,use:Ba,useCallback:Po,useContext:Ti,useEffect:bo,useImperativeHandle:ko,useInsertionEffect:wo,useLayoutEffect:xo,useMemo:To,useReducer:Wa,useRef:mo,useState:function(){return Wa(Ha)},useDebugValue:Eo,useDeferredValue:function(e,t){return Fo(Va(),wa.memoizedState,e,t)},useTransition:function(){var e=Wa(Ha)[0],t=Va().memoizedState;return["boolean"===typeof e?e:Ia(e),t]},useSyncExternalStore:Ya,useId:Oo,useHostTransitionStatus:Ro,useFormState:uo,useActionState:uo,useOptimistic:function(e,t){return eo(Va(),0,e,t)},useMemoCache:Ua,useCacheRefresh:zo},Ko={readContext:Ti,use:Ba,useCallback:Po,useContext:Ti,useEffect:bo,useImperativeHandle:ko,useInsertionEffect:wo,useLayoutEffect:xo,useMemo:To,useReducer:qa,useRef:mo,useState:function(){return qa(Ha)},useDebugValue:Eo,useDeferredValue:function(e,t){var n=Va();return null===wa?Co(n,e,t):Fo(n,wa.memoizedState,e,t)},useTransition:function(){var e=qa(Ha)[0],t=Va().memoizedState;return["boolean"===typeof e?e:Ia(e),t]},useSyncExternalStore:Ya,useId:Oo,useHostTransitionStatus:Ro,useFormState:ho,useActionState:ho,useOptimistic:function(e,t){var n=Va();return null!==wa?eo(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Ua,useCacheRefresh:zo},Qo=null,Xo=0;function Go(e){var t=Xo;return Xo+=1,null===Qo&&(Qo=[]),Gi(Qo,e,t)}function Zo(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Jo(e,t){if(t.$$typeof===h)throw Error(o(525));throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function es(e){return(0,e._init)(e._payload)}function ts(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function i(e,t){return(e=jr(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Ur(n,e.mode,r)).return=e,t):((t=i(t,n)).return=e,t)}function u(e,t,n,r){var a=n.type;return a===g?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"===typeof a&&null!==a&&a.$$typeof===T&&es(a)===t.type)?(Zo(t=i(t,n.props),n),t.return=e,t):(Zo(t=Ir(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Hr(n,e.mode,r)).return=e,t):((t=i(t,n.children||[])).return=e,t)}function d(e,t,n,r,a){return null===t||7!==t.tag?((t=Br(n,e.mode,r,a)).return=e,t):((t=i(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return(t=Ur(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case p:return Zo(n=Ir(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case m:return(t=Hr(t,e.mode,n)).return=e,t;case T:return f(e,t=(0,t._init)(t._payload),n)}if(N(t)||D(t))return(t=Br(t,e.mode,n,null)).return=e,t;if("function"===typeof t.then)return f(e,Go(t),n);if(t.$$typeof===x)return f(e,Ci(e,t),n);Jo(e,t)}return null}function h(e,t,n,r){var i=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return null!==i?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case p:return n.key===i?u(e,t,n,r):null;case m:return n.key===i?c(e,t,n,r):null;case T:return h(e,t,n=(i=n._init)(n._payload),r)}if(N(n)||D(n))return null!==i?null:d(e,t,n,r,null);if("function"===typeof n.then)return h(e,t,Go(n),r);if(n.$$typeof===x)return h(e,t,Ci(e,n),r);Jo(e,n)}return null}function v(e,t,n,r,i){if("string"===typeof r&&""!==r||"number"===typeof r||"bigint"===typeof r)return l(t,e=e.get(n)||null,""+r,i);if("object"===typeof r&&null!==r){switch(r.$$typeof){case p:return u(t,e=e.get(null===r.key?n:r.key)||null,r,i);case m:return c(t,e=e.get(null===r.key?n:r.key)||null,r,i);case T:return v(e,t,n,r=(0,r._init)(r._payload),i)}if(N(r)||D(r))return d(t,e=e.get(n)||null,r,i,null);if("function"===typeof r.then)return v(e,t,n,Go(r),i);if(r.$$typeof===x)return v(e,t,n,Ci(t,r),i);Jo(t,r)}return null}function y(l,u,c,d){if("object"===typeof c&&null!==c&&c.type===g&&null===c.key&&(c=c.props.children),"object"===typeof c&&null!==c){switch(c.$$typeof){case p:e:{for(var b=c.key;null!==u;){if(u.key===b){if((b=c.type)===g){if(7===u.tag){n(l,u.sibling),(d=i(u,c.props.children)).return=l,l=d;break e}}else if(u.elementType===b||"object"===typeof b&&null!==b&&b.$$typeof===T&&es(b)===u.type){n(l,u.sibling),Zo(d=i(u,c.props),c),d.return=l,l=d;break e}n(l,u);break}t(l,u),u=u.sibling}c.type===g?((d=Br(c.props.children,l.mode,d,c.key)).return=l,l=d):(Zo(d=Ir(c.type,c.key,c.props,null,l.mode,d),c),d.return=l,l=d)}return s(l);case m:e:{for(b=c.key;null!==u;){if(u.key===b){if(4===u.tag&&u.stateNode.containerInfo===c.containerInfo&&u.stateNode.implementation===c.implementation){n(l,u.sibling),(d=i(u,c.children||[])).return=l,l=d;break e}n(l,u);break}t(l,u),u=u.sibling}(d=Hr(c,l.mode,d)).return=l,l=d}return s(l);case T:return y(l,u,c=(b=c._init)(c._payload),d)}if(N(c))return function(i,o,s,l){for(var u=null,c=null,d=o,p=o=0,m=null;null!==d&&p<s.length;p++){d.index>p?(m=d,d=null):m=d.sibling;var g=h(i,d,s[p],l);if(null===g){null===d&&(d=m);break}e&&d&&null===g.alternate&&t(i,d),o=a(g,o,p),null===c?u=g:c.sibling=g,c=g,d=m}if(p===s.length)return n(i,d),ai&&Jr(i,p),u;if(null===d){for(;p<s.length;p++)null!==(d=f(i,s[p],l))&&(o=a(d,o,p),null===c?u=d:c.sibling=d,c=d);return ai&&Jr(i,p),u}for(d=r(d);p<s.length;p++)null!==(m=v(d,i,p,s[p],l))&&(e&&null!==m.alternate&&d.delete(null===m.key?p:m.key),o=a(m,o,p),null===c?u=m:c.sibling=m,c=m);return e&&d.forEach(function(e){return t(i,e)}),ai&&Jr(i,p),u}(l,u,c,d);if(D(c)){if("function"!==typeof(b=D(c)))throw Error(o(150));return function(i,s,l,u){if(null==l)throw Error(o(151));for(var c=null,d=null,p=s,m=s=0,g=null,y=l.next();null!==p&&!y.done;m++,y=l.next()){p.index>m?(g=p,p=null):g=p.sibling;var b=h(i,p,y.value,u);if(null===b){null===p&&(p=g);break}e&&p&&null===b.alternate&&t(i,p),s=a(b,s,m),null===d?c=b:d.sibling=b,d=b,p=g}if(y.done)return n(i,p),ai&&Jr(i,m),c;if(null===p){for(;!y.done;m++,y=l.next())null!==(y=f(i,y.value,u))&&(s=a(y,s,m),null===d?c=y:d.sibling=y,d=y);return ai&&Jr(i,m),c}for(p=r(p);!y.done;m++,y=l.next())null!==(y=v(p,i,m,y.value,u))&&(e&&null!==y.alternate&&p.delete(null===y.key?m:y.key),s=a(y,s,m),null===d?c=y:d.sibling=y,d=y);return e&&p.forEach(function(e){return t(i,e)}),ai&&Jr(i,m),c}(l,u,c=b.call(c),d)}if("function"===typeof c.then)return y(l,u,Go(c),d);if(c.$$typeof===x)return y(l,u,Ci(l,c),d);Jo(l,c)}return"string"===typeof c&&""!==c||"number"===typeof c||"bigint"===typeof c?(c=""+c,null!==u&&6===u.tag?(n(l,u.sibling),(d=i(u,c)).return=l,l=d):(n(l,u),(d=Ur(c,l.mode,d)).return=l,l=d),s(l)):n(l,u)}return function(e,t,n,r){try{Xo=0;var i=y(e,t,n,r);return Qo=null,i}catch(o){if(o===$i||o===Yi)throw o;var a=zr(29,o,null,e.mode);return a.lanes=r,a.return=e,a}}}var ns=ts(!0),rs=ts(!1),is=V(null),as=null;function os(e){var t=e.alternate;B(cs,1&cs.current),B(is,e),null===as&&(null===t||null!==ha.current||null!==t.memoizedState)&&(as=e)}function ss(e){if(22===e.tag){if(B(cs,cs.current),B(is,e),null===as){var t=e.alternate;null!==t&&null!==t.memoizedState&&(as=e)}}else ls()}function ls(){B(cs,cs.current),B(is,is.current)}function us(e){I(is),as===e&&(as=null),I(cs)}var cs=V(0);function ds(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||gd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function fs(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:f({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var hs={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ru(),i=ia(r);i.payload=t,void 0!==n&&null!==n&&(i.callback=n),null!==(t=aa(e,i,r))&&(zu(t,e,r),oa(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ru(),i=ia(r);i.tag=1,i.payload=t,void 0!==n&&null!==n&&(i.callback=n),null!==(t=aa(e,i,r))&&(zu(t,e,r),oa(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ru(),r=ia(n);r.tag=2,void 0!==t&&null!==t&&(r.callback=t),null!==(t=aa(e,r,n))&&(zu(t,e,n),oa(t,e,n))}};function ps(e,t,n,r,i,a,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,o):!t.prototype||!t.prototype.isPureReactComponent||(!Xn(n,r)||!Xn(i,a))}function ms(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&hs.enqueueReplaceState(t,t.state,null)}function gs(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var i in n===t&&(n=f({},n)),e)void 0===n[i]&&(n[i]=e[i]);return n}var vs="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function ys(e){vs(e)}function bs(e){console.error(e)}function ws(e){vs(e)}function xs(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout(function(){throw n})}}function Ss(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function ks(e,t,n){return(n=ia(n)).tag=3,n.payload={element:null},n.callback=function(){xs(e,t)},n}function Es(e){return(e=ia(e)).tag=3,e}function Ps(e,t,n,r){var i=n.type.getDerivedStateFromError;if("function"===typeof i){var a=r.value;e.payload=function(){return i(a)},e.callback=function(){Ss(t,n,r)}}var o=n.stateNode;null!==o&&"function"===typeof o.componentDidCatch&&(e.callback=function(){Ss(t,n,r),"function"!==typeof i&&(null===Eu?Eu=new Set([this]):Eu.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Ts=Error(o(461)),Cs=!1;function Fs(e,t,n,r){t.child=null===e?rs(t,null,n,r):ns(t,e.child,n,r)}function As(e,t,n,r,i){n=n.render;var a=t.ref;if("ref"in r){var o={};for(var s in r)"ref"!==s&&(o[s]=r[s])}else o=r;return Pi(t),r=Ma(e,t,n,o,a,i),s=Oa(),null===e||Cs?(ai&&s&&ti(t),t.flags|=1,Fs(e,t,r,i),t.child):(za(e,t,i),Qs(e,t,i))}function Ds(e,t,n,r,i){if(null===e){var a=n.type;return"function"!==typeof a||_r(a)||void 0!==a.defaultProps||null!==n.compare?((e=Ir(n.type,null,r,t,t.mode,i)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,Ms(e,t,a,r,i))}if(a=e.child,!Xs(e,i)){var o=a.memoizedProps;if((n=null!==(n=n.compare)?n:Xn)(o,r)&&e.ref===t.ref)return Qs(e,t,i)}return t.flags|=1,(e=jr(a,r)).ref=t.ref,e.return=t,t.child=e}function Ms(e,t,n,r,i){if(null!==e){var a=e.memoizedProps;if(Xn(a,r)&&e.ref===t.ref){if(Cs=!1,t.pendingProps=r=a,!Xs(e,i))return t.lanes=e.lanes,Qs(e,t,i);0!==(131072&e.flags)&&(Cs=!0)}}return Os(e,t,n,r,i)}function Ls(e,t,n){var r=t.pendingProps,i=r.children,a=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(0!==(128&t.flags)){if(r=null!==a?a.baseLanes|n:n,null!==e){for(i=t.child=e.child,a=0;null!==i;)a=a|i.lanes|i.childLanes,i=i.sibling;t.childLanes=a&~r}else t.childLanes=0,t.child=null;return Ns(e,t,r,n)}if(0===(536870912&n))return t.lanes=t.childLanes=536870912,Ns(e,t,null!==a?a.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Hi(0,null!==a?a.cachePool:null),null!==a?ma(t,a):ga(),ss(t)}else null!==a?(Hi(0,a.cachePool),ma(t,a),ls(),t.memoizedState=null):(null!==e&&Hi(0,null),ga(),ls());return Fs(e,t,i,n),t.child}function Ns(e,t,n,r){var i=Ui();return i=null===i?null:{parent:Li._currentValue,pool:i},t.memoizedState={baseLanes:n,cachePool:i},null!==e&&Hi(0,null),ga(),ss(t),null!==e&&ki(e,t,r,!0),null}function Rs(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!==typeof n&&"object"!==typeof n)throw Error(o(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Os(e,t,n,r,i){return Pi(t),n=Ma(e,t,n,r,void 0,i),r=Oa(),null===e||Cs?(ai&&r&&ti(t),t.flags|=1,Fs(e,t,n,i),t.child):(za(e,t,i),Qs(e,t,i))}function zs(e,t,n,r,i,a){return Pi(t),t.updateQueue=null,n=Na(t,r,n,i),La(e),r=Oa(),null===e||Cs?(ai&&r&&ti(t),t.flags|=1,Fs(e,t,n,a),t.child):(za(e,t,a),Qs(e,t,a))}function _s(e,t,n,r,i){if(Pi(t),null===t.stateNode){var a=Rr,o=n.contextType;"object"===typeof o&&null!==o&&(a=Ti(o)),a=new n(r,a),t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,a.updater=hs,t.stateNode=a,a._reactInternals=t,(a=t.stateNode).props=r,a.state=t.memoizedState,a.refs={},na(t),o=n.contextType,a.context="object"===typeof o&&null!==o?Ti(o):Rr,a.state=t.memoizedState,"function"===typeof(o=n.getDerivedStateFromProps)&&(fs(t,n,o,r),a.state=t.memoizedState),"function"===typeof n.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(o=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),o!==a.state&&hs.enqueueReplaceState(a,a.state,null),ca(t,r,a,i),ua(),a.state=t.memoizedState),"function"===typeof a.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){a=t.stateNode;var s=t.memoizedProps,l=gs(n,s);a.props=l;var u=a.context,c=n.contextType;o=Rr,"object"===typeof c&&null!==c&&(o=Ti(c));var d=n.getDerivedStateFromProps;c="function"===typeof d||"function"===typeof a.getSnapshotBeforeUpdate,s=t.pendingProps!==s,c||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(s||u!==o)&&ms(t,a,r,o),ta=!1;var f=t.memoizedState;a.state=f,ca(t,r,a,i),ua(),u=t.memoizedState,s||f!==u||ta?("function"===typeof d&&(fs(t,n,d,r),u=t.memoizedState),(l=ta||ps(t,n,l,r,f,u,o))?(c||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||("function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"===typeof a.componentDidMount&&(t.flags|=4194308)):("function"===typeof a.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),a.props=r,a.state=u,a.context=o,r=l):("function"===typeof a.componentDidMount&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,ra(e,t),c=gs(n,o=t.memoizedProps),a.props=c,d=t.pendingProps,f=a.context,u=n.contextType,l=Rr,"object"===typeof u&&null!==u&&(l=Ti(u)),(u="function"===typeof(s=n.getDerivedStateFromProps)||"function"===typeof a.getSnapshotBeforeUpdate)||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(o!==d||f!==l)&&ms(t,a,r,l),ta=!1,f=t.memoizedState,a.state=f,ca(t,r,a,i),ua();var h=t.memoizedState;o!==d||f!==h||ta||null!==e&&null!==e.dependencies&&Ei(e.dependencies)?("function"===typeof s&&(fs(t,n,s,r),h=t.memoizedState),(c=ta||ps(t,n,c,r,f,h,l)||null!==e&&null!==e.dependencies&&Ei(e.dependencies))?(u||"function"!==typeof a.UNSAFE_componentWillUpdate&&"function"!==typeof a.componentWillUpdate||("function"===typeof a.componentWillUpdate&&a.componentWillUpdate(r,h,l),"function"===typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,h,l)),"function"===typeof a.componentDidUpdate&&(t.flags|=4),"function"===typeof a.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof a.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),a.props=r,a.state=h,a.context=l,r=c):("function"!==typeof a.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return a=r,Rs(e,t),r=0!==(128&t.flags),a||r?(a=t.stateNode,n=r&&"function"!==typeof n.getDerivedStateFromError?null:a.render(),t.flags|=1,null!==e&&r?(t.child=ns(t,e.child,null,i),t.child=ns(t,null,n,i)):Fs(e,t,n,i),t.memoizedState=a.state,e=t.child):e=Qs(e,t,i),e}function js(e,t,n,r){return hi(),t.flags|=256,Fs(e,t,n,r),t.child}var Vs={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Is(e){return{baseLanes:e,cachePool:Wi()}}function Bs(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=gu),e}function Us(e,t,n){var r,i=t.pendingProps,a=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&cs.current)),r&&(a=!0,t.flags&=-129),r=0!==(32&t.flags),t.flags&=-33,null===e){if(ai){if(a?os(t):ls(),ai){var l,u=ii;if(l=u){e:{for(l=u,u=si;8!==l.nodeType;){if(!u){u=null;break e}if(null===(l=vd(l.nextSibling))){u=null;break e}}u=l}null!==u?(t.memoizedState={dehydrated:u,treeContext:null!==Xr?{id:Gr,overflow:Zr}:null,retryLane:536870912,hydrationErrors:null},(l=zr(18,null,null,0)).stateNode=u,l.return=t,t.child=l,ri=t,ii=null,l=!0):l=!1}l||ui(t)}if(null!==(u=t.memoizedState)&&null!==(u=u.dehydrated))return gd(u)?t.lanes=32:t.lanes=536870912,null;us(t)}return u=i.children,i=i.fallback,a?(ls(),u=Ws({mode:"hidden",children:u},a=t.mode),i=Br(i,a,n,null),u.return=t,i.return=t,u.sibling=i,t.child=u,(a=t.child).memoizedState=Is(n),a.childLanes=Bs(e,r,n),t.memoizedState=Vs,i):(os(t),Hs(t,u))}if(null!==(l=e.memoizedState)&&null!==(u=l.dehydrated)){if(s)256&t.flags?(os(t),t.flags&=-257,t=$s(e,t,n)):null!==t.memoizedState?(ls(),t.child=e.child,t.flags|=128,t=null):(ls(),a=i.fallback,u=t.mode,i=Ws({mode:"visible",children:i.children},u),(a=Br(a,u,n,null)).flags|=2,i.return=t,a.return=t,i.sibling=a,t.child=i,ns(t,e.child,null,n),(i=t.child).memoizedState=Is(n),i.childLanes=Bs(e,r,n),t.memoizedState=Vs,t=a);else if(os(t),gd(u)){if(r=u.nextSibling&&u.nextSibling.dataset)var c=r.dgst;r=c,(i=Error(o(419))).stack="",i.digest=r,mi({value:i,source:null,stack:null}),t=$s(e,t,n)}else if(Cs||ki(e,t,n,!1),r=0!==(n&e.childLanes),Cs||r){if(null!==(r=ru)&&(0!==(i=0!==((i=0!==(42&(i=n&-n))?1:Fe(i))&(r.suspendedLanes|n))?0:i)&&i!==l.retryLane))throw l.retryLane=i,Mr(e,i),zu(r,e,i),Ts;"$?"===u.data||Yu(),t=$s(e,t,n)}else"$?"===u.data?(t.flags|=192,t.child=e.child,t=null):(e=l.treeContext,ii=vd(u.nextSibling),ri=t,ai=!0,oi=null,si=!1,null!==e&&(Kr[Qr++]=Gr,Kr[Qr++]=Zr,Kr[Qr++]=Xr,Gr=e.id,Zr=e.overflow,Xr=t),(t=Hs(t,i.children)).flags|=4096);return t}return a?(ls(),a=i.fallback,u=t.mode,c=(l=e.child).sibling,(i=jr(l,{mode:"hidden",children:i.children})).subtreeFlags=65011712&l.subtreeFlags,null!==c?a=jr(c,a):(a=Br(a,u,n,null)).flags|=2,a.return=t,i.return=t,i.sibling=a,t.child=i,i=a,a=t.child,null===(u=e.child.memoizedState)?u=Is(n):(null!==(l=u.cachePool)?(c=Li._currentValue,l=l.parent!==c?{parent:c,pool:c}:l):l=Wi(),u={baseLanes:u.baseLanes|n,cachePool:l}),a.memoizedState=u,a.childLanes=Bs(e,r,n),t.memoizedState=Vs,i):(os(t),e=(n=e.child).sibling,(n=jr(n,{mode:"visible",children:i.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function Hs(e,t){return(t=Ws({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Ws(e,t){return(e=zr(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function $s(e,t,n){return ns(t,e.child,null,n),(e=Hs(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function qs(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),xi(e.return,t,n)}function Ys(e,t,n,r,i){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=i)}function Ks(e,t,n){var r=t.pendingProps,i=r.revealOrder,a=r.tail;if(Fs(e,t,r.children,n),0!==(2&(r=cs.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&qs(e,n,t);else if(19===e.tag)qs(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(B(cs,r),i){case"forwards":for(n=t.child,i=null;null!==n;)null!==(e=n.alternate)&&null===ds(e)&&(i=n),n=n.sibling;null===(n=i)?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Ys(t,!1,i,n,a);break;case"backwards":for(n=null,i=t.child,t.child=null;null!==i;){if(null!==(e=i.alternate)&&null===ds(e)){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Ys(t,!0,n,null,a);break;case"together":Ys(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Qs(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),hu|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(ki(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=jr(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=jr(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Xs(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!Ei(e))}function Gs(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Cs=!0;else{if(!Xs(e,n)&&0===(128&t.flags))return Cs=!1,function(e,t,n){switch(t.tag){case 3:q(t,t.stateNode.containerInfo),bi(0,Li,e.memoizedState.cache),hi();break;case 27:case 5:K(t);break;case 4:q(t,t.stateNode.containerInfo);break;case 10:bi(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(os(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Us(e,t,n):(os(t),null!==(e=Qs(e,t,n))?e.sibling:null);os(t);break;case 19:var i=0!==(128&e.flags);if((r=0!==(n&t.childLanes))||(ki(e,t,n,!1),r=0!==(n&t.childLanes)),i){if(r)return Ks(e,t,n);t.flags|=128}if(null!==(i=t.memoizedState)&&(i.rendering=null,i.tail=null,i.lastEffect=null),B(cs,cs.current),r)break;return null;case 22:case 23:return t.lanes=0,Ls(e,t,n);case 24:bi(0,Li,e.memoizedState.cache)}return Qs(e,t,n)}(e,t,n);Cs=0!==(131072&e.flags)}else Cs=!1,ai&&0!==(1048576&t.flags)&&ei(t,Yr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,i=r._init;if(r=i(r._payload),t.type=r,"function"!==typeof r){if(void 0!==r&&null!==r){if((i=r.$$typeof)===S){t.tag=11,t=As(null,t,r,e,n);break e}if(i===P){t.tag=14,t=Ds(null,t,r,e,n);break e}}throw t=L(r)||r,Error(o(306,t,""))}_r(r)?(e=gs(r,e),t.tag=1,t=_s(null,t,r,e,n)):(t.tag=0,t=Os(null,t,r,e,n))}return t;case 0:return Os(e,t,t.type,t.pendingProps,n);case 1:return _s(e,t,r=t.type,i=gs(r,t.pendingProps),n);case 3:e:{if(q(t,t.stateNode.containerInfo),null===e)throw Error(o(387));r=t.pendingProps;var a=t.memoizedState;i=a.element,ra(e,t),ca(t,r,null,n);var s=t.memoizedState;if(r=s.cache,bi(0,Li,r),r!==a.cache&&Si(t,[Li],n,!0),ua(),r=s.element,a.isDehydrated){if(a={element:r,isDehydrated:!1,cache:s.cache},t.updateQueue.baseState=a,t.memoizedState=a,256&t.flags){t=js(e,t,r,n);break e}if(r!==i){mi(i=Er(Error(o(424)),t)),t=js(e,t,r,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(ii=vd(e.firstChild),ri=t,ai=!0,oi=null,si=!0,n=rs(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(hi(),r===i){t=Qs(e,t,n);break e}Fs(e,t,r,n)}t=t.child}return t;case 26:return Rs(e,t),null===e?(n=Fd(t.type,null,t.pendingProps,null))?t.memoizedState=n:ai||(n=t.type,e=t.pendingProps,(r=rd(W.current).createElement(n))[Le]=t,r[Ne]=e,ed(r,n,e),$e(r),t.stateNode=r):t.memoizedState=Fd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return K(t),null===e&&ai&&(r=t.stateNode=wd(t.type,t.pendingProps,W.current),ri=t,si=!0,i=ii,hd(t.type)?(yd=i,ii=vd(r.firstChild)):ii=i),Fs(e,t,t.pendingProps.children,n),Rs(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&ai&&((i=r=ii)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var i=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Ve])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(a=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(a!==i.rel||e.getAttribute("href")!==(null==i.href||""===i.href?null:i.href)||e.getAttribute("crossorigin")!==(null==i.crossOrigin?null:i.crossOrigin)||e.getAttribute("title")!==(null==i.title?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((a=e.getAttribute("src"))!==(null==i.src?null:i.src)||e.getAttribute("type")!==(null==i.type?null:i.type)||e.getAttribute("crossorigin")!==(null==i.crossOrigin?null:i.crossOrigin))&&a&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var a=null==i.name?null:""+i.name;if("hidden"===i.type&&e.getAttribute("name")===a)return e}if(null===(e=vd(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,si))?(t.stateNode=r,ri=t,ii=vd(r.firstChild),si=!1,i=!0):i=!1),i||ui(t)),K(t),i=t.type,a=t.pendingProps,s=null!==e?e.memoizedProps:null,r=a.children,od(i,a)?r=null:null!==s&&od(i,s)&&(t.flags|=32),null!==t.memoizedState&&(i=Ma(e,t,Ra,null,null,n),Kd._currentValue=i),Rs(e,t),Fs(e,t,r,n),t.child;case 6:return null===e&&ai&&((e=n=ii)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=vd(e.nextSibling)))return null}return e}(n,t.pendingProps,si))?(t.stateNode=n,ri=t,ii=null,e=!0):e=!1),e||ui(t)),null;case 13:return Us(e,t,n);case 4:return q(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ns(t,null,r,n):Fs(e,t,r,n),t.child;case 11:return As(e,t,t.type,t.pendingProps,n);case 7:return Fs(e,t,t.pendingProps,n),t.child;case 8:case 12:return Fs(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,bi(0,t.type,r.value),Fs(e,t,r.children,n),t.child;case 9:return i=t.type._context,r=t.pendingProps.children,Pi(t),r=r(i=Ti(i)),t.flags|=1,Fs(e,t,r,n),t.child;case 14:return Ds(e,t,t.type,t.pendingProps,n);case 15:return Ms(e,t,t.type,t.pendingProps,n);case 19:return Ks(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},null===e?((n=Ws(r,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=jr(e.child,r)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Ls(e,t,n);case 24:return Pi(t),r=Ti(Li),null===e?(null===(i=Ui())&&(i=ru,a=Ni(),i.pooledCache=a,a.refCount++,null!==a&&(i.pooledCacheLanes|=n),i=a),t.memoizedState={parent:r,cache:i},na(t),bi(0,Li,i)):(0!==(e.lanes&n)&&(ra(e,t),ca(t,null,null,n),ua()),i=e.memoizedState,a=t.memoizedState,i.parent!==r?(i={parent:r,cache:r},t.memoizedState=i,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=i),bi(0,Li,r)):(r=a.cache,bi(0,Li,r),r!==i.cache&&Si(t,[Li],n,!0))),Fs(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(o(156,t.tag))}function Zs(e){e.flags|=4}function Js(e,t){if("stylesheet"!==t.type||0!==(4&t.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!Bd(t)){if(null!==(t=is.current)&&((4194048&au)===au?null!==as:(62914560&au)!==au&&0===(536870912&au)||t!==as))throw Zi=Ki,qi;e.flags|=8192}}function el(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?ke():536870912,e.lanes|=t,vu|=t)}function tl(e,t){if(!ai)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function nl(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=65011712&i.subtreeFlags,r|=65011712&i.flags,i.return=e,i=i.sibling;else for(i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function rl(e,t,n){var r=t.pendingProps;switch(ni(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return nl(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),wi(Li),Y(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(fi(t)?Zs(t):null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,pi())),nl(t),null;case 26:return n=t.memoizedState,null===e?(Zs(t),null!==n?(nl(t),Js(t,n)):(nl(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Zs(t),nl(t),Js(t,n)):(nl(t),t.flags&=-16777217):(e.memoizedProps!==r&&Zs(t),nl(t),t.flags&=-16777217),null;case 27:Q(t),n=W.current;var i=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Zs(t);else{if(!r){if(null===t.stateNode)throw Error(o(166));return nl(t),null}e=U.current,fi(t)?ci(t):(e=wd(i,r,n),t.stateNode=e,Zs(t))}return nl(t),null;case 5:if(Q(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Zs(t);else{if(!r){if(null===t.stateNode)throw Error(o(166));return nl(t),null}if(e=U.current,fi(t))ci(t);else{switch(i=rd(W.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=i.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof r.is?i.createElement("select",{is:r.is}):i.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"===typeof r.is?i.createElement(n,{is:r.is}):i.createElement(n)}}e[Le]=t,e[Ne]=r;e:for(i=t.child;null!==i;){if(5===i.tag||6===i.tag)e.appendChild(i.stateNode);else if(4!==i.tag&&27!==i.tag&&null!==i.child){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;null===i.sibling;){if(null===i.return||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(ed(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Zs(t)}}return nl(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Zs(t);else{if("string"!==typeof r&&null===t.stateNode)throw Error(o(166));if(e=W.current,fi(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(i=ri))switch(i.tag){case 27:case 5:r=i.memoizedProps}e[Le]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Xc(e.nodeValue,n)))||ui(t)}else(e=rd(e).createTextNode(r))[Le]=t,t.stateNode=e}return nl(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(i=fi(t),null!==r&&null!==r.dehydrated){if(null===e){if(!i)throw Error(o(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(o(317));i[Le]=t}else hi(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;nl(t),i=!1}else i=pi(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return 256&t.flags?(us(t),t):(us(t),null)}if(us(t),0!==(128&t.flags))return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){i=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(i=r.alternate.memoizedState.cachePool.pool);var a=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(a=r.memoizedState.cachePool.pool),a!==i&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),el(t,t.updateQueue),nl(t),null;case 4:return Y(),null===e&&Ic(t.stateNode.containerInfo),nl(t),null;case 10:return wi(t.type),nl(t),null;case 19:if(I(cs),null===(i=t.memoizedState))return nl(t),null;if(r=0!==(128&t.flags),null===(a=i.rendering))if(r)tl(i,!1);else{if(0!==fu||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(a=ds(e))){for(t.flags|=128,tl(i,!1),e=a.updateQueue,t.updateQueue=e,el(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Vr(n,e),n=n.sibling;return B(cs,1&cs.current|2),t.child}e=e.sibling}null!==i.tail&&te()>Su&&(t.flags|=128,r=!0,tl(i,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ds(a))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,el(t,e),tl(i,!0),null===i.tail&&"hidden"===i.tailMode&&!a.alternate&&!ai)return nl(t),null}else 2*te()-i.renderingStartTime>Su&&536870912!==n&&(t.flags|=128,r=!0,tl(i,!1),t.lanes=4194304);i.isBackwards?(a.sibling=t.child,t.child=a):(null!==(e=i.last)?e.sibling=a:t.child=a,i.last=a)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=te(),t.sibling=null,e=cs.current,B(cs,r?1&e|2:1&e),t):(nl(t),null);case 22:case 23:return us(t),va(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?0!==(536870912&n)&&0===(128&t.flags)&&(nl(t),6&t.subtreeFlags&&(t.flags|=8192)):nl(t),null!==(n=t.updateQueue)&&el(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&I(Bi),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),wi(Li),nl(t),null;case 25:case 30:return null}throw Error(o(156,t.tag))}function il(e,t){switch(ni(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return wi(Li),Y(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return Q(t),null;case 13:if(us(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));hi()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return I(cs),null;case 4:return Y(),null;case 10:return wi(t.type),null;case 22:case 23:return us(t),va(),null!==e&&I(Bi),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return wi(Li),null;default:return null}}function al(e,t){switch(ni(t),t.tag){case 3:wi(Li),Y();break;case 26:case 27:case 5:Q(t);break;case 4:Y();break;case 13:us(t);break;case 19:I(cs);break;case 10:wi(t.type);break;case 22:case 23:us(t),va(),null!==e&&I(Bi);break;case 24:wi(Li)}}function ol(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var i=r.next;n=i;do{if((n.tag&e)===e){r=void 0;var a=n.create,o=n.inst;r=a(),o.destroy=r}n=n.next}while(n!==i)}}catch(s){cc(t,t.return,s)}}function sl(e,t,n){try{var r=t.updateQueue,i=null!==r?r.lastEffect:null;if(null!==i){var a=i.next;r=a;do{if((r.tag&e)===e){var o=r.inst,s=o.destroy;if(void 0!==s){o.destroy=void 0,i=t;var l=n,u=s;try{u()}catch(c){cc(i,l,c)}}}r=r.next}while(r!==a)}}catch(c){cc(t,t.return,c)}}function ll(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{fa(t,n)}catch(r){cc(e,e.return,r)}}}function ul(e,t,n){n.props=gs(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){cc(e,t,r)}}function cl(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"===typeof n?e.refCleanup=n(r):n.current=r}}catch(i){cc(e,t,i)}}function dl(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"===typeof r)try{r()}catch(i){cc(e,t,i)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof n)try{n(null)}catch(a){cc(e,t,a)}else n.current=null}function fl(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(i){cc(e,e.return,i)}}function hl(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,a=null,s=null,l=null,u=null,c=null,d=null;for(p in n){var f=n[p];if(n.hasOwnProperty(p)&&null!=f)switch(p){case"checked":case"value":break;case"defaultValue":u=f;default:r.hasOwnProperty(p)||Zc(e,t,p,null,r,f)}}for(var h in r){var p=r[h];if(f=n[h],r.hasOwnProperty(h)&&(null!=p||null!=f))switch(h){case"type":a=p;break;case"name":i=p;break;case"checked":c=p;break;case"defaultChecked":d=p;break;case"value":s=p;break;case"defaultValue":l=p;break;case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(o(137,t));break;default:p!==f&&Zc(e,t,h,p,r,f)}}return void gt(e,s,l,u,c,d,a,i);case"select":for(a in p=s=l=h=null,n)if(u=n[a],n.hasOwnProperty(a)&&null!=u)switch(a){case"value":break;case"multiple":p=u;default:r.hasOwnProperty(a)||Zc(e,t,a,null,r,u)}for(i in r)if(a=r[i],u=n[i],r.hasOwnProperty(i)&&(null!=a||null!=u))switch(i){case"value":h=a;break;case"defaultValue":l=a;break;case"multiple":s=a;default:a!==u&&Zc(e,t,i,a,r,u)}return t=l,n=s,r=p,void(null!=h?bt(e,!!n,h,!1):!!r!==!!n&&(null!=t?bt(e,!!n,t,!0):bt(e,!!n,n?[]:"",!1)));case"textarea":for(l in p=h=null,n)if(i=n[l],n.hasOwnProperty(l)&&null!=i&&!r.hasOwnProperty(l))switch(l){case"value":case"children":break;default:Zc(e,t,l,null,r,i)}for(s in r)if(i=r[s],a=n[s],r.hasOwnProperty(s)&&(null!=i||null!=a))switch(s){case"value":h=i;break;case"defaultValue":p=i;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=i)throw Error(o(91));break;default:i!==a&&Zc(e,t,s,i,r,a)}return void wt(e,h,p);case"option":for(var m in n)if(h=n[m],n.hasOwnProperty(m)&&null!=h&&!r.hasOwnProperty(m))if("selected"===m)e.selected=!1;else Zc(e,t,m,null,r,h);for(u in r)if(h=r[u],p=n[u],r.hasOwnProperty(u)&&h!==p&&(null!=h||null!=p))if("selected"===u)e.selected=h&&"function"!==typeof h&&"symbol"!==typeof h;else Zc(e,t,u,h,r,p);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)h=n[g],n.hasOwnProperty(g)&&null!=h&&!r.hasOwnProperty(g)&&Zc(e,t,g,null,r,h);for(c in r)if(h=r[c],p=n[c],r.hasOwnProperty(c)&&h!==p&&(null!=h||null!=p))switch(c){case"children":case"dangerouslySetInnerHTML":if(null!=h)throw Error(o(137,t));break;default:Zc(e,t,c,h,r,p)}return;default:if(Tt(t)){for(var v in n)h=n[v],n.hasOwnProperty(v)&&void 0!==h&&!r.hasOwnProperty(v)&&Jc(e,t,v,void 0,r,h);for(d in r)h=r[d],p=n[d],!r.hasOwnProperty(d)||h===p||void 0===h&&void 0===p||Jc(e,t,d,h,r,p);return}}for(var y in n)h=n[y],n.hasOwnProperty(y)&&null!=h&&!r.hasOwnProperty(y)&&Zc(e,t,y,null,r,h);for(f in r)h=r[f],p=n[f],!r.hasOwnProperty(f)||h===p||null==h&&null==p||Zc(e,t,f,h,r,p)}(r,e.type,n,t),r[Ne]=t}catch(i){cc(e,e.return,i)}}function pl(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&hd(e.type)||4===e.tag}function ml(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||pl(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&hd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function gl(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Gc));else if(4!==r&&(27===r&&hd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(gl(e,t,n),e=e.sibling;null!==e;)gl(e,t,n),e=e.sibling}function vl(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&hd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(vl(e,t,n),e=e.sibling;null!==e;)vl(e,t,n),e=e.sibling}function yl(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);ed(t,r,n),t[Le]=e,t[Ne]=n}catch(a){cc(e,e.return,a)}}var bl=!1,wl=!1,xl=!1,Sl="function"===typeof WeakSet?WeakSet:Set,kl=null;function El(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:_l(e,n),4&r&&ol(5,n);break;case 1:if(_l(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(o){cc(n,n.return,o)}else{var i=gs(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(s){cc(n,n.return,s)}}64&r&&ll(n),512&r&&cl(n,n.return);break;case 3:if(_l(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{fa(e,t)}catch(o){cc(n,n.return,o)}}break;case 27:null===t&&4&r&&yl(n);case 26:case 5:_l(e,n),null===t&&4&r&&fl(n),512&r&&cl(n,n.return);break;case 12:_l(e,n);break;case 13:_l(e,n),4&r&&Dl(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=pc.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||bl)){t=null!==t&&null!==t.memoizedState||wl,i=bl;var a=wl;bl=r,(wl=t)&&!a?Vl(e,n,0!==(8772&n.subtreeFlags)):_l(e,n),bl=i,wl=a}break;case 30:break;default:_l(e,n)}}function Pl(e){var t=e.alternate;null!==t&&(e.alternate=null,Pl(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&Ie(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Tl=null,Cl=!1;function Fl(e,t,n){for(n=n.child;null!==n;)Al(e,t,n),n=n.sibling}function Al(e,t,n){if(de&&"function"===typeof de.onCommitFiberUnmount)try{de.onCommitFiberUnmount(ce,n)}catch(a){}switch(n.tag){case 26:wl||dl(n,t),Fl(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:wl||dl(n,t);var r=Tl,i=Cl;hd(n.type)&&(Tl=n.stateNode,Cl=!1),Fl(e,t,n),xd(n.stateNode),Tl=r,Cl=i;break;case 5:wl||dl(n,t);case 6:if(r=Tl,i=Cl,Tl=null,Fl(e,t,n),Cl=i,null!==(Tl=r))if(Cl)try{(9===Tl.nodeType?Tl.body:"HTML"===Tl.nodeName?Tl.ownerDocument.body:Tl).removeChild(n.stateNode)}catch(o){cc(n,t,o)}else try{Tl.removeChild(n.stateNode)}catch(o){cc(n,t,o)}break;case 18:null!==Tl&&(Cl?(pd(9===(e=Tl).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Ff(e)):pd(Tl,n.stateNode));break;case 4:r=Tl,i=Cl,Tl=n.stateNode.containerInfo,Cl=!0,Fl(e,t,n),Tl=r,Cl=i;break;case 0:case 11:case 14:case 15:wl||sl(2,n,t),wl||sl(4,n,t),Fl(e,t,n);break;case 1:wl||(dl(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount&&ul(n,t,r)),Fl(e,t,n);break;case 21:Fl(e,t,n);break;case 22:wl=(r=wl)||null!==n.memoizedState,Fl(e,t,n),wl=r;break;default:Fl(e,t,n)}}function Dl(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Ff(e)}catch(n){cc(t,t.return,n)}}function Ml(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new Sl),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new Sl),t;default:throw Error(o(435,e.tag))}}(e);t.forEach(function(t){var r=mc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}function Ll(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var i=n[r],a=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 27:if(hd(l.type)){Tl=l.stateNode,Cl=!1;break e}break;case 5:Tl=l.stateNode,Cl=!1;break e;case 3:case 4:Tl=l.stateNode.containerInfo,Cl=!0;break e}l=l.return}if(null===Tl)throw Error(o(160));Al(a,s,i),Tl=null,Cl=!1,null!==(a=i.alternate)&&(a.return=null),i.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Rl(t,e),t=t.sibling}var Nl=null;function Rl(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Ll(t,e),Ol(e),4&r&&(sl(3,e,e.return),ol(3,e),sl(5,e,e.return));break;case 1:Ll(t,e),Ol(e),512&r&&(wl||null===n||dl(n,n.return)),64&r&&bl&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r))));break;case 26:var i=Nl;if(Ll(t,e),Ol(e),512&r&&(wl||null===n||dl(n,n.return)),4&r){var a=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,i=i.ownerDocument||i;t:switch(r){case"title":(!(a=i.getElementsByTagName("title")[0])||a[Ve]||a[Le]||"http://www.w3.org/2000/svg"===a.namespaceURI||a.hasAttribute("itemprop"))&&(a=i.createElement(r),i.head.insertBefore(a,i.querySelector("head > title"))),ed(a,r,n),a[Le]=e,$e(a),r=a;break e;case"link":var s=Vd("link","href",i).get(r+(n.href||""));if(s)for(var l=0;l<s.length;l++)if((a=s[l]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&a.getAttribute("rel")===(null==n.rel?null:n.rel)&&a.getAttribute("title")===(null==n.title?null:n.title)&&a.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){s.splice(l,1);break t}ed(a=i.createElement(r),r,n),i.head.appendChild(a);break;case"meta":if(s=Vd("meta","content",i).get(r+(n.content||"")))for(l=0;l<s.length;l++)if((a=s[l]).getAttribute("content")===(null==n.content?null:""+n.content)&&a.getAttribute("name")===(null==n.name?null:n.name)&&a.getAttribute("property")===(null==n.property?null:n.property)&&a.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&a.getAttribute("charset")===(null==n.charSet?null:n.charSet)){s.splice(l,1);break t}ed(a=i.createElement(r),r,n),i.head.appendChild(a);break;default:throw Error(o(468,r))}a[Le]=e,$e(a),r=a}e.stateNode=r}else Id(i,e.type,e.stateNode);else e.stateNode=Rd(i,r,e.memoizedProps);else a!==r?(null===a?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):a.count--,null===r?Id(i,e.type,e.stateNode):Rd(i,r,e.memoizedProps)):null===r&&null!==e.stateNode&&hl(e,e.memoizedProps,n.memoizedProps)}break;case 27:Ll(t,e),Ol(e),512&r&&(wl||null===n||dl(n,n.return)),null!==n&&4&r&&hl(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Ll(t,e),Ol(e),512&r&&(wl||null===n||dl(n,n.return)),32&e.flags){i=e.stateNode;try{St(i,"")}catch(p){cc(e,e.return,p)}}4&r&&null!=e.stateNode&&hl(e,i=e.memoizedProps,null!==n?n.memoizedProps:i),1024&r&&(xl=!0);break;case 6:if(Ll(t,e),Ol(e),4&r){if(null===e.stateNode)throw Error(o(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(p){cc(e,e.return,p)}}break;case 3:if(jd=null,i=Nl,Nl=Ed(t.containerInfo),Ll(t,e),Nl=i,Ol(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Ff(t.containerInfo)}catch(p){cc(e,e.return,p)}xl&&(xl=!1,zl(e));break;case 4:r=Nl,Nl=Ed(e.stateNode.containerInfo),Ll(t,e),Ol(e),Nl=r;break;case 12:default:Ll(t,e),Ol(e);break;case 13:Ll(t,e),Ol(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==n&&null!==n.memoizedState)&&(xu=te()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Ml(e,r)));break;case 22:i=null!==e.memoizedState;var u=null!==n&&null!==n.memoizedState,c=bl,d=wl;if(bl=c||i,wl=d||u,Ll(t,e),wl=d,bl=c,Ol(e),8192&r)e:for(t=e.stateNode,t._visibility=i?-2&t._visibility:1|t._visibility,i&&(null===n||u||bl||wl||jl(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){u=n=t;try{if(a=u.stateNode,i)"function"===typeof(s=a.style).setProperty?s.setProperty("display","none","important"):s.display="none";else{l=u.stateNode;var f=u.memoizedProps.style,h=void 0!==f&&null!==f&&f.hasOwnProperty("display")?f.display:null;l.style.display=null==h||"boolean"===typeof h?"":(""+h).trim()}}catch(p){cc(u,u.return,p)}}}else if(6===t.tag){if(null===n){u=t;try{u.stateNode.nodeValue=i?"":u.memoizedProps}catch(p){cc(u,u.return,p)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(n=r.retryQueue)&&(r.retryQueue=null,Ml(e,n))));break;case 19:Ll(t,e),Ol(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Ml(e,r)));case 30:case 21:}}function Ol(e){var t=e.flags;if(2&t){try{for(var n,r=e.return;null!==r;){if(pl(r)){n=r;break}r=r.return}if(null==n)throw Error(o(160));switch(n.tag){case 27:var i=n.stateNode;vl(e,ml(e),i);break;case 5:var a=n.stateNode;32&n.flags&&(St(a,""),n.flags&=-33),vl(e,ml(e),a);break;case 3:case 4:var s=n.stateNode.containerInfo;gl(e,ml(e),s);break;default:throw Error(o(161))}}catch(l){cc(e,e.return,l)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function zl(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;zl(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function _l(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)El(e,t.alternate,t),t=t.sibling}function jl(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:sl(4,t,t.return),jl(t);break;case 1:dl(t,t.return);var n=t.stateNode;"function"===typeof n.componentWillUnmount&&ul(t,t.return,n),jl(t);break;case 27:xd(t.stateNode);case 26:case 5:dl(t,t.return),jl(t);break;case 22:null===t.memoizedState&&jl(t);break;default:jl(t)}e=e.sibling}}function Vl(e,t,n){for(n=n&&0!==(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,i=e,a=t,o=a.flags;switch(a.tag){case 0:case 11:case 15:Vl(i,a,n),ol(4,a);break;case 1:if(Vl(i,a,n),"function"===typeof(i=(r=a).stateNode).componentDidMount)try{i.componentDidMount()}catch(u){cc(r,r.return,u)}if(null!==(i=(r=a).updateQueue)){var s=r.stateNode;try{var l=i.shared.hiddenCallbacks;if(null!==l)for(i.shared.hiddenCallbacks=null,i=0;i<l.length;i++)da(l[i],s)}catch(u){cc(r,r.return,u)}}n&&64&o&&ll(a),cl(a,a.return);break;case 27:yl(a);case 26:case 5:Vl(i,a,n),n&&null===r&&4&o&&fl(a),cl(a,a.return);break;case 12:Vl(i,a,n);break;case 13:Vl(i,a,n),n&&4&o&&Dl(i,a);break;case 22:null===a.memoizedState&&Vl(i,a,n),cl(a,a.return);break;case 30:break;default:Vl(i,a,n)}t=t.sibling}}function Il(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Ri(n))}function Bl(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Ri(e))}function Ul(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Hl(e,t,n,r),t=t.sibling}function Hl(e,t,n,r){var i=t.flags;switch(t.tag){case 0:case 11:case 15:Ul(e,t,n,r),2048&i&&ol(9,t);break;case 1:case 13:default:Ul(e,t,n,r);break;case 3:Ul(e,t,n,r),2048&i&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Ri(e)));break;case 12:if(2048&i){Ul(e,t,n,r),e=t.stateNode;try{var a=t.memoizedProps,o=a.id,s=a.onPostCommit;"function"===typeof s&&s(o,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(l){cc(t,t.return,l)}}else Ul(e,t,n,r);break;case 23:break;case 22:a=t.stateNode,o=t.alternate,null!==t.memoizedState?2&a._visibility?Ul(e,t,n,r):$l(e,t):2&a._visibility?Ul(e,t,n,r):(a._visibility|=2,Wl(e,t,n,r,0!==(10256&t.subtreeFlags))),2048&i&&Il(o,t);break;case 24:Ul(e,t,n,r),2048&i&&Bl(t.alternate,t)}}function Wl(e,t,n,r,i){for(i=i&&0!==(10256&t.subtreeFlags),t=t.child;null!==t;){var a=e,o=t,s=n,l=r,u=o.flags;switch(o.tag){case 0:case 11:case 15:Wl(a,o,s,l,i),ol(8,o);break;case 23:break;case 22:var c=o.stateNode;null!==o.memoizedState?2&c._visibility?Wl(a,o,s,l,i):$l(a,o):(c._visibility|=2,Wl(a,o,s,l,i)),i&&2048&u&&Il(o.alternate,o);break;case 24:Wl(a,o,s,l,i),i&&2048&u&&Bl(o.alternate,o);break;default:Wl(a,o,s,l,i)}t=t.sibling}}function $l(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,i=r.flags;switch(r.tag){case 22:$l(n,r),2048&i&&Il(r.alternate,r);break;case 24:$l(n,r),2048&i&&Bl(r.alternate,r);break;default:$l(n,r)}t=t.sibling}}var ql=8192;function Yl(e){if(e.subtreeFlags&ql)for(e=e.child;null!==e;)Kl(e),e=e.sibling}function Kl(e){switch(e.tag){case 26:Yl(e),e.flags&ql&&null!==e.memoizedState&&function(e,t,n){if(null===Ud)throw Error(o(475));var r=Ud;if("stylesheet"===t.type&&("string"!==typeof n.media||!1!==matchMedia(n.media).matches)&&0===(4&t.state.loading)){if(null===t.instance){var i=Ad(n.href),a=e.querySelector(Dd(i));if(a)return null!==(e=a._p)&&"object"===typeof e&&"function"===typeof e.then&&(r.count++,r=Wd.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=a,void $e(a);a=e.ownerDocument||e,n=Md(n),(i=Sd.get(i))&&zd(n,i),$e(a=a.createElement("link"));var s=a;s._p=new Promise(function(e,t){s.onload=e,s.onerror=t}),ed(a,"link",n),t.instance=a}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&0===(3&t.state.loading)&&(r.count++,t=Wd.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(Nl,e.memoizedState,e.memoizedProps);break;case 5:default:Yl(e);break;case 3:case 4:var t=Nl;Nl=Ed(e.stateNode.containerInfo),Yl(e),Nl=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=ql,ql=16777216,Yl(e),ql=t):Yl(e))}}function Ql(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Xl(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];kl=r,Jl(r,e)}Ql(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Gl(e),e=e.sibling}function Gl(e){switch(e.tag){case 0:case 11:case 15:Xl(e),2048&e.flags&&sl(9,e,e.return);break;case 3:case 12:default:Xl(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,Zl(e)):Xl(e)}}function Zl(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];kl=r,Jl(r,e)}Ql(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:sl(8,t,t.return),Zl(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,Zl(t));break;default:Zl(t)}e=e.sibling}}function Jl(e,t){for(;null!==kl;){var n=kl;switch(n.tag){case 0:case 11:case 15:sl(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Ri(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,kl=r;else e:for(n=e;null!==kl;){var i=(r=kl).sibling,a=r.return;if(Pl(r),r===n){kl=null;break e}if(null!==i){i.return=a,kl=i;break e}kl=a}}}var eu={getCacheForType:function(e){var t=Ti(Li),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},tu="function"===typeof WeakMap?WeakMap:Map,nu=0,ru=null,iu=null,au=0,ou=0,su=null,lu=!1,uu=!1,cu=!1,du=0,fu=0,hu=0,pu=0,mu=0,gu=0,vu=0,yu=null,bu=null,wu=!1,xu=0,Su=1/0,ku=null,Eu=null,Pu=0,Tu=null,Cu=null,Fu=0,Au=0,Du=null,Mu=null,Lu=0,Nu=null;function Ru(){if(0!==(2&nu)&&0!==au)return au&-au;if(null!==R.T){return 0!==_i?_i:Ac()}return De()}function Ou(){0===gu&&(gu=0===(536870912&au)||ai?Se():536870912);var e=is.current;return null!==e&&(e.flags|=32),gu}function zu(e,t,n){(e!==ru||2!==ou&&9!==ou)&&null===e.cancelPendingCommit||(Hu(e,0),Iu(e,au,gu,!1)),Pe(e,n),0!==(2&nu)&&e===ru||(e===ru&&(0===(2&nu)&&(pu|=n),4===fu&&Iu(e,au,gu,!1)),Sc(e))}function _u(e,t,n){if(0!==(6&nu))throw Error(o(327));for(var r=!n&&0===(124&t)&&0===(t&e.expiredLanes)||we(e,t),i=r?function(e,t){var n=nu;nu|=2;var r=$u(),i=qu();ru!==e||au!==t?(ku=null,Su=te()+500,Hu(e,t)):uu=we(e,t);e:for(;;)try{if(0!==ou&&null!==iu){t=iu;var a=su;t:switch(ou){case 1:ou=0,su=null,Ju(e,t,a,1);break;case 2:case 9:if(Qi(a)){ou=0,su=null,Zu(t);break}t=function(){2!==ou&&9!==ou||ru!==e||(ou=7),Sc(e)},a.then(t,t);break e;case 3:ou=7;break e;case 4:ou=5;break e;case 7:Qi(a)?(ou=0,su=null,Zu(t)):(ou=0,su=null,Ju(e,t,a,7));break;case 5:var s=null;switch(iu.tag){case 26:s=iu.memoizedState;case 5:case 27:var l=iu;if(!s||Bd(s)){ou=0,su=null;var u=l.sibling;if(null!==u)iu=u;else{var c=l.return;null!==c?(iu=c,ec(c)):iu=null}break t}}ou=0,su=null,Ju(e,t,a,5);break;case 6:ou=0,su=null,Ju(e,t,a,6);break;case 8:Uu(),fu=6;break e;default:throw Error(o(462))}}Xu();break}catch(d){Wu(e,d)}return yi=vi=null,R.H=r,R.A=i,nu=n,null!==iu?0:(ru=null,au=0,Fr(),fu)}(e,t):Ku(e,t,!0),a=r;;){if(0===i){uu&&!r&&Iu(e,t,0,!1);break}if(n=e.current.alternate,!a||Vu(n)){if(2===i){if(a=t,e.errorRecoveryDisabledLanes&a)var s=0;else s=0!==(s=-536870913&e.pendingLanes)?s:536870912&s?536870912:0;if(0!==s){t=s;e:{var l=e;i=yu;var u=l.current.memoizedState.isDehydrated;if(u&&(Hu(l,s).flags|=256),2!==(s=Ku(l,s,!1))){if(cu&&!u){l.errorRecoveryDisabledLanes|=a,pu|=a,i=4;break e}a=bu,bu=i,null!==a&&(null===bu?bu=a:bu.push.apply(bu,a))}i=s}if(a=!1,2!==i)continue}}if(1===i){Hu(e,0),Iu(e,t,0,!0);break}e:{switch(r=e,a=i){case 0:case 1:throw Error(o(345));case 4:if((4194048&t)!==t)break;case 6:Iu(r,t,gu,!lu);break e;case 2:bu=null;break;case 3:case 5:break;default:throw Error(o(329))}if((62914560&t)===t&&10<(i=xu+300-te())){if(Iu(r,t,gu,!lu),0!==be(r,0,!0))break e;r.timeoutHandle=ld(ju.bind(null,r,n,bu,ku,wu,t,gu,pu,vu,lu,a,2,-0,0),i)}else ju(r,n,bu,ku,wu,t,gu,pu,vu,lu,a,0,-0,0)}break}i=Ku(e,t,!1),a=!1}Sc(e)}function ju(e,t,n,r,i,a,s,l,u,c,d,f,h,p){if(e.timeoutHandle=-1,(8192&(f=t.subtreeFlags)||16785408===(16785408&f))&&(Ud={stylesheets:null,count:0,unsuspend:Hd},Kl(t),null!==(f=function(){if(null===Ud)throw Error(o(475));var e=Ud;return e.stylesheets&&0===e.count&&qd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&qd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=f(nc.bind(null,e,t,a,n,r,i,s,l,u,d,1,h,p)),void Iu(e,a,s,!c);nc(e,t,a,n,r,i,s,l,u)}function Vu(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var i=n[r],a=i.getSnapshot;i=i.value;try{if(!Qn(a(),i))return!1}catch(o){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Iu(e,t,n,r){t&=~mu,t&=~pu,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var i=t;0<i;){var a=31-he(i),o=1<<a;r[a]=-1,i&=~o}0!==n&&Te(e,n,t)}function Bu(){return 0!==(6&nu)||(kc(0,!1),!1)}function Uu(){if(null!==iu){if(0===ou)var e=iu.return;else yi=vi=null,_a(e=iu),Qo=null,Xo=0,e=iu;for(;null!==e;)al(e.alternate,e),e=e.return;iu=null}}function Hu(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,ud(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Uu(),ru=e,iu=n=jr(e.current,null),au=t,ou=0,su=null,lu=!1,uu=we(e,t),cu=!1,vu=gu=mu=pu=hu=fu=0,bu=yu=null,wu=!1,0!==(8&t)&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var i=31-he(r),a=1<<i;t|=e[i],r&=~a}return du=t,Fr(),n}function Wu(e,t){ba=null,R.H=$o,t===$i||t===Yi?(t=Ji(),ou=3):t===qi?(t=Ji(),ou=4):ou=t===Ts?8:null!==t&&"object"===typeof t&&"function"===typeof t.then?6:1,su=t,null===iu&&(fu=1,xs(e,Er(t,e.current)))}function $u(){var e=R.H;return R.H=$o,null===e?$o:e}function qu(){var e=R.A;return R.A=eu,e}function Yu(){fu=4,lu||(4194048&au)!==au&&null!==is.current||(uu=!0),0===(134217727&hu)&&0===(134217727&pu)||null===ru||Iu(ru,au,gu,!1)}function Ku(e,t,n){var r=nu;nu|=2;var i=$u(),a=qu();ru===e&&au===t||(ku=null,Hu(e,t)),t=!1;var o=fu;e:for(;;)try{if(0!==ou&&null!==iu){var s=iu,l=su;switch(ou){case 8:Uu(),o=6;break e;case 3:case 2:case 9:case 6:null===is.current&&(t=!0);var u=ou;if(ou=0,su=null,Ju(e,s,l,u),n&&uu){o=0;break e}break;default:u=ou,ou=0,su=null,Ju(e,s,l,u)}}Qu(),o=fu;break}catch(c){Wu(e,c)}return t&&e.shellSuspendCounter++,yi=vi=null,nu=r,R.H=i,R.A=a,null===iu&&(ru=null,au=0,Fr()),o}function Qu(){for(;null!==iu;)Gu(iu)}function Xu(){for(;null!==iu&&!J();)Gu(iu)}function Gu(e){var t=Gs(e.alternate,e,du);e.memoizedProps=e.pendingProps,null===t?ec(e):iu=t}function Zu(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=zs(n,t,t.pendingProps,t.type,void 0,au);break;case 11:t=zs(n,t,t.pendingProps,t.type.render,t.ref,au);break;case 5:_a(t);default:al(n,t),t=Gs(n,t=iu=Vr(t,du),du)}e.memoizedProps=e.pendingProps,null===t?ec(e):iu=t}function Ju(e,t,n,r){yi=vi=null,_a(t),Qo=null,Xo=0;var i=t.return;try{if(function(e,t,n,r,i){if(n.flags|=32768,null!==r&&"object"===typeof r&&"function"===typeof r.then){if(null!==(t=n.alternate)&&ki(t,n,i,!0),null!==(n=is.current)){switch(n.tag){case 13:return null===as?Yu():null===n.alternate&&0===fu&&(fu=3),n.flags&=-257,n.flags|=65536,n.lanes=i,r===Ki?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),dc(e,r,i)),!1;case 22:return n.flags|=65536,r===Ki?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),dc(e,r,i)),!1}throw Error(o(435,n.tag))}return dc(e,r,i),Yu(),!1}if(ai)return null!==(t=is.current)?(0===(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=i,r!==li&&mi(Er(e=Error(o(422),{cause:r}),n))):(r!==li&&mi(Er(t=Error(o(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,i&=-i,e.lanes|=i,r=Er(r,n),sa(e,i=ks(e.stateNode,r,i)),4!==fu&&(fu=2)),!1;var a=Error(o(520),{cause:r});if(a=Er(a,n),null===yu?yu=[a]:yu.push(a),4!==fu&&(fu=2),null===t)return!0;r=Er(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=i&-i,n.lanes|=e,sa(n,e=ks(n.stateNode,r,e)),!1;case 1:if(t=n.type,a=n.stateNode,0===(128&n.flags)&&("function"===typeof t.getDerivedStateFromError||null!==a&&"function"===typeof a.componentDidCatch&&(null===Eu||!Eu.has(a))))return n.flags|=65536,i&=-i,n.lanes|=i,Ps(i=Es(i),e,n,r),sa(n,i),!1}n=n.return}while(null!==n);return!1}(e,i,t,n,au))return fu=1,xs(e,Er(n,e.current)),void(iu=null)}catch(a){if(null!==i)throw iu=i,a;return fu=1,xs(e,Er(n,e.current)),void(iu=null)}32768&t.flags?(ai||1===r?e=!0:uu||0!==(536870912&au)?e=!1:(lu=e=!0,(2===r||9===r||3===r||6===r)&&(null!==(r=is.current)&&13===r.tag&&(r.flags|=16384))),tc(t,e)):ec(t)}function ec(e){var t=e;do{if(0!==(32768&t.flags))return void tc(t,lu);e=t.return;var n=rl(t.alternate,t,du);if(null!==n)return void(iu=n);if(null!==(t=t.sibling))return void(iu=t);iu=t=e}while(null!==t);0===fu&&(fu=5)}function tc(e,t){do{var n=il(e.alternate,e);if(null!==n)return n.flags&=32767,void(iu=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(iu=e);iu=e=n}while(null!==e);fu=6,iu=null}function nc(e,t,n,r,i,a,s,l,u){e.cancelPendingCommit=null;do{sc()}while(0!==Pu);if(0!==(6&nu))throw Error(o(327));if(null!==t){if(t===e.current)throw Error(o(177));if(a=t.lanes|t.childLanes,function(e,t,n,r,i,a){var o=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var s=e.entanglements,l=e.expirationTimes,u=e.hiddenUpdates;for(n=o&~n;0<n;){var c=31-he(n),d=1<<c;s[c]=0,l[c]=-1;var f=u[c];if(null!==f)for(u[c]=null,c=0;c<f.length;c++){var h=f[c];null!==h&&(h.lane&=-536870913)}n&=~d}0!==r&&Te(e,r,0),0!==a&&0===i&&0!==e.tag&&(e.suspendedLanes|=a&~(o&~t))}(e,n,a|=Cr,s,l,u),e===ru&&(iu=ru=null,au=0),Cu=t,Tu=e,Fu=n,Au=a,Du=i,Mu=r,0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?(e.callbackNode=null,e.callbackPriority=0,G(ae,function(){return lc(),null})):(e.callbackNode=null,e.callbackPriority=0),r=0!==(13878&t.flags),0!==(13878&t.subtreeFlags)||r){r=R.T,R.T=null,i=O.p,O.p=2,s=nu,nu|=4;try{!function(e,t){if(e=e.containerInfo,td=nf,tr(e=er(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var i=r.anchorOffset,a=r.focusNode;r=r.focusOffset;try{n.nodeType,a.nodeType}catch(g){n=null;break e}var s=0,l=-1,u=-1,c=0,d=0,f=e,h=null;t:for(;;){for(var p;f!==n||0!==i&&3!==f.nodeType||(l=s+i),f!==a||0!==r&&3!==f.nodeType||(u=s+r),3===f.nodeType&&(s+=f.nodeValue.length),null!==(p=f.firstChild);)h=f,f=p;for(;;){if(f===e)break t;if(h===n&&++c===i&&(l=s),h===a&&++d===r&&(u=s),null!==(p=f.nextSibling))break;h=(f=h).parentNode}f=p}n=-1===l||-1===u?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(nd={focusedElem:e,selectionRange:n},nf=!1,kl=t;null!==kl;)if(e=(t=kl).child,0!==(1024&t.subtreeFlags)&&null!==e)e.return=t,kl=e;else for(;null!==kl;){switch(a=(t=kl).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==a){e=void 0,n=t,i=a.memoizedProps,a=a.memoizedState,r=n.stateNode;try{var m=gs(n.type,i,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(m,a),r.__reactInternalSnapshotBeforeUpdate=e}catch(v){cc(n,n.return,v)}}break;case 3:if(0!==(1024&e))if(9===(n=(e=t.stateNode.containerInfo).nodeType))md(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":md(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(o(163))}if(null!==(e=t.sibling)){e.return=t.return,kl=e;break}kl=t.return}}(e,t)}finally{nu=s,O.p=i,R.T=r}}Pu=1,rc(),ic(),ac()}}function rc(){if(1===Pu){Pu=0;var e=Tu,t=Cu,n=0!==(13878&t.flags);if(0!==(13878&t.subtreeFlags)||n){n=R.T,R.T=null;var r=O.p;O.p=2;var i=nu;nu|=4;try{Rl(t,e);var a=nd,o=er(e.containerInfo),s=a.focusedElem,l=a.selectionRange;if(o!==s&&s&&s.ownerDocument&&Jn(s.ownerDocument.documentElement,s)){if(null!==l&&tr(s)){var u=l.start,c=l.end;if(void 0===c&&(c=u),"selectionStart"in s)s.selectionStart=u,s.selectionEnd=Math.min(c,s.value.length);else{var d=s.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var h=f.getSelection(),p=s.textContent.length,m=Math.min(l.start,p),g=void 0===l.end?m:Math.min(l.end,p);!h.extend&&m>g&&(o=g,g=m,m=o);var v=Zn(s,m),y=Zn(s,g);if(v&&y&&(1!==h.rangeCount||h.anchorNode!==v.node||h.anchorOffset!==v.offset||h.focusNode!==y.node||h.focusOffset!==y.offset)){var b=d.createRange();b.setStart(v.node,v.offset),h.removeAllRanges(),m>g?(h.addRange(b),h.extend(y.node,y.offset)):(b.setEnd(y.node,y.offset),h.addRange(b))}}}}for(d=[],h=s;h=h.parentNode;)1===h.nodeType&&d.push({element:h,left:h.scrollLeft,top:h.scrollTop});for("function"===typeof s.focus&&s.focus(),s=0;s<d.length;s++){var w=d[s];w.element.scrollLeft=w.left,w.element.scrollTop=w.top}}nf=!!td,nd=td=null}finally{nu=i,O.p=r,R.T=n}}e.current=t,Pu=2}}function ic(){if(2===Pu){Pu=0;var e=Tu,t=Cu,n=0!==(8772&t.flags);if(0!==(8772&t.subtreeFlags)||n){n=R.T,R.T=null;var r=O.p;O.p=2;var i=nu;nu|=4;try{El(e,t.alternate,t)}finally{nu=i,O.p=r,R.T=n}}Pu=3}}function ac(){if(4===Pu||3===Pu){Pu=0,ee();var e=Tu,t=Cu,n=Fu,r=Mu;0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?Pu=5:(Pu=0,Cu=Tu=null,oc(e,e.pendingLanes));var i=e.pendingLanes;if(0===i&&(Eu=null),Ae(n),t=t.stateNode,de&&"function"===typeof de.onCommitFiberRoot)try{de.onCommitFiberRoot(ce,t,void 0,128===(128&t.current.flags))}catch(l){}if(null!==r){t=R.T,i=O.p,O.p=2,R.T=null;try{for(var a=e.onRecoverableError,o=0;o<r.length;o++){var s=r[o];a(s.value,{componentStack:s.stack})}}finally{R.T=t,O.p=i}}0!==(3&Fu)&&sc(),Sc(e),i=e.pendingLanes,0!==(4194090&n)&&0!==(42&i)?e===Nu?Lu++:(Lu=0,Nu=e):Lu=0,kc(0,!1)}}function oc(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,Ri(t)))}function sc(e){return rc(),ic(),ac(),lc()}function lc(){if(5!==Pu)return!1;var e=Tu,t=Au;Au=0;var n=Ae(Fu),r=R.T,i=O.p;try{O.p=32>n?32:n,R.T=null,n=Du,Du=null;var a=Tu,s=Fu;if(Pu=0,Cu=Tu=null,Fu=0,0!==(6&nu))throw Error(o(331));var l=nu;if(nu|=4,Gl(a.current),Hl(a,a.current,s,n),nu=l,kc(0,!1),de&&"function"===typeof de.onPostCommitFiberRoot)try{de.onPostCommitFiberRoot(ce,a)}catch(u){}return!0}finally{O.p=i,R.T=r,oc(e,t)}}function uc(e,t,n){t=Er(n,t),null!==(e=aa(e,t=ks(e.stateNode,t,2),2))&&(Pe(e,2),Sc(e))}function cc(e,t,n){if(3===e.tag)uc(e,e,n);else for(;null!==t;){if(3===t.tag){uc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Eu||!Eu.has(r))){e=Er(n,e),null!==(r=aa(t,n=Es(2),2))&&(Ps(n,r,t,e),Pe(r,2),Sc(r));break}}t=t.return}}function dc(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new tu;var i=new Set;r.set(t,i)}else void 0===(i=r.get(t))&&(i=new Set,r.set(t,i));i.has(n)||(cu=!0,i.add(n),e=fc.bind(null,e,t,n),t.then(e,e))}function fc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,ru===e&&(au&n)===n&&(4===fu||3===fu&&(62914560&au)===au&&300>te()-xu?0===(2&nu)&&Hu(e,0):mu|=n,vu===au&&(vu=0)),Sc(e)}function hc(e,t){0===t&&(t=ke()),null!==(e=Mr(e,t))&&(Pe(e,t),Sc(e))}function pc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),hc(e,n)}function mc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;null!==i&&(n=i.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(o(314))}null!==r&&r.delete(t),hc(e,n)}var gc=null,vc=null,yc=!1,bc=!1,wc=!1,xc=0;function Sc(e){e!==vc&&null===e.next&&(null===vc?gc=vc=e:vc=vc.next=e),bc=!0,yc||(yc=!0,dd(function(){0!==(6&nu)?G(re,Ec):Pc()}))}function kc(e,t){if(!wc&&bc){wc=!0;do{for(var n=!1,r=gc;null!==r;){if(!t)if(0!==e){var i=r.pendingLanes;if(0===i)var a=0;else{var o=r.suspendedLanes,s=r.pingedLanes;a=(1<<31-he(42|e)+1)-1,a=201326741&(a&=i&~(o&~s))?201326741&a|1:a?2|a:0}0!==a&&(n=!0,Fc(r,a))}else a=au,0===(3&(a=be(r,r===ru?a:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||we(r,a)||(n=!0,Fc(r,a));r=r.next}}while(n);wc=!1}}function Ec(){Pc()}function Pc(){bc=yc=!1;var e=0;0!==xc&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==sd&&(sd=e,!0);return sd=null,!1}()&&(e=xc),xc=0);for(var t=te(),n=null,r=gc;null!==r;){var i=r.next,a=Tc(r,t);0===a?(r.next=null,null===n?gc=i:n.next=i,null===i&&(vc=n)):(n=r,(0!==e||0!==(3&a))&&(bc=!0)),r=i}kc(e,!1)}function Tc(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,a=-62914561&e.pendingLanes;0<a;){var o=31-he(a),s=1<<o,l=i[o];-1===l?0!==(s&n)&&0===(s&r)||(i[o]=xe(s,t)):l<=t&&(e.expiredLanes|=s),a&=~s}if(n=au,n=be(e,e===(t=ru)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===ou||9===ou)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&Z(r),e.callbackNode=null,e.callbackPriority=0;if(0===(3&n)||we(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&Z(r),Ae(n)){case 2:case 8:n=ie;break;case 32:default:n=ae;break;case 268435456:n=se}return r=Cc.bind(null,e),n=G(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&Z(r),e.callbackPriority=2,e.callbackNode=null,2}function Cc(e,t){if(0!==Pu&&5!==Pu)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(sc()&&e.callbackNode!==n)return null;var r=au;return 0===(r=be(e,e===ru?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(_u(e,r,t),Tc(e,te()),null!=e.callbackNode&&e.callbackNode===n?Cc.bind(null,e):null)}function Fc(e,t){if(sc())return null;_u(e,t,!0)}function Ac(){return 0===xc&&(xc=Se()),xc}function Dc(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:At(""+e)}function Mc(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Lc=0;Lc<xr.length;Lc++){var Nc=xr[Lc];Sr(Nc.toLowerCase(),"on"+(Nc[0].toUpperCase()+Nc.slice(1)))}Sr(hr,"onAnimationEnd"),Sr(pr,"onAnimationIteration"),Sr(mr,"onAnimationStart"),Sr("dblclick","onDoubleClick"),Sr("focusin","onFocus"),Sr("focusout","onBlur"),Sr(gr,"onTransitionRun"),Sr(vr,"onTransitionStart"),Sr(yr,"onTransitionCancel"),Sr(br,"onTransitionEnd"),Qe("onMouseEnter",["mouseout","mouseover"]),Qe("onMouseLeave",["mouseout","mouseover"]),Qe("onPointerEnter",["pointerout","pointerover"]),Qe("onPointerLeave",["pointerout","pointerover"]),Ke("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ke("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ke("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ke("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ke("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ke("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Rc="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Oc=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Rc));function zc(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var o=r.length-1;0<=o;o--){var s=r[o],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==a&&i.isPropagationStopped())break e;a=s,i.currentTarget=u;try{a(i)}catch(c){vs(c)}i.currentTarget=null,a=l}else for(o=0;o<r.length;o++){if(l=(s=r[o]).instance,u=s.currentTarget,s=s.listener,l!==a&&i.isPropagationStopped())break e;a=s,i.currentTarget=u;try{a(i)}catch(c){vs(c)}i.currentTarget=null,a=l}}}}function _c(e,t){var n=t[Oe];void 0===n&&(n=t[Oe]=new Set);var r=e+"__bubble";n.has(r)||(Bc(t,e,2,!1),n.add(r))}function jc(e,t,n){var r=0;t&&(r|=4),Bc(n,e,r,t)}var Vc="_reactListening"+Math.random().toString(36).slice(2);function Ic(e){if(!e[Vc]){e[Vc]=!0,qe.forEach(function(t){"selectionchange"!==t&&(Oc.has(t)||jc(t,!1,e),jc(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Vc]||(t[Vc]=!0,jc("selectionchange",!1,t))}}function Bc(e,t,n,r){switch(cf(t)){case 2:var i=rf;break;case 8:i=af;break;default:i=of}n=i.bind(null,t,n,e),i=void 0,!Vt||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(i=!0),r?void 0!==i?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):void 0!==i?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Uc(e,t,n,r,i){var a=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var s=r.stateNode.containerInfo;if(s===i)break;if(4===o)for(o=r.return;null!==o;){var u=o.tag;if((3===u||4===u)&&o.stateNode.containerInfo===i)return;o=o.return}for(;null!==s;){if(null===(o=Be(s)))return;if(5===(u=o.tag)||6===u||26===u||27===u){r=a=o;continue e}s=s.parentNode}}r=r.return}zt(function(){var r=a,i=Mt(n),o=[];e:{var s=wr.get(e);if(void 0!==s){var u=Jt,c=e;switch(e){case"keypress":if(0===$t(n))break e;case"keydown":case"keyup":u=mn;break;case"focusin":c="focus",u=on;break;case"focusout":c="blur",u=on;break;case"beforeblur":case"afterblur":u=on;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=rn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=an;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=vn;break;case hr:case pr:case mr:u=sn;break;case br:u=yn;break;case"scroll":case"scrollend":u=tn;break;case"wheel":u=bn;break;case"copy":case"cut":case"paste":u=ln;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=gn;break;case"toggle":case"beforetoggle":u=wn}var d=0!==(4&t),f=!d&&("scroll"===e||"scrollend"===e),h=d?null!==s?s+"Capture":null:s;d=[];for(var p,m=r;null!==m;){var g=m;if(p=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===p||null===h||null!=(g=_t(m,h))&&d.push(Hc(m,g,p)),f)break;m=m.return}0<d.length&&(s=new u(s,c,null,n,i),o.push({event:s,listeners:d}))}}if(0===(7&t)){if(u="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===Dt||!(c=n.relatedTarget||n.fromElement)||!Be(c)&&!c[Re])&&(u||s)&&(s=i.window===i?i:(s=i.ownerDocument)?s.defaultView||s.parentWindow:window,u?(u=r,null!==(c=(c=n.relatedTarget||n.toElement)?Be(c):null)&&(f=l(c),d=c.tag,c!==f||5!==d&&27!==d&&6!==d)&&(c=null)):(u=null,c=r),u!==c)){if(d=rn,g="onMouseLeave",h="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(d=gn,g="onPointerLeave",h="onPointerEnter",m="pointer"),f=null==u?s:He(u),p=null==c?s:He(c),(s=new d(g,m+"leave",u,n,i)).target=f,s.relatedTarget=p,g=null,Be(i)===r&&((d=new d(h,m+"enter",c,n,i)).target=p,d.relatedTarget=f,g=d),f=g,u&&c)e:{for(h=c,m=0,p=d=u;p;p=$c(p))m++;for(p=0,g=h;g;g=$c(g))p++;for(;0<m-p;)d=$c(d),m--;for(;0<p-m;)h=$c(h),p--;for(;m--;){if(d===h||null!==h&&d===h.alternate)break e;d=$c(d),h=$c(h)}d=null}else d=null;null!==u&&qc(o,s,u,d,!1),null!==c&&null!==f&&qc(o,f,c,d,!0)}if("select"===(u=(s=r?He(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===u&&"file"===s.type)var v=jn;else if(Ln(s))if(Vn)v=Kn;else{v=qn;var y=$n}else!(u=s.nodeName)||"input"!==u.toLowerCase()||"checkbox"!==s.type&&"radio"!==s.type?r&&Tt(r.elementType)&&(v=jn):v=Yn;switch(v&&(v=v(e,r))?Nn(o,v,n,i):(y&&y(e,s,r),"focusout"===e&&r&&"number"===s.type&&null!=r.memoizedProps.value&&yt(s,"number",s.value)),y=r?He(r):window,e){case"focusin":(Ln(y)||"true"===y.contentEditable)&&(rr=y,ir=r,ar=null);break;case"focusout":ar=ir=rr=null;break;case"mousedown":or=!0;break;case"contextmenu":case"mouseup":case"dragend":or=!1,sr(o,n,i);break;case"selectionchange":if(nr)break;case"keydown":case"keyup":sr(o,n,i)}var b;if(Sn)e:{switch(e){case"compositionstart":var w="onCompositionStart";break e;case"compositionend":w="onCompositionEnd";break e;case"compositionupdate":w="onCompositionUpdate";break e}w=void 0}else Dn?Fn(e,n)&&(w="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(w="onCompositionStart");w&&(Pn&&"ko"!==n.locale&&(Dn||"onCompositionStart"!==w?"onCompositionEnd"===w&&Dn&&(b=Wt()):(Ut="value"in(Bt=i)?Bt.value:Bt.textContent,Dn=!0)),0<(y=Wc(r,w)).length&&(w=new un(w,e,null,n,i),o.push({event:w,listeners:y}),b?w.data=b:null!==(b=An(n))&&(w.data=b))),(b=En?function(e,t){switch(e){case"compositionend":return An(t);case"keypress":return 32!==t.which?null:(Cn=!0,Tn);case"textInput":return(e=t.data)===Tn&&Cn?null:e;default:return null}}(e,n):function(e,t){if(Dn)return"compositionend"===e||!Sn&&Fn(e,t)?(e=Wt(),Ht=Ut=Bt=null,Dn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Pn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(w=Wc(r,"onBeforeInput")).length&&(y=new un("onBeforeInput","beforeinput",null,n,i),o.push({event:y,listeners:w}),y.data=b)),function(e,t,n,r,i){if("submit"===t&&n&&n.stateNode===i){var a=Dc((i[Ne]||null).action),o=r.submitter;o&&null!==(t=(t=o[Ne]||null)?Dc(t.formAction):o.getAttribute("formAction"))&&(a=t,o=null);var s=new Jt("action","action",null,r,i);e.push({event:s,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==xc){var e=o?Mc(i,o):new FormData(i);Mo(n,{pending:!0,data:e,method:i.method,action:a},null,e)}}else"function"===typeof a&&(s.preventDefault(),e=o?Mc(i,o):new FormData(i),Mo(n,{pending:!0,data:e,method:i.method,action:a},a,e))},currentTarget:i}]})}}(o,e,r,n,i)}zc(o,t)})}function Hc(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Wc(e,t){for(var n=t+"Capture",r=[];null!==e;){var i=e,a=i.stateNode;if(5!==(i=i.tag)&&26!==i&&27!==i||null===a||(null!=(i=_t(e,n))&&r.unshift(Hc(e,i,a)),null!=(i=_t(e,t))&&r.push(Hc(e,i,a))),3===e.tag)return r;e=e.return}return[]}function $c(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function qc(e,t,n,r,i){for(var a=t._reactName,o=[];null!==n&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(s=s.tag,null!==l&&l===r)break;5!==s&&26!==s&&27!==s||null===u||(l=u,i?null!=(u=_t(n,a))&&o.unshift(Hc(n,u,l)):i||null!=(u=_t(n,a))&&o.push(Hc(n,u,l))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var Yc=/\r\n?/g,Kc=/\u0000|\uFFFD/g;function Qc(e){return("string"===typeof e?e:""+e).replace(Yc,"\n").replace(Kc,"")}function Xc(e,t){return t=Qc(t),Qc(e)===t}function Gc(){}function Zc(e,t,n,r,i,a){switch(n){case"children":"string"===typeof r?"body"===t||"textarea"===t&&""===r||St(e,r):("number"===typeof r||"bigint"===typeof r)&&"body"!==t&&St(e,""+r);break;case"className":nt(e,"class",r);break;case"tabIndex":nt(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":nt(e,n,r);break;case"style":Pt(e,r,a);break;case"data":if("object"!==t){nt(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"===typeof r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=At(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"===typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof a&&("formAction"===n?("input"!==t&&Zc(e,t,"name",i.name,i,null),Zc(e,t,"formEncType",i.formEncType,i,null),Zc(e,t,"formMethod",i.formMethod,i,null),Zc(e,t,"formTarget",i.formTarget,i,null)):(Zc(e,t,"encType",i.encType,i,null),Zc(e,t,"method",i.method,i,null),Zc(e,t,"target",i.target,i,null))),null==r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=At(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Gc);break;case"onScroll":null!=r&&_c("scroll",e);break;case"onScrollEnd":null!=r&&_c("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(o(61));if(null!=(n=r.__html)){if(null!=i.children)throw Error(o(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"muted":e.muted=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"===typeof r||"boolean"===typeof r||"symbol"===typeof r){e.removeAttribute("xlink:href");break}n=At(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!==typeof r&&"symbol"!==typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"===typeof r||"symbol"===typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":_c("beforetoggle",e),_c("toggle",e),tt(e,"popover",r);break;case"xlinkActuate":rt(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":rt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":rt(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":rt(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":rt(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":rt(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":rt(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":rt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":rt(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":tt(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&tt(e,n=Ct.get(n)||n,r)}}function Jc(e,t,n,r,i,a){switch(n){case"style":Pt(e,r,a);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(o(61));if(null!=(n=r.__html)){if(null!=i.children)throw Error(o(60));e.innerHTML=n}}break;case"children":"string"===typeof r?St(e,r):("number"===typeof r||"bigint"===typeof r)&&St(e,""+r);break;case"onScroll":null!=r&&_c("scroll",e);break;case"onScrollEnd":null!=r&&_c("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Gc);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Ye.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(i=n.endsWith("Capture"),t=n.slice(2,i?n.length-7:void 0),"function"===typeof(a=null!=(a=e[Ne]||null)?a[n]:null)&&e.removeEventListener(t,a,i),"function"!==typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):tt(e,n,r):("function"!==typeof a&&null!==a&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,i)))}}function ed(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":_c("error",e),_c("load",e);var r,i=!1,a=!1;for(r in n)if(n.hasOwnProperty(r)){var s=n[r];if(null!=s)switch(r){case"src":i=!0;break;case"srcSet":a=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Zc(e,t,r,s,n,null)}}return a&&Zc(e,t,"srcSet",n.srcSet,n,null),void(i&&Zc(e,t,"src",n.src,n,null));case"input":_c("invalid",e);var l=r=s=a=null,u=null,c=null;for(i in n)if(n.hasOwnProperty(i)){var d=n[i];if(null!=d)switch(i){case"name":a=d;break;case"type":s=d;break;case"checked":u=d;break;case"defaultChecked":c=d;break;case"value":r=d;break;case"defaultValue":l=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(o(137,t));break;default:Zc(e,t,i,d,n,null)}}return vt(e,r,l,u,c,s,a,!1),void dt(e);case"select":for(a in _c("invalid",e),i=s=r=null,n)if(n.hasOwnProperty(a)&&null!=(l=n[a]))switch(a){case"value":r=l;break;case"defaultValue":s=l;break;case"multiple":i=l;default:Zc(e,t,a,l,n,null)}return t=r,n=s,e.multiple=!!i,void(null!=t?bt(e,!!i,t,!1):null!=n&&bt(e,!!i,n,!0));case"textarea":for(s in _c("invalid",e),r=a=i=null,n)if(n.hasOwnProperty(s)&&null!=(l=n[s]))switch(s){case"value":i=l;break;case"defaultValue":a=l;break;case"children":r=l;break;case"dangerouslySetInnerHTML":if(null!=l)throw Error(o(91));break;default:Zc(e,t,s,l,n,null)}return xt(e,i,a,r),void dt(e);case"option":for(u in n)if(n.hasOwnProperty(u)&&null!=(i=n[u]))if("selected"===u)e.selected=i&&"function"!==typeof i&&"symbol"!==typeof i;else Zc(e,t,u,i,n,null);return;case"dialog":_c("beforetoggle",e),_c("toggle",e),_c("cancel",e),_c("close",e);break;case"iframe":case"object":_c("load",e);break;case"video":case"audio":for(i=0;i<Rc.length;i++)_c(Rc[i],e);break;case"image":_c("error",e),_c("load",e);break;case"details":_c("toggle",e);break;case"embed":case"source":case"link":_c("error",e),_c("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&null!=(i=n[c]))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Zc(e,t,c,i,n,null)}return;default:if(Tt(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(i=n[d])&&Jc(e,t,d,i,n,void 0));return}}for(l in n)n.hasOwnProperty(l)&&(null!=(i=n[l])&&Zc(e,t,l,i,n,null))}var td=null,nd=null;function rd(e){return 9===e.nodeType?e:e.ownerDocument}function id(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function ad(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function od(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"bigint"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var sd=null;var ld="function"===typeof setTimeout?setTimeout:void 0,ud="function"===typeof clearTimeout?clearTimeout:void 0,cd="function"===typeof Promise?Promise:void 0,dd="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof cd?function(e){return cd.resolve(null).then(e).catch(fd)}:ld;function fd(e){setTimeout(function(){throw e})}function hd(e){return"head"===e}function pd(e,t){var n=t,r=0,i=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0<r&&8>r){n=r;var o=e.ownerDocument;if(1&n&&xd(o.documentElement),2&n&&xd(o.body),4&n)for(xd(n=o.head),o=n.firstChild;o;){var s=o.nextSibling,l=o.nodeName;o[Ve]||"SCRIPT"===l||"STYLE"===l||"LINK"===l&&"stylesheet"===o.rel.toLowerCase()||n.removeChild(o),o=s}}if(0===i)return e.removeChild(a),void Ff(t);i--}else"$"===n||"$?"===n||"$!"===n?i++:r=n.charCodeAt(0)-48;else r=0;n=a}while(n);Ff(t)}function md(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":md(n),Ie(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function gd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function vd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var yd=null;function bd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function wd(e,t,n){switch(t=rd(n),e){case"html":if(!(e=t.documentElement))throw Error(o(452));return e;case"head":if(!(e=t.head))throw Error(o(453));return e;case"body":if(!(e=t.body))throw Error(o(454));return e;default:throw Error(o(451))}}function xd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Ie(e)}var Sd=new Map,kd=new Set;function Ed(e){return"function"===typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Pd=O.d;O.d={f:function(){var e=Pd.f(),t=Bu();return e||t},r:function(e){var t=Ue(e);null!==t&&5===t.tag&&"form"===t.type?No(t):Pd.r(e)},D:function(e){Pd.D(e),Cd("dns-prefetch",e,null)},C:function(e,t){Pd.C(e,t),Cd("preconnect",e,t)},L:function(e,t,n){Pd.L(e,t,n);var r=Td;if(r&&e&&t){var i='link[rel="preload"][as="'+mt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(i+='[imagesrcset="'+mt(n.imageSrcSet)+'"]',"string"===typeof n.imageSizes&&(i+='[imagesizes="'+mt(n.imageSizes)+'"]')):i+='[href="'+mt(e)+'"]';var a=i;switch(t){case"style":a=Ad(e);break;case"script":a=Ld(e)}Sd.has(a)||(e=f({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),Sd.set(a,e),null!==r.querySelector(i)||"style"===t&&r.querySelector(Dd(a))||"script"===t&&r.querySelector(Nd(a))||(ed(t=r.createElement("link"),"link",e),$e(t),r.head.appendChild(t)))}},m:function(e,t){Pd.m(e,t);var n=Td;if(n&&e){var r=t&&"string"===typeof t.as?t.as:"script",i='link[rel="modulepreload"][as="'+mt(r)+'"][href="'+mt(e)+'"]',a=i;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":a=Ld(e)}if(!Sd.has(a)&&(e=f({rel:"modulepreload",href:e},t),Sd.set(a,e),null===n.querySelector(i))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Nd(a)))return}ed(r=n.createElement("link"),"link",e),$e(r),n.head.appendChild(r)}}},X:function(e,t){Pd.X(e,t);var n=Td;if(n&&e){var r=We(n).hoistableScripts,i=Ld(e),a=r.get(i);a||((a=n.querySelector(Nd(i)))||(e=f({src:e,async:!0},t),(t=Sd.get(i))&&_d(e,t),$e(a=n.createElement("script")),ed(a,"link",e),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},r.set(i,a))}},S:function(e,t,n){Pd.S(e,t,n);var r=Td;if(r&&e){var i=We(r).hoistableStyles,a=Ad(e);t=t||"default";var o=i.get(a);if(!o){var s={loading:0,preload:null};if(o=r.querySelector(Dd(a)))s.loading=5;else{e=f({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Sd.get(a))&&zd(e,n);var l=o=r.createElement("link");$e(l),ed(l,"link",e),l._p=new Promise(function(e,t){l.onload=e,l.onerror=t}),l.addEventListener("load",function(){s.loading|=1}),l.addEventListener("error",function(){s.loading|=2}),s.loading|=4,Od(o,t,r)}o={type:"stylesheet",instance:o,count:1,state:s},i.set(a,o)}}},M:function(e,t){Pd.M(e,t);var n=Td;if(n&&e){var r=We(n).hoistableScripts,i=Ld(e),a=r.get(i);a||((a=n.querySelector(Nd(i)))||(e=f({src:e,async:!0,type:"module"},t),(t=Sd.get(i))&&_d(e,t),$e(a=n.createElement("script")),ed(a,"link",e),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},r.set(i,a))}}};var Td="undefined"===typeof document?null:document;function Cd(e,t,n){var r=Td;if(r&&"string"===typeof t&&t){var i=mt(t);i='link[rel="'+e+'"][href="'+i+'"]',"string"===typeof n&&(i+='[crossorigin="'+n+'"]'),kd.has(i)||(kd.add(i),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(i)&&(ed(t=r.createElement("link"),"link",e),$e(t),r.head.appendChild(t)))}}function Fd(e,t,n,r){var i,a,s,l,u=(u=W.current)?Ed(u):null;if(!u)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof n.precedence&&"string"===typeof n.href?(t=Ad(n.href),(r=(n=We(u).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"===typeof n.href&&"string"===typeof n.precedence){e=Ad(n.href);var c=We(u).hoistableStyles,d=c.get(e);if(d||(u=u.ownerDocument||u,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,d),(c=u.querySelector(Dd(e)))&&!c._p&&(d.instance=c,d.state.loading=5),Sd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Sd.set(e,n),c||(i=u,a=e,s=n,l=d.state,i.querySelector('link[rel="preload"][as="style"]['+a+"]")?l.loading=1:(a=i.createElement("link"),l.preload=a,a.addEventListener("load",function(){return l.loading|=1}),a.addEventListener("error",function(){return l.loading|=2}),ed(a,"link",s),$e(a),i.head.appendChild(a))))),t&&null===r)throw Error(o(528,""));return d}if(t&&null!==r)throw Error(o(529,""));return null;case"script":return t=n.async,"string"===typeof(n=n.src)&&t&&"function"!==typeof t&&"symbol"!==typeof t?(t=Ld(n),(r=(n=We(u).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function Ad(e){return'href="'+mt(e)+'"'}function Dd(e){return'link[rel="stylesheet"]['+e+"]"}function Md(e){return f({},e,{"data-precedence":e.precedence,precedence:null})}function Ld(e){return'[src="'+mt(e)+'"]'}function Nd(e){return"script[async]"+e}function Rd(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+mt(n.href)+'"]');if(r)return t.instance=r,$e(r),r;var i=f({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return $e(r=(e.ownerDocument||e).createElement("style")),ed(r,"style",i),Od(r,n.precedence,e),t.instance=r;case"stylesheet":i=Ad(n.href);var a=e.querySelector(Dd(i));if(a)return t.state.loading|=4,t.instance=a,$e(a),a;r=Md(n),(i=Sd.get(i))&&zd(r,i),$e(a=(e.ownerDocument||e).createElement("link"));var s=a;return s._p=new Promise(function(e,t){s.onload=e,s.onerror=t}),ed(a,"link",r),t.state.loading|=4,Od(a,n.precedence,e),t.instance=a;case"script":return a=Ld(n.src),(i=e.querySelector(Nd(a)))?(t.instance=i,$e(i),i):(r=n,(i=Sd.get(a))&&_d(r=f({},n),i),$e(i=(e=e.ownerDocument||e).createElement("script")),ed(i,"link",r),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(o(443,t.type))}else"stylesheet"===t.type&&0===(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,Od(r,n.precedence,e));return t.instance}function Od(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=r.length?r[r.length-1]:null,a=i,o=0;o<r.length;o++){var s=r[o];if(s.dataset.precedence===t)a=s;else if(a!==i)break}a?a.parentNode.insertBefore(e,a.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function zd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function _d(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var jd=null;function Vd(e,t,n){if(null===jd){var r=new Map,i=jd=new Map;i.set(n,r)}else(r=(i=jd).get(n))||(r=new Map,i.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),i=0;i<n.length;i++){var a=n[i];if(!(a[Ve]||a[Le]||"link"===e&&"stylesheet"===a.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==a.namespaceURI){var o=a.getAttribute(t)||"";o=e+o;var s=r.get(o);s?s.push(a):r.set(o,[a])}}return r}function Id(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Bd(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var Ud=null;function Hd(){}function Wd(){if(this.count--,0===this.count)if(this.stylesheets)qd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var $d=null;function qd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,$d=new Map,t.forEach(Yd,e),$d=null,Wd.call(e))}function Yd(e,t){if(!(4&t.state.loading)){var n=$d.get(e);if(n)var r=n.get(null);else{n=new Map,$d.set(e,n);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),a=0;a<i.length;a++){var o=i[a];"LINK"!==o.nodeName&&"not all"===o.getAttribute("media")||(n.set(o.dataset.precedence,o),r=o)}r&&n.set(null,r)}o=(i=t.instance).getAttribute("data-precedence"),(a=n.get(o)||r)===r&&n.set(null,i),n.set(o,i),this.count++,r=Wd.bind(this),i.addEventListener("load",r),i.addEventListener("error",r),a?a.parentNode.insertBefore(i,a.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(i,e.firstChild),t.state.loading|=4}}var Kd={$$typeof:x,Provider:null,Consumer:null,_currentValue:z,_currentValue2:z,_threadCount:0};function Qd(e,t,n,r,i,a,o,s){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Ee(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ee(0),this.hiddenUpdates=Ee(null),this.identifierPrefix=r,this.onUncaughtError=i,this.onCaughtError=a,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=s,this.incompleteTransitions=new Map}function Xd(e,t,n,r,i,a,o,s,l,u,c,d){return e=new Qd(e,t,n,o,s,l,u,d),t=1,!0===a&&(t|=24),a=zr(3,null,null,t),e.current=a,a.stateNode=e,(t=Ni()).refCount++,e.pooledCache=t,t.refCount++,a.memoizedState={element:r,isDehydrated:n,cache:t},na(a),e}function Gd(e){return e?e=Rr:Rr}function Zd(e,t,n,r,i,a){i=Gd(i),null===r.context?r.context=i:r.pendingContext=i,(r=ia(t)).payload={element:n},null!==(a=void 0===a?null:a)&&(r.callback=a),null!==(n=aa(e,r,t))&&(zu(n,0,t),oa(n,e,t))}function Jd(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function ef(e,t){Jd(e,t),(e=e.alternate)&&Jd(e,t)}function tf(e){if(13===e.tag){var t=Mr(e,67108864);null!==t&&zu(t,0,67108864),ef(e,67108864)}}var nf=!0;function rf(e,t,n,r){var i=R.T;R.T=null;var a=O.p;try{O.p=2,of(e,t,n,r)}finally{O.p=a,R.T=i}}function af(e,t,n,r){var i=R.T;R.T=null;var a=O.p;try{O.p=8,of(e,t,n,r)}finally{O.p=a,R.T=i}}function of(e,t,n,r){if(nf){var i=sf(r);if(null===i)Uc(e,t,r,lf,n),bf(e,r);else if(function(e,t,n,r,i){switch(t){case"focusin":return ff=wf(ff,e,t,n,r,i),!0;case"dragenter":return hf=wf(hf,e,t,n,r,i),!0;case"mouseover":return pf=wf(pf,e,t,n,r,i),!0;case"pointerover":var a=i.pointerId;return mf.set(a,wf(mf.get(a)||null,e,t,n,r,i)),!0;case"gotpointercapture":return a=i.pointerId,gf.set(a,wf(gf.get(a)||null,e,t,n,r,i)),!0}return!1}(i,e,t,n,r))r.stopPropagation();else if(bf(e,r),4&t&&-1<yf.indexOf(e)){for(;null!==i;){var a=Ue(i);if(null!==a)switch(a.tag){case 3:if((a=a.stateNode).current.memoizedState.isDehydrated){var o=ye(a.pendingLanes);if(0!==o){var s=a;for(s.pendingLanes|=2,s.entangledLanes|=2;o;){var l=1<<31-he(o);s.entanglements[1]|=l,o&=~l}Sc(a),0===(6&nu)&&(Su=te()+500,kc(0,!1))}}break;case 13:null!==(s=Mr(a,2))&&zu(s,0,2),Bu(),ef(a,2)}if(null===(a=sf(r))&&Uc(e,t,r,lf,n),a===i)break;i=a}null!==i&&r.stopPropagation()}else Uc(e,t,r,null,n)}}function sf(e){return uf(e=Mt(e))}var lf=null;function uf(e){if(lf=null,null!==(e=Be(e))){var t=l(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=u(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return lf=e,null}function cf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ne()){case re:return 2;case ie:return 8;case ae:case oe:return 32;case se:return 268435456;default:return 32}default:return 32}}var df=!1,ff=null,hf=null,pf=null,mf=new Map,gf=new Map,vf=[],yf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function bf(e,t){switch(e){case"focusin":case"focusout":ff=null;break;case"dragenter":case"dragleave":hf=null;break;case"mouseover":case"mouseout":pf=null;break;case"pointerover":case"pointerout":mf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":gf.delete(t.pointerId)}}function wf(e,t,n,r,i,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[i]},null!==t&&(null!==(t=Ue(t))&&tf(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==i&&-1===t.indexOf(i)&&t.push(i),e)}function xf(e){var t=Be(e.target);if(null!==t){var n=l(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=u(n)))return e.blockedOn=t,void function(e,t){var n=O.p;try{return O.p=e,t()}finally{O.p=n}}(e.priority,function(){if(13===n.tag){var e=Ru();e=Fe(e);var t=Mr(n,e);null!==t&&zu(t,0,e),ef(n,e)}})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Sf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=sf(e.nativeEvent);if(null!==n)return null!==(t=Ue(n))&&tf(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Dt=r,n.target.dispatchEvent(r),Dt=null,t.shift()}return!0}function kf(e,t,n){Sf(e)&&n.delete(t)}function Ef(){df=!1,null!==ff&&Sf(ff)&&(ff=null),null!==hf&&Sf(hf)&&(hf=null),null!==pf&&Sf(pf)&&(pf=null),mf.forEach(kf),gf.forEach(kf)}function Pf(e,t){e.blockedOn===t&&(e.blockedOn=null,df||(df=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Ef)))}var Tf=null;function Cf(e){Tf!==e&&(Tf=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){Tf===e&&(Tf=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],i=e[t+2];if("function"!==typeof r){if(null===uf(r||n))continue;break}var a=Ue(n);null!==a&&(e.splice(t,3),t-=3,Mo(a,{pending:!0,data:i,method:n.method,action:r},r,i))}}))}function Ff(e){function t(t){return Pf(t,e)}null!==ff&&Pf(ff,e),null!==hf&&Pf(hf,e),null!==pf&&Pf(pf,e),mf.forEach(t),gf.forEach(t);for(var n=0;n<vf.length;n++){var r=vf[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<vf.length&&null===(n=vf[0]).blockedOn;)xf(n),null===n.blockedOn&&vf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var i=n[r],a=n[r+1],o=i[Ne]||null;if("function"===typeof a)o||Cf(n);else if(o){var s=null;if(a&&a.hasAttribute("formAction")){if(i=a,o=a[Ne]||null)s=o.formAction;else if(null!==uf(i))continue}else s=o.action;"function"===typeof s?n[r+1]=s:(n.splice(r,3),r-=3),Cf(n)}}}function Af(e){this._internalRoot=e}function Df(e){this._internalRoot=e}Df.prototype.render=Af.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));Zd(t.current,Ru(),e,t,null,null)},Df.prototype.unmount=Af.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;Zd(e.current,2,null,e,null,null),Bu(),t[Re]=null}},Df.prototype.unstable_scheduleHydration=function(e){if(e){var t=De();e={blockedOn:null,target:e,priority:t};for(var n=0;n<vf.length&&0!==t&&t<vf[n].priority;n++);vf.splice(n,0,e),0===n&&xf(e)}};var Mf=i.version;if("19.1.0"!==Mf)throw Error(o(527,Mf,"19.1.0"));O.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=l(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(null===i)break;var a=i.alternate;if(null===a){if(null!==(r=i.return)){n=r;continue}break}if(i.child===a.child){for(a=i.child;a;){if(a===n)return c(i),e;if(a===r)return c(i),t;a=a.sibling}throw Error(o(188))}if(n.return!==r.return)n=i,r=a;else{for(var s=!1,u=i.child;u;){if(u===n){s=!0,n=i,r=a;break}if(u===r){s=!0,r=i,n=a;break}u=u.sibling}if(!s){for(u=a.child;u;){if(u===n){s=!0,n=a,r=i;break}if(u===r){s=!0,r=a,n=i;break}u=u.sibling}if(!s)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?d(e):null)?null:e.stateNode};var Lf={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:R,reconcilerVersion:"19.1.0"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Nf=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Nf.isDisabled&&Nf.supportsFiber)try{ce=Nf.inject(Lf),de=Nf}catch(Of){}}t.createRoot=function(e,t){if(!s(e))throw Error(o(299));var n=!1,r="",i=ys,a=bs,l=ws;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onUncaughtError&&(i=t.onUncaughtError),void 0!==t.onCaughtError&&(a=t.onCaughtError),void 0!==t.onRecoverableError&&(l=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Xd(e,1,!1,null,0,n,r,i,a,l,0,null),e[Re]=t.current,Ic(e),new Af(t)},t.hydrateRoot=function(e,t,n){if(!s(e))throw Error(o(299));var r=!1,i="",a=ys,l=bs,u=ws,c=null;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(i=n.identifierPrefix),void 0!==n.onUncaughtError&&(a=n.onUncaughtError),void 0!==n.onCaughtError&&(l=n.onCaughtError),void 0!==n.onRecoverableError&&(u=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(c=n.formState)),(t=Xd(e,1,!0,t,0,r,i,a,l,u,0,c)).context=Gd(null),n=t.current,(i=ia(r=Fe(r=Ru()))).callback=null,aa(n,i,r),n=r,t.current.lanes=n,Pe(t,n),Sc(t),e[Re]=t.current,Ic(e),new Df(t)},t.version="19.1.0"},43:(e,t,n)=>{e.exports=n(288)},288:(e,t)=>{var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),s=Symbol.for("react.consumer"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),h=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var w=b.prototype=new y;w.constructor=b,m(w,v.prototype),w.isPureReactComponent=!0;var x=Array.isArray,S={H:null,A:null,T:null,S:null,V:null},k=Object.prototype.hasOwnProperty;function E(e,t,r,i,a,o){return r=o.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:o}}function P(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var T=/\/+/g;function C(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function F(){}function A(e,t,i,a,o){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l,u,c=!1;if(null===e)c=!0;else switch(s){case"bigint":case"string":case"number":c=!0;break;case"object":switch(e.$$typeof){case n:case r:c=!0;break;case f:return A((c=e._init)(e._payload),t,i,a,o)}}if(c)return o=o(e),c=""===a?"."+C(e,0):a,x(o)?(i="",null!=c&&(i=c.replace(T,"$&/")+"/"),A(o,t,i,"",function(e){return e})):null!=o&&(P(o)&&(l=o,u=i+(null==o.key||e&&e.key===o.key?"":(""+o.key).replace(T,"$&/")+"/")+c,o=E(l.type,u,void 0,0,0,l.props)),t.push(o)),1;c=0;var d,p=""===a?".":a+":";if(x(e))for(var m=0;m<e.length;m++)c+=A(a=e[m],t,i,s=p+C(a,m),o);else if("function"===typeof(m=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=h&&d[h]||d["@@iterator"])?d:null))for(e=m.call(e),m=0;!(a=e.next()).done;)c+=A(a=a.value,t,i,s=p+C(a,m++),o);else if("object"===s){if("function"===typeof e.then)return A(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(F,F):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,i,a,o);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return c}function D(e,t,n){if(null==e)return e;var r=[],i=0;return A(e,r,"","",function(e){return t.call(n,e,i++)}),r}function M(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var L="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function N(){}t.Children={map:D,forEach:function(e,t,n){D(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return D(e,function(){t++}),t},toArray:function(e){return D(e,function(e){return e})||[]},only:function(e){if(!P(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=i,t.Profiler=o,t.PureComponent=b,t.StrictMode=a,t.Suspense=c,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=S,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return S.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=m({},e.props),i=e.key;if(null!=t)for(a in void 0!==t.ref&&void 0,void 0!==t.key&&(i=""+t.key),t)!k.call(t,a)||"key"===a||"__self"===a||"__source"===a||"ref"===a&&void 0===t.ref||(r[a]=t[a]);var a=arguments.length-2;if(1===a)r.children=n;else if(1<a){for(var o=Array(a),s=0;s<a;s++)o[s]=arguments[s+2];r.children=o}return E(e.type,i,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:s,_context:e},e},t.createElement=function(e,t,n){var r,i={},a=null;if(null!=t)for(r in void 0!==t.key&&(a=""+t.key),t)k.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(i[r]=t[r]);var o=arguments.length-2;if(1===o)i.children=n;else if(1<o){for(var s=Array(o),l=0;l<o;l++)s[l]=arguments[l+2];i.children=s}if(e&&e.defaultProps)for(r in o=e.defaultProps)void 0===i[r]&&(i[r]=o[r]);return E(e,a,void 0,0,0,i)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=P,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:M}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=S.T,n={};S.T=n;try{var r=e(),i=S.S;null!==i&&i(n,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(N,L)}catch(a){L(a)}finally{S.T=t}},t.unstable_useCacheRefresh=function(){return S.H.useCacheRefresh()},t.use=function(e){return S.H.use(e)},t.useActionState=function(e,t,n){return S.H.useActionState(e,t,n)},t.useCallback=function(e,t){return S.H.useCallback(e,t)},t.useContext=function(e){return S.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return S.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=S.H;if("function"===typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return S.H.useId()},t.useImperativeHandle=function(e,t,n){return S.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return S.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return S.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return S.H.useMemo(e,t)},t.useOptimistic=function(e,t){return S.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return S.H.useReducer(e,t,n)},t.useRef=function(e){return S.H.useRef(e)},t.useState=function(e){return S.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return S.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return S.H.useTransition()},t.version="19.1.0"},391:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(4)},579:(e,t,n)=>{e.exports=n(799)},672:(e,t,n)=>{var r=n(43);function i(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function a(){}var o={d:{f:a,r:function(){throw Error(i(522))},D:a,C:a,L:a,m:a,X:a,S:a,M:a},p:0,findDOMNode:null},s=Symbol.for("react.portal");var l=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function u(e,t){return"font"===e?"":"string"===typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(i(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:s,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=l.T,n=o.p;try{if(l.T=null,o.p=2,e)return e()}finally{l.T=t,o.p=n,o.d.f()}},t.preconnect=function(e,t){"string"===typeof e&&(t?t="string"===typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,o.d.C(e,t))},t.prefetchDNS=function(e){"string"===typeof e&&o.d.D(e)},t.preinit=function(e,t){if("string"===typeof e&&t&&"string"===typeof t.as){var n=t.as,r=u(n,t.crossOrigin),i="string"===typeof t.integrity?t.integrity:void 0,a="string"===typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?o.d.S(e,"string"===typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:i,fetchPriority:a}):"script"===n&&o.d.X(e,{crossOrigin:r,integrity:i,fetchPriority:a,nonce:"string"===typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"===typeof e)if("object"===typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=u(t.as,t.crossOrigin);o.d.M(e,{crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0})}}else null==t&&o.d.M(e)},t.preload=function(e,t){if("string"===typeof e&&"object"===typeof t&&null!==t&&"string"===typeof t.as){var n=t.as,r=u(n,t.crossOrigin);o.d.L(e,n,{crossOrigin:r,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0,type:"string"===typeof t.type?t.type:void 0,fetchPriority:"string"===typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"===typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"===typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"===typeof t.imageSizes?t.imageSizes:void 0,media:"string"===typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"===typeof e)if(t){var n=u(t.as,t.crossOrigin);o.d.m(e,{as:"string"===typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0})}else o.d.m(e)},t.requestFormReset=function(e){o.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return l.H.useFormState(e,t,n)},t.useFormStatus=function(){return l.H.useHostTransitionStatus()},t.version="19.1.0"},799:(e,t)=>{var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function i(e,t,r){var i=null;if(void 0!==r&&(i=""+r),void 0!==t.key&&(i=""+t.key),"key"in t)for(var a in r={},t)"key"!==a&&(r[a]=t[a]);else r=t;return t=r.ref,{$$typeof:n,type:e,key:i,ref:void 0!==t?t:null,props:r}}t.Fragment=r,t.jsx=i,t.jsxs=i},853:(e,t,n)=>{e.exports=n(896)},896:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,i=e[r];if(!(0<a(i,t)))break e;e[r]=t,e[n]=i,n=r}}function r(e){return 0===e.length?null:e[0]}function i(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,i=e.length,o=i>>>1;r<o;){var s=2*(r+1)-1,l=e[s],u=s+1,c=e[u];if(0>a(l,n))u<i&&0>a(c,l)?(e[r]=c,e[u]=n,r=u):(e[r]=l,e[s]=n,r=s);else{if(!(u<i&&0>a(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var u=[],c=[],d=1,f=null,h=3,p=!1,m=!1,g=!1,v=!1,y="function"===typeof setTimeout?setTimeout:null,b="function"===typeof clearTimeout?clearTimeout:null,w="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var t=r(c);null!==t;){if(null===t.callback)i(c);else{if(!(t.startTime<=e))break;i(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function S(e){if(g=!1,x(e),!m)if(null!==r(u))m=!0,E||(E=!0,k());else{var t=r(c);null!==t&&L(S,t.startTime-e)}}var k,E=!1,P=-1,T=5,C=-1;function F(){return!!v||!(t.unstable_now()-C<T)}function A(){if(v=!1,E){var e=t.unstable_now();C=e;var n=!0;try{e:{m=!1,g&&(g=!1,b(P),P=-1),p=!0;var a=h;try{t:{for(x(e),f=r(u);null!==f&&!(f.expirationTime>e&&F());){var o=f.callback;if("function"===typeof o){f.callback=null,h=f.priorityLevel;var s=o(f.expirationTime<=e);if(e=t.unstable_now(),"function"===typeof s){f.callback=s,x(e),n=!0;break t}f===r(u)&&i(u),x(e)}else i(u);f=r(u)}if(null!==f)n=!0;else{var l=r(c);null!==l&&L(S,l.startTime-e),n=!1}}break e}finally{f=null,h=a,p=!1}n=void 0}}finally{n?k():E=!1}}}if("function"===typeof w)k=function(){w(A)};else if("undefined"!==typeof MessageChannel){var D=new MessageChannel,M=D.port2;D.port1.onmessage=A,k=function(){M.postMessage(null)}}else k=function(){y(A,0)};function L(e,n){P=y(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):T=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_requestPaint=function(){v=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,i,a){var o=t.unstable_now();switch("object"===typeof a&&null!==a?a="number"===typeof(a=a.delay)&&0<a?o+a:o:a=o,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:d++,callback:i,priorityLevel:e,startTime:a,expirationTime:s=a+s,sortIndex:-1},a>o?(e.sortIndex=a,n(c,e),null===r(u)&&e===r(c)&&(g?(b(P),P=-1):g=!0,L(S,a-o))):(e.sortIndex=s,n(u,e),m||p||(m=!0,E||(E=!0,k()))),e},t.unstable_shouldYield=F,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(672)}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}n.m=e,n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,r)=>(n.f[r](e,t),t),[])),n.u=e=>"static/js/"+e+".ef022779.chunk.js",n.miniCssF=e=>{},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="mahjong-master-react:";n.l=(r,i,a,o)=>{if(e[r])e[r].push(i);else{var s,l;if(void 0!==a)for(var u=document.getElementsByTagName("script"),c=0;c<u.length;c++){var d=u[c];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+a){s=d;break}}s||(l=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,n.nc&&s.setAttribute("nonce",n.nc),s.setAttribute("data-webpack",t+a),s.src=r),e[r]=[i];var f=(t,n)=>{s.onerror=s.onload=null,clearTimeout(h);var i=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),i&&i.forEach(e=>e(n)),t)return t(n)},h=setTimeout(f.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=f.bind(null,s.onerror),s.onload=f.bind(null,s.onload),l&&document.head.appendChild(s)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/",(()=>{var e={792:0};n.f.j=(t,r)=>{var i=n.o(e,t)?e[t]:void 0;if(0!==i)if(i)r.push(i[2]);else{var a=new Promise((n,r)=>i=e[t]=[n,r]);r.push(i[2]=a);var o=n.p+n.u(t),s=new Error;n.l(o,r=>{if(n.o(e,t)&&(0!==(i=e[t])&&(e[t]=void 0),i)){var a=r&&("load"===r.type?"missing":r.type),o=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+a+": "+o+")",s.name="ChunkLoadError",s.type=a,s.request=o,i[1](s)}},"chunk-"+t,t)}};var t=(t,r)=>{var i,a,o=r[0],s=r[1],l=r[2],u=0;if(o.some(t=>0!==e[t])){for(i in s)n.o(s,i)&&(n.m[i]=s[i]);if(l)l(n)}for(t&&t(r);u<o.length;u++)a=o[u],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0},r=self.webpackChunkmahjong_master_react=self.webpackChunkmahjong_master_react||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var r=n(43),i=n(391),a=n(579);const o=(0,r.createContext)({});function s(e){const t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}const l="undefined"!==typeof window,u=l?r.useLayoutEffect:r.useEffect;function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function d(e){var t=function(e,t){if("object"!=c(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=c(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==c(t)?t:t+""}function f(e,t,n){return(t=d(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach(function(t){f(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}const m=(0,r.createContext)(null);function g(e){return"object"===typeof e&&null!==e}function v(e){return g(e)&&"offsetHeight"in e}const y=(0,r.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class b extends r.Component{getSnapshotBeforeUpdate(e){const t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){const e=t.offsetParent,n=v(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function w(e){let{children:t,isPresent:n,anchorX:i,root:o}=e;const s=(0,r.useId)(),l=(0,r.useRef)(null),u=(0,r.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:c}=(0,r.useContext)(y);return(0,r.useInsertionEffect)(()=>{const{width:e,height:t,top:r,left:a,right:d}=u.current;if(n||!l.current||!e||!t)return;const f="left"===i?"left: ".concat(a):"right: ".concat(d);l.current.dataset.motionPopId=s;const h=document.createElement("style");c&&(h.nonce=c);const p=null!==o&&void 0!==o?o:document.head;return p.appendChild(h),h.sheet&&h.sheet.insertRule('\n          [data-motion-pop-id="'.concat(s,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat(f,"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{p.removeChild(h),p.contains(h)&&p.removeChild(h)}},[n]),(0,a.jsx)(b,{isPresent:n,childRef:l,sizeRef:u,children:r.cloneElement(t,{ref:l})})}const x=e=>{let{children:t,initial:n,isPresent:i,onExitComplete:o,custom:l,presenceAffectsLayout:u,mode:c,anchorX:d,root:f}=e;const h=s(S),g=(0,r.useId)();let v=!0,y=(0,r.useMemo)(()=>(v=!1,{id:g,initial:n,isPresent:i,custom:l,onExitComplete:e=>{h.set(e,!0);for(const t of h.values())if(!t)return;o&&o()},register:e=>(h.set(e,!1),()=>h.delete(e))}),[i,h,o]);return u&&v&&(y=p({},y)),(0,r.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[i]),r.useEffect(()=>{!i&&!h.size&&o&&o()},[i]),"popLayout"===c&&(t=(0,a.jsx)(w,{isPresent:i,anchorX:d,root:f,children:t})),(0,a.jsx)(m.Provider,{value:y,children:t})};function S(){return new Map}function k(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const t=(0,r.useContext)(m);if(null===t)return[!0,null];const{isPresent:n,onExitComplete:i,register:a}=t,o=(0,r.useId)();(0,r.useEffect)(()=>{if(e)return a(o)},[e]);const s=(0,r.useCallback)(()=>e&&i&&i(o),[o,i,e]);return!n&&i?[!1,s]:[!0]}const E=e=>e.key||"";function P(e){const t=[];return r.Children.forEach(e,e=>{(0,r.isValidElement)(e)&&t.push(e)}),t}const T=e=>{let{children:t,custom:n,initial:i=!0,onExitComplete:l,presenceAffectsLayout:c=!0,mode:d="sync",propagate:f=!1,anchorX:h="left",root:p}=e;const[m,g]=k(f),v=(0,r.useMemo)(()=>P(t),[t]),y=f&&!m?[]:v.map(E),b=(0,r.useRef)(!0),w=(0,r.useRef)(v),S=s(()=>new Map),[T,C]=(0,r.useState)(v),[F,A]=(0,r.useState)(v);u(()=>{b.current=!1,w.current=v;for(let e=0;e<F.length;e++){const t=E(F[e]);y.includes(t)?S.delete(t):!0!==S.get(t)&&S.set(t,!1)}},[F,y.length,y.join("-")]);const D=[];if(v!==T){let e=[...v];for(let t=0;t<F.length;t++){const n=F[t],r=E(n);y.includes(r)||(e.splice(t,0,n),D.push(n))}return"wait"===d&&D.length&&(e=D),A(P(e)),C(v),null}const{forceRender:M}=(0,r.useContext)(o);return(0,a.jsx)(a.Fragment,{children:F.map(e=>{const t=E(e),r=!(f&&!m)&&(v===F||y.includes(t));return(0,a.jsx)(x,{isPresent:r,initial:!(b.current&&!i)&&void 0,custom:n,presenceAffectsLayout:c,mode:d,root:p,onExitComplete:r?void 0:()=>{if(!S.has(t))return;S.set(t,!0);let e=!0;S.forEach(t=>{t||(e=!1)}),e&&(null===M||void 0===M||M(),A(w.current),f&&(null===g||void 0===g||g()),l&&l())},anchorX:h,children:e},t)})})};function C(e){if("undefined"===typeof Proxy)return e;const t=new Map;return new Proxy(function(){return e(...arguments)},{get:(n,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}function F(e){return null!==e&&"object"===typeof e&&"function"===typeof e.start}function A(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function D(e){const t=[{},{}];return null===e||void 0===e||e.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function M(e,t,n,r){if("function"===typeof t){const[i,a]=D(r);t=t(void 0!==n?n:e.custom,i,a)}if("string"===typeof t&&(t=e.variants&&e.variants[t]),"function"===typeof t){const[i,a]=D(r);t=t(void 0!==n?n:e.custom,i,a)}return t}function L(e,t,n){const r=e.getProps();return M(r,t,void 0!==n?n:r.custom,e)}function N(e,t){var n,r;return null!==(n=null!==(r=null===e||void 0===e?void 0:e[t])&&void 0!==r?r:null===e||void 0===e?void 0:e.default)&&void 0!==n?n:e}const R=e=>e,O={},z=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],_={value:null,addProjectionMetrics:null};function j(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},a=()=>n=!0,o=z.reduce((e,n)=>(e[n]=function(e,t){let n=new Set,r=new Set,i=!1,a=!1;const o=new WeakSet;let s={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){o.has(t)&&(c.schedule(t),e()),l++,t(s)}const c={schedule:function(e){const t=arguments.length>2&&void 0!==arguments[2]&&arguments[2]&&i?n:r;return arguments.length>1&&void 0!==arguments[1]&&arguments[1]&&o.add(e),t.has(e)||t.add(e),e},cancel:e=>{r.delete(e),o.delete(e)},process:e=>{s=e,i?a=!0:(i=!0,[n,r]=[r,n],n.forEach(u),t&&_.value&&_.value.frameloop[t].push(l),l=0,n.clear(),i=!1,a&&(a=!1,c.process(e)))}};return c}(a,t?n:void 0),e),{}),{setup:s,read:l,resolveKeyframes:u,preUpdate:c,update:d,preRender:f,render:h,postRender:p}=o,m=()=>{const a=O.useManualTiming?i.timestamp:performance.now();n=!1,O.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(a-i.timestamp,40),1)),i.timestamp=a,i.isProcessing=!0,s.process(i),l.process(i),u.process(i),c.process(i),d.process(i),f.process(i),h.process(i),p.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(m))},g=z.reduce((t,a)=>{const s=o[a];return t[a]=function(t){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return n||(n=!0,r=!0,i.isProcessing||e(m)),s.schedule(t,a,o)},t},{});return{schedule:g,cancel:e=>{for(let t=0;t<z.length;t++)o[z[t]].cancel(e)},state:i,steps:o}}const{schedule:V,cancel:I,state:B,steps:U}=j("undefined"!==typeof requestAnimationFrame?requestAnimationFrame:R,!0),H=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],W=(()=>new Set(H))(),$=new Set(["width","height","top","left","right","bottom",...H]);function q(e,t){-1===e.indexOf(t)&&e.push(t)}function Y(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class K{constructor(){this.subscriptions=[]}add(e){return q(this.subscriptions,e),()=>Y(this.subscriptions,e)}notify(e,t,n){const r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){const r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function Q(e,t){return t?e*(1e3/t):0}let X;function G(){X=void 0}const Z={now:()=>(void 0===X&&Z.set(B.isProcessing||O.useManualTiming?B.timestamp:performance.now()),X),set:e=>{X=e,queueMicrotask(G)}},J={current:void 0};class ee{constructor(e){var t=this;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canTrackVelocity=null,this.events={},this.updateAndNotify=function(e){let n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const r=Z.now();var i,a;if((t.updatedAt!==r&&t.setPrevFrameValue(),t.prev=t.current,t.setCurrent(e),t.current!==t.prev)&&(null===(i=t.events.change)||void 0===i||i.notify(t.current),t.dependents))for(const o of t.dependents)o.dirty();n&&(null===(a=t.events.renderRequest)||void 0===a||a.notify(t.current))},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){var t;this.current=e,this.updatedAt=Z.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=(t=this.current,!isNaN(parseFloat(t))))}setPrevFrameValue(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.current;this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new K);const n=this.events[e].add(t);return"change"===e?()=>{n(),V.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var e;null===(e=this.events.change)||void 0===e||e.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return J.current&&J.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const e=Z.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;const t=Math.min(this.updatedAt-this.prevUpdatedAt,30);return Q(parseFloat(this.current)-parseFloat(this.prevFrameValue),t)}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var e,t;null===(e=this.dependents)||void 0===e||e.clear(),null===(t=this.events.destroy)||void 0===t||t.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function te(e,t){return new ee(e,t)}const ne=e=>Array.isArray(e),re=["transitionEnd","transition"];function ie(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,te(n))}function ae(e){return ne(e)?e[e.length-1]||0:e}const oe=e=>Boolean(e&&e.getVelocity);function se(e,t){const n=e.getValue("willChange");if(r=n,Boolean(oe(r)&&r.add))return n.add(t);if(!n&&O.WillChange){const n=new O.WillChange("auto");e.addValue("willChange",n),n.add(t)}var r}const le=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),ue="data-"+le("framerAppearId");function ce(e){return e.props[ue]}const de=(e,t)=>n=>t(e(n)),fe=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce(de)},he=(e,t,n)=>n>t?t:n<e?e:n,pe=e=>1e3*e,me=e=>e/1e3,ge={layout:0,mainThread:0,waapi:0};const ve=e=>t=>"string"===typeof t&&t.startsWith(e),ye=ve("--"),be=ve("var(--"),we=e=>!!be(e)&&xe.test(e.split("/*")[0].trim()),xe=/var\(--(?:[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*|[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*,(?:[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:(?![\t-\r \(\)\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uD800-\uDFFF\uFEFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])|[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*\((?:(?:(?![\(\)\uD800-\uDFFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])|\((?:(?![\(\)\uD800-\uDFFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])*\))*\))+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)\)$/i,Se={test:e=>"number"===typeof e,parse:parseFloat,transform:e=>e},ke=p(p({},Se),{},{transform:e=>he(0,1,e)}),Ee=p(p({},Se),{},{default:1}),Pe=e=>Math.round(1e5*e)/1e5,Te=/-?(?:[0-9]+(?:\.[0-9]+)?|\.[0-9]+)/g;const Ce=/^(?:#[0-9a-f]{3,8}|(?:rgb|h[s\u017F]l)a?\((?:-?[\.0-9]+%?[\t-\r ,\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+){2}-?[\.0-9]+%?[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:[,\/][\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)?(?:\b[0-9]+(?:\.[0-9]+)?|\.[0-9]+)?%?\))$/i,Fe=(e,t)=>n=>Boolean("string"===typeof n&&Ce.test(n)&&n.startsWith(e)||t&&!function(e){return null==e}(n)&&Object.prototype.hasOwnProperty.call(n,t)),Ae=(e,t,n)=>r=>{if("string"!==typeof r)return r;const[i,a,o,s]=r.match(Te);return{[e]:parseFloat(i),[t]:parseFloat(a),[n]:parseFloat(o),alpha:void 0!==s?parseFloat(s):1}},De=p(p({},Se),{},{transform:e=>Math.round((e=>he(0,255,e))(e))}),Me={test:Fe("rgb","red"),parse:Ae("red","green","blue"),transform:e=>{let{red:t,green:n,blue:r,alpha:i=1}=e;return"rgba("+De.transform(t)+", "+De.transform(n)+", "+De.transform(r)+", "+Pe(ke.transform(i))+")"}};const Le={test:Fe("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:Me.transform},Ne=e=>({test:t=>"string"===typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>"".concat(t).concat(e)}),Re=Ne("deg"),Oe=Ne("%"),ze=Ne("px"),_e=Ne("vh"),je=Ne("vw"),Ve=(()=>p(p({},Oe),{},{parse:e=>Oe.parse(e)/100,transform:e=>Oe.transform(100*e)}))(),Ie={test:Fe("hsl","hue"),parse:Ae("hue","saturation","lightness"),transform:e=>{let{hue:t,saturation:n,lightness:r,alpha:i=1}=e;return"hsla("+Math.round(t)+", "+Oe.transform(Pe(n))+", "+Oe.transform(Pe(r))+", "+Pe(ke.transform(i))+")"}},Be={test:e=>Me.test(e)||Le.test(e)||Ie.test(e),parse:e=>Me.test(e)?Me.parse(e):Ie.test(e)?Ie.parse(e):Le.parse(e),transform:e=>"string"===typeof e?e:e.hasOwnProperty("red")?Me.transform(e):Ie.transform(e),getAnimatableNone:e=>{const t=Be.parse(e);return t.alpha=0,Be.transform(t)}},Ue=/(?:#[0-9a-f]{3,8}|(?:rgb|h[s\u017F]l)a?\((?:-?[\.0-9]+%?[\t-\r ,\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+){2}-?[\.0-9]+%?[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:[,\/][\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)?(?:\b[0-9]+(?:\.[0-9]+)?|\.[0-9]+)?%?\))/gi;const He="number",We="color",$e=/var[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*\([\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*--(?:[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*|[\x2D0-9A-Z_a-z\u017F\u212A]+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*,(?:[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:(?![\t-\r \(\)\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uD800-\uDFFF\uFEFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])|[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*\((?:(?:(?![\(\)\uD800-\uDFFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])|\((?:(?![\(\)\uD800-\uDFFF])[^]|[\uD800-\uDBFF][\uDC00-\uDFFF])*\))*\))+[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)\)|#[0-9a-f]{3,8}|(?:rgb|h[s\u017F]l)a?\((?:-?[\.0-9]+%?[\t-\r ,\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+){2}-?[\.0-9]+%?[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*(?:[,\/][\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)?(?:\b[0-9]+(?:\.[0-9]+)?|\.[0-9]+)?%?\)|-?(?:[0-9]+(?:\.[0-9]+)?|\.[0-9]+)/gi;function qe(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[];let a=0;const o=t.replace($e,e=>(Be.test(e)?(r.color.push(a),i.push(We),n.push(Be.parse(e))):e.startsWith("var(")?(r.var.push(a),i.push("var"),n.push(e)):(r.number.push(a),i.push(He),n.push(parseFloat(e))),++a,"${}")).split("${}");return{values:n,split:o,indexes:r,types:i}}function Ye(e){return qe(e).values}function Ke(e){const{split:t,types:n}=qe(e),r=t.length;return e=>{let i="";for(let a=0;a<r;a++)if(i+=t[a],void 0!==e[a]){const t=n[a];i+=t===He?Pe(e[a]):t===We?Be.transform(e[a]):e[a]}return i}}const Qe=e=>"number"===typeof e?0:Be.test(e)?Be.getAnimatableNone(e):e;const Xe={test:function(e){var t,n;return isNaN(e)&&"string"===typeof e&&((null===(t=e.match(Te))||void 0===t?void 0:t.length)||0)+((null===(n=e.match(Ue))||void 0===n?void 0:n.length)||0)>0},parse:Ye,createTransformer:Ke,getAnimatableNone:function(e){const t=Ye(e);return Ke(e)(t.map(Qe))}};function Ge(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Ze(e,t){return n=>n>0?t:e}const Je=(e,t,n)=>e+(t-e)*n,et=(e,t,n)=>{const r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},tt=[Le,Me,Ie];function nt(e){const t=(n=e,tt.find(e=>e.test(n)));var n;if(Boolean(t),"'".concat(e,"' is not an animatable color. Use the equivalent color code instead."),!Boolean(t))return!1;let r=t.parse(e);return t===Ie&&(r=function(e){let{hue:t,saturation:n,lightness:r,alpha:i}=e;t/=360,n/=100,r/=100;let a=0,o=0,s=0;if(n){const e=r<.5?r*(1+n):r+n-r*n,i=2*r-e;a=Ge(i,e,t+1/3),o=Ge(i,e,t),s=Ge(i,e,t-1/3)}else a=o=s=r;return{red:Math.round(255*a),green:Math.round(255*o),blue:Math.round(255*s),alpha:i}}(r)),r}const rt=(e,t)=>{const n=nt(e),r=nt(t);if(!n||!r)return Ze(e,t);const i=p({},n);return e=>(i.red=et(n.red,r.red,e),i.green=et(n.green,r.green,e),i.blue=et(n.blue,r.blue,e),i.alpha=Je(n.alpha,r.alpha,e),Me.transform(i))},it=new Set(["none","hidden"]);function at(e,t){return n=>Je(e,t,n)}function ot(e){return"number"===typeof e?at:"string"===typeof e?we(e)?Ze:Be.test(e)?rt:ut:Array.isArray(e)?st:"object"===typeof e?Be.test(e)?rt:lt:Ze}function st(e,t){const n=[...e],r=n.length,i=e.map((e,n)=>ot(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function lt(e,t){const n=p(p({},e),t),r={};for(const i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=ot(e[i])(e[i],t[i]));return e=>{for(const t in r)n[t]=r[t](e);return n}}const ut=(e,t)=>{const n=Xe.createTransformer(t),r=qe(e),i=qe(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?it.has(e)&&!i.values.length||it.has(t)&&!r.values.length?function(e,t){return it.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):fe(st(function(e,t){const n=[],r={color:0,var:0,number:0};for(let a=0;a<t.values.length;a++){var i;const o=t.types[a],s=e.indexes[o][r[o]],l=null!==(i=e.values[s])&&void 0!==i?i:0;n[a]=l,r[o]++}return n}(r,i),i.values),n):("Complex values '".concat(e,"' and '").concat(t,"' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition."),Ze(e,t))};function ct(e,t,n){if("number"===typeof e&&"number"===typeof t&&"number"===typeof n)return Je(e,t,n);return ot(e)(e,t)}const dt=e=>{const t=t=>{let{timestamp:n}=t;return e(n)};return{start:function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return V.update(t,e)},stop:()=>I(t),now:()=>B.isProcessing?B.timestamp:Z.now()}},ft=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,r="";const i=Math.max(Math.round(t/n),2);for(let a=0;a<i;a++)r+=Math.round(1e4*e(a/(i-1)))/1e4+", ";return"linear(".concat(r.substring(0,r.length-2),")")},ht=2e4;function pt(e){let t=0;let n=e.next(t);for(;!n.done&&t<ht;)t+=50,n=e.next(t);return t>=ht?1/0:t}function mt(e,t,n){const r=Math.max(t-5,0);return Q(n-e(r),t-r)}const gt={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},vt=.001;function yt(e){let t,n,{duration:r=gt.duration,bounce:i=gt.bounce,velocity:a=gt.velocity,mass:o=gt.mass}=e;pe(gt.maxDuration);let s=1-i;s=he(gt.minDamping,gt.maxDamping,s),r=he(gt.minDuration,gt.maxDuration,me(r)),s<1?(t=e=>{const t=e*s,n=t*r,i=t-a,o=wt(e,s),l=Math.exp(-n);return vt-i/o*l},n=e=>{const n=e*s*r,i=n*a+a,o=Math.pow(s,2)*Math.pow(e,2)*r,l=Math.exp(-n),u=wt(Math.pow(e,2),s);return(-t(e)+vt>0?-1:1)*((i-o)*l)/u}):(t=e=>Math.exp(-e*r)*((e-a)*r+1)-.001,n=e=>Math.exp(-e*r)*(r*r*(a-e)));const l=function(e,t,n){let r=n;for(let i=1;i<bt;i++)r-=e(r)/t(r);return r}(t,n,5/r);if(r=pe(r),isNaN(l))return{stiffness:gt.stiffness,damping:gt.damping,duration:r};{const e=Math.pow(l,2)*o;return{stiffness:e,damping:2*s*Math.sqrt(o*e),duration:r}}}const bt=12;function wt(e,t){return e*Math.sqrt(1-t*t)}const xt=["duration","bounce"],St=["stiffness","damping","mass"];function kt(e,t){return t.some(t=>void 0!==e[t])}function Et(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:gt.visualDuration,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:gt.bounce;const n="object"!==typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:i}=n;const a=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],s={done:!1,value:a},{stiffness:l,damping:u,mass:c,duration:d,velocity:f,isResolvedFromDuration:h}=function(e){let t=p({velocity:gt.velocity,stiffness:gt.stiffness,damping:gt.damping,mass:gt.mass,isResolvedFromDuration:!1},e);if(!kt(e,St)&&kt(e,xt))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(1.2*n),i=r*r,a=2*he(.05,1,1-(e.bounce||0))*Math.sqrt(i);t=p(p({},t),{},{mass:gt.mass,stiffness:i,damping:a})}else{const n=yt(e);t=p(p(p({},t),n),{},{mass:gt.mass}),t.isResolvedFromDuration=!0}return t}(p(p({},n),{},{velocity:-me(n.velocity||0)})),m=f||0,g=u/(2*Math.sqrt(l*c)),v=o-a,y=me(Math.sqrt(l/c)),b=Math.abs(v)<5;let w;if(r||(r=b?gt.restSpeed.granular:gt.restSpeed.default),i||(i=b?gt.restDelta.granular:gt.restDelta.default),g<1){const e=wt(y,g);w=t=>{const n=Math.exp(-g*y*t);return o-n*((m+g*y*v)/e*Math.sin(e*t)+v*Math.cos(e*t))}}else if(1===g)w=e=>o-Math.exp(-y*e)*(v+(m+y*v)*e);else{const e=y*Math.sqrt(g*g-1);w=t=>{const n=Math.exp(-g*y*t),r=Math.min(e*t,300);return o-n*((m+g*y*v)*Math.sinh(r)+e*v*Math.cosh(r))/e}}const x={calculatedDuration:h&&d||null,next:e=>{const t=w(e);if(h)s.done=e>=d;else{let n=0===e?m:0;g<1&&(n=0===e?pe(m):mt(w,e,t));const a=Math.abs(n)<=r,l=Math.abs(o-t)<=i;s.done=a&&l}return s.value=s.done?o:t,s},toString:()=>{const e=Math.min(pt(x),ht),t=ft(t=>x.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return x}function Pt(e){let{keyframes:t,velocity:n=0,power:r=.8,timeConstant:i=325,bounceDamping:a=10,bounceStiffness:o=500,modifyTarget:s,min:l,max:u,restDelta:c=.5,restSpeed:d}=e;const f=t[0],h={done:!1,value:f},p=e=>void 0===l?u:void 0===u||Math.abs(l-e)<Math.abs(u-e)?l:u;let m=r*n;const g=f+m,v=void 0===s?g:s(g);v!==g&&(m=v-f);const y=e=>-m*Math.exp(-e/i),b=e=>v+y(e),w=e=>{const t=y(e),n=b(e);h.done=Math.abs(t)<=c,h.value=h.done?v:n};let x,S;const k=e=>{var t;(t=h.value,void 0!==l&&t<l||void 0!==u&&t>u)&&(x=e,S=Et({keyframes:[h.value,p(h.value)],velocity:mt(b,e,h.value),damping:a,stiffness:o,restDelta:c,restSpeed:d}))};return k(0),{calculatedDuration:null,next:e=>{let t=!1;return S||void 0!==x||(t=!0,w(e),k(e)),void 0!==x&&e>=x?S.next(e-x):(!t&&w(e),h)}}}Et.applyToOptions=e=>{const t=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;const n=(arguments.length>2?arguments[2]:void 0)(p(p({},e),{},{keyframes:[0,t]})),r=Math.min(pt(n),ht);return{type:"keyframes",ease:e=>n.next(r*e).value/t,duration:me(r)}}(e,100,Et);return e.ease=t.ease,e.duration=pe(t.duration),e.type="keyframes",e};const Tt=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function Ct(e,t,n,r){if(e===t&&n===r)return R;const i=t=>function(e,t,n,r,i){let a,o,s=0;do{o=t+(n-t)/2,a=Tt(o,r,i)-e,a>0?n=o:t=o}while(Math.abs(a)>1e-7&&++s<12);return o}(t,0,1,e,n);return e=>0===e||1===e?e:Tt(i(e),t,r)}const Ft=Ct(.42,0,1,1),At=Ct(0,0,.58,1),Dt=Ct(.42,0,.58,1),Mt=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Lt=e=>t=>1-e(1-t),Nt=Ct(.33,1.53,.69,.99),Rt=Lt(Nt),Ot=Mt(Rt),zt=e=>(e*=2)<1?.5*Rt(e):.5*(2-Math.pow(2,-10*(e-1))),_t=e=>1-Math.sin(Math.acos(e)),jt=Lt(_t),Vt=Mt(_t),It=e=>Array.isArray(e)&&"number"===typeof e[0],Bt={linear:R,easeIn:Ft,easeInOut:Dt,easeOut:At,circIn:_t,circInOut:Vt,circOut:jt,backIn:Rt,backInOut:Ot,backOut:Nt,anticipate:zt},Ut=e=>{if(It(e)){e.length;const[t,n,r,i]=e;return Ct(t,n,r,i)}return"string"===typeof e?("Invalid easing type '".concat(e,"'"),Bt[e]):e},Ht=(e,t,n)=>{const r=t-e;return 0===r?1:(n-e)/r};function Wt(e,t){let{clamp:n=!0,ease:r,mixer:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const a=e.length;if(t.length,1===a)return()=>t[0];if(2===a&&t[0]===t[1])return()=>t[1];const o=e[0]===e[1];e[0]>e[a-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=function(e,t,n){const r=[],i=n||O.mix||ct,a=e.length-1;for(let o=0;o<a;o++){let n=i(e[o],e[o+1]);if(t){const e=Array.isArray(t)?t[o]||R:t;n=fe(e,n)}r.push(n)}return r}(t,r,i),l=s.length,u=n=>{if(o&&n<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(n<e[r+1]);r++);const i=Ht(e[r],e[r+1],n);return s[r](i)};return n?t=>u(he(e[0],e[a-1],t)):u}function $t(e){const t=[0];return function(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=Ht(0,t,r);e.push(Je(n,1,i))}}(t,e.length-1),t}function qt(e){let{duration:t=300,keyframes:n,times:r,ease:i="easeInOut"}=e;const a=(e=>Array.isArray(e)&&"number"!==typeof e[0])(i)?i.map(Ut):Ut(i),o={done:!1,value:n[0]},s=function(e,t){return e.map(e=>e*t)}(r&&r.length===n.length?r:$t(n),t),l=Wt(s,n,{ease:Array.isArray(a)?a:(u=n,c=a,u.map(()=>c||Dt).splice(0,u.length-1))});var u,c;return{calculatedDuration:t,next:e=>(o.value=l(e),o.done=e>=t,o)}}const Yt=e=>null!==e;function Kt(e,t,n){let{repeat:r,repeatType:i="loop"}=t,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;const o=e.filter(Yt),s=a<0||r&&"loop"!==i&&r%2===1?0:o.length-1;return s&&void 0!==n?n:o[s]}const Qt={decay:Pt,inertia:Pt,tween:qt,keyframes:qt,spring:Et};function Xt(e){"string"===typeof e.type&&(e.type=Qt[e.type])}class Gt{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}const Zt=e=>e/100;class Jt extends Gt{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var e,t;const{motionValue:n}=this.options;n&&n.updatedAt!==Z.now()&&this.tick(Z.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),null===(e=(t=this.options).onStop)||void 0===e||e.call(t))},ge.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){const{options:e}=this;Xt(e);const{type:t=qt,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:a=0}=e;let{keyframes:o}=e;const s=t||qt;s!==qt&&"number"!==typeof o[0]&&(this.mixKeyframes=fe(Zt,ct(o[0],o[1])),o=[0,100]);const l=s(p(p({},e),{},{keyframes:o}));"mirror"===i&&(this.mirroredGenerator=s(p(p({},e),{},{keyframes:[...o].reverse(),velocity:-a}))),null===l.calculatedDuration&&(l.calculatedDuration=pt(l));const{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=l}updateTime(e){const t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:a,resolvedDuration:o,calculatedDuration:s}=this;if(null===this.startTime)return n.next(0);const{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:f,type:h,onUpdate:p,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);const g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let y=this.currentTime,b=n;if(c){const e=Math.min(this.currentTime,r)/o;let t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,t=Math.min(t,c+1);Boolean(t%2)&&("reverse"===d?(n=1-n,f&&(n-=f/o)):"mirror"===d&&(b=a)),y=he(0,1,n)*o}const w=v?{done:!1,value:u[0]}:b.next(y);i&&(w.value=i(w.value));let{done:x}=w;v||null===s||(x=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);const S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&x);return S&&h!==Pt&&(w.value=Kt(u,this.options,m,this.speed)),p&&p(w.value),S&&this.finish(),w}then(e,t){return this.finished.then(e,t)}get duration(){return me(this.calculatedDuration)}get time(){return me(this.currentTime)}set time(e){var t;e=pe(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),null===(t=this.driver)||void 0===t||t.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(Z.now());const t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=me(this.currentTime))}play(){var e,t;if(this.isStopped)return;const{driver:n=dt,startTime:r}=this.options;this.driver||(this.driver=n(e=>this.tick(e))),null===(e=(t=this.options).onPlay)||void 0===e||e.call(t);const i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=null!==r&&void 0!==r?r:i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(Z.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){var e,t;this.notifyFinished(),this.teardown(),this.state="finished",null===(e=(t=this.options).onComplete)||void 0===e||e.call(t)}cancel(){var e,t;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),null===(e=(t=this.options).onCancel)||void 0===e||e.call(t)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,ge.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){var t;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),null===(t=this.driver)||void 0===t||t.stop(),e.observe(this)}}const en=e=>180*e/Math.PI,tn=e=>{const t=en(Math.atan2(e[1],e[0]));return rn(t)},nn={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tn,rotateZ:tn,skewX:e=>en(Math.atan(e[1])),skewY:e=>en(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},rn=e=>((e%=360)<0&&(e+=360),e),an=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),on=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),sn={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:an,scaleY:on,scale:e=>(an(e)+on(e))/2,rotateX:e=>rn(en(Math.atan2(e[6],e[5]))),rotateY:e=>rn(en(Math.atan2(-e[2],e[0]))),rotateZ:tn,rotate:tn,skewX:e=>en(Math.atan(e[4])),skewY:e=>en(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function ln(e){return e.includes("scale")?1:0}function un(e,t){if(!e||"none"===e)return ln(t);const n=e.match(/^matrix3d\(([\t-\r ,-\.0-9e\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+)\)$/);let r,i;if(n)r=sn,i=n;else{const t=e.match(/^matrix\(([\t-\r ,-\.0-9e\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+)\)$/);r=nn,i=t}if(!i)return ln(t);const a=r[t],o=i[1].split(",").map(cn);return"function"===typeof a?a(o):o[a]}function cn(e){return parseFloat(e.trim())}const dn=e=>e===Se||e===ze,fn=new Set(["x","y","z"]),hn=H.filter(e=>!fn.has(e));const pn={width:(e,t)=>{let{x:n}=e,{paddingLeft:r="0",paddingRight:i="0"}=t;return n.max-n.min-parseFloat(r)-parseFloat(i)},height:(e,t)=>{let{y:n}=e,{paddingTop:r="0",paddingBottom:i="0"}=t;return n.max-n.min-parseFloat(r)-parseFloat(i)},top:(e,t)=>{let{top:n}=t;return parseFloat(n)},left:(e,t)=>{let{left:n}=t;return parseFloat(n)},bottom:(e,t)=>{let{y:n}=e,{top:r}=t;return parseFloat(r)+(n.max-n.min)},right:(e,t)=>{let{x:n}=e,{left:r}=t;return parseFloat(r)+(n.max-n.min)},x:(e,t)=>{let{transform:n}=t;return un(n,"x")},y:(e,t)=>{let{transform:n}=t;return un(n,"y")}};pn.translateX=pn.x,pn.translateY=pn.y;const mn=new Set;let gn=!1,vn=!1,yn=!1;function bn(){if(vn){const e=Array.from(mn).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{const t=function(e){const t=[];return hn.forEach(n=>{const r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();const t=n.get(e);t&&t.forEach(t=>{var n;let[r,i]=t;null===(n=e.getValue(r))||void 0===n||n.set(i)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}vn=!1,gn=!1,mn.forEach(e=>e.complete(yn)),mn.clear()}function wn(){mn.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(vn=!0)})}class xn{constructor(e,t,n,r,i){let a=arguments.length>5&&void 0!==arguments[5]&&arguments[5];this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=a}scheduleResolve(){this.state="scheduled",this.isAsync?(mn.add(this),gn||(gn=!0,V.read(wn),V.resolveKeyframes(bn))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;if(null===e[0]){const i=null===r||void 0===r?void 0:r.get(),a=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){const r=n.readValue(t,a);void 0!==r&&null!==r&&(e[0]=r)}void 0===e[0]&&(e[0]=a),r&&void 0===i&&r.set(e[0])}!function(e){for(let n=1;n<e.length;n++){var t;null!==(t=e[n])&&void 0!==t||(e[n]=e[n-1])}}(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),mn.delete(this)}cancel(){"scheduled"===this.state&&(mn.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}function Sn(e){let t;return()=>(void 0===t&&(t=e()),t)}const kn=Sn(()=>void 0!==window.ScrollTimeline),En={};function Pn(e,t){const n=Sn(e);return()=>{var e;return null!==(e=En[t])&&void 0!==e?e:n()}}const Tn=Pn(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),Cn=e=>{let[t,n,r,i]=e;return"cubic-bezier(".concat(t,", ").concat(n,", ").concat(r,", ").concat(i,")")},Fn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Cn([0,.65,.55,1]),circOut:Cn([.55,0,1,.45]),backIn:Cn([.31,.01,.66,-.59]),backOut:Cn([.33,1.53,.69,.99])};function An(e,t){return e?"function"===typeof e?Tn()?ft(e,t):"ease-out":It(e)?Cn(e):Array.isArray(e)?e.map(e=>An(e,t)||Fn.easeOut):Fn[e]:void 0}function Dn(e,t,n){let{delay:r=0,duration:i=300,repeat:a=0,repeatType:o="loop",ease:s="easeOut",times:l}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},u=arguments.length>4&&void 0!==arguments[4]?arguments[4]:void 0;const c={[t]:n};l&&(c.offset=l);const d=An(s,i);Array.isArray(d)&&(c.easing=d),_.value&&ge.waapi++;const f={delay:r,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:a+1,direction:"reverse"===o?"alternate":"normal"};u&&(f.pseudoElement=u);const h=e.animate(c,f);return _.value&&h.finished.finally(()=>{ge.waapi--}),h}function Mn(e){return"function"===typeof e&&"applyToOptions"in e}const Ln=["type"];class Nn extends Gt{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;const{element:t,name:n,keyframes:r,pseudoElement:i,allowFlatten:a=!1,finalKeyframe:o,onComplete:s}=e;this.isPseudoElement=Boolean(i),this.allowFlatten=a,this.options=e,e.type;const l=function(e){let{type:t}=e,n=A(e,Ln);return Mn(t)&&Tn()?t.applyToOptions(n):(null!==(r=n.duration)&&void 0!==r||(n.duration=300),null!==(i=n.ease)&&void 0!==i||(n.ease="easeOut"),n);var r,i}(e);this.animation=Dn(t,n,r,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){const e=Kt(r,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,n){(e=>e.startsWith("--"))(t)?e.style.setProperty(t,n):e.style[t]=n}(t,n,e),this.animation.cancel()}null===s||void 0===s||s(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){var e,t;null===(e=(t=this.animation).finish)||void 0===e||e.call(t)}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var e,t;this.isPseudoElement||(null===(e=(t=this.animation).commitStyles)||void 0===e||e.call(t))}get duration(){var e,t;const n=(null===(e=this.animation.effect)||void 0===e||null===(t=e.getComputedTiming)||void 0===t?void 0:t.call(e).duration)||0;return me(Number(n))}get time(){return me(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=pe(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline(e){let{timeline:t,observe:n}=e;var r;this.allowFlatten&&(null===(r=this.animation.effect)||void 0===r||r.updateTiming({easing:"linear"}));return this.animation.onfinish=null,t&&kn()?(this.animation.timeline=t,R):n(this)}}const Rn={anticipate:zt,backInOut:Ot,circInOut:Vt};function On(e){"string"===typeof e.ease&&e.ease in Rn&&(e.ease=Rn[e.ease])}const zn=["motionValue","onUpdate","onComplete","element"];class _n extends Nn{constructor(e){On(e),Xt(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){var t;const n=this.options,{motionValue:r,onUpdate:i,onComplete:a,element:o}=n,s=A(n,zn);if(!r)return;if(void 0!==e)return void r.set(e);const l=new Jt(p(p({},s),{},{autoplay:!1})),u=pe(null!==(t=this.finishedTime)&&void 0!==t?t:this.time);r.setWithVelocity(l.sample(u-10).value,l.sample(u).value,10),l.stop()}}const jn=(e,t)=>"zIndex"!==t&&(!("number"!==typeof e&&!Array.isArray(e))||!("string"!==typeof e||!Xe.test(e)&&"0"!==e||e.startsWith("url(")));const Vn=new Set(["opacity","clipPath","filter","transform"]),In=Sn(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));const Bn=["autoplay","delay","type","repeat","repeatDelay","repeatType","keyframes","name","motionValue","element"];class Un extends Gt{constructor(e){var t;let{autoplay:n=!0,delay:r=0,type:i="keyframes",repeat:a=0,repeatDelay:o=0,repeatType:s="loop",keyframes:l,name:u,motionValue:c,element:d}=e,f=A(e,Bn);super(),this.stop=()=>{var e,t;this._animation&&(this._animation.stop(),null===(t=this.stopTimeline)||void 0===t||t.call(this));null===(e=this.keyframeResolver)||void 0===e||e.cancel()},this.createdAt=Z.now();const h=p({autoplay:n,delay:r,type:i,repeat:a,repeatDelay:o,repeatType:s,name:u,motionValue:c,element:d},f),m=(null===d||void 0===d?void 0:d.KeyframeResolver)||xn;this.keyframeResolver=new m(l,(e,t,n)=>this.onKeyframesResolved(e,t,h,!n),u,c,d),null===(t=this.keyframeResolver)||void 0===t||t.scheduleResolve()}onKeyframesResolved(e,t,n,r){this.keyframeResolver=void 0;const{name:i,type:a,velocity:o,delay:s,isHandoff:l,onUpdate:u}=n;this.resolvedAt=Z.now(),function(e,t,n,r){const i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;const a=e[e.length-1],o=jn(i,t),s=jn(a,t);return"You are trying to animate ".concat(t,' from "').concat(i,'" to "').concat(a,'". ').concat(i," is not an animatable value - to enable this animation set ").concat(i," to a value animatable to ").concat(a," via the `style` property."),!(!o||!s)&&(function(e){const t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||Mn(n))&&r)}(e,i,a,o)||(!O.instantAnimations&&s||null===u||void 0===u||u(Kt(e,n,t)),e[0]=e[e.length-1],n.duration=0,n.repeat=0);const c=p(p({startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t},n),{},{keyframes:e}),d=!l&&function(e){var t;const{motionValue:n,name:r,repeatDelay:i,repeatType:a,damping:o,type:s}=e;if(!v(null===n||void 0===n||null===(t=n.owner)||void 0===t?void 0:t.current))return!1;const{onUpdate:l,transformTemplate:u}=n.owner.getProps();return In()&&r&&Vn.has(r)&&("transform"!==r||!u)&&!l&&!i&&"mirror"!==a&&0!==o&&"inertia"!==s}(c)?new _n(p(p({},c),{},{element:c.motionValue.owner.current})):new Jt(c);d.finished.then(()=>this.notifyFinished()).catch(R),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){var e;this._animation||(null===(e=this.keyframeResolver)||void 0===e||e.resume(),yn=!0,wn(),bn(),yn=!1);return this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var e;this._animation&&this.animation.cancel(),null===(e=this.keyframeResolver)||void 0===e||e.cancel()}}const Hn=e=>null!==e;const Wn={type:"spring",stiffness:500,damping:25,restSpeed:10},$n={type:"keyframes",duration:.8},qn={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Yn=(e,t)=>{let{keyframes:n}=t;return n.length>2?$n:W.has(e)?e.startsWith("scale")?{type:"spring",stiffness:550,damping:0===n[1]?2*Math.sqrt(550):30,restSpeed:10}:Wn:qn},Kn=["when","delay","delayChildren","staggerChildren","staggerDirection","repeat","repeatType","repeatDelay","from","elapsed"];const Qn=function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4?arguments[4]:void 0,a=arguments.length>5?arguments[5]:void 0;return o=>{const s=N(r,e)||{},l=s.delay||r.delay||0;let{elapsed:u=0}=r;u-=pe(l);const c=p(p({keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity()},s),{},{delay:-u,onUpdate:e=>{t.set(e),s.onUpdate&&s.onUpdate(e)},onComplete:()=>{o(),s.onComplete&&s.onComplete()},name:e,motionValue:t,element:a?void 0:i});(function(e){let{when:t,delay:n,delayChildren:r,staggerChildren:i,staggerDirection:a,repeat:o,repeatType:s,repeatDelay:l,from:u,elapsed:c}=e,d=A(e,Kn);return!!Object.keys(d).length})(s)||Object.assign(c,Yn(e,c)),c.duration&&(c.duration=pe(c.duration)),c.repeatDelay&&(c.repeatDelay=pe(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let d=!1;if((!1===c.type||0===c.duration&&!c.repeatDelay)&&(c.duration=0,0===c.delay&&(d=!0)),(O.instantAnimations||O.skipAnimations)&&(d=!0,c.duration=0,c.delay=0),c.allowFlatten=!s.type&&!s.ease,d&&!a&&void 0!==t.get()){const e=function(e,t,n){let{repeat:r,repeatType:i="loop"}=t;const a=e.filter(Hn),o=r&&"loop"!==i&&r%2===1?0:a.length-1;return o&&void 0!==n?n:a[o]}(c.keyframes,s);if(void 0!==e)return void V.update(()=>{c.onUpdate(e),c.onComplete()})}return s.isSync?new Jt(c):new Un(c)}},Xn=["transition","transitionEnd"];function Gn(e,t){let{protectedKeys:n,needsAnimating:r}=e;const i=n.hasOwnProperty(t)&&!0!==r[t];return r[t]=!1,i}function Zn(e,t){let{delay:n=0,transitionOverride:r,type:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{transition:a=e.getDefaultTransition(),transitionEnd:o}=t,s=A(t,Xn);r&&(a=r);const l=[],u=i&&e.animationState&&e.animationState.getState()[i];for(const d in s){var c;const t=e.getValue(d,null!==(c=e.latestValues[d])&&void 0!==c?c:null),r=s[d];if(void 0===r||u&&Gn(u,d))continue;const i=p({delay:n},N(a||{},d)),o=t.get();if(void 0!==o&&!t.isAnimating&&!Array.isArray(r)&&r===o&&!i.velocity)continue;let f=!1;if(window.MotionHandoffAnimation){const t=ce(e);if(t){const e=window.MotionHandoffAnimation(t,d,V);null!==e&&(i.startTime=e,f=!0)}}se(e,d),t.start(Qn(d,t,r,e.shouldReduceMotion&&$.has(d)?{type:!1}:i,e,f));const h=t.animation;h&&l.push(h)}return o&&Promise.all(l).then(()=>{V.update(()=>{o&&function(e,t){let n=L(e,t)||{},{transitionEnd:r={},transition:i={}}=n,a=A(n,re);a=p(p({},a),r);for(const o in a)ie(e,o,ae(a[o]))}(e,o)})}),l}function Jn(e,t){var n;let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=L(e,t,"exit"===r.type?null===(n=e.presenceContext)||void 0===n?void 0:n.custom:void 0);let{transition:a=e.getDefaultTransition()||{}}=i||{};r.transitionOverride&&(a=r.transitionOverride);const o=i?()=>Promise.all(Zn(e,i,r)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?function(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;const{delayChildren:i=0,staggerChildren:o,staggerDirection:s}=a;return function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,a=arguments.length>5?arguments[5]:void 0;const o=[],s=(e.variantChildren.size-1)*r,l=1===i?function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)*r}:function(){return s-(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)*r};return Array.from(e.variantChildren).sort(er).forEach((e,r)=>{e.notify("AnimationStart",t),o.push(Jn(e,t,p(p({},a),{},{delay:n+l(r)})).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(o)}(e,t,i+n,o,s,r)}:()=>Promise.resolve(),{when:l}=a;if(l){const[e,t]="beforeChildren"===l?[o,s]:[s,o];return e().then(()=>t())}return Promise.all([o(),s(r.delay)])}function er(e,t){return e.sortNodePosition(t)}function tr(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function nr(e){return"string"===typeof e||Array.isArray(e)}const rr=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ir=["initial",...rr],ar=ir.length;function or(e){if(!e)return;if(!e.isControllingVariants){const t=e.parent&&or(e.parent)||{};return void 0!==e.props.initial&&(t.initial=e.props.initial),t}const t={};for(let n=0;n<ar;n++){const r=ir[n],i=e.props[r];(nr(i)||!1===i)&&(t[r]=i)}return t}const sr=["transition","transitionEnd"],lr=[...rr].reverse(),ur=rr.length;function cr(e){return t=>Promise.all(t.map(t=>{let{animation:n,options:r}=t;return function(e,t){let n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e.notify("AnimationStart",t),Array.isArray(t)){const i=t.map(t=>Jn(e,t,r));n=Promise.all(i)}else if("string"===typeof t)n=Jn(e,t,r);else{const i="function"===typeof t?L(e,t,r.custom):t;n=Promise.all(Zn(e,i,r))}return n.then(()=>{e.notify("AnimationComplete",t)})}(e,n,r)}))}function dr(e){let t=cr(e),n=pr(),r=!0;const i=t=>(n,r)=>{var i;const a=L(e,r,"exit"===t?null===(i=e.presenceContext)||void 0===i?void 0:i.custom:void 0);if(a){const{transition:e,transitionEnd:t}=a,r=A(a,sr);n=p(p(p({},n),r),t)}return n};function a(a){const{props:o}=e,s=or(e.parent)||{},l=[],u=new Set;let c={},d=1/0;for(let t=0;t<ur;t++){const f=lr[t],h=n[f],m=void 0!==o[f]?o[f]:s[f],g=nr(m),v=f===a?h.isActive:null;!1===v&&(d=t);let y=m===s[f]&&m!==o[f]&&g;if(y&&r&&e.manuallyAnimateOnMount&&(y=!1),h.protectedKeys=p({},c),!h.isActive&&null===v||!m&&!h.prevProp||F(m)||"boolean"===typeof m)continue;const b=fr(h.prevProp,m);let w=b||f===a&&h.isActive&&!y&&g||t>d&&g,x=!1;const S=Array.isArray(m)?m:[m];let k=S.reduce(i(f),{});!1===v&&(k={});const{prevResolvedValues:E={}}=h,P=p(p({},E),k),T=t=>{w=!0,u.has(t)&&(x=!0,u.delete(t)),h.needsAnimating[t]=!0;const n=e.getValue(t);n&&(n.liveStyle=!1)};for(const e in P){const t=k[e],n=E[e];if(c.hasOwnProperty(e))continue;let r=!1;r=ne(t)&&ne(n)?!tr(t,n):t!==n,r?void 0!==t&&null!==t?T(e):u.add(e):void 0!==t&&u.has(e)?T(e):h.protectedKeys[e]=!0}h.prevProp=m,h.prevResolvedValues=k,h.isActive&&(c=p(p({},c),k)),r&&e.blockInitialAnimation&&(w=!1);w&&(!(y&&b)||x)&&l.push(...S.map(e=>({animation:e,options:{type:f}})))}if(u.size){const t={};if("boolean"!==typeof o.initial){const n=L(e,Array.isArray(o.initial)?o.initial[0]:o.initial);n&&n.transition&&(t.transition=n.transition)}u.forEach(n=>{const r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=null!==r&&void 0!==r?r:null}),l.push({animation:t})}let f=Boolean(l.length);return!r||!1!==o.initial&&o.initial!==o.animate||e.manuallyAnimateOnMount||(f=!1),r=!1,f?t(l):Promise.resolve()}return{animateChanges:a,setActive:function(t,r){var i;if(n[t].isActive===r)return Promise.resolve();null===(i=e.variantChildren)||void 0===i||i.forEach(e=>{var n;return null===(n=e.animationState)||void 0===n?void 0:n.setActive(t,r)}),n[t].isActive=r;const o=a(t);for(const e in n)n[e].protectedKeys={};return o},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=pr(),r=!0}}}function fr(e,t){return"string"===typeof t?t!==e:!!Array.isArray(t)&&!tr(t,e)}function hr(){return{isActive:arguments.length>0&&void 0!==arguments[0]&&arguments[0],protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function pr(){return{animate:hr(!0),whileInView:hr(),whileHover:hr(),whileTap:hr(),whileDrag:hr(),whileFocus:hr(),exit:hr()}}class mr{constructor(e){this.isMounted=!1,this.node=e}update(){}}let gr=0;const vr={animation:{Feature:class extends mr{constructor(e){super(e),e.animationState||(e.animationState=dr(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();F(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),null===(e=this.unmountControls)||void 0===e||e.call(this)}}},exit:{Feature:class extends mr{constructor(){super(...arguments),this.id=gr++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;const r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){const{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}}},yr={x:!1,y:!1};function br(){return yr.x||yr.y}function wr(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const xr=e=>"mouse"===e.pointerType?"number"!==typeof e.button||e.button<=0:!1!==e.isPrimary;function Sr(e){return{point:{x:e.pageX,y:e.pageY}}}function kr(e,t,n,r){return wr(e,t,(e=>t=>xr(t)&&e(t,Sr(t)))(n),r)}function Er(e){let{top:t,left:n,right:r,bottom:i}=e;return{x:{min:n,max:r},y:{min:t,max:i}}}function Pr(e){return e.max-e.min}function Tr(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;e.origin=r,e.originPoint=Je(t.min,t.max,e.origin),e.scale=Pr(n)/Pr(t),e.translate=Je(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function Cr(e,t,n,r){Tr(e.x,t.x,n.x,r?r.originX:void 0),Tr(e.y,t.y,n.y,r?r.originY:void 0)}function Fr(e,t,n){e.min=n.min+t.min,e.max=e.min+Pr(t)}function Ar(e,t,n){e.min=t.min-n.min,e.max=e.min+Pr(t)}function Dr(e,t,n){Ar(e.x,t.x,n.x),Ar(e.y,t.y,n.y)}const Mr=()=>({x:{min:0,max:0},y:{min:0,max:0}});function Lr(e){return[e("x"),e("y")]}function Nr(e){return void 0===e||1===e}function Rr(e){let{scale:t,scaleX:n,scaleY:r}=e;return!Nr(t)||!Nr(n)||!Nr(r)}function Or(e){return Rr(e)||zr(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function zr(e){return _r(e.x)||_r(e.y)}function _r(e){return e&&"0%"!==e}function jr(e,t,n){return n+t*(e-n)}function Vr(e,t,n,r,i){return void 0!==i&&(e=jr(e,i,r)),jr(e,n,r)+t}function Ir(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0;e.min=Vr(e.min,t,n,r,i),e.max=Vr(e.max,t,n,r,i)}function Br(e,t){let{x:n,y:r}=t;Ir(e.x,n.translate,n.scale,n.originPoint),Ir(e.y,r.translate,r.scale,r.originPoint)}const Ur=.999999999999,Hr=1.0000000000001;function Wr(e,t){e.min=e.min+t,e.max=e.max+t}function $r(e,t,n,r){let i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:.5;Ir(e,t,n,Je(e.min,e.max,i),r)}function qr(e,t){$r(e.x,t.x,t.scaleX,t.scale,t.originX),$r(e.y,t.y,t.scaleY,t.scale,t.originY)}function Yr(e,t){return Er(function(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}const Kr=e=>{let{current:t}=e;return t?t.ownerDocument.defaultView:null};function Qr(e){return e&&"object"===typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}const Xr=(e,t)=>Math.abs(e-t);class Gr{constructor(e,t){let{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:i=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const e=ei(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){const n=Xr(e.x,t.x),r=Xr(e.y,t.y);return Math.sqrt(n**2+r**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;const{point:r}=e,{timestamp:i}=B;this.history.push(p(p({},r),{},{timestamp:i}));const{onStart:a,onMove:o}=this.handlers;t||(a&&a(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=Zr(t,this.transformPagePoint),V.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();const{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const a=ei("pointercancel"===e.type?this.lastMoveEventInfo:Zr(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,a),r&&r(e,a)},!xr(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.contextWindow=r||window;const a=Zr(Sr(e),this.transformPagePoint),{point:o}=a,{timestamp:s}=B;this.history=[p(p({},o),{},{timestamp:s})];const{onSessionStart:l}=t;l&&l(e,ei(a,this.history)),this.removeListeners=fe(kr(this.contextWindow,"pointermove",this.handlePointerMove),kr(this.contextWindow,"pointerup",this.handlePointerUp),kr(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),I(this.updatePoint)}}function Zr(e,t){return t?{point:t(e.point)}:e}function Jr(e,t){return{x:e.x-t.x,y:e.y-t.y}}function ei(e,t){let{point:n}=e;return{point:n,delta:Jr(n,ni(t)),offset:Jr(n,ti(t)),velocity:ri(t,.1)}}function ti(e){return e[0]}function ni(e){return e[e.length-1]}function ri(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=ni(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>pe(t)));)n--;if(!r)return{x:0,y:0};const a=me(i.timestamp-r.timestamp);if(0===a)return{x:0,y:0};const o={x:(i.x-r.x)/a,y:(i.y-r.y)/a};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function ii(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function ai(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}const oi=.35;function si(e,t,n){return{min:li(e,t),max:li(e,n)}}function li(e,t){return"number"===typeof e?e:e[t]||0}const ui=new WeakMap;class ci{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=e}start(e){let{snapToCursor:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:r}=this.getProps();this.panSession=new Gr(e,{onSessionStart:e=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(Sr(e).point)},onStart:(e,t)=>{const{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(a=n)||"y"===a?yr[a]?null:(yr[a]=!0,()=>{yr[a]=!1}):yr.x||yr.y?null:(yr.x=yr.y=!0,()=>{yr.x=yr.y=!1}),!this.openDragLock))return;var a;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Lr(e=>{let t=this.getAxisMotionValue(e).get()||0;if(Oe.test(t)){const{projection:n}=this.visualElement;if(n&&n.layout){const r=n.layout.layoutBox[e];if(r){t=Pr(r)*(parseFloat(t)/100)}}}this.originPoint[e]=t}),i&&V.postRender(()=>i(e,t)),se(this.visualElement,"transform");const{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{const{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:a}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:o}=t;if(r&&null===this.currentDirection)return this.currentDirection=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,n=null;Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x");return n}(o),void(null!==this.currentDirection&&i&&i(this.currentDirection));this.updateAxis("x",t.point,o),this.updateAxis("y",t.point,o),this.visualElement.render(),a&&a(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>Lr(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:Kr(this.visualElement)})}stop(e,t){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:r}=t;this.startAnimation(r);const{onDragEnd:i}=this.getProps();i&&V.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;const{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){const{drag:r}=this.getProps();if(!n||!di(e,r,this.currentDirection))return;const i=this.getAxisMotionValue(e);let a=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(a=function(e,t,n){let{min:r,max:i}=t;return void 0!==r&&e<r?e=n?Je(r,e,n.min):Math.max(e,r):void 0!==i&&e>i&&(e=n?Je(i,e,n.max):Math.min(e,i)),e}(a,this.constraints[e],this.elastic[e])),i.set(a)}resolveConstraints(){var e;const{dragConstraints:t,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,i=this.constraints;t&&Qr(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!r)&&function(e,t){let{top:n,left:r,bottom:i,right:a}=t;return{x:ii(e.x,r,a),y:ii(e.y,n,i)}}(r.layoutBox,t),this.elastic=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:oi;return!1===e?e=0:!0===e&&(e=oi),{x:si(e,"left","right"),y:si(e,"top","bottom")}}(n),i!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&Lr(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){const n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:t}=this.getProps();if(!e||!Qr(e))return!1;const n=e.current,{projection:r}=this.visualElement;if(!r||!r.layout)return!1;const i=function(e,t,n){const r=Yr(e,n),{scroll:i}=t;return i&&(Wr(r.x,i.offset.x),Wr(r.y,i.offset.y)),r}(n,r.root,this.visualElement.getTransformPagePoint());let a=function(e,t){return{x:ai(e.x,t.x),y:ai(e.y,t.y)}}(r.layout.layoutBox,i);if(t){const e=t(function(e){let{x:t,y:n}=e;return{top:n.min,right:t.max,bottom:n.max,left:t.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=Er(e))}return a}startAnimation(e){const{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:a,onDragTransitionEnd:o}=this.getProps(),s=this.constraints||{},l=Lr(o=>{if(!di(o,t,this.currentDirection))return;let l=s&&s[o]||{};a&&(l={min:0,max:0});const u=r?200:1e6,c=r?40:1e7,d=p(p({type:"inertia",velocity:n?e[o]:0,bounceStiffness:u,bounceDamping:c,timeConstant:750,restDelta:1,restSpeed:10},i),l);return this.startAxisValueAnimation(o,d)});return Promise.all(l).then(o)}startAxisValueAnimation(e,t){const n=this.getAxisMotionValue(e);return se(this.visualElement,e),n.start(Qn(e,n,0,t,this.visualElement,!1))}stopAnimation(){Lr(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){Lr(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){const t="_drag".concat(e.toUpperCase()),n=this.visualElement.getProps(),r=n[t];return r||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){Lr(t=>{const{drag:n}=this.getProps();if(!di(t,n,this.currentDirection))return;const{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){const{min:n,max:a}=r.layout.layoutBox[t];i.set(e[t]-Je(n,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!Qr(t)||!n||!this.constraints)return;this.stopAnimation();const r={x:0,y:0};Lr(e=>{const t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){const n=t.get();r[e]=function(e,t){let n=.5;const r=Pr(e),i=Pr(t);return i>r?n=Ht(t.min,t.max-r,e.min):r>i&&(n=Ht(e.min,e.max-i,t.min)),he(0,1,n)}({min:n,max:n},this.constraints[e])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Lr(t=>{if(!di(t,e,null))return;const n=this.getAxisMotionValue(t),{min:i,max:a}=this.constraints[t];n.set(Je(i,a,r[t]))})}addListeners(){if(!this.visualElement.current)return;ui.set(this.visualElement,this);const e=kr(this.visualElement.current,"pointerdown",e=>{const{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{const{dragConstraints:e}=this.getProps();Qr(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),V.read(t);const i=wr(window,"resize",()=>this.scalePositionWithinConstraints()),a=n.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:n}=e;this.isDragging&&n&&(Lr(e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:a=oi,dragMomentum:o=!0}=e;return p(p({},e),{},{drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:a,dragMomentum:o})}}function di(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}const fi=e=>(t,n)=>{e&&V.postRender(()=>e(t,n))};const{schedule:hi,cancel:pi}=j(queueMicrotask,!1),mi=(0,r.createContext)({}),gi={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function vi(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const yi={correct:(e,t)=>{if(!t.target)return e;if("string"===typeof e){if(!ze.test(e))return e;e=parseFloat(e)}const n=vi(e,t.target.x),r=vi(e,t.target.y);return"".concat(n,"% ").concat(r,"%")}},bi={correct:(e,t)=>{let{treeScale:n,projectionDelta:r}=t;const i=e,a=Xe.parse(e);if(a.length>5)return i;const o=Xe.createTransformer(e),s="number"!==typeof a[0]?1:0,l=r.x.scale*n.x,u=r.y.scale*n.y;a[0+s]/=l,a[1+s]/=u;const c=Je(l,u,.5);return"number"===typeof a[2+s]&&(a[2+s]/=c),"number"===typeof a[3+s]&&(a[3+s]/=c),o(a)}},wi={};class xi extends r.Component{componentDidMount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;!function(e){for(const t in e)wi[t]=e[t],ye(t)&&(wi[t].isCSSVariable=!0)}(ki),i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions(p(p({},i.options),{},{onExitComplete:()=>this.safeToRemove()}))),gi.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,{projection:a}=n;return a?(a.isPresent=i,r||e.layoutDependency!==t||void 0===t||e.isPresent!==i?a.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?a.promote():a.relegate()||V.postRender(()=>{const e=a.getStack();e&&e.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),hi.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Si(e){const[t,n]=k(),i=(0,r.useContext)(o);return(0,a.jsx)(xi,p(p({},e),{},{layoutGroup:i,switchLayoutGroup:(0,r.useContext)(mi),isPresent:t,safeToRemove:n}))}const ki={borderRadius:p(p({},yi),{},{applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]}),borderTopLeftRadius:yi,borderTopRightRadius:yi,borderBottomLeftRadius:yi,borderBottomRightRadius:yi,boxShadow:bi};function Ei(e){return g(e)&&"ownerSVGElement"in e}const Pi=(e,t)=>e.depth-t.depth;class Ti{constructor(){this.children=[],this.isDirty=!1}add(e){q(this.children,e),this.isDirty=!0}remove(e){Y(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Pi),this.isDirty=!1,this.children.forEach(e)}}function Ci(e,t){const n=Z.now(),r=i=>{let{timestamp:a}=i;const o=a-n;o>=t&&(I(r),e(o-t))};return V.setup(r,!0),()=>I(r)}function Fi(e){return oe(e)?e.get():e}const Ai=["TopLeft","TopRight","BottomLeft","BottomRight"],Di=Ai.length,Mi=e=>"string"===typeof e?parseFloat(e):e,Li=e=>"number"===typeof e||ze.test(e);function Ni(e,t){return void 0!==e[t]?e[t]:e.borderRadius}const Ri=zi(0,.5,jt),Oi=zi(.5,.95,R);function zi(e,t,n){return r=>r<e?0:r>t?1:n(Ht(e,t,r))}function _i(e,t){e.min=t.min,e.max=t.max}function ji(e,t){_i(e.x,t.x),_i(e.y,t.y)}function Vi(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Ii(e,t,n,r,i){return e=jr(e-=t,1/n,r),void 0!==i&&(e=jr(e,1/i,r)),e}function Bi(e,t,n,r,i){let[a,o,s]=n;!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,i=arguments.length>4?arguments[4]:void 0,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:e;Oe.test(t)&&(t=parseFloat(t),t=Je(o.min,o.max,t/100)-o.min);if("number"!==typeof t)return;let s=Je(a.min,a.max,r);e===a&&(s-=t),e.min=Ii(e.min,t,n,s,i),e.max=Ii(e.max,t,n,s,i)}(e,t[a],t[o],t[s],t.scale,r,i)}const Ui=["x","scaleX","originX"],Hi=["y","scaleY","originY"];function Wi(e,t,n,r){Bi(e.x,t,Ui,n?n.x:void 0,r?r.x:void 0),Bi(e.y,t,Hi,n?n.y:void 0,r?r.y:void 0)}function $i(e){return 0===e.translate&&1===e.scale}function qi(e){return $i(e.x)&&$i(e.y)}function Yi(e,t){return e.min===t.min&&e.max===t.max}function Ki(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function Qi(e,t){return Ki(e.x,t.x)&&Ki(e.y,t.y)}function Xi(e){return Pr(e.x)/Pr(e.y)}function Gi(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class Zi{constructor(){this.members=[]}add(e){q(this.members,e),e.scheduleRender()}remove(e){if(Y(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){const t=this.members.findIndex(t=>e===t);if(0===t)return!1;let n;for(let r=t;r>=0;r--){const e=this.members[r];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(e,t){const n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const Ji={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},ea=["","X","Y","Z"],ta={visibility:"hidden"};let na=0;function ra(e,t,n,r){const{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function ia(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=ce(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:t,layoutId:r}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",V,!(t||r))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&ia(r)}function aa(e){let{attachResizeListener:t,defaultParent:n,measureScroll:r,checkIsScrollRoot:i,resetTransform:a}=e;return class{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===n||void 0===n?void 0:n();this.id=na++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,_.value&&(Ji.nodes=Ji.calculatedTargetDeltas=Ji.calculatedProjections=0),this.nodes.forEach(la),this.nodes.forEach(ma),this.nodes.forEach(ga),this.nodes.forEach(ua),_.addProjectionMetrics&&_.addProjectionMetrics(Ji)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=t?t.root||t:this,this.path=t?[...t.path,t]:[],this.parent=t,this.depth=t?t.depth+1:0;for(let n=0;n<this.path.length;n++)this.path[n].shouldResetTransform=!0;this.root===this&&(this.nodes=new Ti)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new K),this.eventHandlers.get(e).add(t)}notifyListeners(e){const t=this.eventHandlers.get(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];t&&t.notify(...r)}hasListeners(e){return this.eventHandlers.has(e)}mount(e){if(this.instance)return;var n;this.isSVG=Ei(e)&&!(Ei(n=e)&&"svg"===n.tagName),this.instance=e;const{layoutId:r,layout:i,visualElement:a}=this.options;if(a&&!a.current&&a.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(i||r)&&(this.isLayoutDirty=!0),t){let n;const r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=Ci(r,250),gi.hasAnimatedSinceResize&&(gi.hasAnimatedSinceResize=!1,this.nodes.forEach(pa))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&a&&(r||i)&&this.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:n,hasRelativeLayoutChanged:r,layout:i}=e;if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const o=this.options.transition||a.getDefaultTransition()||Sa,{onLayoutAnimationStart:s,onLayoutAnimationComplete:l}=a.getProps(),u=!this.targetLayout||!Qi(this.targetLayout,i),c=!n&&r;if(this.options.layoutRoot||this.resumeFrom||c||n&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const e=p(p({},N(o,"layout")),{},{onPlay:s,onComplete:l});(a.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,c)}else n||pa(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),I(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(va),this.animationId++)}getTransformTemplate(){const{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&ia(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let i=0;i<this.path.length;i++){const e=this.path[i];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;const r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(da);if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(fa);this.isUpdating||this.nodes.forEach(fa),this.animationCommitId=this.animationId,this.isUpdating=!1,this.nodes.forEach(ha),this.nodes.forEach(oa),this.nodes.forEach(sa),this.clearAllSnapshots();const e=Z.now();B.delta=he(0,1e3/60,e-B.timestamp),B.timestamp=e,B.isProcessing=!0,U.update.process(B),U.preRender.process(B),U.render.process(B),B.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,hi.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(ca),this.sharedNodes.forEach(ya)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,V.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){V.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||Pr(this.snapshot.measuredBox.x)||Pr(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),(!this.options.alwaysMeasureLayout||!this.isLead())&&!this.isLayoutDirty)return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const e=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",t=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){const t=i(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!a)return;const e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!qi(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,i=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||Or(this.latestValues)||i)&&(a(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const t=this.measurePageBox();let n=this.removeElementScroll(t);var r;return e&&(n=this.removeTransform(n)),Pa((r=n).x),Pa(r.y),{animationId:this.root.animationId,measuredBox:t,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var e;const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const n=t.measureViewportBox();if(!((null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)||this.path.some(Ca))){const{scroll:e}=this.root;e&&(Wr(n.x,e.offset.x),Wr(n.y,e.offset.y))}return n}removeElementScroll(e){var t;const n={x:{min:0,max:0},y:{min:0,max:0}};if(ji(n,e),null!==(t=this.scroll)&&void 0!==t&&t.wasRoot)return n;for(let r=0;r<this.path.length;r++){const t=this.path[r],{scroll:i,options:a}=t;t!==this.root&&i&&a.layoutScroll&&(i.wasRoot&&ji(n,e),Wr(n.x,i.offset.x),Wr(n.y,i.offset.y))}return n}applyTransform(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n={x:{min:0,max:0},y:{min:0,max:0}};ji(n,e);for(let r=0;r<this.path.length;r++){const e=this.path[r];!t&&e.options.layoutScroll&&e.scroll&&e!==e.root&&qr(n,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),Or(e.latestValues)&&qr(n,e.latestValues)}return Or(this.latestValues)&&qr(n,this.latestValues),n}removeTransform(e){const t={x:{min:0,max:0},y:{min:0,max:0}};ji(t,e);for(let n=0;n<this.path.length;n++){const e=this.path[n];if(!e.instance)continue;if(!Or(e.latestValues))continue;Rr(e.latestValues)&&e.updateSnapshot();const r=Mr();ji(r,e.measurePageBox()),Wi(t,e.latestValues,e.snapshot?e.snapshot.layoutBox:void 0,r)}return Or(this.latestValues)&&Wi(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options=p(p(p({},this.options),e),{},{crossfade:void 0===e.crossfade||e.crossfade})}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==B.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(){var e;let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const r=Boolean(this.resumingFrom)||this!==n;if(!(t||r&&this.isSharedProjectionDirty||this.isProjectionDirty||null!==(e=this.parent)&&void 0!==e&&e.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:i,layoutId:a}=this.options;if(this.layout&&(i||a)){if(this.resolvedRelativeTargetAt=B.timestamp,!this.targetDelta&&!this.relativeTarget){const e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Dr(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),ji(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var o,s,l;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),o=this.target,s=this.relativeTarget,l=this.relativeParent.target,Fr(o.x,s.x,l.x),Fr(o.y,s.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):ji(this.target,this.layout.layoutBox),Br(this.target,this.targetDelta)):ji(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const e=this.getClosestProjectingParent();e&&Boolean(e.resumingFrom)===Boolean(this.resumingFrom)&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Dr(this.relativeTargetOrigin,this.target,e.target),ji(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}_.value&&Ji.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!Rr(this.parent.latestValues)&&!zr(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;const t=this.getLead(),n=Boolean(this.resumingFrom)||this!==t;let r=!0;if((this.isProjectionDirty||null!==(e=this.parent)&&void 0!==e&&e.isProjectionDirty)&&(r=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===B.timestamp&&(r=!1),r)return;const{layout:i,layoutId:a}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!i&&!a)return;ji(this.layoutCorrected,this.layout.layoutBox);const o=this.treeScale.x,s=this.treeScale.y;!function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const i=n.length;if(!i)return;let a,o;t.x=t.y=1;for(let s=0;s<i;s++){a=n[s],o=a.projectionDelta;const{visualElement:i}=a.options;i&&i.props.style&&"contents"===i.props.style.display||(r&&a.options.layoutScroll&&a.scroll&&a!==a.root&&qr(e,{x:-a.scroll.offset.x,y:-a.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,Br(e,o)),r&&Or(a.latestValues)&&qr(e,a.latestValues))}t.x<Hr&&t.x>Ur&&(t.x=1),t.y<Hr&&t.y>Ur&&(t.y=1)}(this.layoutCorrected,this.treeScale,this.path,n),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:l}=t;l?(this.projectionDelta&&this.prevProjectionDelta?(Vi(this.prevProjectionDelta.x,this.projectionDelta.x),Vi(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),Cr(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===s&&Gi(this.projectionDelta.x,this.prevProjectionDelta.x)&&Gi(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),_.value&&Ji.calculatedProjections++):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(){var e;let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(null===(e=this.options.visualElement)||void 0===e||e.scheduleRender(),t){const e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=this.snapshot,r=n?n.latestValues:{},i=p({},this.latestValues),a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;const o={x:{min:0,max:0},y:{min:0,max:0}},s=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(s&&!u&&!0===this.options.crossfade&&!this.path.some(xa));let d;this.animationProgress=0,this.mixTargetDelta=t=>{const n=t/1e3;var l,f,h,p,m,g;ba(a.x,e.x,n),ba(a.y,e.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Dr(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),h=this.relativeTarget,p=this.relativeTargetOrigin,m=o,g=n,wa(h.x,p.x,m.x,g),wa(h.y,p.y,m.y,g),d&&(l=this.relativeTarget,f=d,Yi(l.x,f.x)&&Yi(l.y,f.y))&&(this.isProjectionDirty=!1),d||(d={x:{min:0,max:0},y:{min:0,max:0}}),ji(d,this.relativeTarget)),s&&(this.animationValues=i,function(e,t,n,r,i,a){var o,s;if(i)e.opacity=Je(0,null!==(o=n.opacity)&&void 0!==o?o:1,Ri(r)),e.opacityExit=Je(null!==(s=t.opacity)&&void 0!==s?s:1,0,Oi(r));else if(a){var l,u;e.opacity=Je(null!==(l=t.opacity)&&void 0!==l?l:1,null!==(u=n.opacity)&&void 0!==u?u:1,r)}for(let c=0;c<Di;c++){const i="border".concat(Ai[c],"Radius");let a=Ni(t,i),o=Ni(n,i);void 0===a&&void 0===o||(a||(a=0),o||(o=0),0===a||0===o||Li(a)===Li(o)?(e[i]=Math.max(Je(Mi(a),Mi(o),r),0),(Oe.test(o)||Oe.test(a))&&(e[i]+="%")):e[i]=o)}(t.rotate||n.rotate)&&(e.rotate=Je(t.rotate||0,n.rotate||0,r))}(i,r,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){var t,n;this.notifyListeners("animationStart"),null===(t=this.currentAnimation)||void 0===t||t.stop(),null===(n=this.resumingFrom)||void 0===n||null===(n=n.currentAnimation)||void 0===n||n.stop(),this.pendingAnimation&&(I(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=V.update(()=>{gi.hasAnimatedSinceResize=!0,ge.layout++,this.motionValue||(this.motionValue=te(0)),this.currentAnimation=function(e,t,n){const r=oe(e)?e:te(e);return r.start(Qn("",r,t,n)),r.animation}(this.motionValue,[0,1e3],p(p({},e),{},{velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{ge.layout--},onComplete:()=>{ge.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}})),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const e=this.getLead();let{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&Ta(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const t=Pr(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;const r=Pr(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}ji(t,n),qr(t,i),Cr(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new Zi);this.sharedNodes.get(e).add(t);const n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){const e=this.getStack();return!e||e.lead===this}getLead(){var e;const{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;const{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){const{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote(){let{needsReset:e,transition:t,preserveFollowOpacity:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){const e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){const{visualElement:e}=this.options;if(!e)return;let t=!1;const{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;const r={};n.z&&ra("z",e,r,this.animationValues);for(let i=0;i<ea.length;i++)ra("rotate".concat(ea[i]),e,r,this.animationValues),ra("skew".concat(ea[i]),e,r,this.animationValues);e.render();for(const i in r)e.setStaticValue(i,r[i]),this.animationValues&&(this.animationValues[i]=r[i]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return ta;const t={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=Fi(null===e||void 0===e?void 0:e.pointerEvents)||"",t.transform=n?n(this.latestValues,""):"none",t;const r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){const t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=Fi(null===e||void 0===e?void 0:e.pointerEvents)||""),this.hasProjected&&!Or(this.latestValues)&&(t.transform=n?n({},""):"none",this.hasProjected=!1),t}const i=r.animationValues||r.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,n){let r="";const i=e.x.translate/t.x,a=e.y.translate/t.y,o=(null===n||void 0===n?void 0:n.z)||0;if((i||a||o)&&(r="translate3d(".concat(i,"px, ").concat(a,"px, ").concat(o,"px) ")),1===t.x&&1===t.y||(r+="scale(".concat(1/t.x,", ").concat(1/t.y,") ")),n){const{transformPerspective:e,rotate:t,rotateX:i,rotateY:a,skewX:o,skewY:s}=n;e&&(r="perspective(".concat(e,"px) ").concat(r)),t&&(r+="rotate(".concat(t,"deg) ")),i&&(r+="rotateX(".concat(i,"deg) ")),a&&(r+="rotateY(".concat(a,"deg) ")),o&&(r+="skewX(".concat(o,"deg) ")),s&&(r+="skewY(".concat(s,"deg) "))}const s=e.x.scale*t.x,l=e.y.scale*t.y;return 1===s&&1===l||(r+="scale(".concat(s,", ").concat(l,")")),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,i),n&&(t.transform=n(i,t.transform));const{x:a,y:o}=this.projectionDelta;var s,l;(t.transformOrigin="".concat(100*a.origin,"% ").concat(100*o.origin,"% 0"),r.animationValues)?t.opacity=r===this?null!==(s=null!==(l=i.opacity)&&void 0!==l?l:this.latestValues.opacity)&&void 0!==s?s:1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:t.opacity=r===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0;for(const u in wi){if(void 0===i[u])continue;const{correct:e,applyTo:n,isCSSVariable:a}=wi[u],o="none"===t.transform?i[u]:e(i[u],r);if(n){const e=n.length;for(let r=0;r<e;r++)t[n[r]]=o}else a?this.options.visualElement.renderState.vars[u]=o:t[u]=o}return this.options.layoutId&&(t.pointerEvents=r===this?Fi(null===e||void 0===e?void 0:e.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(da),this.root.sharedNodes.clear()}}}function oa(e){e.updateLayout()}function sa(e){var t;const n=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:t,measuredBox:r}=e.layout,{animationType:i}=e.options,a=n.source!==e.layout.source;"size"===i?Lr(e=>{const r=a?n.measuredBox[e]:n.layoutBox[e],i=Pr(r);r.min=t[e].min,r.max=r.min+i}):Ta(i,n.layoutBox,t)&&Lr(r=>{const i=a?n.measuredBox[r]:n.layoutBox[r],o=Pr(t[r]);i.max=i.min+o,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+o)});const o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Cr(o,t,n.layoutBox);const s={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};a?Cr(s,e.applyTransform(r,!0),n.measuredBox):Cr(s,t,n.layoutBox);const l=!qi(o);let u=!1;if(!e.resumeFrom){const r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){const{snapshot:i,layout:a}=r;if(i&&a){const o={x:{min:0,max:0},y:{min:0,max:0}};Dr(o,n.layoutBox,i.layoutBox);const s={x:{min:0,max:0},y:{min:0,max:0}};Dr(s,t,a.layoutBox),Qi(o,s)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=o,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:n,delta:s,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){const{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function la(e){_.value&&Ji.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=Boolean(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function ua(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function ca(e){e.clearSnapshot()}function da(e){e.clearMeasurements()}function fa(e){e.isLayoutDirty=!1}function ha(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function pa(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function ma(e){e.resolveTargetDelta()}function ga(e){e.calcProjection()}function va(e){e.resetSkewAndRotation()}function ya(e){e.removeLeadSnapshot()}function ba(e,t,n){e.translate=Je(t.translate,0,n),e.scale=Je(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function wa(e,t,n,r){e.min=Je(t.min,n.min,r),e.max=Je(t.max,n.max,r)}function xa(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}const Sa={duration:.45,ease:[.4,0,.1,1]},ka=e=>"undefined"!==typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),Ea=ka("applewebkit/")&&!ka("chrome/")?Math.round:R;function Pa(e){e.min=Ea(e.min),e.max=Ea(e.max)}function Ta(e,t,n){return"position"===e||"preserve-aspect"===e&&(r=Xi(t),i=Xi(n),a=.2,!(Math.abs(r-i)<=a));var r,i,a}function Ca(e){var t;return e!==e.root&&(null===(t=e.scroll)||void 0===t?void 0:t.wasRoot)}const Fa=aa({attachResizeListener:(e,t)=>wr(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Aa={current:void 0},Da=aa({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Aa.current){const e=new Fa({});e.mount(window),e.setOptions({layoutScroll:!0}),Aa.current=e}return Aa.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>Boolean("fixed"===window.getComputedStyle(e).position)}),Ma={pan:{Feature:class extends mr{constructor(){super(...arguments),this.removePointerDownListener=R}onPointerDown(e){this.session=new Gr(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Kr(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:fi(e),onStart:fi(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&V.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=kr(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends mr{constructor(e){super(e),this.removeGroupControls=R,this.removeListeners=R,this.controls=new ci(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||R}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Da,MeasureLayout:Si}};function La(e,t){const n=function(e,t,n){if(e instanceof EventTarget)return[e];if("string"===typeof e){var r;let i=document;t&&(i=t.current);const a=null!==(r=null===n||void 0===n?void 0:n[e])&&void 0!==r?r:i.querySelectorAll(e);return a?Array.from(a):[]}return Array.from(e)}(e),r=new AbortController;return[n,p(p({passive:!0},t),{},{signal:r.signal}),()=>r.abort()]}function Na(e){return!("touch"===e.pointerType||br())}function Ra(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);const i=r["onHover"+n];i&&V.postRender(()=>i(t,Sr(t)))}const Oa=(e,t)=>!!t&&(e===t||Oa(e,t.parentElement)),za=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const _a=new WeakSet;function ja(e){return t=>{"Enter"===t.key&&e(t)}}function Va(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}function Ia(e){return xr(e)&&!br()}function Ba(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const[r,i,a]=La(e,n),o=e=>{const r=e.currentTarget;if(!Ia(e))return;_a.add(r);const a=t(r,e),o=(e,t)=>{window.removeEventListener("pointerup",s),window.removeEventListener("pointercancel",l),_a.has(r)&&_a.delete(r),Ia(e)&&"function"===typeof a&&a(e,{success:t})},s=e=>{o(e,r===window||r===document||n.useGlobalTarget||Oa(r,e.target))},l=e=>{o(e,!1)};window.addEventListener("pointerup",s,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{var t;(n.useGlobalTarget?window:e).addEventListener("pointerdown",o,i),v(e)&&(e.addEventListener("focus",e=>((e,t)=>{const n=e.currentTarget;if(!n)return;const r=ja(()=>{if(_a.has(n))return;Va(n,"down");const e=ja(()=>{Va(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>Va(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)})(e,i)),t=e,za.has(t.tagName)||-1!==t.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),a}function Ua(e,t,n){const{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);const i=r["onTap"+("End"===n?"":n)];i&&V.postRender(()=>i(t,Sr(t)))}const Ha=["root"],Wa=new WeakMap,$a=new WeakMap,qa=e=>{const t=Wa.get(e.target);t&&t(e)},Ya=e=>{e.forEach(qa)};function Ka(e,t,n){const r=function(e){let{root:t}=e,n=A(e,Ha);const r=t||document;$a.has(r)||$a.set(r,{});const i=$a.get(r),a=JSON.stringify(n);return i[a]||(i[a]=new IntersectionObserver(Ya,p({root:t},n))),i[a]}(t);return Wa.set(e,n),r.observe(e),()=>{Wa.delete(e),r.unobserve(e)}}const Qa={some:0,all:1};const Xa={inView:{Feature:class extends mr{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,a={root:t?t.current:void 0,rootMargin:n,threshold:"number"===typeof r?r:Qa[r]};return Ka(this.node.current,a,e=>{const{isIntersecting:t}=e;if(this.isInView===t)return;if(this.isInView=t,i&&!t&&this.hasEnteredView)return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);const{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),a=t?n:r;a&&a(e)})}mount(){this.startObserver()}update(){if("undefined"===typeof IntersectionObserver)return;const{props:e,prevProps:t}=this.node,n=["amount","margin","root"].some(function(e){let{viewport:t={}}=e,{viewport:n={}}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e=>t[e]!==n[e]}(e,t));n&&this.startObserver()}unmount(){}}},tap:{Feature:class extends mr{mount(){const{current:e}=this.node;e&&(this.unmount=Ba(e,(e,t)=>(Ua(this.node,t,"Start"),(e,t)=>{let{success:n}=t;return Ua(this.node,e,n?"End":"Cancel")}),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends mr{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=fe(wr(this.node.current,"focus",()=>this.onFocus()),wr(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends mr{mount(){const{current:e}=this.node;e&&(this.unmount=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const[r,i,a]=La(e,n),o=e=>{if(!Na(e))return;const{target:n}=e,r=t(n,e);if("function"!==typeof r||!n)return;const a=e=>{Na(e)&&(r(e),n.removeEventListener("pointerleave",a))};n.addEventListener("pointerleave",a,i)};return r.forEach(e=>{e.addEventListener("pointerenter",o,i)}),a}(e,(e,t)=>(Ra(this.node,t,"Start"),e=>Ra(this.node,e,"End"))))}unmount(){}}}},Ga={layout:{ProjectionNode:Da,MeasureLayout:Si}},Za=(0,r.createContext)({strict:!1}),Ja=(0,r.createContext)({});function eo(e){return F(e.animate)||ir.some(t=>nr(e[t]))}function to(e){return Boolean(eo(e)||e.variants)}function no(e){const{initial:t,animate:n}=function(e,t){if(eo(e)){const{initial:t,animate:n}=e;return{initial:!1===t||nr(t)?t:void 0,animate:nr(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,r.useContext)(Ja));return(0,r.useMemo)(()=>({initial:t,animate:n}),[ro(t),ro(n)])}function ro(e){return Array.isArray(e)?e.join(" "):e}const io={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ao={};for(const Cs in io)ao[Cs]={isEnabled:e=>io[Cs].some(t=>!!e[t])};const oo=Symbol.for("motionComponentSymbol");function so(e,t,n){return(0,r.useCallback)(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&("function"===typeof n?n(r):Qr(n)&&(n.current=r))},[t])}function lo(e,t,n,i,a){var o,s,l,c;const{visualElement:d}=(0,r.useContext)(Ja),f=(0,r.useContext)(Za),h=(0,r.useContext)(m),p=(0,r.useContext)(y).reducedMotion,g=(0,r.useRef)(null);i=i||f.renderer,!g.current&&i&&(g.current=i(e,{visualState:t,parent:d,props:n,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:p}));const v=g.current,b=(0,r.useContext)(mi);!v||v.projection||!a||"html"!==v.type&&"svg"!==v.type||function(e,t,n,r){const{layoutId:i,layout:a,drag:o,dragConstraints:s,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:uo(e.parent)),e.projection.setOptions({layoutId:i,layout:a,alwaysMeasureLayout:Boolean(o)||s&&Qr(s),visualElement:e,animationType:"string"===typeof a?a:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(g.current,n,a,b);const w=(0,r.useRef)(!1);(0,r.useInsertionEffect)(()=>{v&&w.current&&v.update(n,h)});const x=n[ue],S=(0,r.useRef)(Boolean(x)&&!(null!==(o=(s=window).MotionHandoffIsComplete)&&void 0!==o&&o.call(s,x))&&(null===(l=(c=window).MotionHasOptimisedAnimation)||void 0===l?void 0:l.call(c,x)));return u(()=>{v&&(w.current=!0,window.MotionIsMounted=!0,v.updateFeatures(),hi.render(v.render),S.current&&v.animationState&&v.animationState.animateChanges())}),(0,r.useEffect)(()=>{v&&(!S.current&&v.animationState&&v.animationState.animateChanges(),S.current&&(queueMicrotask(()=>{var e,t;null===(e=(t=window).MotionHandoffMarkAsComplete)||void 0===e||e.call(t,x)}),S.current=!1))}),v}function uo(e){if(e)return!1!==e.options.allowProjection?e.projection:uo(e.parent)}function co(e){var t,n;let{preloadedFeatures:i,createVisualElement:o,useRender:s,useVisualState:u,Component:c}=e;function d(e,t){let n;const i=p(p(p({},(0,r.useContext)(y)),e),{},{layoutId:fo(e)}),{isStatic:d}=i,f=no(e),h=u(e,d);if(!d&&l){!function(){(0,r.useContext)(Za).strict;0}();const e=function(e){const{drag:t,layout:n}=ao;if(!t&&!n)return{};const r=p(p({},t),n);return{MeasureLayout:null!==t&&void 0!==t&&t.isEnabled(e)||null!==n&&void 0!==n&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(i);n=e.MeasureLayout,f.visualElement=lo(c,h,i,o,e.ProjectionNode)}return(0,a.jsxs)(Ja.Provider,{value:f,children:[n&&f.visualElement?(0,a.jsx)(n,p({visualElement:f.visualElement},i)):null,s(c,e,so(h,f.visualElement,t),h,d,f.visualElement)]})}i&&function(e){for(const t in e)ao[t]=p(p({},ao[t]),e[t])}(i),d.displayName="motion.".concat("string"===typeof c?c:"create(".concat(null!==(t=null!==(n=c.displayName)&&void 0!==n?n:c.name)&&void 0!==t?t:"",")"));const f=(0,r.forwardRef)(d);return f[oo]=c,f}function fo(e){let{layoutId:t}=e;const n=(0,r.useContext)(o).id;return n&&void 0!==t?n+"-"+t:t}function ho(e,t){let{layout:n,layoutId:r}=t;return W.has(e)||e.startsWith("origin")||(n||void 0!==r)&&(!!wi[e]||"opacity"===e)}const po=(e,t)=>t&&"number"===typeof e?t.transform(e):e,mo=p(p({},Se),{},{transform:Math.round}),go=p(p({borderWidth:ze,borderTopWidth:ze,borderRightWidth:ze,borderBottomWidth:ze,borderLeftWidth:ze,borderRadius:ze,radius:ze,borderTopLeftRadius:ze,borderTopRightRadius:ze,borderBottomRightRadius:ze,borderBottomLeftRadius:ze,width:ze,maxWidth:ze,height:ze,maxHeight:ze,top:ze,right:ze,bottom:ze,left:ze,padding:ze,paddingTop:ze,paddingRight:ze,paddingBottom:ze,paddingLeft:ze,margin:ze,marginTop:ze,marginRight:ze,marginBottom:ze,marginLeft:ze,backgroundPositionX:ze,backgroundPositionY:ze},{rotate:Re,rotateX:Re,rotateY:Re,rotateZ:Re,scale:Ee,scaleX:Ee,scaleY:Ee,scaleZ:Ee,skew:Re,skewX:Re,skewY:Re,distance:ze,translateX:ze,translateY:ze,translateZ:ze,x:ze,y:ze,z:ze,perspective:ze,transformPerspective:ze,opacity:ke,originX:Ve,originY:Ve,originZ:ze}),{},{zIndex:mo,fillOpacity:ke,strokeOpacity:ke,numOctaves:mo}),vo={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},yo=H.length;function bo(e,t,n){const{style:r,vars:i,transformOrigin:a}=e;let o=!1,s=!1;for(const l in t){const e=t[l];if(W.has(l))o=!0;else if(ye(l))i[l]=e;else{const t=po(e,go[l]);l.startsWith("origin")?(s=!0,a[l]=t):r[l]=t}}if(t.transform||(o||n?r.transform=function(e,t,n){let r="",i=!0;for(let a=0;a<yo;a++){const o=H[a],s=e[o];if(void 0===s)continue;let l=!0;if(l="number"===typeof s?s===(o.startsWith("scale")?1:0):0===parseFloat(s),!l||n){const e=po(s,go[o]);l||(i=!1,r+="".concat(vo[o]||o,"(").concat(e,") ")),n&&(t[o]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),s){const{originX:e="50%",originY:t="50%",originZ:n=0}=a;r.transformOrigin="".concat(e," ").concat(t," ").concat(n)}}const wo=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function xo(e,t,n){for(const r in t)oe(t[r])||ho(r,n)||(e[r]=t[r])}function So(e,t){const n={};return xo(n,e.style||{},e),Object.assign(n,function(e,t){let{transformTemplate:n}=e;return(0,r.useMemo)(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return bo(e,t,n),Object.assign({},e.vars,e.style)},[t])}(e,t)),n}function ko(e,t){const n={},r=So(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":"pan-".concat("x"===e.drag?"y":"x")),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}const Eo={offset:"stroke-dashoffset",array:"stroke-dasharray"},Po={offset:"strokeDashoffset",array:"strokeDasharray"};const To=["attrX","attrY","attrScale","pathLength","pathSpacing","pathOffset"];function Co(e,t,n,r,i){let{attrX:a,attrY:o,attrScale:s,pathLength:l,pathSpacing:u=1,pathOffset:c=0}=t;if(bo(e,A(t,To),r),n)return void(e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox));e.attrs=e.style,e.style={};const{attrs:d,style:f}=e;var h,p;(d.transform&&(f.transform=d.transform,delete d.transform),f.transform||d.transformOrigin)&&(f.transformOrigin=null!==(h=d.transformOrigin)&&void 0!==h?h:"50% 50%",delete d.transformOrigin);f.transform&&(f.transformBox=null!==(p=null===i||void 0===i?void 0:i.transformBox)&&void 0!==p?p:"fill-box",delete d.transformBox);void 0!==a&&(d.x=a),void 0!==o&&(d.y=o),void 0!==s&&(d.scale=s),void 0!==l&&function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];e.pathLength=1;const a=i?Eo:Po;e[a.offset]=ze.transform(-r);const o=ze.transform(t),s=ze.transform(n);e[a.array]="".concat(o," ").concat(s)}(d,l,u,c,!1)}const Fo=()=>p(p({},{style:{},transform:{},transformOrigin:{},vars:{}}),{},{attrs:{}}),Ao=e=>"string"===typeof e&&"svg"===e.toLowerCase();function Do(e,t,n,i){const a=(0,r.useMemo)(()=>{const n=Fo();return Co(n,t,Ao(i),e.transformTemplate,e.style),p(p({},n.attrs),{},{style:p({},n.style)})},[t]);if(e.style){const t={};xo(t,e.style,e),a.style=p(p({},t),a.style)}return a}const Mo=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Lo(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Mo.has(e)}let No=e=>!Lo(e);try{"function"===typeof(Ro=require("@emotion/is-prop-valid").default)&&(No=e=>e.startsWith("on")?!Lo(e):Ro(e))}catch(Ts){}var Ro;const Oo=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function zo(e){return"string"===typeof e&&!e.includes("-")&&!!(Oo.indexOf(e)>-1||/[A-Z]/.test(e))}function _o(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(t,n,i,a,o)=>{let{latestValues:s}=a;const l=(zo(t)?Do:ko)(n,s,o,t),u=function(e,t,n){const r={};for(const i in e)"values"===i&&"object"===typeof e.values||(No(i)||!0===n&&Lo(i)||!t&&!Lo(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"===typeof t,e),c=t!==r.Fragment?p(p(p({},u),l),{},{ref:i}):{},{children:d}=n,f=(0,r.useMemo)(()=>oe(d)?d.get():d,[d]);return(0,r.createElement)(t,p(p({},c),{},{children:f}))}}const jo=["transitionEnd","transition"];const Vo=e=>(t,n)=>{const i=(0,r.useContext)(Ja),a=(0,r.useContext)(m),o=()=>function(e,t,n,r){let{scrapeMotionValuesFromProps:i,createRenderState:a}=e;return{latestValues:Io(t,n,r,i),renderState:a()}}(e,t,i,a);return n?o():s(o)};function Io(e,t,n,r){const i={},a=r(e,{});for(const f in a)i[f]=Fi(a[f]);let{initial:o,animate:s}=e;const l=eo(e),u=to(e);t&&u&&!l&&!1!==e.inherit&&(void 0===o&&(o=t.initial),void 0===s&&(s=t.animate));let c=!!n&&!1===n.initial;c=c||!1===o;const d=c?s:o;if(d&&"boolean"!==typeof d&&!F(d)){const t=Array.isArray(d)?d:[d];for(let n=0;n<t.length;n++){const r=M(e,t[n]);if(r){const{transitionEnd:e,transition:t}=r,n=A(r,jo);for(const r in n){let e=n[r];if(Array.isArray(e)){e=e[c?e.length-1:0]}null!==e&&(i[r]=e)}for(const r in e)i[r]=e[r]}}}return i}function Bo(e,t,n){const{style:r}=e,i={};for(const o in r){var a;(oe(r[o])||t.style&&oe(t.style[o])||ho(o,e)||void 0!==(null===n||void 0===n||null===(a=n.getValue(o))||void 0===a?void 0:a.liveStyle))&&(i[o]=r[o])}return i}const Uo={useVisualState:Vo({scrapeMotionValuesFromProps:Bo,createRenderState:wo})};function Ho(e,t,n){const r=Bo(e,t,n);for(const i in e)if(oe(e[i])||oe(t[i])){r[-1!==H.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]}return r}const Wo={useVisualState:Vo({scrapeMotionValuesFromProps:Ho,createRenderState:Fo})};function $o(e,t){return function(n){let{forwardMotionProps:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{forwardMotionProps:!1};return co(p(p({},zo(n)?Wo:Uo),{},{preloadedFeatures:e,useRender:_o(r),createVisualElement:t,Component:n}))}}const qo=e=>t=>t.test(e),Yo=[Se,ze,Oe,Re,je,_e,{test:e=>"auto"===e,parse:e=>e}],Ko=e=>Yo.find(qo(e)),Qo=e=>/^-?(?:[0-9]+(?:\.[0-9]+)?|\.[0-9]+)$/.test(e),Xo=/^var\(--(?:([\x2D0-9A-Z_a-z]+)|([\x2D0-9A-Z_a-z]+), ?([ #%\(\),-\.0-9A-Za-z]+))\)/;function Go(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;'Max CSS variable fallback depth detected in property "'.concat(e,'". This may indicate a circular fallback dependency.');const[r,i]=function(e){const t=Xo.exec(e);if(!t)return[,];const[,n,r,i]=t;return["--".concat(null!==n&&void 0!==n?n:r),i]}(e);if(!r)return;const a=window.getComputedStyle(t).getPropertyValue(r);if(a){const e=a.trim();return Qo(e)?parseFloat(e):e}return we(i)?Go(i,t,n+1):i}const Zo=e=>/^0(?:[\0-\x08\x0E-\x1F!-\x2D\/-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+$/.test(e);function Jo(e){return"number"===typeof e?0===e:null===e||("none"===e||"0"===e||Zo(e))}const es=new Set(["brightness","contrast","saturate","opacity"]);function ts(e){const[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;const[r]=n.match(Te)||[];if(!r)return e;const i=n.replace(r,"");let a=es.has(t)?1:0;return r!==n&&(a*=100),t+"("+a+i+")"}const ns=/\b([\x2Da-z]*)\((?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*?\)/g,rs=p(p({},Xe),{},{getAnimatableNone:e=>{const t=e.match(ns);return t?t.map(ts).join(" "):e}}),is=p(p({},go),{},{color:Be,backgroundColor:Be,outlineColor:Be,fill:Be,stroke:Be,borderColor:Be,borderTopColor:Be,borderRightColor:Be,borderBottomColor:Be,borderLeftColor:Be,filter:rs,WebkitFilter:rs}),as=e=>is[e];function os(e,t){let n=as(e);return n!==rs&&(n=Xe),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const ss=new Set(["auto","none","0"]);class ls extends xn{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let s=0;s<e.length;s++){let n=e[s];if("string"===typeof n&&(n=n.trim(),we(n))){const r=Go(n,t.current);void 0!==r&&(e[s]=r),s===e.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!$.has(n)||2!==e.length)return;const[r,i]=e,a=Ko(r),o=Ko(i);if(a!==o)if(dn(a)&&dn(o))for(let s=0;s<e.length;s++){const t=e[s];"string"===typeof t&&(e[s]=parseFloat(t))}else pn[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:t}=this,n=[];for(let r=0;r<e.length;r++)(null===e[r]||Jo(e[r]))&&n.push(r);n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){const t=e[i];"string"===typeof t&&!ss.has(t)&&qe(t).values.length&&(r=e[i]),i++}if(r&&n)for(const a of t)e[a]=os(n,r)}(e,n,t)}measureInitialState(){const{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=pn[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;const r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){var e;const{element:t,name:n,unresolvedKeyframes:r}=this;if(!t||!t.current)return;const i=t.getValue(n);i&&i.jump(this.measuredOrigin,!1);const a=r.length-1,o=r[a];r[a]=pn[n](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),null!==(e=this.removedTransforms)&&void 0!==e&&e.length&&this.removedTransforms.forEach(e=>{let[n,r]=e;t.getValue(n).set(r)}),this.resolveNoneKeyframes()}}const us=[...Yo,Be,Xe],cs={current:null},ds={current:!1};const fs=new WeakMap;const hs=["willChange"],ps=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ms{scrapeMotionValuesFromProps(e,t,n){return{}}constructor(e){let{parent:t,props:n,presenceContext:r,reducedMotionConfig:i,blockInitialAnimation:a,visualState:o}=e,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=xn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const e=Z.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,V.render(this.render,!1,!0))};const{latestValues:l,renderState:u}=o;this.latestValues=l,this.baseTarget=p({},l),this.initialValues=n.initial?p({},l):{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.blockInitialAnimation=Boolean(a),this.isControllingVariants=eo(n),this.isVariantNode=to(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const c=this.scrapeMotionValuesFromProps(n,{},this),{willChange:d}=c,f=A(c,hs);for(const h in f){const e=f[h];void 0!==l[h]&&oe(e)&&e.set(l[h],!1)}}mount(e){this.current=e,fs.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),ds.current||function(){if(ds.current=!0,l)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>cs.current=e.matches;e.addListener(t),t()}else cs.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||cs.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),I(this.notifyUpdate),I(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const n=W.has(e);n&&this.onBindTransform&&this.onBindTransform();const r=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&V.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),i=t.on("renderRequest",this.scheduleRender);let a;window.MotionCheckAppearSync&&(a=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{r(),i(),a&&a(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in ao){const t=ao[e];if(!t)continue;const{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){const t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let n=0;n<ps.length;n++){const t=ps[n];this.propEventSubscriptions[t]&&(this.propEventSubscriptions[t](),delete this.propEventSubscriptions[t]);const r=e["on"+t];r&&(this.propEventSubscriptions[t]=this.on(t,r))}this.prevMotionValues=function(e,t,n){for(const r in t){const i=t[r],a=n[r];if(oe(i))e.addValue(r,i);else if(oe(a))e.addValue(r,te(i,{owner:e}));else if(a!==i)if(e.hasValue(r)){const t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{const t=e.getStaticValue(r);e.addValue(r,te(void 0!==t?t:i,{owner:e}))}}for(const r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){const n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);const t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=te(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){var n;let r=void 0===this.latestValues[e]&&this.current?null!==(n=this.getBaseTargetFromProps(this.props,e))&&void 0!==n?n:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];var i;return void 0!==r&&null!==r&&("string"===typeof r&&(Qo(r)||Zo(r))?r=parseFloat(r):(i=r,!us.find(qo(i))&&Xe.test(t)&&(r=os(e,t))),this.setBaseTarget(e,oe(r)?r.get():r)),oe(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){const{initial:t}=this.props;let n;if("string"===typeof t||"object"===typeof t){var r;const i=M(this.props,t,null===(r=this.presenceContext)||void 0===r?void 0:r.custom);i&&(n=i[e])}if(t&&void 0!==n)return n;const i=this.getBaseTargetFromProps(this.props,e);return void 0===i||oe(i)?void 0!==this.initialValues[e]&&void 0===n?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new K),this.events[e].add(t)}notify(e){if(this.events[e]){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];this.events[e].notify(...n)}}}class gs extends ms{constructor(){super(...arguments),this.KeyframeResolver=ls}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,t){let{vars:n,style:r}=t;delete n[e],delete r[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;oe(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent="".concat(e))}))}}function vs(e,t,n,r){let{style:i,vars:a}=t;Object.assign(e.style,i,r&&r.getProjectionStyles(n));for(const o in a)e.style.setProperty(o,a[o])}class ys extends gs{constructor(){super(...arguments),this.type="html",this.renderInstance=vs}readValueFromInstance(e,t){var n,r;if(W.has(t))return null!==(n=this.projection)&&void 0!==n&&n.isProjecting?ln(t):((e,t)=>{const{transform:n="none"}=getComputedStyle(e);return un(n,t)})(e,t);{const n=(r=e,window.getComputedStyle(r)),i=(ye(t)?n.getPropertyValue(t):n[t])||0;return"string"===typeof i?i.trim():i}}measureInstanceViewportBox(e,t){let{transformPagePoint:n}=t;return Yr(e,n)}build(e,t,n){bo(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return Bo(e,t,n)}}const bs=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class ws extends gs{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Mr}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(W.has(t)){const e=as(t);return e&&e.default||0}return t=bs.has(t)?t:le(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return Ho(e,t,n)}build(e,t,n){Co(e,t,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(e,t,n,r){!function(e,t,n,r){vs(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(bs.has(i)?i:le(i),t.attrs[i])}(e,t,0,r)}mount(e){this.isSVGTag=Ao(e.tagName),super.mount(e)}}const xs=(e,t)=>zo(e)?new ws(t):new ys(t,{allowProjection:e!==r.Fragment}),Ss=C($o(p(p(p(p({},vr),Xa),Ma),Ga),xs));function ks(e){let{id:t,type:n,value:r,isSelected:i,onClick:o}=e;const s=e=>{e.preventDefault(),e.stopPropagation(),console.log("MahjongTile clicked:",t),alert("\u70b9\u51fb\u4e86\u724c: ".concat(t," - \u503c: ").concat(r)),o()};return(0,a.jsx)("div",{className:"\n        w-12 h-16 rounded-lg border-2 cursor-pointer flex items-center justify-center font-bold text-sm\n        transition-all duration-200 hover:scale-105 hover:shadow-lg select-none\n        ".concat((()=>{switch(n){case"character":return"bg-red-100 border-red-300 text-red-800";case"bamboo":return"bg-green-100 border-green-300 text-green-800";case"dot":return"bg-blue-100 border-blue-300 text-blue-800";case"wind":return"bg-yellow-100 border-yellow-300 text-yellow-800";case"dragon":return"bg-purple-100 border-purple-300 text-purple-800";default:return"bg-gray-100 border-gray-300 text-gray-800"}})(),"\n        ").concat(i?"ring-4 ring-yellow-400 transform -translate-y-2":"","\n      "),onClick:s,onMouseDown:s,onTouchStart:s,style:{userSelect:"none",WebkitUserSelect:"none",MozUserSelect:"none",msUserSelect:"none",pointerEvents:"auto"},children:r})}const Es=function(){const[e,t]=(0,r.useState)(!1),[n,i]=(0,r.useState)([]),[o]=(0,r.useState)(()=>{const e=["character","bamboo","dot","wind","dragon"];return Array.from({length:13},(t,n)=>({id:"tile-".concat(n),type:e[Math.floor(Math.random()*e.length)],value:Math.floor(9*Math.random())+1,isSelected:!1}))}),s=e=>{console.log("\u6267\u884c\u52a8\u4f5c: ".concat(e,"\uff0c\u9009\u4e2d\u7684\u724c: ").concat(n)),i([])};return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-20",children:(0,a.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-yellow-500/10 to-green-500/10"})}),(0,a.jsx)(T,{children:e&&(0,a.jsx)(Ss.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80",children:(0,a.jsxs)(Ss.div,{initial:{scale:.8,y:50},animate:{scale:1,y:0},exit:{scale:.8,y:-50},className:"text-center text-white",children:[(0,a.jsx)(Ss.h1,{className:"text-6xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent",animate:{filter:["drop-shadow(0 0 20px rgba(245, 158, 11, 0.5))","drop-shadow(0 0 40px rgba(245, 158, 11, 0.8))","drop-shadow(0 0 20px rgba(245, 158, 11, 0.5))"]},transition:{duration:2,repeat:1/0},children:"\u795e\u706f\u9ebb\u5c06\u5927\u5e08"}),(0,a.jsx)(Ss.p,{className:"text-xl mb-8 text-gray-300",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:"\u8d85\u8d8a\u4f20\u7edf\uff0c\u91cd\u65b0\u5b9a\u4e49\u9ebb\u5c06\u4f53\u9a8c"}),(0,a.jsx)(Ss.button,{className:"bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white font-bold py-3 px-8 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 text-lg",whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>t(!1),children:"\u5f00\u59cb\u6e38\u620f"})]})})}),!e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Ss.div,{initial:{y:-100},animate:{y:0},className:"absolute top-0 left-0 right-0 z-40 bg-black bg-opacity-50 backdrop-blur-sm p-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-center max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-yellow-400",children:"\u795e\u706f\u9ebb\u5c06\u5927\u5e08"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-white",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{children:"\u5728\u7ebf"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"text-white",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"\u5f53\u524d\u5c40\u6570:"}),(0,a.jsx)("span",{className:"ml-2 text-yellow-400 font-bold",children:"\u7b2c1\u5c40"})]}),(0,a.jsxs)("div",{className:"text-white",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"\u98ce\u5708:"}),(0,a.jsx)("span",{className:"ml-2 text-green-400 font-bold",children:"\u4e1c\u98ce"})]})]})]})}),(0,a.jsx)("div",{className:"flex flex-col items-center justify-center h-screen pt-20 pb-20",children:(0,a.jsxs)(Ss.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},className:"bg-green-800 rounded-3xl p-8 shadow-2xl",children:[(0,a.jsx)("h3",{className:"text-white text-xl font-bold mb-6 text-center",children:"\u60a8\u7684\u624b\u724c"}),(0,a.jsx)("div",{className:"flex gap-2 flex-wrap justify-center",children:o.map(e=>(0,a.jsx)(ks,{id:e.id,type:e.type,value:e.value,isSelected:n.includes(e.id),onClick:()=>{return t=e.id,console.log("\u70b9\u51fb\u4e86\u9ebb\u5c06\u724c:",t),void i(e=>{const n=e.includes(t)?e.filter(e=>e!==t):[...e,t];return console.log("\u9009\u4e2d\u7684\u724c:",n),n});var t}},e.id))})]})}),(0,a.jsx)(T,{children:n.length>0&&(0,a.jsx)(Ss.div,{initial:{y:100,opacity:0},animate:{y:0,opacity:1},exit:{y:100,opacity:0},className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 z-40",children:(0,a.jsxs)("div",{className:"bg-black bg-opacity-70 backdrop-blur-sm rounded-2xl p-4 flex space-x-3",children:[(0,a.jsx)(Ss.button,{className:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150",whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>s("\u51fa\u724c"),children:"\u51fa\u724c"}),(0,a.jsx)(Ss.button,{className:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150",whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>s("\u5403"),children:"\u5403"}),(0,a.jsx)(Ss.button,{className:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150",whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>s("\u78b0"),children:"\u78b0"}),(0,a.jsx)(Ss.button,{className:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-150",whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>s("\u6760"),children:"\u6760"}),(0,a.jsx)(Ss.button,{className:"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-bold py-2 px-6 rounded-lg shadow-lg transform hover:scale-105 transition-all duration-150",whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>s("\u80e1\u724c"),children:"\u80e1\u724c"})]})})})]})]})},Ps=e=>{e&&e instanceof Function&&n.e(453).then(n.bind(n,453)).then(t=>{let{getCLS:n,getFID:r,getFCP:i,getLCP:a,getTTFB:o}=t;n(e),r(e),i(e),a(e),o(e)})};i.createRoot(document.getElementById("root")).render((0,a.jsx)(r.StrictMode,{children:(0,a.jsx)(Es,{})})),Ps()})();
//# sourceMappingURL=main.c7c88e96.js.map