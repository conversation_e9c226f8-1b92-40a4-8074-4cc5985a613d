{"ast": null, "code": "function isObject(value) {\n  return typeof value === \"object\" && value !== null;\n}\nexport { isObject };", "map": {"version": 3, "names": ["isObject", "value"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/motion-utils/dist/es/is-object.mjs"], "sourcesContent": ["function isObject(value) {\n    return typeof value === \"object\" && value !== null;\n}\n\nexport { isObject };\n"], "mappings": "AAAA,SAASA,QAAQA,CAACC,KAAK,EAAE;EACrB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI;AACtD;AAEA,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}