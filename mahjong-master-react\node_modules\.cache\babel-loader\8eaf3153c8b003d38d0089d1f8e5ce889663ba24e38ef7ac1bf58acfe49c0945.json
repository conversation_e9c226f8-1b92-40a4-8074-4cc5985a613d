{"ast": null, "code": "import { IS_LEAFNODE_FLAG } from '../Constants.js';\n\n/****************************************************/\n/* This file is generated from \"refit.template.js\". */\n/****************************************************/\n\nfunction refit(bvh, nodeIndices = null) {\n  if (nodeIndices && Array.isArray(nodeIndices)) {\n    nodeIndices = new Set(nodeIndices);\n  }\n  const geometry = bvh.geometry;\n  const indexArr = geometry.index ? geometry.index.array : null;\n  const posAttr = geometry.attributes.position;\n  let buffer, uint32Array, uint16Array, float32Array;\n  let byteOffset = 0;\n  const roots = bvh._roots;\n  for (let i = 0, l = roots.length; i < l; i++) {\n    buffer = roots[i];\n    uint32Array = new Uint32Array(buffer);\n    uint16Array = new Uint16Array(buffer);\n    float32Array = new Float32Array(buffer);\n    _traverse(0, byteOffset);\n    byteOffset += buffer.byteLength;\n  }\n  function _traverse(node32Index, byteOffset, force = false) {\n    const node16Index = node32Index * 2;\n    const isLeaf = uint16Array[node16Index + 15] === IS_LEAFNODE_FLAG;\n    if (isLeaf) {\n      const offset = uint32Array[node32Index + 6];\n      const count = uint16Array[node16Index + 14];\n      let minx = Infinity;\n      let miny = Infinity;\n      let minz = Infinity;\n      let maxx = -Infinity;\n      let maxy = -Infinity;\n      let maxz = -Infinity;\n      for (let i = 3 * offset, l = 3 * (offset + count); i < l; i++) {\n        let index = indexArr[i];\n        const x = posAttr.getX(index);\n        const y = posAttr.getY(index);\n        const z = posAttr.getZ(index);\n        if (x < minx) minx = x;\n        if (x > maxx) maxx = x;\n        if (y < miny) miny = y;\n        if (y > maxy) maxy = y;\n        if (z < minz) minz = z;\n        if (z > maxz) maxz = z;\n      }\n      if (float32Array[node32Index + 0] !== minx || float32Array[node32Index + 1] !== miny || float32Array[node32Index + 2] !== minz || float32Array[node32Index + 3] !== maxx || float32Array[node32Index + 4] !== maxy || float32Array[node32Index + 5] !== maxz) {\n        float32Array[node32Index + 0] = minx;\n        float32Array[node32Index + 1] = miny;\n        float32Array[node32Index + 2] = minz;\n        float32Array[node32Index + 3] = maxx;\n        float32Array[node32Index + 4] = maxy;\n        float32Array[node32Index + 5] = maxz;\n        return true;\n      } else {\n        return false;\n      }\n    } else {\n      const left = node32Index + 8;\n      const right = uint32Array[node32Index + 6];\n\n      // the identifying node indices provided by the shapecast function include offsets of all\n      // root buffers to guarantee they're unique between roots so offset left and right indices here.\n      const offsetLeft = left + byteOffset;\n      const offsetRight = right + byteOffset;\n      let forceChildren = force;\n      let includesLeft = false;\n      let includesRight = false;\n      if (nodeIndices) {\n        // if we see that neither the left or right child are included in the set that need to be updated\n        // then we assume that all children need to be updated.\n        if (!forceChildren) {\n          includesLeft = nodeIndices.has(offsetLeft);\n          includesRight = nodeIndices.has(offsetRight);\n          forceChildren = !includesLeft && !includesRight;\n        }\n      } else {\n        includesLeft = true;\n        includesRight = true;\n      }\n      const traverseLeft = forceChildren || includesLeft;\n      const traverseRight = forceChildren || includesRight;\n      let leftChange = false;\n      if (traverseLeft) {\n        leftChange = _traverse(left, byteOffset, forceChildren);\n      }\n      let rightChange = false;\n      if (traverseRight) {\n        rightChange = _traverse(right, byteOffset, forceChildren);\n      }\n      const didChange = leftChange || rightChange;\n      if (didChange) {\n        for (let i = 0; i < 3; i++) {\n          const lefti = left + i;\n          const righti = right + i;\n          const minLeftValue = float32Array[lefti];\n          const maxLeftValue = float32Array[lefti + 3];\n          const minRightValue = float32Array[righti];\n          const maxRightValue = float32Array[righti + 3];\n          float32Array[node32Index + i] = minLeftValue < minRightValue ? minLeftValue : minRightValue;\n          float32Array[node32Index + i + 3] = maxLeftValue > maxRightValue ? maxLeftValue : maxRightValue;\n        }\n      }\n      return didChange;\n    }\n  }\n}\nexport { refit };", "map": {"version": 3, "names": ["IS_LEAFNODE_FLAG", "refit", "bvh", "nodeIndices", "Array", "isArray", "Set", "geometry", "indexArr", "index", "array", "posAttr", "attributes", "position", "buffer", "uint32Array", "uint16Array", "float32Array", "byteOffset", "roots", "_roots", "i", "l", "length", "Uint32Array", "Uint16Array", "Float32Array", "_traverse", "byteLength", "node32Index", "force", "node16Index", "<PERSON><PERSON><PERSON><PERSON>", "offset", "count", "minx", "Infinity", "miny", "minz", "maxx", "maxy", "maxz", "x", "getX", "y", "getY", "z", "getZ", "left", "right", "offsetLeft", "offsetRight", "forceChildren", "includesLeft", "includesRight", "has", "traverseLeft", "traverseRight", "leftChange", "rightChange", "<PERSON><PERSON><PERSON><PERSON>", "lefti", "righti", "minLeftValue", "maxLeftValue", "minRightValue", "maxRightValue"], "sources": ["F:/= 神灯智库/- AI 创作/AI APP/神灯麻将大师/mahjong-master-react/node_modules/three-mesh-bvh/src/core/cast/refit.generated.js"], "sourcesContent": ["import { IS_LEAFNODE_FLAG } from '../Constants.js';\n\n/****************************************************/\n/* This file is generated from \"refit.template.js\". */\n/****************************************************/\n\nfunction refit( bvh, nodeIndices = null ) {\n\n\tif ( nodeIndices && Array.isArray( nodeIndices ) ) {\n\n\t\tnodeIndices = new Set( nodeIndices );\n\n\t}\n\n\tconst geometry = bvh.geometry;\n\tconst indexArr = geometry.index ? geometry.index.array : null;\n\tconst posAttr = geometry.attributes.position;\n\n\tlet buffer, uint32Array, uint16Array, float32Array;\n\tlet byteOffset = 0;\n\tconst roots = bvh._roots;\n\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\tbuffer = roots[ i ];\n\t\tuint32Array = new Uint32Array( buffer );\n\t\tuint16Array = new Uint16Array( buffer );\n\t\tfloat32Array = new Float32Array( buffer );\n\n\t\t_traverse( 0, byteOffset );\n\t\tbyteOffset += buffer.byteLength;\n\n\t}\n\n\tfunction _traverse( node32Index, byteOffset, force = false ) {\n\n\t\tconst node16Index = node32Index * 2;\n\t\tconst isLeaf = uint16Array[ node16Index + 15 ] === IS_LEAFNODE_FLAG;\n\t\tif ( isLeaf ) {\n\n\t\t\tconst offset = uint32Array[ node32Index + 6 ];\n\t\t\tconst count = uint16Array[ node16Index + 14 ];\n\n\t\t\tlet minx = Infinity;\n\t\t\tlet miny = Infinity;\n\t\t\tlet minz = Infinity;\n\t\t\tlet maxx = - Infinity;\n\t\t\tlet maxy = - Infinity;\n\t\t\tlet maxz = - Infinity;\n\n\n\t\t\tfor ( let i = 3 * offset, l = 3 * ( offset + count ); i < l; i ++ ) {\n\n\t\t\t\tlet index = indexArr[ i ];\n\t\t\t\tconst x = posAttr.getX( index );\n\t\t\t\tconst y = posAttr.getY( index );\n\t\t\t\tconst z = posAttr.getZ( index );\n\n\t\t\t\tif ( x < minx ) minx = x;\n\t\t\t\tif ( x > maxx ) maxx = x;\n\n\t\t\t\tif ( y < miny ) miny = y;\n\t\t\t\tif ( y > maxy ) maxy = y;\n\n\t\t\t\tif ( z < minz ) minz = z;\n\t\t\t\tif ( z > maxz ) maxz = z;\n\n\t\t\t}\n\n\n\t\t\tif (\n\t\t\t\tfloat32Array[ node32Index + 0 ] !== minx ||\n\t\t\t\tfloat32Array[ node32Index + 1 ] !== miny ||\n\t\t\t\tfloat32Array[ node32Index + 2 ] !== minz ||\n\n\t\t\t\tfloat32Array[ node32Index + 3 ] !== maxx ||\n\t\t\t\tfloat32Array[ node32Index + 4 ] !== maxy ||\n\t\t\t\tfloat32Array[ node32Index + 5 ] !== maxz\n\t\t\t) {\n\n\t\t\t\tfloat32Array[ node32Index + 0 ] = minx;\n\t\t\t\tfloat32Array[ node32Index + 1 ] = miny;\n\t\t\t\tfloat32Array[ node32Index + 2 ] = minz;\n\n\t\t\t\tfloat32Array[ node32Index + 3 ] = maxx;\n\t\t\t\tfloat32Array[ node32Index + 4 ] = maxy;\n\t\t\t\tfloat32Array[ node32Index + 5 ] = maxz;\n\n\t\t\t\treturn true;\n\n\t\t\t} else {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconst left = node32Index + 8;\n\t\t\tconst right = uint32Array[ node32Index + 6 ];\n\n\t\t\t// the identifying node indices provided by the shapecast function include offsets of all\n\t\t\t// root buffers to guarantee they're unique between roots so offset left and right indices here.\n\t\t\tconst offsetLeft = left + byteOffset;\n\t\t\tconst offsetRight = right + byteOffset;\n\t\t\tlet forceChildren = force;\n\t\t\tlet includesLeft = false;\n\t\t\tlet includesRight = false;\n\n\t\t\tif ( nodeIndices ) {\n\n\t\t\t\t// if we see that neither the left or right child are included in the set that need to be updated\n\t\t\t\t// then we assume that all children need to be updated.\n\t\t\t\tif ( ! forceChildren ) {\n\n\t\t\t\t\tincludesLeft = nodeIndices.has( offsetLeft );\n\t\t\t\t\tincludesRight = nodeIndices.has( offsetRight );\n\t\t\t\t\tforceChildren = ! includesLeft && ! includesRight;\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tincludesLeft = true;\n\t\t\t\tincludesRight = true;\n\n\t\t\t}\n\n\t\t\tconst traverseLeft = forceChildren || includesLeft;\n\t\t\tconst traverseRight = forceChildren || includesRight;\n\n\t\t\tlet leftChange = false;\n\t\t\tif ( traverseLeft ) {\n\n\t\t\t\tleftChange = _traverse( left, byteOffset, forceChildren );\n\n\t\t\t}\n\n\t\t\tlet rightChange = false;\n\t\t\tif ( traverseRight ) {\n\n\t\t\t\trightChange = _traverse( right, byteOffset, forceChildren );\n\n\t\t\t}\n\n\t\t\tconst didChange = leftChange || rightChange;\n\t\t\tif ( didChange ) {\n\n\t\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\t\tconst lefti = left + i;\n\t\t\t\t\tconst righti = right + i;\n\t\t\t\t\tconst minLeftValue = float32Array[ lefti ];\n\t\t\t\t\tconst maxLeftValue = float32Array[ lefti + 3 ];\n\t\t\t\t\tconst minRightValue = float32Array[ righti ];\n\t\t\t\t\tconst maxRightValue = float32Array[ righti + 3 ];\n\n\t\t\t\t\tfloat32Array[ node32Index + i ] = minLeftValue < minRightValue ? minLeftValue : minRightValue;\n\t\t\t\t\tfloat32Array[ node32Index + i + 3 ] = maxLeftValue > maxRightValue ? maxLeftValue : maxRightValue;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn didChange;\n\n\t\t}\n\n\t}\n\n}\n\nexport { refit };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,iBAAiB;;AAElD;AACA;AACA;;AAEA,SAASC,KAAKA,CAAEC,GAAG,EAAEC,WAAW,GAAG,IAAI,EAAG;EAEzC,IAAKA,WAAW,IAAIC,KAAK,CAACC,OAAO,CAAEF,WAAY,CAAC,EAAG;IAElDA,WAAW,GAAG,IAAIG,GAAG,CAAEH,WAAY,CAAC;EAErC;EAEA,MAAMI,QAAQ,GAAGL,GAAG,CAACK,QAAQ;EAC7B,MAAMC,QAAQ,GAAGD,QAAQ,CAACE,KAAK,GAAGF,QAAQ,CAACE,KAAK,CAACC,KAAK,GAAG,IAAI;EAC7D,MAAMC,OAAO,GAAGJ,QAAQ,CAACK,UAAU,CAACC,QAAQ;EAE5C,IAAIC,MAAM,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY;EAClD,IAAIC,UAAU,GAAG,CAAC;EAClB,MAAMC,KAAK,GAAGjB,GAAG,CAACkB,MAAM;EACxB,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;IAEhDP,MAAM,GAAGK,KAAK,CAAEE,CAAC,CAAE;IACnBN,WAAW,GAAG,IAAIS,WAAW,CAAEV,MAAO,CAAC;IACvCE,WAAW,GAAG,IAAIS,WAAW,CAAEX,MAAO,CAAC;IACvCG,YAAY,GAAG,IAAIS,YAAY,CAAEZ,MAAO,CAAC;IAEzCa,SAAS,CAAE,CAAC,EAAET,UAAW,CAAC;IAC1BA,UAAU,IAAIJ,MAAM,CAACc,UAAU;EAEhC;EAEA,SAASD,SAASA,CAAEE,WAAW,EAAEX,UAAU,EAAEY,KAAK,GAAG,KAAK,EAAG;IAE5D,MAAMC,WAAW,GAAGF,WAAW,GAAG,CAAC;IACnC,MAAMG,MAAM,GAAGhB,WAAW,CAAEe,WAAW,GAAG,EAAE,CAAE,KAAK/B,gBAAgB;IACnE,IAAKgC,MAAM,EAAG;MAEb,MAAMC,MAAM,GAAGlB,WAAW,CAAEc,WAAW,GAAG,CAAC,CAAE;MAC7C,MAAMK,KAAK,GAAGlB,WAAW,CAAEe,WAAW,GAAG,EAAE,CAAE;MAE7C,IAAII,IAAI,GAAGC,QAAQ;MACnB,IAAIC,IAAI,GAAGD,QAAQ;MACnB,IAAIE,IAAI,GAAGF,QAAQ;MACnB,IAAIG,IAAI,GAAG,CAAEH,QAAQ;MACrB,IAAII,IAAI,GAAG,CAAEJ,QAAQ;MACrB,IAAIK,IAAI,GAAG,CAAEL,QAAQ;MAGrB,KAAM,IAAIf,CAAC,GAAG,CAAC,GAAGY,MAAM,EAAEX,CAAC,GAAG,CAAC,IAAKW,MAAM,GAAGC,KAAK,CAAE,EAAEb,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;QAEnE,IAAIZ,KAAK,GAAGD,QAAQ,CAAEa,CAAC,CAAE;QACzB,MAAMqB,CAAC,GAAG/B,OAAO,CAACgC,IAAI,CAAElC,KAAM,CAAC;QAC/B,MAAMmC,CAAC,GAAGjC,OAAO,CAACkC,IAAI,CAAEpC,KAAM,CAAC;QAC/B,MAAMqC,CAAC,GAAGnC,OAAO,CAACoC,IAAI,CAAEtC,KAAM,CAAC;QAE/B,IAAKiC,CAAC,GAAGP,IAAI,EAAGA,IAAI,GAAGO,CAAC;QACxB,IAAKA,CAAC,GAAGH,IAAI,EAAGA,IAAI,GAAGG,CAAC;QAExB,IAAKE,CAAC,GAAGP,IAAI,EAAGA,IAAI,GAAGO,CAAC;QACxB,IAAKA,CAAC,GAAGJ,IAAI,EAAGA,IAAI,GAAGI,CAAC;QAExB,IAAKE,CAAC,GAAGR,IAAI,EAAGA,IAAI,GAAGQ,CAAC;QACxB,IAAKA,CAAC,GAAGL,IAAI,EAAGA,IAAI,GAAGK,CAAC;MAEzB;MAGA,IACC7B,YAAY,CAAEY,WAAW,GAAG,CAAC,CAAE,KAAKM,IAAI,IACxClB,YAAY,CAAEY,WAAW,GAAG,CAAC,CAAE,KAAKQ,IAAI,IACxCpB,YAAY,CAAEY,WAAW,GAAG,CAAC,CAAE,KAAKS,IAAI,IAExCrB,YAAY,CAAEY,WAAW,GAAG,CAAC,CAAE,KAAKU,IAAI,IACxCtB,YAAY,CAAEY,WAAW,GAAG,CAAC,CAAE,KAAKW,IAAI,IACxCvB,YAAY,CAAEY,WAAW,GAAG,CAAC,CAAE,KAAKY,IAAI,EACvC;QAEDxB,YAAY,CAAEY,WAAW,GAAG,CAAC,CAAE,GAAGM,IAAI;QACtClB,YAAY,CAAEY,WAAW,GAAG,CAAC,CAAE,GAAGQ,IAAI;QACtCpB,YAAY,CAAEY,WAAW,GAAG,CAAC,CAAE,GAAGS,IAAI;QAEtCrB,YAAY,CAAEY,WAAW,GAAG,CAAC,CAAE,GAAGU,IAAI;QACtCtB,YAAY,CAAEY,WAAW,GAAG,CAAC,CAAE,GAAGW,IAAI;QACtCvB,YAAY,CAAEY,WAAW,GAAG,CAAC,CAAE,GAAGY,IAAI;QAEtC,OAAO,IAAI;MAEZ,CAAC,MAAM;QAEN,OAAO,KAAK;MAEb;IAED,CAAC,MAAM;MAEN,MAAMO,IAAI,GAAGnB,WAAW,GAAG,CAAC;MAC5B,MAAMoB,KAAK,GAAGlC,WAAW,CAAEc,WAAW,GAAG,CAAC,CAAE;;MAE5C;MACA;MACA,MAAMqB,UAAU,GAAGF,IAAI,GAAG9B,UAAU;MACpC,MAAMiC,WAAW,GAAGF,KAAK,GAAG/B,UAAU;MACtC,IAAIkC,aAAa,GAAGtB,KAAK;MACzB,IAAIuB,YAAY,GAAG,KAAK;MACxB,IAAIC,aAAa,GAAG,KAAK;MAEzB,IAAKnD,WAAW,EAAG;QAElB;QACA;QACA,IAAK,CAAEiD,aAAa,EAAG;UAEtBC,YAAY,GAAGlD,WAAW,CAACoD,GAAG,CAAEL,UAAW,CAAC;UAC5CI,aAAa,GAAGnD,WAAW,CAACoD,GAAG,CAAEJ,WAAY,CAAC;UAC9CC,aAAa,GAAG,CAAEC,YAAY,IAAI,CAAEC,aAAa;QAElD;MAED,CAAC,MAAM;QAEND,YAAY,GAAG,IAAI;QACnBC,aAAa,GAAG,IAAI;MAErB;MAEA,MAAME,YAAY,GAAGJ,aAAa,IAAIC,YAAY;MAClD,MAAMI,aAAa,GAAGL,aAAa,IAAIE,aAAa;MAEpD,IAAII,UAAU,GAAG,KAAK;MACtB,IAAKF,YAAY,EAAG;QAEnBE,UAAU,GAAG/B,SAAS,CAAEqB,IAAI,EAAE9B,UAAU,EAAEkC,aAAc,CAAC;MAE1D;MAEA,IAAIO,WAAW,GAAG,KAAK;MACvB,IAAKF,aAAa,EAAG;QAEpBE,WAAW,GAAGhC,SAAS,CAAEsB,KAAK,EAAE/B,UAAU,EAAEkC,aAAc,CAAC;MAE5D;MAEA,MAAMQ,SAAS,GAAGF,UAAU,IAAIC,WAAW;MAC3C,IAAKC,SAAS,EAAG;QAEhB,KAAM,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;UAE9B,MAAMwC,KAAK,GAAGb,IAAI,GAAG3B,CAAC;UACtB,MAAMyC,MAAM,GAAGb,KAAK,GAAG5B,CAAC;UACxB,MAAM0C,YAAY,GAAG9C,YAAY,CAAE4C,KAAK,CAAE;UAC1C,MAAMG,YAAY,GAAG/C,YAAY,CAAE4C,KAAK,GAAG,CAAC,CAAE;UAC9C,MAAMI,aAAa,GAAGhD,YAAY,CAAE6C,MAAM,CAAE;UAC5C,MAAMI,aAAa,GAAGjD,YAAY,CAAE6C,MAAM,GAAG,CAAC,CAAE;UAEhD7C,YAAY,CAAEY,WAAW,GAAGR,CAAC,CAAE,GAAG0C,YAAY,GAAGE,aAAa,GAAGF,YAAY,GAAGE,aAAa;UAC7FhD,YAAY,CAAEY,WAAW,GAAGR,CAAC,GAAG,CAAC,CAAE,GAAG2C,YAAY,GAAGE,aAAa,GAAGF,YAAY,GAAGE,aAAa;QAElG;MAED;MAEA,OAAON,SAAS;IAEjB;EAED;AAED;AAEA,SAAS3D,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}